load("//tools/build_defs/oss:rn_defs.bzl", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "jni-interface",
    srcs = [],
    header_namespace = "hermes/reactexecutor",
    exported_headers = glob(["*.h"]),
    visibility = ["PUBLIC"],
)

rn_xplat_cxx_library(
    name = "jni",
    srcs = ["OnLoad.cpp"],
    headers = [],
    header_namespace = "",
    compiler_flags = ["-fexceptions"],
    soname = "libhermes_executor.$(ext)",
    visibility = ["PUBLIC"],
)
