"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
Object.defineProperty(exports, "useAsyncStorage", {
  enumerable: true,
  get: function () {
    return _hooks.useAsyncStorage;
  }
});
var _AsyncStorage = _interopRequireDefault(require("./AsyncStorage"));
var _hooks = require("./hooks");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var _default = _AsyncStorage.default;
exports.default = _default;
//# sourceMappingURL=index.js.map