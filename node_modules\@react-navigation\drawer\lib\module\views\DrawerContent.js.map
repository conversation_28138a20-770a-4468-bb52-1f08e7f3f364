{"version": 3, "names": ["React", "DrawerContentScrollView", "DrawerItemList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "descriptors", "state", "rest", "focusedRoute", "routes", "index", "focusedDescriptor", "key", "focusedOptions", "options", "drawerContentStyle", "drawerContentContainerStyle"], "sourceRoot": "../../../src", "sources": ["views/DrawerContent.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,eAAe,SAASC,aAAa,OAIL;EAAA,IAJM;IACpCC,WAAW;IACXC,KAAK;IACL,GAAGC;EACwB,CAAC;EAC5B,MAAMC,YAAY,GAAGF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACI,KAAK,CAAC;EAC9C,MAAMC,iBAAiB,GAAGN,WAAW,CAACG,YAAY,CAACI,GAAG,CAAC;EACvD,MAAMC,cAAc,GAAGF,iBAAiB,CAACG,OAAO;EAEhD,MAAM;IAAEC,kBAAkB;IAAEC;EAA4B,CAAC,GAAGH,cAAc;EAE1E,oBACE,oBAAC,uBAAuB,eAClBN,IAAI;IACR,qBAAqB,EAAES,2BAA4B;IACnD,KAAK,EAAED;EAAmB,iBAE1B,oBAAC,cAAc;IAAC,WAAW,EAAEV,WAAY;IAAC,KAAK,EAAEC;EAAM,GAAKC,IAAI,EAAI,CAC5C;AAE9B"}