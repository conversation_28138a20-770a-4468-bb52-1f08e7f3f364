'use client';

import { useState } from 'react';
import {
  DocumentArrowDownIcon,
  CalendarIcon,
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  BriefcaseIcon,
  ClockIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingState';

interface Report {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  type: 'financial' | 'user' | 'service' | 'performance';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  lastGenerated: string;
  fileSize: string;
  downloadUrl: string;
  isScheduled: boolean;
}

// Mock data for reports
const mockReports: Report[] = [
  {
    id: '1',
    name: 'Monthly Revenue Report',
    nameAr: 'تقرير الإيرادات الشهرية',
    description: 'Comprehensive monthly revenue analysis',
    descriptionAr: 'تحليل شامل للإيرادات الشهرية',
    type: 'financial',
    frequency: 'monthly',
    lastGenerated: '2024-06-01',
    fileSize: '2.3 MB',
    downloadUrl: '/reports/revenue-2024-06.pdf',
    isScheduled: true,
  },
  {
    id: '2',
    name: 'User Growth Analysis',
    nameAr: 'تحليل نمو المستخدمين',
    description: 'Weekly user registration and activity report',
    descriptionAr: 'تقرير أسبوعي لتسجيل المستخدمين والنشاط',
    type: 'user',
    frequency: 'weekly',
    lastGenerated: '2024-06-08',
    fileSize: '1.8 MB',
    downloadUrl: '/reports/users-2024-w23.pdf',
    isScheduled: true,
  },
  {
    id: '3',
    name: 'Service Performance Report',
    nameAr: 'تقرير أداء الخدمات',
    description: 'Monthly analysis of service bookings and ratings',
    descriptionAr: 'تحليل شهري لحجوزات الخدمات والتقييمات',
    type: 'service',
    frequency: 'monthly',
    lastGenerated: '2024-06-01',
    fileSize: '3.1 MB',
    downloadUrl: '/reports/services-2024-06.pdf',
    isScheduled: true,
  },
  {
    id: '4',
    name: 'Expert Performance Dashboard',
    nameAr: 'لوحة أداء الخبراء',
    description: 'Quarterly expert performance and earnings report',
    descriptionAr: 'تقرير ربع سنوي لأداء الخبراء والأرباح',
    type: 'performance',
    frequency: 'quarterly',
    lastGenerated: '2024-04-01',
    fileSize: '4.7 MB',
    downloadUrl: '/reports/experts-2024-q1.pdf',
    isScheduled: false,
  },
  {
    id: '5',
    name: 'Daily Operations Summary',
    nameAr: 'ملخص العمليات اليومية',
    description: 'Daily summary of platform activities',
    descriptionAr: 'ملخص يومي لأنشطة المنصة',
    type: 'performance',
    frequency: 'daily',
    lastGenerated: '2024-06-10',
    fileSize: '856 KB',
    downloadUrl: '/reports/daily-2024-06-10.pdf',
    isScheduled: true,
  },
];

const reportTypeIcons = {
  financial: CurrencyDollarIcon,
  user: UsersIcon,
  service: BriefcaseIcon,
  performance: ChartBarIcon,
};

const reportTypeColors = {
  financial: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  user: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  service: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  performance: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
};

const frequencyLabels = {
  daily: 'يومي',
  weekly: 'أسبوعي',
  monthly: 'شهري',
  quarterly: 'ربع سنوي',
  yearly: 'سنوي',
};

export default function ReportsPage() {
  const [reports, setReports] = useState<Report[]>(mockReports);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<'all' | 'financial' | 'user' | 'service' | 'performance'>('all');
  const [selectedFrequency, setSelectedFrequency] = useState<'all' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'>('all');
  const [isGenerating, setIsGenerating] = useState<string | null>(null);

  // Filter reports
  const filteredReports = reports.filter(report => {
    const matchesType = selectedType === 'all' || report.type === selectedType;
    const matchesFrequency = selectedFrequency === 'all' || report.frequency === selectedFrequency;
    return matchesType && matchesFrequency;
  });

  const handleGenerateReport = async (reportId: string) => {
    setIsGenerating(reportId);
    // Simulate report generation
    setTimeout(() => {
      setReports(prev => prev.map(report => 
        report.id === reportId 
          ? { ...report, lastGenerated: new Date().toISOString().split('T')[0] }
          : report
      ));
      setIsGenerating(null);
    }, 3000);
  };

  const handleDownloadReport = (report: Report) => {
    // Simulate download
    console.log(`Downloading report: ${report.downloadUrl}`);
  };

  const handleToggleSchedule = (reportId: string) => {
    setReports(prev => prev.map(report => 
      report.id === reportId 
        ? { ...report, isScheduled: !report.isScheduled }
        : report
    ));
  };

  if (isLoading) {
    return <LoadingState message="جاري تحميل التقارير..." />;
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          التقارير والتحليلات
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          إنشاء وتحميل التقارير التفصيلية لأداء منصة فريلا سوريا
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentArrowDownIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي التقارير
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    التقارير المجدولة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.filter(r => r.isScheduled).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    تقارير هذا الشهر
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.filter(r => new Date(r.lastGenerated).getMonth() === new Date().getMonth()).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    حجم البيانات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.reduce((total, r) => total + parseFloat(r.fileSize), 0).toFixed(1)} MB
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Type filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نوع التقرير
            </label>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as any)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الأنواع</option>
              <option value="financial">مالية</option>
              <option value="user">المستخدمين</option>
              <option value="service">الخدمات</option>
              <option value="performance">الأداء</option>
            </select>
          </div>

          {/* Frequency filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              التكرار
            </label>
            <select
              value={selectedFrequency}
              onChange={(e) => setSelectedFrequency(e.target.value as any)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع التكرارات</option>
              <option value="daily">يومي</option>
              <option value="weekly">أسبوعي</option>
              <option value="monthly">شهري</option>
              <option value="quarterly">ربع سنوي</option>
              <option value="yearly">سنوي</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reports grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredReports.map((report) => {
          const IconComponent = reportTypeIcons[report.type];
          return (
            <div key={report.id} className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="mr-3">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {report.nameAr}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {report.descriptionAr}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${reportTypeColors[report.type]}`}>
                      {report.type === 'financial' ? 'مالي' :
                       report.type === 'user' ? 'مستخدمين' :
                       report.type === 'service' ? 'خدمات' : 'أداء'}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">التكرار:</span>
                    <span className="text-gray-900 dark:text-white">{frequencyLabels[report.frequency]}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">آخر إنشاء:</span>
                    <span className="text-gray-900 dark:text-white">
                      {new Date(report.lastGenerated).toLocaleDateString('ar-SY')}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">حجم الملف:</span>
                    <span className="text-gray-900 dark:text-white">{report.fileSize}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">مجدول:</span>
                    <button
                      type="button"
                      onClick={() => handleToggleSchedule(report.id)}
                      className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                        report.isScheduled
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                      }`}
                    >
                      {report.isScheduled ? 'نعم' : 'لا'}
                    </button>
                  </div>
                </div>

                <div className="mt-6 flex space-x-3 space-x-reverse">
                  <button
                    type="button"
                    onClick={() => handleDownloadReport(report)}
                    className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 ml-2" />
                    تحميل
                  </button>
                  <button
                    type="button"
                    onClick={() => handleGenerateReport(report.id)}
                    disabled={isGenerating === report.id}
                    className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50"
                  >
                    {isGenerating === report.id ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 dark:border-white ml-2"></div>
                        جاري الإنشاء...
                      </>
                    ) : (
                      <>
                        <ChartBarIcon className="h-4 w-4 ml-2" />
                        إنشاء جديد
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          إجراءات سريعة
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            type="button"
            className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <CurrencyDollarIcon className="h-5 w-5 ml-2" />
            تقرير الإيرادات الفوري
          </button>
          <button
            type="button"
            className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <UsersIcon className="h-5 w-5 ml-2" />
            تقرير المستخدمين الفوري
          </button>
          <button
            type="button"
            className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <BriefcaseIcon className="h-5 w-5 ml-2" />
            تقرير الخدمات الفوري
          </button>
        </div>
      </div>
    </div>
  );
}
