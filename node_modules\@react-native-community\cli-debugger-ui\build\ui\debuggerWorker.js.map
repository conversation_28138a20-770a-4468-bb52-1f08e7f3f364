{"version": 3, "names": ["onmessage", "visibilityState", "showVisibilityWarning", "hasWarned", "console", "warn", "toString", "includes", "messageHandlers", "executeApplicationScript", "message", "sendReply", "key", "inject", "self", "JSON", "parse", "error", "importScripts", "url", "err", "setDebuggerVisibility", "object", "data", "result", "postMessage", "replyID", "id", "handler", "method", "returnValue", "__fbBatchedBridge", "apply", "arguments", "stringify"], "sources": ["../../src/ui/debuggerWorker.js"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/* global __fbBatchedBridge, self, importScripts, postMessage, onmessage: true */\n/* eslint no-unused-vars: 0 */\n\nonmessage = (function () {\n  var visibilityState;\n  var showVisibilityWarning = (function () {\n    var hasWarned = false;\n    return function () {\n      // Wait until `YellowBox` gets initialized before displaying the warning.\n      if (hasWarned || console.warn.toString().includes('[native code]')) {\n        return;\n      }\n      hasWarned = true;\n      console.warn(\n        'Remote debugger is in a background tab which may cause apps to ' +\n          'perform slowly. Fix this by foregrounding the tab (or opening it in ' +\n          'a separate window).',\n      );\n    };\n  })();\n\n  var messageHandlers = {\n    executeApplicationScript: function (message, sendReply) {\n      for (var key in message.inject) {\n        self[key] = JSON.parse(message.inject[key]);\n      }\n      var error;\n      try {\n        importScripts(message.url);\n      } catch (err) {\n        error = err.message;\n      }\n      sendReply(null /* result */, error);\n    },\n    setDebuggerVisibility: function (message) {\n      visibilityState = message.visibilityState;\n    },\n  };\n\n  return function (message) {\n    if (visibilityState === 'hidden') {\n      showVisibilityWarning();\n    }\n\n    var object = message.data;\n\n    var sendReply = function (result, error) {\n      postMessage({replyID: object.id, result: result, error: error});\n    };\n\n    var handler = messageHandlers[object.method];\n    if (handler) {\n      // Special cased handlers\n      handler(object, sendReply);\n    } else {\n      // Other methods get called on the bridge\n      var returnValue = [[], [], [], 0];\n      var error;\n      try {\n        if (typeof __fbBatchedBridge === 'object') {\n          returnValue = __fbBatchedBridge[object.method].apply(\n            null,\n            object.arguments,\n          );\n        } else {\n          error = 'Failed to call function, __fbBatchedBridge is undefined';\n        }\n      } catch (err) {\n        error = err.message;\n      } finally {\n        sendReply(JSON.stringify(returnValue), error);\n      }\n    }\n  };\n})();\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEAA,SAAS,GAAI,YAAY;EACvB,IAAIC,eAAe;EACnB,IAAIC,qBAAqB,GAAI,YAAY;IACvC,IAAIC,SAAS,GAAG,KAAK;IACrB,OAAO,YAAY;MACjB;MACA,IAAIA,SAAS,IAAIC,OAAO,CAACC,IAAI,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;QAClE;MACF;MACAJ,SAAS,GAAG,IAAI;MAChBC,OAAO,CAACC,IAAI,CACV,iEAAiE,GAC/D,sEAAsE,GACtE,qBAAqB,CACxB;IACH,CAAC;EACH,CAAC,EAAG;EAEJ,IAAIG,eAAe,GAAG;IACpBC,wBAAwB,EAAE,UAAUC,OAAO,EAAEC,SAAS,EAAE;MACtD,KAAK,IAAIC,GAAG,IAAIF,OAAO,CAACG,MAAM,EAAE;QAC9BC,IAAI,CAACF,GAAG,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACN,OAAO,CAACG,MAAM,CAACD,GAAG,CAAC,CAAC;MAC7C;MACA,IAAIK,KAAK;MACT,IAAI;QACFC,aAAa,CAACR,OAAO,CAACS,GAAG,CAAC;MAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZH,KAAK,GAAGG,GAAG,CAACV,OAAO;MACrB;MACAC,SAAS,CAAC,IAAI,CAAC,cAAcM,KAAK,CAAC;IACrC,CAAC;IACDI,qBAAqB,EAAE,UAAUX,OAAO,EAAE;MACxCT,eAAe,GAAGS,OAAO,CAACT,eAAe;IAC3C;EACF,CAAC;EAED,OAAO,UAAUS,OAAO,EAAE;IACxB,IAAIT,eAAe,KAAK,QAAQ,EAAE;MAChCC,qBAAqB,EAAE;IACzB;IAEA,IAAIoB,MAAM,GAAGZ,OAAO,CAACa,IAAI;IAEzB,IAAIZ,SAAS,GAAG,UAAUa,MAAM,EAAEP,KAAK,EAAE;MACvCQ,WAAW,CAAC;QAACC,OAAO,EAAEJ,MAAM,CAACK,EAAE;QAAEH,MAAM,EAAEA,MAAM;QAAEP,KAAK,EAAEA;MAAK,CAAC,CAAC;IACjE,CAAC;IAED,IAAIW,OAAO,GAAGpB,eAAe,CAACc,MAAM,CAACO,MAAM,CAAC;IAC5C,IAAID,OAAO,EAAE;MACX;MACAA,OAAO,CAACN,MAAM,EAAEX,SAAS,CAAC;IAC5B,CAAC,MAAM;MACL;MACA,IAAImB,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACjC,IAAIb,KAAK;MACT,IAAI;QACF,IAAI,OAAOc,iBAAiB,KAAK,QAAQ,EAAE;UACzCD,WAAW,GAAGC,iBAAiB,CAACT,MAAM,CAACO,MAAM,CAAC,CAACG,KAAK,CAClD,IAAI,EACJV,MAAM,CAACW,SAAS,CACjB;QACH,CAAC,MAAM;UACLhB,KAAK,GAAG,yDAAyD;QACnE;MACF,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZH,KAAK,GAAGG,GAAG,CAACV,OAAO;MACrB,CAAC,SAAS;QACRC,SAAS,CAACI,IAAI,CAACmB,SAAS,CAACJ,WAAW,CAAC,EAAEb,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;AACH,CAAC,EAAG"}