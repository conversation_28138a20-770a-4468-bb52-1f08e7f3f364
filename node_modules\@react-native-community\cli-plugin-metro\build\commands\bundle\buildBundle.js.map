{"version": 3, "names": ["outputBundle", "require", "buildBundle", "args", "ctx", "output", "config", "loadMetroConfig", "maxWorkers", "resetCache", "buildBundleWithConfig", "resolver", "platforms", "indexOf", "platform", "logger", "error", "chalk", "bold", "info", "map", "x", "join", "Error", "process", "env", "NODE_ENV", "dev", "sourceMapUrl", "sourcemapOutput", "sourcemapUseAbsolutePath", "path", "basename", "requestOpts", "entryFile", "minify", "undefined", "unstable_transformProfile", "unstableTransformProfile", "generateStaticViewConfigs", "server", "Server", "bundle", "build", "save", "outputAssets", "getAssets", "DEFAULT_BUNDLE_OPTIONS", "bundleType", "saveAssets", "assetsDest", "assetCatalogDest", "end"], "sources": ["../../../src/commands/bundle/buildBundle.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport Server from 'metro/src/Server';\n// @ts-ignore - no typed definition for the package\nconst outputBundle = require('metro/src/shared/output/bundle');\nimport type {BundleOptions} from 'metro/src/shared/types';\nimport type {ConfigT} from 'metro-config';\nimport path from 'path';\nimport chalk from 'chalk';\nimport {CommandLineArgs} from './bundleCommandLineArgs';\nimport type {Config} from '@react-native-community/cli-types';\nimport saveAssets from './saveAssets';\nimport {default as loadMetroConfig} from '../../tools/loadMetroConfig';\nimport {logger} from '@react-native-community/cli-tools';\n\ninterface RequestOptions {\n  entryFile: string;\n  sourceMapUrl: string | undefined;\n  dev: boolean;\n  minify: boolean;\n  platform: string | undefined;\n  unstable_transformProfile: BundleOptions['unstable_transformProfile'];\n  generateStaticViewConfigs: boolean;\n}\n\nasync function buildBundle(\n  args: CommandLineArgs,\n  ctx: Config,\n  output: typeof outputBundle = outputBundle,\n) {\n  const config = await loadMetroConfig(ctx, {\n    maxWorkers: args.maxWorkers,\n    resetCache: args.resetCache,\n    config: args.config,\n  });\n\n  return buildBundleWithConfig(args, config, output);\n}\n\n/**\n * Create a bundle using a pre-loaded Metro config. The config can be\n * re-used for several bundling calls if multiple platforms are being\n * bundled.\n */\nexport async function buildBundleWithConfig(\n  args: CommandLineArgs,\n  config: ConfigT,\n  output: typeof outputBundle = outputBundle,\n) {\n  if (config.resolver.platforms.indexOf(args.platform) === -1) {\n    logger.error(\n      `Invalid platform ${\n        args.platform ? `\"${chalk.bold(args.platform)}\" ` : ''\n      }selected.`,\n    );\n\n    logger.info(\n      `Available platforms are: ${config.resolver.platforms\n        .map((x) => `\"${chalk.bold(x)}\"`)\n        .join(\n          ', ',\n        )}. If you are trying to bundle for an out-of-tree platform, it may not be installed.`,\n    );\n\n    throw new Error('Bundling failed');\n  }\n\n  // This is used by a bazillion of npm modules we don't control so we don't\n  // have other choice than defining it as an env variable here.\n  process.env.NODE_ENV = args.dev ? 'development' : 'production';\n\n  let sourceMapUrl = args.sourcemapOutput;\n  if (sourceMapUrl && !args.sourcemapUseAbsolutePath) {\n    sourceMapUrl = path.basename(sourceMapUrl);\n  }\n\n  const requestOpts: RequestOptions = {\n    entryFile: args.entryFile,\n    sourceMapUrl,\n    dev: args.dev,\n    minify: args.minify !== undefined ? args.minify : !args.dev,\n    platform: args.platform,\n    unstable_transformProfile: args.unstableTransformProfile as BundleOptions['unstable_transformProfile'],\n    generateStaticViewConfigs: args.generateStaticViewConfigs,\n  };\n  const server = new Server(config);\n\n  try {\n    const bundle = await output.build(server, requestOpts);\n\n    await output.save(bundle, args, logger.info);\n\n    // Save the assets of the bundle\n    const outputAssets = await server.getAssets({\n      ...Server.DEFAULT_BUNDLE_OPTIONS,\n      ...requestOpts,\n      bundleType: 'todo',\n    });\n\n    // When we're done saving bundle output and the assets, we're done.\n    return await saveAssets(\n      outputAssets,\n      args.platform,\n      args.assetsDest,\n      args.assetCatalogDest,\n    );\n  } finally {\n    server.end();\n  }\n}\n\nexport default buildBundle;\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAnBzD;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAqB9D,eAAeC,WAAW,CACxBC,IAAqB,EACrBC,GAAW,EACXC,MAA2B,GAAGL,YAAY,EAC1C;EACA,MAAMM,MAAM,GAAG,MAAM,IAAAC,wBAAe,EAACH,GAAG,EAAE;IACxCI,UAAU,EAAEL,IAAI,CAACK,UAAU;IAC3BC,UAAU,EAAEN,IAAI,CAACM,UAAU;IAC3BH,MAAM,EAAEH,IAAI,CAACG;EACf,CAAC,CAAC;EAEF,OAAOI,qBAAqB,CAACP,IAAI,EAAEG,MAAM,EAAED,MAAM,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACO,eAAeK,qBAAqB,CACzCP,IAAqB,EACrBG,MAAe,EACfD,MAA2B,GAAGL,YAAY,EAC1C;EACA,IAAIM,MAAM,CAACK,QAAQ,CAACC,SAAS,CAACC,OAAO,CAACV,IAAI,CAACW,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;IAC3DC,kBAAM,CAACC,KAAK,CACT,oBACCb,IAAI,CAACW,QAAQ,GAAI,IAAGG,gBAAK,CAACC,IAAI,CAACf,IAAI,CAACW,QAAQ,CAAE,IAAG,GAAG,EACrD,WAAU,CACZ;IAEDC,kBAAM,CAACI,IAAI,CACR,4BAA2Bb,MAAM,CAACK,QAAQ,CAACC,SAAS,CAClDQ,GAAG,CAAEC,CAAC,IAAM,IAAGJ,gBAAK,CAACC,IAAI,CAACG,CAAC,CAAE,GAAE,CAAC,CAChCC,IAAI,CACH,IAAI,CACJ,qFAAoF,CACzF;IAED,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC;;EAEA;EACA;EACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAGvB,IAAI,CAACwB,GAAG,GAAG,aAAa,GAAG,YAAY;EAE9D,IAAIC,YAAY,GAAGzB,IAAI,CAAC0B,eAAe;EACvC,IAAID,YAAY,IAAI,CAACzB,IAAI,CAAC2B,wBAAwB,EAAE;IAClDF,YAAY,GAAGG,eAAI,CAACC,QAAQ,CAACJ,YAAY,CAAC;EAC5C;EAEA,MAAMK,WAA2B,GAAG;IAClCC,SAAS,EAAE/B,IAAI,CAAC+B,SAAS;IACzBN,YAAY;IACZD,GAAG,EAAExB,IAAI,CAACwB,GAAG;IACbQ,MAAM,EAAEhC,IAAI,CAACgC,MAAM,KAAKC,SAAS,GAAGjC,IAAI,CAACgC,MAAM,GAAG,CAAChC,IAAI,CAACwB,GAAG;IAC3Db,QAAQ,EAAEX,IAAI,CAACW,QAAQ;IACvBuB,yBAAyB,EAAElC,IAAI,CAACmC,wBAAsE;IACtGC,yBAAyB,EAAEpC,IAAI,CAACoC;EAClC,CAAC;EACD,MAAMC,MAAM,GAAG,KAAIC,iBAAM,EAACnC,MAAM,CAAC;EAEjC,IAAI;IACF,MAAMoC,MAAM,GAAG,MAAMrC,MAAM,CAACsC,KAAK,CAACH,MAAM,EAAEP,WAAW,CAAC;IAEtD,MAAM5B,MAAM,CAACuC,IAAI,CAACF,MAAM,EAAEvC,IAAI,EAAEY,kBAAM,CAACI,IAAI,CAAC;;IAE5C;IACA,MAAM0B,YAAY,GAAG,MAAML,MAAM,CAACM,SAAS,CAAC;MAC1C,GAAGL,iBAAM,CAACM,sBAAsB;MAChC,GAAGd,WAAW;MACde,UAAU,EAAE;IACd,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM,IAAAC,mBAAU,EACrBJ,YAAY,EACZ1C,IAAI,CAACW,QAAQ,EACbX,IAAI,CAAC+C,UAAU,EACf/C,IAAI,CAACgD,gBAAgB,CACtB;EACH,CAAC,SAAS;IACRX,MAAM,CAACY,GAAG,EAAE;EACd;AACF;AAAC,eAEclD,WAAW;AAAA"}