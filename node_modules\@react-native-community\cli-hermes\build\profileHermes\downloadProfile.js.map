{"version": 3, "names": ["getLatestFile", "packageNameWithSuffix", "file", "execSync", "toString", "trim", "e", "execSyncWithLog", "command", "logger", "debug", "downloadProfile", "ctx", "dst<PERSON><PERSON>", "filename", "sourcemapPath", "raw", "shouldGenerateSourcemap", "port", "appId", "appIdSuffix", "host", "androidProject", "getAndroidProject", "packageName", "filter", "Boolean", "join", "CLIError", "info", "root", "success", "osTmpDir", "os", "tmpdir", "tempFile<PERSON>ath", "path", "bundleOptions", "getMetroBundleOptions", "generateSourcemap", "findSourcemap", "warn", "events", "transformer", "transformedFilePath", "basename", "fs", "writeFileSync", "JSON", "stringify", "undefined"], "sources": ["../../src/profileHermes/downloadProfile.ts"], "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {execSync} from 'child_process';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport transformer from 'hermes-profile-transformer';\nimport {findSourcemap, generateSourcemap} from './sourcemapUtils';\nimport {getAndroidProject} from '@react-native-community/cli-platform-android';\nimport {getMetroBundleOptions} from './metroBundleOptions';\n/**\n * Get the last modified hermes profile\n * @param packageNameWithSuffix\n */\nfunction getLatestFile(packageNameWithSuffix: string): string {\n  try {\n    const file = execSync(`adb shell run-as ${packageNameWithSuffix} ls cache/ -tp | grep -v /$ | grep -E '.cpuprofile' | head -1\n        `);\n    return file.toString().trim();\n  } catch (e) {\n    throw e;\n  }\n}\n\nfunction execSyncWithLog(command: string) {\n  logger.debug(`${command}`);\n  return execSync(command);\n}\n\n/**\n * Pull and convert a Hermes tracing profile to Chrome tracing profile\n * @param ctx\n * @param dstPath\n * @param fileName\n * @param sourceMapPath\n * @param raw\n * @param generateSourceMap\n * @param appId\n * @param appIdSuffix\n */\nexport async function downloadProfile(\n  ctx: Config,\n  dstPath: string,\n  filename?: string,\n  sourcemapPath?: string,\n  raw?: boolean,\n  shouldGenerateSourcemap?: boolean,\n  port: string = '8081',\n  appId?: string,\n  appIdSuffix?: string,\n  host: string = 'localhost',\n) {\n  try {\n    const androidProject = getAndroidProject(ctx);\n    const packageNameWithSuffix = [\n      appId || androidProject.packageName,\n      appIdSuffix,\n    ]\n      .filter(Boolean)\n      .join('.');\n\n    // If file name is not specified, pull the latest file from device\n    const file = filename || getLatestFile(packageNameWithSuffix);\n    if (!file) {\n      throw new CLIError(\n        'There is no file in the cache/ directory. Did you record a profile from the developer menu?',\n      );\n    }\n\n    logger.info(`File to be pulled: ${file}`);\n\n    // If destination path is not specified, pull to the current directory\n    dstPath = dstPath || ctx.root;\n\n    logger.debug('Internal commands run to pull the file:');\n\n    // If --raw, pull the hermes profile to dstPath\n    if (raw) {\n      execSyncWithLog(\n        `adb shell run-as ${packageNameWithSuffix} cat cache/${file} > ${dstPath}/${file}`,\n      );\n      logger.success(`Successfully pulled the file to ${dstPath}/${file}`);\n    }\n\n    // Else: transform the profile to Chrome format and pull it to dstPath\n    else {\n      const osTmpDir = os.tmpdir();\n      const tempFilePath = path.join(osTmpDir, file);\n\n      execSyncWithLog(\n        `adb shell run-as ${packageNameWithSuffix} cat cache/${file} > ${tempFilePath}`,\n      );\n\n      const bundleOptions = getMetroBundleOptions(tempFilePath, host);\n\n      // If path to source map is not given\n      if (!sourcemapPath) {\n        // Get or generate the source map\n        if (shouldGenerateSourcemap) {\n          sourcemapPath = await generateSourcemap(port, bundleOptions);\n        } else {\n          sourcemapPath = await findSourcemap(ctx, port, bundleOptions);\n        }\n\n        // Run without source map\n        if (!sourcemapPath) {\n          logger.warn(\n            'Cannot find source maps, running the transformer without it',\n          );\n          logger.info(\n            'Instructions on how to get source maps: set `bundleInDebug: true` in your app/build.gradle file, inside the `project.ext.react` map.',\n          );\n        }\n      }\n\n      // Run transformer tool to convert from Hermes to Chrome format\n      const events = await transformer(\n        tempFilePath,\n        sourcemapPath,\n        'index.bundle',\n      );\n\n      const transformedFilePath = `${dstPath}/${path.basename(\n        file,\n        '.cpuprofile',\n      )}-converted.json`;\n      fs.writeFileSync(\n        transformedFilePath,\n        JSON.stringify(events, undefined, 4),\n        'utf-8',\n      );\n      logger.success(\n        `Successfully converted to Chrome tracing format and pulled the file to ${transformedFilePath}`,\n      );\n    }\n  } catch (e) {\n    throw e;\n  }\n}\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA2D;AAC3D;AACA;AACA;AACA;AACA,SAASA,aAAa,CAACC,qBAA6B,EAAU;EAC5D,IAAI;IACF,MAAMC,IAAI,GAAG,IAAAC,yBAAQ,EAAE,oBAAmBF,qBAAsB;AACpE,SAAS,CAAC;IACN,OAAOC,IAAI,CAACE,QAAQ,EAAE,CAACC,IAAI,EAAE;EAC/B,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,MAAMA,CAAC;EACT;AACF;AAEA,SAASC,eAAe,CAACC,OAAe,EAAE;EACxCC,kBAAM,CAACC,KAAK,CAAE,GAAEF,OAAQ,EAAC,CAAC;EAC1B,OAAO,IAAAL,yBAAQ,EAACK,OAAO,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeG,eAAe,CACnCC,GAAW,EACXC,OAAe,EACfC,QAAiB,EACjBC,aAAsB,EACtBC,GAAa,EACbC,uBAAiC,EACjCC,IAAY,GAAG,MAAM,EACrBC,KAAc,EACdC,WAAoB,EACpBC,IAAY,GAAG,WAAW,EAC1B;EACA,IAAI;IACF,MAAMC,cAAc,GAAG,IAAAC,uCAAiB,EAACX,GAAG,CAAC;IAC7C,MAAMX,qBAAqB,GAAG,CAC5BkB,KAAK,IAAIG,cAAc,CAACE,WAAW,EACnCJ,WAAW,CACZ,CACEK,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;;IAEZ;IACA,MAAMzB,IAAI,GAAGY,QAAQ,IAAId,aAAa,CAACC,qBAAqB,CAAC;IAC7D,IAAI,CAACC,IAAI,EAAE;MACT,MAAM,KAAI0B,oBAAQ,EAChB,6FAA6F,CAC9F;IACH;IAEAnB,kBAAM,CAACoB,IAAI,CAAE,sBAAqB3B,IAAK,EAAC,CAAC;;IAEzC;IACAW,OAAO,GAAGA,OAAO,IAAID,GAAG,CAACkB,IAAI;IAE7BrB,kBAAM,CAACC,KAAK,CAAC,yCAAyC,CAAC;;IAEvD;IACA,IAAIM,GAAG,EAAE;MACPT,eAAe,CACZ,oBAAmBN,qBAAsB,cAAaC,IAAK,MAAKW,OAAQ,IAAGX,IAAK,EAAC,CACnF;MACDO,kBAAM,CAACsB,OAAO,CAAE,mCAAkClB,OAAQ,IAAGX,IAAK,EAAC,CAAC;IACtE;;IAEA;IAAA,KACK;MACH,MAAM8B,QAAQ,GAAGC,aAAE,CAACC,MAAM,EAAE;MAC5B,MAAMC,YAAY,GAAGC,eAAI,CAACT,IAAI,CAACK,QAAQ,EAAE9B,IAAI,CAAC;MAE9CK,eAAe,CACZ,oBAAmBN,qBAAsB,cAAaC,IAAK,MAAKiC,YAAa,EAAC,CAChF;MAED,MAAME,aAAa,GAAG,IAAAC,yCAAqB,EAACH,YAAY,EAAEd,IAAI,CAAC;;MAE/D;MACA,IAAI,CAACN,aAAa,EAAE;QAClB;QACA,IAAIE,uBAAuB,EAAE;UAC3BF,aAAa,GAAG,MAAM,IAAAwB,iCAAiB,EAACrB,IAAI,EAAEmB,aAAa,CAAC;QAC9D,CAAC,MAAM;UACLtB,aAAa,GAAG,MAAM,IAAAyB,6BAAa,EAAC5B,GAAG,EAAEM,IAAI,EAAEmB,aAAa,CAAC;QAC/D;;QAEA;QACA,IAAI,CAACtB,aAAa,EAAE;UAClBN,kBAAM,CAACgC,IAAI,CACT,6DAA6D,CAC9D;UACDhC,kBAAM,CAACoB,IAAI,CACT,sIAAsI,CACvI;QACH;MACF;;MAEA;MACA,MAAMa,MAAM,GAAG,MAAM,IAAAC,mCAAW,EAC9BR,YAAY,EACZpB,aAAa,EACb,cAAc,CACf;MAED,MAAM6B,mBAAmB,GAAI,GAAE/B,OAAQ,IAAGuB,eAAI,CAACS,QAAQ,CACrD3C,IAAI,EACJ,aAAa,CACb,iBAAgB;MAClB4C,aAAE,CAACC,aAAa,CACdH,mBAAmB,EACnBI,IAAI,CAACC,SAAS,CAACP,MAAM,EAAEQ,SAAS,EAAE,CAAC,CAAC,EACpC,OAAO,CACR;MACDzC,kBAAM,CAACsB,OAAO,CACX,0EAAyEa,mBAAoB,EAAC,CAChG;IACH;EACF,CAAC,CAAC,OAAOtC,CAAC,EAAE;IACV,MAAMA,CAAC;EACT;AACF"}