{"version": 3, "names": ["logAndroid", "logger", "info", "emitter", "<PERSON><PERSON><PERSON>", "platform", "priority", "AndroidPriority", "VERBOSE", "filter", "makeTagsFilter", "on", "entry", "log", "formatEntry", "error", "formatError", "name", "description", "func"], "sources": ["../../../src/commands/logAndroid/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport {\n  logkitty,\n  makeTagsFilter,\n  formatEntry,\n  formatError,\n  AndroidPriority,\n} from 'logkitty';\nimport {logger} from '@react-native-community/cli-tools';\n\nasync function logAndroid() {\n  logger.info('Starting logkitty');\n\n  const emitter = logkitty({\n    platform: 'android',\n    priority: AndroidPriority.VERBOSE,\n    filter: makeTagsFilter('ReactNative', 'ReactNativeJS'),\n  });\n\n  emitter.on('entry', (entry) => {\n    logger.log(formatEntry(entry));\n  });\n\n  emitter.on('error', (error) => {\n    logger.log(formatError(error));\n  });\n}\n\nexport default {\n  name: 'log-android',\n  description: 'starts logkitty',\n  func: logAndroid,\n};\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAdA;AACA;AACA;AACA;AACA;AACA;;AAWA,eAAeA,UAAU,GAAG;EAC1BC,kBAAM,CAACC,IAAI,CAAC,mBAAmB,CAAC;EAEhC,MAAMC,OAAO,GAAG,IAAAC,oBAAQ,EAAC;IACvBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAEC,2BAAe,CAACC,OAAO;IACjCC,MAAM,EAAE,IAAAC,0BAAc,EAAC,aAAa,EAAE,eAAe;EACvD,CAAC,CAAC;EAEFP,OAAO,CAACQ,EAAE,CAAC,OAAO,EAAGC,KAAK,IAAK;IAC7BX,kBAAM,CAACY,GAAG,CAAC,IAAAC,uBAAW,EAACF,KAAK,CAAC,CAAC;EAChC,CAAC,CAAC;EAEFT,OAAO,CAACQ,EAAE,CAAC,OAAO,EAAGI,KAAK,IAAK;IAC7Bd,kBAAM,CAACY,GAAG,CAAC,IAAAG,uBAAW,EAACD,KAAK,CAAC,CAAC;EAChC,CAAC,CAAC;AACJ;AAAC,eAEc;EACbE,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,iBAAiB;EAC9BC,IAAI,EAAEnB;AACR,CAAC;AAAA"}