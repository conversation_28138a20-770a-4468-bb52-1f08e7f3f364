{"version": 3, "names": ["Badge", "children", "style", "visible", "size", "rest", "opacity", "React", "useState", "Animated", "Value", "rendered", "setRendered", "theme", "useTheme", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "finished", "stopAnimation", "backgroundColor", "colors", "notification", "restStyle", "StyleSheet", "flatten", "textColor", "color", "isLight", "borderRadius", "fontSize", "Math", "floor", "transform", "scale", "interpolate", "inputRange", "outputRange", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "create", "alignSelf", "textAlign", "paddingHorizontal", "overflow"], "sourceRoot": "../../../src", "sources": ["views/Badge.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAA0E;AAAA;AAAA;AAAA;AAqB3D,SAASA,KAAK,OAMnB;EAAA,IANoB;IAC5BC,QAAQ;IACRC,KAAK;IACLC,OAAO,GAAG,IAAI;IACdC,IAAI,GAAG,EAAE;IACT,GAAGC;EACE,CAAC;EACN,MAAM,CAACC,OAAO,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,MAAM,IAAIC,qBAAQ,CAACC,KAAK,CAACP,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGL,KAAK,CAACC,QAAQ,CAACL,OAAO,CAAC;EAEvD,MAAMU,KAAK,GAAG,IAAAC,gBAAQ,GAAE;EAExBP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAI,CAACJ,QAAQ,EAAE;MACb;IACF;IAEAF,qBAAQ,CAACO,MAAM,CAACV,OAAO,EAAE;MACvBW,OAAO,EAAEd,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBe,QAAQ,EAAE,GAAG;MACbC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,SAAkB;MAAA,IAAjB;QAAEC;MAAS,CAAC;MACpB,IAAIA,QAAQ,IAAI,CAAClB,OAAO,EAAE;QACxBS,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMN,OAAO,CAACgB,aAAa,EAAE;EACtC,CAAC,EAAE,CAAChB,OAAO,EAAEK,QAAQ,EAAER,OAAO,CAAC,CAAC;EAEhC,IAAI,CAACQ,QAAQ,EAAE;IACb,IAAIR,OAAO,EAAE;MACXS,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAM;IAAEW,eAAe,GAAGV,KAAK,CAACW,MAAM,CAACC,YAAY;IAAE,GAAGC;EAAU,CAAC,GACjEC,uBAAU,CAACC,OAAO,CAAC1B,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAM2B,SAAS,GAAG,IAAAC,cAAK,EAACP,eAAe,CAAC,CAACQ,OAAO,EAAE,GAAG,OAAO,GAAG,OAAO;EAEtE,MAAMC,YAAY,GAAG5B,IAAI,GAAG,CAAC;EAC7B,MAAM6B,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAE/B,IAAI,GAAG,CAAC,GAAI,CAAC,CAAC;EAE3C,oBACE,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAE,CAAE;IACjB,KAAK,EAAE,CACL;MACEgC,SAAS,EAAE,CACT;QACEC,KAAK,EAAE/B,OAAO,CAACgC,WAAW,CAAC;UACzBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;MACH,CAAC,CACF;MACDV,KAAK,EAAED,SAAS;MAChBY,UAAU,EAAErC,IAAI,GAAG,CAAC;MACpBsC,MAAM,EAAEtC,IAAI;MACZuC,QAAQ,EAAEvC,IAAI;MACdE,OAAO;MACPiB,eAAe;MACfU,QAAQ;MACRD;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBnB,SAAS;EACT,GACErB,IAAI,GAEPJ,QAAQ,CACK;AAEpB;AAEA,MAAM2C,MAAM,GAAGjB,uBAAU,CAACmB,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}