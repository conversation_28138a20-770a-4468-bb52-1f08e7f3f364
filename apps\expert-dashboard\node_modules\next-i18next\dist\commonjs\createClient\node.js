"use strict";

require("core-js/modules/es.object.keys.js");
require("core-js/modules/es.symbol.js");
require("core-js/modules/es.array.filter.js");
require("core-js/modules/es.object.get-own-property-descriptor.js");
require("core-js/modules/es.object.get-own-property-descriptors.js");
require("core-js/modules/es.object.define-properties.js");
require("core-js/modules/es.object.define-property.js");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
require("core-js/modules/es.array.some.js");
require("core-js/modules/es.object.to-string.js");
require("core-js/modules/es.array.for-each.js");
require("core-js/modules/web.dom-collections.for-each.js");
require("core-js/modules/es.promise.js");
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _i18next = _interopRequireDefault(require("i18next"));
var _i18nextFsBackend = _interopRequireDefault(require("i18next-fs-backend"));
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2["default"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var globalInstance;
var _default = exports["default"] = function _default(config) {
  if (config.ns === undefined) config.ns = [];
  var instance;
  if (!globalInstance) {
    globalInstance = _i18next["default"].createInstance(config);
    instance = globalInstance;
  } else {
    instance = globalInstance.cloneInstance(_objectSpread(_objectSpread({}, config), {}, {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      initAsync: false,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      initImmediate: false // i18next < 24
    }));
  }

  var initPromise;
  if (!instance.isInitialized) {
    var _config$use, _config$use2;
    var hasCustomBackend = config === null || config === void 0 || (_config$use = config.use) === null || _config$use === void 0 ? void 0 : _config$use.some(function (b) {
      return b.type === 'backend';
    });
    if (!hasCustomBackend) {
      instance.use(_i18nextFsBackend["default"]);
    }
    config === null || config === void 0 || (_config$use2 = config.use) === null || _config$use2 === void 0 || _config$use2.forEach(function (x) {
      return instance.use(x);
    });
    if (typeof config.onPreInitI18next === 'function') {
      config.onPreInitI18next(instance);
    }
    initPromise = instance.init(config);
  } else {
    initPromise = Promise.resolve(_i18next["default"].t);
  }
  return {
    i18n: instance,
    initPromise: initPromise
  };
};
module.exports = exports.default;
module.exports.default = exports.default;