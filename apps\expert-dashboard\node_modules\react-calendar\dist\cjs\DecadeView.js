"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var prop_types_1 = __importDefault(require("prop-types"));
var Years_js_1 = __importDefault(require("./DecadeView/Years.js"));
var propTypes_js_1 = require("./shared/propTypes.js");
/**
 * Displays a given decade.
 */
var DecadeView = function DecadeView(props) {
    function renderYears() {
        return react_1.default.createElement(Years_js_1.default, __assign({}, props));
    }
    return react_1.default.createElement("div", { className: "react-calendar__decade-view" }, renderYears());
};
DecadeView.propTypes = __assign(__assign({}, propTypes_js_1.tileGroupProps), { showNeighboringDecade: prop_types_1.default.bool });
exports.default = DecadeView;
