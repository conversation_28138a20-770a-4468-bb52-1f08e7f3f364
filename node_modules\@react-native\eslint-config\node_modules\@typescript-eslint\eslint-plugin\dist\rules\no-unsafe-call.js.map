{"version": 3, "file": "no-unsafe-call.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-call.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,iDAAmC;AAEnC,8CAAgC;AAChC,kCAA4C;AAQ5C,kBAAe,IAAI,CAAC,UAAU,CAAiB;IAC7C,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,sCAAsC;YAClD,cAAc,EAAE;gBACd,gEAAgE;gBAChE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,SAAS,EAAE,2CAA2C;YACtD,iBAAiB,EAAE,gCAAgC;SACpD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,SAAS,CAChB,IAAmB,EACnB,aAA4B,EAC5B,SAAqB;YAErB,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhE,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;gBAC5B,IAAI,CAAC,gBAAgB,EAAE;oBACrB,gDAAgD;oBAChD,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;oBAC/C,IACE,cAAc;wBACd,IAAI,CAAC,aAAa,CAChB,IAAI,CAAC,4BAA4B,CAC/B,OAAO,EACP,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,CAC1C,CACF,EACD;wBACA,SAAS,GAAG,gBAAgB,CAAC;qBAC9B;iBACF;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,SAAS;iBACrB,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,2BAA2B,CACzB,IAAuC;gBAEvC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;YACD,kCAAkC,CAAC,IAAmB;gBACpD,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;YAC7C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}