{"version": 3, "file": "index.production.js", "sources": ["../../../table-core/build/lib/index.mjs", "../../src/index.tsx"], "sourcesContent": ["/**\n   * table-core\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nfunction createColumnHelper() {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function' ? {\n        ...column,\n        accessorFn: accessor\n      } : {\n        ...column,\n        accessorKey: accessor\n      };\n    },\n    display: column => column,\n    group: column => column\n  };\n}\n\n// Is this type a tuple?\n\n// If this type is a tuple, what indices are allowed?\n\n///\n\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction noop() {\n  //\n}\nfunction makeStateUpdater(key, instance) {\n  return updater => {\n    instance.setState(old => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, old[key])\n      };\n    });\n  };\n}\nfunction isFunction(d) {\n  return d instanceof Function;\n}\nfunction isNumberArray(d) {\n  return Array.isArray(d) && d.every(val => typeof val === 'number');\n}\nfunction flattenBy(arr, getChildren) {\n  const flat = [];\n  const recurse = subArr => {\n    subArr.forEach(item => {\n      flat.push(item);\n      const children = getChildren(item);\n      if (children != null && children.length) {\n        recurse(children);\n      }\n    });\n  };\n  recurse(arr);\n  return flat;\n}\nfunction memo(getDeps, fn, opts) {\n  let deps = [];\n  let result;\n  return depArgs => {\n    let depTime;\n    if (opts.key && opts.debug) depTime = Date.now();\n    const newDeps = getDeps(depArgs);\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && opts.debug) resultTime = Date.now();\n    result = fn(...newDeps);\n    opts == null || opts.onChange == null || opts.onChange(result);\n    if (opts.key && opts.debug) {\n      if (opts != null && opts.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n        const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n        const resultFpsPercentage = resultEndTime / 16;\n        const pad = (str, num) => {\n          str = String(str);\n          while (str.length < num) {\n            str = ' ' + str;\n          }\n          return str;\n        };\n        console.info(`%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`, `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120))}deg 100% 31%);`, opts == null ? void 0 : opts.key);\n      }\n    }\n    return result;\n  };\n}\nfunction getMemoOptions(tableOptions, debugLevel, key, onChange) {\n  return {\n    debug: () => {\n      var _tableOptions$debugAl;\n      return (_tableOptions$debugAl = tableOptions == null ? void 0 : tableOptions.debugAll) != null ? _tableOptions$debugAl : tableOptions[debugLevel];\n    },\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange\n  };\n}\n\nfunction createCell(table, row, column, columnId) {\n  const getRenderValue = () => {\n    var _cell$getValue;\n    return (_cell$getValue = cell.getValue()) != null ? _cell$getValue : table.options.renderFallbackValue;\n  };\n  const cell = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(() => [table, column, row, cell], (table, column, row, cell) => ({\n      table,\n      column,\n      row,\n      cell: cell,\n      getValue: cell.getValue,\n      renderValue: cell.renderValue\n    }), getMemoOptions(table.options, 'debugCells', 'cell.getContext'))\n  };\n  table._features.forEach(feature => {\n    feature.createCell == null || feature.createCell(cell, column, row, table);\n  }, {});\n  return cell;\n}\n\nfunction createColumn(table, columnDef, depth, parent) {\n  var _ref, _resolvedColumnDef$id;\n  const defaultColumn = table._getDefaultColumnDef();\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef\n  };\n  const accessorKey = resolvedColumnDef.accessorKey;\n  let id = (_ref = (_resolvedColumnDef$id = resolvedColumnDef.id) != null ? _resolvedColumnDef$id : accessorKey ? typeof String.prototype.replaceAll === 'function' ? accessorKey.replaceAll('.', '_') : accessorKey.replace(/\\./g, '_') : undefined) != null ? _ref : typeof resolvedColumnDef.header === 'string' ? resolvedColumnDef.header : undefined;\n  let accessorFn;\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn;\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = originalRow => {\n        let result = originalRow;\n        for (const key of accessorKey.split('.')) {\n          var _result;\n          result = (_result = result) == null ? void 0 : _result[key];\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(`\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`);\n          }\n        }\n        return result;\n      };\n    } else {\n      accessorFn = originalRow => originalRow[resolvedColumnDef.accessorKey];\n    }\n  }\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(resolvedColumnDef.accessorFn ? `Columns require an id when using an accessorFn` : `Columns require an id when using a non-string header`);\n    }\n    throw new Error();\n  }\n  let column = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent,\n    depth,\n    columnDef: resolvedColumnDef,\n    columns: [],\n    getFlatColumns: memo(() => [true], () => {\n      var _column$columns;\n      return [column, ...((_column$columns = column.columns) == null ? void 0 : _column$columns.flatMap(d => d.getFlatColumns()))];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')),\n    getLeafColumns: memo(() => [table._getOrderColumnsFn()], orderColumns => {\n      var _column$columns2;\n      if ((_column$columns2 = column.columns) != null && _column$columns2.length) {\n        let leafColumns = column.columns.flatMap(column => column.getLeafColumns());\n        return orderColumns(leafColumns);\n      }\n      return [column];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns'))\n  };\n  for (const feature of table._features) {\n    feature.createColumn == null || feature.createColumn(column, table);\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column;\n}\n\nconst debug = 'debugHeaders';\n//\n\nfunction createHeader(table, column, options) {\n  var _options$id;\n  const id = (_options$id = options.id) != null ? _options$id : column.id;\n  let header = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null,\n    getLeafHeaders: () => {\n      const leafHeaders = [];\n      const recurseHeader = h => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader);\n        }\n        leafHeaders.push(h);\n      };\n      recurseHeader(header);\n      return leafHeaders;\n    },\n    getContext: () => ({\n      table,\n      header: header,\n      column\n    })\n  };\n  table._features.forEach(feature => {\n    feature.createHeader == null || feature.createHeader(header, table);\n  });\n  return header;\n}\nconst Headers = {\n  createTable: table => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      var _left$map$filter, _right$map$filter;\n      const leftColumns = (_left$map$filter = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter : [];\n      const rightColumns = (_right$map$filter = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter : [];\n      const centerColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      const headerGroups = buildHeaderGroups(allColumns, [...leftColumns, ...centerColumns, ...rightColumns], table);\n      return headerGroups;\n    }, getMemoOptions(table.options, debug, 'getHeaderGroups'));\n    table.getCenterHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      leafColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      return buildHeaderGroups(allColumns, leafColumns, table, 'center');\n    }, getMemoOptions(table.options, debug, 'getCenterHeaderGroups'));\n    table.getLeftHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left], (allColumns, leafColumns, left) => {\n      var _left$map$filter2;\n      const orderedLeafColumns = (_left$map$filter2 = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left');\n    }, getMemoOptions(table.options, debug, 'getLeftHeaderGroups'));\n    table.getRightHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.right], (allColumns, leafColumns, right) => {\n      var _right$map$filter2;\n      const orderedLeafColumns = (_right$map$filter2 = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right');\n    }, getMemoOptions(table.options, debug, 'getRightHeaderGroups'));\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getFooterGroups'));\n    table.getLeftFooterGroups = memo(() => [table.getLeftHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getLeftFooterGroups'));\n    table.getCenterFooterGroups = memo(() => [table.getCenterHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getCenterFooterGroups'));\n    table.getRightFooterGroups = memo(() => [table.getRightHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getRightFooterGroups'));\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return headerGroups.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getFlatHeaders'));\n    table.getLeftFlatHeaders = memo(() => [table.getLeftHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeftFlatHeaders'));\n    table.getCenterFlatHeaders = memo(() => [table.getCenterHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getCenterFlatHeaders'));\n    table.getRightFlatHeaders = memo(() => [table.getRightHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getRightFlatHeaders'));\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(() => [table.getCenterFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders;\n        return !((_header$subHeaders = header.subHeaders) != null && _header$subHeaders.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getCenterLeafHeaders'));\n    table.getLeftLeafHeaders = memo(() => [table.getLeftFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders2;\n        return !((_header$subHeaders2 = header.subHeaders) != null && _header$subHeaders2.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getLeftLeafHeaders'));\n    table.getRightLeafHeaders = memo(() => [table.getRightFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders3;\n        return !((_header$subHeaders3 = header.subHeaders) != null && _header$subHeaders3.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getRightLeafHeaders'));\n    table.getLeafHeaders = memo(() => [table.getLeftHeaderGroups(), table.getCenterHeaderGroups(), table.getRightHeaderGroups()], (left, center, right) => {\n      var _left$0$headers, _left$, _center$0$headers, _center$, _right$0$headers, _right$;\n      return [...((_left$0$headers = (_left$ = left[0]) == null ? void 0 : _left$.headers) != null ? _left$0$headers : []), ...((_center$0$headers = (_center$ = center[0]) == null ? void 0 : _center$.headers) != null ? _center$0$headers : []), ...((_right$0$headers = (_right$ = right[0]) == null ? void 0 : _right$.headers) != null ? _right$0$headers : [])].map(header => {\n        return header.getLeafHeaders();\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeafHeaders'));\n  }\n};\nfunction buildHeaderGroups(allColumns, columnsToGroup, table, headerFamily) {\n  var _headerGroups$0$heade, _headerGroups$;\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0;\n  const findMaxDepth = function (columns, depth) {\n    if (depth === void 0) {\n      depth = 1;\n    }\n    maxDepth = Math.max(maxDepth, depth);\n    columns.filter(column => column.getIsVisible()).forEach(column => {\n      var _column$columns;\n      if ((_column$columns = column.columns) != null && _column$columns.length) {\n        findMaxDepth(column.columns, depth + 1);\n      }\n    }, 0);\n  };\n  findMaxDepth(allColumns);\n  let headerGroups = [];\n  const createHeaderGroup = (headersToGroup, depth) => {\n    // The header group we are creating\n    const headerGroup = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: []\n    };\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders = [];\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0];\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth;\n      let column;\n      let isPlaceholder = false;\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent;\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column;\n        isPlaceholder = true;\n      }\n      if (latestPendingParentHeader && (latestPendingParentHeader == null ? void 0 : latestPendingParentHeader.column) === column) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup);\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup == null ? void 0 : headerToGroup.id].filter(Boolean).join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder ? `${pendingParentHeaders.filter(d => d.column === column).length}` : undefined,\n          depth,\n          index: pendingParentHeaders.length\n        });\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup);\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header);\n      }\n      headerGroup.headers.push(headerToGroup);\n      headerToGroup.headerGroup = headerGroup;\n    });\n    headerGroups.push(headerGroup);\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1);\n    }\n  };\n  const bottomHeaders = columnsToGroup.map((column, index) => createHeader(table, column, {\n    depth: maxDepth,\n    index\n  }));\n  createHeaderGroup(bottomHeaders, maxDepth - 1);\n  headerGroups.reverse();\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = headers => {\n    const filteredHeaders = headers.filter(header => header.column.getIsVisible());\n    return filteredHeaders.map(header => {\n      let colSpan = 0;\n      let rowSpan = 0;\n      let childRowSpans = [0];\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = [];\n        recurseHeadersForSpans(header.subHeaders).forEach(_ref => {\n          let {\n            colSpan: childColSpan,\n            rowSpan: childRowSpan\n          } = _ref;\n          colSpan += childColSpan;\n          childRowSpans.push(childRowSpan);\n        });\n      } else {\n        colSpan = 1;\n      }\n      const minChildRowSpan = Math.min(...childRowSpans);\n      rowSpan = rowSpan + minChildRowSpan;\n      header.colSpan = colSpan;\n      header.rowSpan = rowSpan;\n      return {\n        colSpan,\n        rowSpan\n      };\n    });\n  };\n  recurseHeadersForSpans((_headerGroups$0$heade = (_headerGroups$ = headerGroups[0]) == null ? void 0 : _headerGroups$.headers) != null ? _headerGroups$0$heade : []);\n  return headerGroups;\n}\n\nconst createRow = (table, id, original, rowIndex, depth, subRows, parentId) => {\n  let row = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      row._valuesCache[columnId] = column.accessorFn(row.original, rowIndex);\n      return row._valuesCache[columnId];\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)];\n        return row._uniqueValuesCache[columnId];\n      }\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(row.original, rowIndex);\n      return row._uniqueValuesCache[columnId];\n    },\n    renderValue: columnId => {\n      var _row$getValue;\n      return (_row$getValue = row.getValue(columnId)) != null ? _row$getValue : table.options.renderFallbackValue;\n    },\n    subRows: subRows != null ? subRows : [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows = [];\n      let currentRow = row;\n      while (true) {\n        const parentRow = currentRow.getParentRow();\n        if (!parentRow) break;\n        parentRows.push(parentRow);\n        currentRow = parentRow;\n      }\n      return parentRows.reverse();\n    },\n    getAllCells: memo(() => [table.getAllLeafColumns()], leafColumns => {\n      return leafColumns.map(column => {\n        return createCell(table, row, column, column.id);\n      });\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCells')),\n    _getAllCellsByColumnId: memo(() => [row.getAllCells()], allCells => {\n      return allCells.reduce((acc, cell) => {\n        acc[cell.column.id] = cell;\n        return acc;\n      }, {});\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId'))\n  };\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i];\n    feature == null || feature.createRow == null || feature.createRow(row, table);\n  }\n  return row;\n};\n\n//\n\nconst ColumnFaceting = {\n  createColumn: (column, table) => {\n    column._getFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, column.id);\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return column._getFacetedRowModel();\n    };\n    column._getFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, column.id);\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map();\n      }\n      return column._getFacetedUniqueValues();\n    };\n    column._getFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, column.id);\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined;\n      }\n      return column._getFacetedMinMaxValues();\n    };\n  }\n};\n\nconst includesString = (row, columnId, filterValue) => {\n  var _filterValue$toString, _row$getValue;\n  const search = filterValue == null || (_filterValue$toString = filterValue.toString()) == null ? void 0 : _filterValue$toString.toLowerCase();\n  return Boolean((_row$getValue = row.getValue(columnId)) == null || (_row$getValue = _row$getValue.toString()) == null || (_row$getValue = _row$getValue.toLowerCase()) == null ? void 0 : _row$getValue.includes(search));\n};\nincludesString.autoRemove = val => testFalsey(val);\nconst includesStringSensitive = (row, columnId, filterValue) => {\n  var _row$getValue2;\n  return Boolean((_row$getValue2 = row.getValue(columnId)) == null || (_row$getValue2 = _row$getValue2.toString()) == null ? void 0 : _row$getValue2.includes(filterValue));\n};\nincludesStringSensitive.autoRemove = val => testFalsey(val);\nconst equalsString = (row, columnId, filterValue) => {\n  var _row$getValue3;\n  return ((_row$getValue3 = row.getValue(columnId)) == null || (_row$getValue3 = _row$getValue3.toString()) == null ? void 0 : _row$getValue3.toLowerCase()) === (filterValue == null ? void 0 : filterValue.toLowerCase());\n};\nequalsString.autoRemove = val => testFalsey(val);\nconst arrIncludes = (row, columnId, filterValue) => {\n  var _row$getValue4;\n  return (_row$getValue4 = row.getValue(columnId)) == null ? void 0 : _row$getValue4.includes(filterValue);\n};\narrIncludes.autoRemove = val => testFalsey(val);\nconst arrIncludesAll = (row, columnId, filterValue) => {\n  return !filterValue.some(val => {\n    var _row$getValue5;\n    return !((_row$getValue5 = row.getValue(columnId)) != null && _row$getValue5.includes(val));\n  });\n};\narrIncludesAll.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst arrIncludesSome = (row, columnId, filterValue) => {\n  return filterValue.some(val => {\n    var _row$getValue6;\n    return (_row$getValue6 = row.getValue(columnId)) == null ? void 0 : _row$getValue6.includes(val);\n  });\n};\narrIncludesSome.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst equals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) === filterValue;\n};\nequals.autoRemove = val => testFalsey(val);\nconst weakEquals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) == filterValue;\n};\nweakEquals.autoRemove = val => testFalsey(val);\nconst inNumberRange = (row, columnId, filterValue) => {\n  let [min, max] = filterValue;\n  const rowValue = row.getValue(columnId);\n  return rowValue >= min && rowValue <= max;\n};\ninNumberRange.resolveFilterValue = val => {\n  let [unsafeMin, unsafeMax] = val;\n  let parsedMin = typeof unsafeMin !== 'number' ? parseFloat(unsafeMin) : unsafeMin;\n  let parsedMax = typeof unsafeMax !== 'number' ? parseFloat(unsafeMax) : unsafeMax;\n  let min = unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin;\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax;\n  if (min > max) {\n    const temp = min;\n    min = max;\n    max = temp;\n  }\n  return [min, max];\n};\ninNumberRange.autoRemove = val => testFalsey(val) || testFalsey(val[0]) && testFalsey(val[1]);\n\n// Export\n\nconst filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange\n};\n// Utils\n\nfunction testFalsey(val) {\n  return val === undefined || val === null || val === '';\n}\n\n//\n\nconst ColumnFiltering = {\n  getDefaultColumnDef: () => {\n    return {\n      filterFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      columnFilters: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return filterFns.includesString;\n      }\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange;\n      }\n      if (typeof value === 'boolean') {\n        return filterFns.equals;\n      }\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals;\n      }\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes;\n      }\n      return filterFns.weakEquals;\n    };\n    column.getFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      return isFunction(column.columnDef.filterFn) ? column.columnDef.filterFn : column.columnDef.filterFn === 'auto' ? column.getAutoFilterFn() : // @ts-ignore\n      (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[column.columnDef.filterFn]) != null ? _table$options$filter : filterFns[column.columnDef.filterFn];\n    };\n    column.getCanFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2;\n      return ((_column$columnDef$ena = column.columnDef.enableColumnFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnFilters) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && !!column.accessorFn;\n    };\n    column.getIsFiltered = () => column.getFilterIndex() > -1;\n    column.getFilterValue = () => {\n      var _table$getState$colum;\n      return (_table$getState$colum = table.getState().columnFilters) == null || (_table$getState$colum = _table$getState$colum.find(d => d.id === column.id)) == null ? void 0 : _table$getState$colum.value;\n    };\n    column.getFilterIndex = () => {\n      var _table$getState$colum2, _table$getState$colum3;\n      return (_table$getState$colum2 = (_table$getState$colum3 = table.getState().columnFilters) == null ? void 0 : _table$getState$colum3.findIndex(d => d.id === column.id)) != null ? _table$getState$colum2 : -1;\n    };\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn();\n        const previousFilter = old == null ? void 0 : old.find(d => d.id === column.id);\n        const newFilter = functionalUpdate(value, previousFilter ? previousFilter.value : undefined);\n\n        //\n        if (shouldAutoRemoveFilter(filterFn, newFilter, column)) {\n          var _old$filter;\n          return (_old$filter = old == null ? void 0 : old.filter(d => d.id !== column.id)) != null ? _old$filter : [];\n        }\n        const newFilterObj = {\n          id: column.id,\n          value: newFilter\n        };\n        if (previousFilter) {\n          var _old$map;\n          return (_old$map = old == null ? void 0 : old.map(d => {\n            if (d.id === column.id) {\n              return newFilterObj;\n            }\n            return d;\n          })) != null ? _old$map : [];\n        }\n        if (old != null && old.length) {\n          return [...old, newFilterObj];\n        }\n        return [newFilterObj];\n      });\n    };\n  },\n  createRow: (row, _table) => {\n    row.columnFilters = {};\n    row.columnFiltersMeta = {};\n  },\n  createTable: table => {\n    table.setColumnFilters = updater => {\n      const leafColumns = table.getAllLeafColumns();\n      const updateFn = old => {\n        var _functionalUpdate;\n        return (_functionalUpdate = functionalUpdate(updater, old)) == null ? void 0 : _functionalUpdate.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id);\n          if (column) {\n            const filterFn = column.getFilterFn();\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false;\n            }\n          }\n          return true;\n        });\n      };\n      table.options.onColumnFiltersChange == null || table.options.onColumnFiltersChange(updateFn);\n    };\n    table.resetColumnFilters = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      table.setColumnFilters(defaultState ? [] : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnFilters) != null ? _table$initialState$c : []);\n    };\n    table.getPreFilteredRowModel = () => table.getCoreRowModel();\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table);\n      }\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getFilteredRowModel();\n    };\n  }\n};\nfunction shouldAutoRemoveFilter(filterFn, value, column) {\n  return (filterFn && filterFn.autoRemove ? filterFn.autoRemove(value, column) : false) || typeof value === 'undefined' || typeof value === 'string' && !value;\n}\n\nconst sum = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId);\n    return sum + (typeof nextValue === 'number' ? nextValue : 0);\n  }, 0);\n};\nconst min = (columnId, _leafRows, childRows) => {\n  let min;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (min > value || min === undefined && value >= value)) {\n      min = value;\n    }\n  });\n  return min;\n};\nconst max = (columnId, _leafRows, childRows) => {\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (max < value || max === undefined && value >= value)) {\n      max = value;\n    }\n  });\n  return max;\n};\nconst extent = (columnId, _leafRows, childRows) => {\n  let min;\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value;\n      } else {\n        if (min > value) min = value;\n        if (max < value) max = value;\n      }\n    }\n  });\n  return [min, max];\n};\nconst mean = (columnId, leafRows) => {\n  let count = 0;\n  let sum = 0;\n  leafRows.forEach(row => {\n    let value = row.getValue(columnId);\n    if (value != null && (value = +value) >= value) {\n      ++count, sum += value;\n    }\n  });\n  if (count) return sum / count;\n  return;\n};\nconst median = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return;\n  }\n  const values = leafRows.map(row => row.getValue(columnId));\n  if (!isNumberArray(values)) {\n    return;\n  }\n  if (values.length === 1) {\n    return values[0];\n  }\n  const mid = Math.floor(values.length / 2);\n  const nums = values.sort((a, b) => a - b);\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1] + nums[mid]) / 2;\n};\nconst unique = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values());\n};\nconst uniqueCount = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size;\n};\nconst count = (_columnId, leafRows) => {\n  return leafRows.length;\n};\nconst aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count\n};\n\n//\n\nconst ColumnGrouping = {\n  getDefaultColumnDef: () => {\n    return {\n      aggregatedCell: props => {\n        var _toString, _props$getValue;\n        return (_toString = (_props$getValue = props.getValue()) == null || _props$getValue.toString == null ? void 0 : _props$getValue.toString()) != null ? _toString : null;\n      },\n      aggregationFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      grouping: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder'\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old != null && old.includes(column.id)) {\n          return old.filter(d => d !== column.id);\n        }\n        return [...(old != null ? old : []), column.id];\n      });\n    };\n    column.getCanGroup = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableGrouping) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGrouping) != null ? _table$options$enable : true) && (!!column.accessorFn || !!column.columnDef.getGroupingValue);\n    };\n    column.getIsGrouped = () => {\n      var _table$getState$group;\n      return (_table$getState$group = table.getState().grouping) == null ? void 0 : _table$getState$group.includes(column.id);\n    };\n    column.getGroupedIndex = () => {\n      var _table$getState$group2;\n      return (_table$getState$group2 = table.getState().grouping) == null ? void 0 : _table$getState$group2.indexOf(column.id);\n    };\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup();\n      return () => {\n        if (!canGroup) return;\n        column.toggleGrouping();\n      };\n    };\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'number') {\n        return aggregationFns.sum;\n      }\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent;\n      }\n    };\n    column.getAggregationFn = () => {\n      var _table$options$aggreg, _table$options$aggreg2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.aggregationFn) ? column.columnDef.aggregationFn : column.columnDef.aggregationFn === 'auto' ? column.getAutoAggregationFn() : (_table$options$aggreg = (_table$options$aggreg2 = table.options.aggregationFns) == null ? void 0 : _table$options$aggreg2[column.columnDef.aggregationFn]) != null ? _table$options$aggreg : aggregationFns[column.columnDef.aggregationFn];\n    };\n  },\n  createTable: table => {\n    table.setGrouping = updater => table.options.onGroupingChange == null ? void 0 : table.options.onGroupingChange(updater);\n    table.resetGrouping = defaultState => {\n      var _table$initialState$g, _table$initialState;\n      table.setGrouping(defaultState ? [] : (_table$initialState$g = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.grouping) != null ? _table$initialState$g : []);\n    };\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel();\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table);\n      }\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel();\n      }\n      return table._getGroupedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.getIsGrouped = () => !!row.groupingColumnId;\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.columnDef.getGroupingValue)) {\n        return row.getValue(columnId);\n      }\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(row.original);\n      return row._groupingValuesCache[columnId];\n    };\n    row._groupingValuesCache = {};\n  },\n  createCell: (cell, column, row, table) => {\n    cell.getIsGrouped = () => column.getIsGrouped() && column.id === row.groupingColumnId;\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped();\n    cell.getIsAggregated = () => {\n      var _row$subRows;\n      return !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n  }\n};\nfunction orderColumns(leafColumns, grouping, groupedColumnMode) {\n  if (!(grouping != null && grouping.length) || !groupedColumnMode) {\n    return leafColumns;\n  }\n  const nonGroupingColumns = leafColumns.filter(col => !grouping.includes(col.id));\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns;\n  }\n  const groupingColumns = grouping.map(g => leafColumns.find(col => col.id === g)).filter(Boolean);\n  return [...groupingColumns, ...nonGroupingColumns];\n}\n\n//\n\nconst ColumnOrdering = {\n  getInitialState: state => {\n    return {\n      columnOrder: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getIndex = memo(position => [_getVisibleLeafColumns(table, position)], columns => columns.findIndex(d => d.id === column.id), getMemoOptions(table.options, 'debugColumns', 'getIndex'));\n    column.getIsFirstColumn = position => {\n      var _columns$;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns$ = columns[0]) == null ? void 0 : _columns$.id) === column.id;\n    };\n    column.getIsLastColumn = position => {\n      var _columns;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns = columns[columns.length - 1]) == null ? void 0 : _columns.id) === column.id;\n    };\n  },\n  createTable: table => {\n    table.setColumnOrder = updater => table.options.onColumnOrderChange == null ? void 0 : table.options.onColumnOrderChange(updater);\n    table.resetColumnOrder = defaultState => {\n      var _table$initialState$c;\n      table.setColumnOrder(defaultState ? [] : (_table$initialState$c = table.initialState.columnOrder) != null ? _table$initialState$c : []);\n    };\n    table._getOrderColumnsFn = memo(() => [table.getState().columnOrder, table.getState().grouping, table.options.groupedColumnMode], (columnOrder, grouping, groupedColumnMode) => columns => {\n      // Sort grouped columns to the start of the column list\n      // before the headers are built\n      let orderedColumns = [];\n\n      // If there is no order, return the normal columns\n      if (!(columnOrder != null && columnOrder.length)) {\n        orderedColumns = columns;\n      } else {\n        const columnOrderCopy = [...columnOrder];\n\n        // If there is an order, make a copy of the columns\n        const columnsCopy = [...columns];\n\n        // And make a new ordered array of the columns\n\n        // Loop over the columns and place them in order into the new array\n        while (columnsCopy.length && columnOrderCopy.length) {\n          const targetColumnId = columnOrderCopy.shift();\n          const foundIndex = columnsCopy.findIndex(d => d.id === targetColumnId);\n          if (foundIndex > -1) {\n            orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]);\n          }\n        }\n\n        // If there are any columns left, add them to the end\n        orderedColumns = [...orderedColumns, ...columnsCopy];\n      }\n      return orderColumns(orderedColumns, grouping, groupedColumnMode);\n    }, getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn'));\n  }\n};\n\n//\n\nconst getDefaultColumnPinningState = () => ({\n  left: [],\n  right: []\n});\nconst ColumnPinning = {\n  getInitialState: state => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.pin = position => {\n      const columnIds = column.getLeafColumns().map(d => d.id).filter(Boolean);\n      table.setColumnPinning(old => {\n        var _old$left3, _old$right3;\n        if (position === 'right') {\n          var _old$left, _old$right;\n          return {\n            left: ((_old$left = old == null ? void 0 : old.left) != null ? _old$left : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n            right: [...((_old$right = old == null ? void 0 : old.right) != null ? _old$right : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds]\n          };\n        }\n        if (position === 'left') {\n          var _old$left2, _old$right2;\n          return {\n            left: [...((_old$left2 = old == null ? void 0 : old.left) != null ? _old$left2 : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds],\n            right: ((_old$right2 = old == null ? void 0 : old.right) != null ? _old$right2 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n          };\n        }\n        return {\n          left: ((_old$left3 = old == null ? void 0 : old.left) != null ? _old$left3 : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n          right: ((_old$right3 = old == null ? void 0 : old.right) != null ? _old$right3 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n        };\n      });\n    };\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns();\n      return leafColumns.some(d => {\n        var _d$columnDef$enablePi, _ref, _table$options$enable;\n        return ((_d$columnDef$enablePi = d.columnDef.enablePinning) != null ? _d$columnDef$enablePi : true) && ((_ref = (_table$options$enable = table.options.enableColumnPinning) != null ? _table$options$enable : table.options.enablePinning) != null ? _ref : true);\n      });\n    };\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id);\n      const {\n        left,\n        right\n      } = table.getState().columnPinning;\n      const isLeft = leafColumnIds.some(d => left == null ? void 0 : left.includes(d));\n      const isRight = leafColumnIds.some(d => right == null ? void 0 : right.includes(d));\n      return isLeft ? 'left' : isRight ? 'right' : false;\n    };\n    column.getPinnedIndex = () => {\n      var _table$getState$colum, _table$getState$colum2;\n      const position = column.getIsPinned();\n      return position ? (_table$getState$colum = (_table$getState$colum2 = table.getState().columnPinning) == null || (_table$getState$colum2 = _table$getState$colum2[position]) == null ? void 0 : _table$getState$colum2.indexOf(column.id)) != null ? _table$getState$colum : -1 : 0;\n    };\n  },\n  createRow: (row, table) => {\n    row.getCenterVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allCells, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allCells.filter(d => !leftAndRight.includes(d.column.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells'));\n    row.getLeftVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left], (allCells, left) => {\n      const cells = (left != null ? left : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'left'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells'));\n    row.getRightVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.right], (allCells, right) => {\n      const cells = (right != null ? right : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'right'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells'));\n  },\n  createTable: table => {\n    table.setColumnPinning = updater => table.options.onColumnPinningChange == null ? void 0 : table.options.onColumnPinningChange(updater);\n    table.resetColumnPinning = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      return table.setColumnPinning(defaultState ? getDefaultColumnPinningState() : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnPinning) != null ? _table$initialState$c : getDefaultColumnPinningState());\n    };\n    table.getIsSomeColumnsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().columnPinning;\n      if (!position) {\n        var _pinningState$left, _pinningState$right;\n        return Boolean(((_pinningState$left = pinningState.left) == null ? void 0 : _pinningState$left.length) || ((_pinningState$right = pinningState.right) == null ? void 0 : _pinningState$right.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table.getLeftLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left], (allColumns, left) => {\n      return (left != null ? left : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns'));\n    table.getRightLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.right], (allColumns, right) => {\n      return (right != null ? right : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns'));\n    table.getCenterLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allColumns.filter(d => !leftAndRight.includes(d.id));\n    }, getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns'));\n  }\n};\n\nfunction safelyAccessDocument(_document) {\n  return _document || (typeof document !== 'undefined' ? document : null);\n}\n\n//\n\n//\n\nconst defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER\n};\nconst getDefaultColumnSizingInfoState = () => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: []\n});\nconst ColumnSizing = {\n  getDefaultColumnDef: () => {\n    return defaultColumnSizing;\n  },\n  getInitialState: state => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getSize = () => {\n      var _column$columnDef$min, _ref, _column$columnDef$max;\n      const columnSize = table.getState().columnSizing[column.id];\n      return Math.min(Math.max((_column$columnDef$min = column.columnDef.minSize) != null ? _column$columnDef$min : defaultColumnSizing.minSize, (_ref = columnSize != null ? columnSize : column.columnDef.size) != null ? _ref : defaultColumnSizing.size), (_column$columnDef$max = column.columnDef.maxSize) != null ? _column$columnDef$max : defaultColumnSizing.maxSize);\n    };\n    column.getStart = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(0, column.getIndex(position)).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getStart'));\n    column.getAfter = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(column.getIndex(position) + 1).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getAfter'));\n    column.resetSize = () => {\n      table.setColumnSizing(_ref2 => {\n        let {\n          [column.id]: _,\n          ...rest\n        } = _ref2;\n        return rest;\n      });\n    };\n    column.getCanResize = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableResizing) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnResizing) != null ? _table$options$enable : true);\n    };\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id;\n    };\n  },\n  createHeader: (header, table) => {\n    header.getSize = () => {\n      let sum = 0;\n      const recurse = header => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse);\n        } else {\n          var _header$column$getSiz;\n          sum += (_header$column$getSiz = header.column.getSize()) != null ? _header$column$getSiz : 0;\n        }\n      };\n      recurse(header);\n      return sum;\n    };\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1];\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize();\n      }\n      return 0;\n    };\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id);\n      const canResize = column == null ? void 0 : column.getCanResize();\n      return e => {\n        if (!column || !canResize) {\n          return;\n        }\n        e.persist == null || e.persist();\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return;\n          }\n        }\n        const startSize = header.getSize();\n        const columnSizingStart = header ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()]) : [[column.id, column.getSize()]];\n        const clientX = isTouchStartEvent(e) ? Math.round(e.touches[0].clientX) : e.clientX;\n        const newColumnSizing = {};\n        const updateOffset = (eventType, clientXPos) => {\n          if (typeof clientXPos !== 'number') {\n            return;\n          }\n          table.setColumnSizingInfo(old => {\n            var _old$startOffset, _old$startSize;\n            const deltaDirection = table.options.columnResizeDirection === 'rtl' ? -1 : 1;\n            const deltaOffset = (clientXPos - ((_old$startOffset = old == null ? void 0 : old.startOffset) != null ? _old$startOffset : 0)) * deltaDirection;\n            const deltaPercentage = Math.max(deltaOffset / ((_old$startSize = old == null ? void 0 : old.startSize) != null ? _old$startSize : 0), -0.999999);\n            old.columnSizingStart.forEach(_ref3 => {\n              let [columnId, headerSize] = _ref3;\n              newColumnSizing[columnId] = Math.round(Math.max(headerSize + headerSize * deltaPercentage, 0) * 100) / 100;\n            });\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage\n            };\n          });\n          if (table.options.columnResizeMode === 'onChange' || eventType === 'end') {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing\n            }));\n          }\n        };\n        const onMove = clientXPos => updateOffset('move', clientXPos);\n        const onEnd = clientXPos => {\n          updateOffset('end', clientXPos);\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: []\n          }));\n        };\n        const contextDocument = safelyAccessDocument(_contextDocument);\n        const mouseEvents = {\n          moveHandler: e => onMove(e.clientX),\n          upHandler: e => {\n            contextDocument == null || contextDocument.removeEventListener('mousemove', mouseEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('mouseup', mouseEvents.upHandler);\n            onEnd(e.clientX);\n          }\n        };\n        const touchEvents = {\n          moveHandler: e => {\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onMove(e.touches[0].clientX);\n            return false;\n          },\n          upHandler: e => {\n            var _e$touches$;\n            contextDocument == null || contextDocument.removeEventListener('touchmove', touchEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('touchend', touchEvents.upHandler);\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onEnd((_e$touches$ = e.touches[0]) == null ? void 0 : _e$touches$.clientX);\n          }\n        };\n        const passiveIfSupported = passiveEventSupported() ? {\n          passive: false\n        } : false;\n        if (isTouchStartEvent(e)) {\n          contextDocument == null || contextDocument.addEventListener('touchmove', touchEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('touchend', touchEvents.upHandler, passiveIfSupported);\n        } else {\n          contextDocument == null || contextDocument.addEventListener('mousemove', mouseEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('mouseup', mouseEvents.upHandler, passiveIfSupported);\n        }\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id\n        }));\n      };\n    };\n  },\n  createTable: table => {\n    table.setColumnSizing = updater => table.options.onColumnSizingChange == null ? void 0 : table.options.onColumnSizingChange(updater);\n    table.setColumnSizingInfo = updater => table.options.onColumnSizingInfoChange == null ? void 0 : table.options.onColumnSizingInfoChange(updater);\n    table.resetColumnSizing = defaultState => {\n      var _table$initialState$c;\n      table.setColumnSizing(defaultState ? {} : (_table$initialState$c = table.initialState.columnSizing) != null ? _table$initialState$c : {});\n    };\n    table.resetHeaderSizeInfo = defaultState => {\n      var _table$initialState$c2;\n      table.setColumnSizingInfo(defaultState ? getDefaultColumnSizingInfoState() : (_table$initialState$c2 = table.initialState.columnSizingInfo) != null ? _table$initialState$c2 : getDefaultColumnSizingInfoState());\n    };\n    table.getTotalSize = () => {\n      var _table$getHeaderGroup, _table$getHeaderGroup2;\n      return (_table$getHeaderGroup = (_table$getHeaderGroup2 = table.getHeaderGroups()[0]) == null ? void 0 : _table$getHeaderGroup2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getHeaderGroup : 0;\n    };\n    table.getLeftTotalSize = () => {\n      var _table$getLeftHeaderG, _table$getLeftHeaderG2;\n      return (_table$getLeftHeaderG = (_table$getLeftHeaderG2 = table.getLeftHeaderGroups()[0]) == null ? void 0 : _table$getLeftHeaderG2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getLeftHeaderG : 0;\n    };\n    table.getCenterTotalSize = () => {\n      var _table$getCenterHeade, _table$getCenterHeade2;\n      return (_table$getCenterHeade = (_table$getCenterHeade2 = table.getCenterHeaderGroups()[0]) == null ? void 0 : _table$getCenterHeade2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getCenterHeade : 0;\n    };\n    table.getRightTotalSize = () => {\n      var _table$getRightHeader, _table$getRightHeader2;\n      return (_table$getRightHeader = (_table$getRightHeader2 = table.getRightHeaderGroups()[0]) == null ? void 0 : _table$getRightHeader2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getRightHeader : 0;\n    };\n  }\n};\nlet passiveSupported = null;\nfunction passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported;\n  let supported = false;\n  try {\n    const options = {\n      get passive() {\n        supported = true;\n        return false;\n      }\n    };\n    const noop = () => {};\n    window.addEventListener('test', noop, options);\n    window.removeEventListener('test', noop);\n  } catch (err) {\n    supported = false;\n  }\n  passiveSupported = supported;\n  return passiveSupported;\n}\nfunction isTouchStartEvent(e) {\n  return e.type === 'touchstart';\n}\n\n//\n\nconst ColumnVisibility = {\n  getInitialState: state => {\n    return {\n      columnVisibility: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value != null ? value : !column.getIsVisible()\n        }));\n      }\n    };\n    column.getIsVisible = () => {\n      var _ref, _table$getState$colum;\n      const childColumns = column.columns;\n      return (_ref = childColumns.length ? childColumns.some(c => c.getIsVisible()) : (_table$getState$colum = table.getState().columnVisibility) == null ? void 0 : _table$getState$colum[column.id]) != null ? _ref : true;\n    };\n    column.getCanHide = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableHiding) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableHiding) != null ? _table$options$enable : true);\n    };\n    column.getToggleVisibilityHandler = () => {\n      return e => {\n        column.toggleVisibility == null || column.toggleVisibility(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row._getAllVisibleCells = memo(() => [row.getAllCells(), table.getState().columnVisibility], cells => {\n      return cells.filter(cell => cell.column.getIsVisible());\n    }, getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells'));\n    row.getVisibleCells = memo(() => [row.getLeftVisibleCells(), row.getCenterVisibleCells(), row.getRightVisibleCells()], (left, center, right) => [...left, ...center, ...right], getMemoOptions(table.options, 'debugRows', 'getVisibleCells'));\n  },\n  createTable: table => {\n    const makeVisibleColumnsMethod = (key, getColumns) => {\n      return memo(() => [getColumns(), getColumns().filter(d => d.getIsVisible()).map(d => d.id).join('_')], columns => {\n        return columns.filter(d => d.getIsVisible == null ? void 0 : d.getIsVisible());\n      }, getMemoOptions(table.options, 'debugColumns', key));\n    };\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod('getVisibleFlatColumns', () => table.getAllFlatColumns());\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod('getVisibleLeafColumns', () => table.getAllLeafColumns());\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod('getLeftVisibleLeafColumns', () => table.getLeftLeafColumns());\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod('getRightVisibleLeafColumns', () => table.getRightLeafColumns());\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod('getCenterVisibleLeafColumns', () => table.getCenterLeafColumns());\n    table.setColumnVisibility = updater => table.options.onColumnVisibilityChange == null ? void 0 : table.options.onColumnVisibilityChange(updater);\n    table.resetColumnVisibility = defaultState => {\n      var _table$initialState$c;\n      table.setColumnVisibility(defaultState ? {} : (_table$initialState$c = table.initialState.columnVisibility) != null ? _table$initialState$c : {});\n    };\n    table.toggleAllColumnsVisible = value => {\n      var _value;\n      value = (_value = value) != null ? _value : !table.getIsAllColumnsVisible();\n      table.setColumnVisibility(table.getAllLeafColumns().reduce((obj, column) => ({\n        ...obj,\n        [column.id]: !value ? !(column.getCanHide != null && column.getCanHide()) : value\n      }), {}));\n    };\n    table.getIsAllColumnsVisible = () => !table.getAllLeafColumns().some(column => !(column.getIsVisible != null && column.getIsVisible()));\n    table.getIsSomeColumnsVisible = () => table.getAllLeafColumns().some(column => column.getIsVisible == null ? void 0 : column.getIsVisible());\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return e => {\n        var _target;\n        table.toggleAllColumnsVisible((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nfunction _getVisibleLeafColumns(table, position) {\n  return !position ? table.getVisibleLeafColumns() : position === 'center' ? table.getCenterVisibleLeafColumns() : position === 'left' ? table.getLeftVisibleLeafColumns() : table.getRightVisibleLeafColumns();\n}\n\n//\n\nconst GlobalFaceting = {\n  createTable: table => {\n    table._getGlobalFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, '__global__');\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getGlobalFacetedRowModel();\n    };\n    table._getGlobalFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, '__global__');\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map();\n      }\n      return table._getGlobalFacetedUniqueValues();\n    };\n    table._getGlobalFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, '__global__');\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return;\n      }\n      return table._getGlobalFacetedMinMaxValues();\n    };\n  }\n};\n\n//\n\nconst GlobalFiltering = {\n  getInitialState: state => {\n    return {\n      globalFilter: undefined,\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        var _table$getCoreRowMode;\n        const value = (_table$getCoreRowMode = table.getCoreRowModel().flatRows[0]) == null || (_table$getCoreRowMode = _table$getCoreRowMode._getAllCellsByColumnId()[column.id]) == null ? void 0 : _table$getCoreRowMode.getValue();\n        return typeof value === 'string' || typeof value === 'number';\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getCanGlobalFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2, _table$options$getCol;\n      return ((_column$columnDef$ena = column.columnDef.enableGlobalFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGlobalFilter) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && ((_table$options$getCol = table.options.getColumnCanGlobalFilter == null ? void 0 : table.options.getColumnCanGlobalFilter(column)) != null ? _table$options$getCol : true) && !!column.accessorFn;\n    };\n  },\n  createTable: table => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString;\n    };\n    table.getGlobalFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      const {\n        globalFilterFn: globalFilterFn\n      } = table.options;\n      return isFunction(globalFilterFn) ? globalFilterFn : globalFilterFn === 'auto' ? table.getGlobalAutoFilterFn() : (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[globalFilterFn]) != null ? _table$options$filter : filterFns[globalFilterFn];\n    };\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange == null || table.options.onGlobalFilterChange(updater);\n    };\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(defaultState ? undefined : table.initialState.globalFilter);\n    };\n  }\n};\n\n//\n\nconst RowExpanding = {\n  getInitialState: state => {\n    return {\n      expanded: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetExpanded = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetExpanded) != null ? _ref : !table.options.manualExpanding) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetExpanded();\n          queued = false;\n        });\n      }\n    };\n    table.setExpanded = updater => table.options.onExpandedChange == null ? void 0 : table.options.onExpandedChange(updater);\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded != null ? expanded : !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true);\n      } else {\n        table.setExpanded({});\n      }\n    };\n    table.resetExpanded = defaultState => {\n      var _table$initialState$e, _table$initialState;\n      table.setExpanded(defaultState ? {} : (_table$initialState$e = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.expanded) != null ? _table$initialState$e : {});\n    };\n    table.getCanSomeRowsExpand = () => {\n      return table.getPrePaginationRowModel().flatRows.some(row => row.getCanExpand());\n    };\n    table.getToggleAllRowsExpandedHandler = () => {\n      return e => {\n        e.persist == null || e.persist();\n        table.toggleAllRowsExpanded();\n      };\n    };\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n      return expanded === true || Object.values(expanded).some(Boolean);\n    };\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true;\n      }\n      if (!Object.keys(expanded).length) {\n        return false;\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false;\n      }\n\n      // They must all be expanded :shrug:\n      return true;\n    };\n    table.getExpandedDepth = () => {\n      let maxDepth = 0;\n      const rowIds = table.getState().expanded === true ? Object.keys(table.getRowModel().rowsById) : Object.keys(table.getState().expanded);\n      rowIds.forEach(id => {\n        const splitId = id.split('.');\n        maxDepth = Math.max(maxDepth, splitId.length);\n      });\n      return maxDepth;\n    };\n    table.getPreExpandedRowModel = () => table.getSortedRowModel();\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table);\n      }\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel();\n      }\n      return table._getExpandedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        var _expanded;\n        const exists = old === true ? true : !!(old != null && old[row.id]);\n        let oldExpanded = {};\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true;\n          });\n        } else {\n          oldExpanded = old;\n        }\n        expanded = (_expanded = expanded) != null ? _expanded : !exists;\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true\n          };\n        }\n        if (exists && !expanded) {\n          const {\n            [row.id]: _,\n            ...rest\n          } = oldExpanded;\n          return rest;\n        }\n        return old;\n      });\n    };\n    row.getIsExpanded = () => {\n      var _table$options$getIsR;\n      const expanded = table.getState().expanded;\n      return !!((_table$options$getIsR = table.options.getIsRowExpanded == null ? void 0 : table.options.getIsRowExpanded(row)) != null ? _table$options$getIsR : expanded === true || (expanded == null ? void 0 : expanded[row.id]));\n    };\n    row.getCanExpand = () => {\n      var _table$options$getRow, _table$options$enable, _row$subRows;\n      return (_table$options$getRow = table.options.getRowCanExpand == null ? void 0 : table.options.getRowCanExpand(row)) != null ? _table$options$getRow : ((_table$options$enable = table.options.enableExpanding) != null ? _table$options$enable : true) && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true;\n      let currentRow = row;\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true);\n        isFullyExpanded = currentRow.getIsExpanded();\n      }\n      return isFullyExpanded;\n    };\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand();\n      return () => {\n        if (!canExpand) return;\n        row.toggleExpanded();\n      };\n    };\n  }\n};\n\n//\n\nconst defaultPageIndex = 0;\nconst defaultPageSize = 10;\nconst getDefaultPaginationState = () => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize\n});\nconst RowPagination = {\n  getInitialState: state => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...(state == null ? void 0 : state.pagination)\n      }\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table)\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetPageIndex = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetPageIndex) != null ? _ref : !table.options.manualPagination) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetPageIndex();\n          queued = false;\n        });\n      }\n    };\n    table.setPagination = updater => {\n      const safeUpdater = old => {\n        let newState = functionalUpdate(updater, old);\n        return newState;\n      };\n      return table.options.onPaginationChange == null ? void 0 : table.options.onPaginationChange(safeUpdater);\n    };\n    table.resetPagination = defaultState => {\n      var _table$initialState$p;\n      table.setPagination(defaultState ? getDefaultPaginationState() : (_table$initialState$p = table.initialState.pagination) != null ? _table$initialState$p : getDefaultPaginationState());\n    };\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex);\n        const maxPageIndex = typeof table.options.pageCount === 'undefined' || table.options.pageCount === -1 ? Number.MAX_SAFE_INTEGER : table.options.pageCount - 1;\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex));\n        return {\n          ...old,\n          pageIndex\n        };\n      });\n    };\n    table.resetPageIndex = defaultState => {\n      var _table$initialState$p2, _table$initialState;\n      table.setPageIndex(defaultState ? defaultPageIndex : (_table$initialState$p2 = (_table$initialState = table.initialState) == null || (_table$initialState = _table$initialState.pagination) == null ? void 0 : _table$initialState.pageIndex) != null ? _table$initialState$p2 : defaultPageIndex);\n    };\n    table.resetPageSize = defaultState => {\n      var _table$initialState$p3, _table$initialState2;\n      table.setPageSize(defaultState ? defaultPageSize : (_table$initialState$p3 = (_table$initialState2 = table.initialState) == null || (_table$initialState2 = _table$initialState2.pagination) == null ? void 0 : _table$initialState2.pageSize) != null ? _table$initialState$p3 : defaultPageSize);\n    };\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize));\n        const topRowIndex = old.pageSize * old.pageIndex;\n        const pageIndex = Math.floor(topRowIndex / pageSize);\n        return {\n          ...old,\n          pageIndex,\n          pageSize\n        };\n      });\n    };\n    //deprecated\n    table.setPageCount = updater => table.setPagination(old => {\n      var _table$options$pageCo;\n      let newPageCount = functionalUpdate(updater, (_table$options$pageCo = table.options.pageCount) != null ? _table$options$pageCo : -1);\n      if (typeof newPageCount === 'number') {\n        newPageCount = Math.max(-1, newPageCount);\n      }\n      return {\n        ...old,\n        pageCount: newPageCount\n      };\n    });\n    table.getPageOptions = memo(() => [table.getPageCount()], pageCount => {\n      let pageOptions = [];\n      if (pageCount && pageCount > 0) {\n        pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i);\n      }\n      return pageOptions;\n    }, getMemoOptions(table.options, 'debugTable', 'getPageOptions'));\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0;\n    table.getCanNextPage = () => {\n      const {\n        pageIndex\n      } = table.getState().pagination;\n      const pageCount = table.getPageCount();\n      if (pageCount === -1) {\n        return true;\n      }\n      if (pageCount === 0) {\n        return false;\n      }\n      return pageIndex < pageCount - 1;\n    };\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1);\n    };\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1;\n      });\n    };\n    table.firstPage = () => {\n      return table.setPageIndex(0);\n    };\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1);\n    };\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel();\n    table.getPaginationRowModel = () => {\n      if (!table._getPaginationRowModel && table.options.getPaginationRowModel) {\n        table._getPaginationRowModel = table.options.getPaginationRowModel(table);\n      }\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel();\n      }\n      return table._getPaginationRowModel();\n    };\n    table.getPageCount = () => {\n      var _table$options$pageCo2;\n      return (_table$options$pageCo2 = table.options.pageCount) != null ? _table$options$pageCo2 : Math.ceil(table.getRowCount() / table.getState().pagination.pageSize);\n    };\n    table.getRowCount = () => {\n      var _table$options$rowCou;\n      return (_table$options$rowCou = table.options.rowCount) != null ? _table$options$rowCou : table.getPrePaginationRowModel().rows.length;\n    };\n  }\n};\n\n//\n\nconst getDefaultRowPinningState = () => ({\n  top: [],\n  bottom: []\n});\nconst RowPinning = {\n  getInitialState: state => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table)\n    };\n  },\n  createRow: (row, table) => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows ? row.getLeafRows().map(_ref => {\n        let {\n          id\n        } = _ref;\n        return id;\n      }) : [];\n      const parentRowIds = includeParentRows ? row.getParentRows().map(_ref2 => {\n        let {\n          id\n        } = _ref2;\n        return id;\n      }) : [];\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds]);\n      table.setRowPinning(old => {\n        var _old$top3, _old$bottom3;\n        if (position === 'bottom') {\n          var _old$top, _old$bottom;\n          return {\n            top: ((_old$top = old == null ? void 0 : old.top) != null ? _old$top : []).filter(d => !(rowIds != null && rowIds.has(d))),\n            bottom: [...((_old$bottom = old == null ? void 0 : old.bottom) != null ? _old$bottom : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)]\n          };\n        }\n        if (position === 'top') {\n          var _old$top2, _old$bottom2;\n          return {\n            top: [...((_old$top2 = old == null ? void 0 : old.top) != null ? _old$top2 : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)],\n            bottom: ((_old$bottom2 = old == null ? void 0 : old.bottom) != null ? _old$bottom2 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n          };\n        }\n        return {\n          top: ((_old$top3 = old == null ? void 0 : old.top) != null ? _old$top3 : []).filter(d => !(rowIds != null && rowIds.has(d))),\n          bottom: ((_old$bottom3 = old == null ? void 0 : old.bottom) != null ? _old$bottom3 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n        };\n      });\n    };\n    row.getCanPin = () => {\n      var _ref3;\n      const {\n        enableRowPinning,\n        enablePinning\n      } = table.options;\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row);\n      }\n      return (_ref3 = enableRowPinning != null ? enableRowPinning : enablePinning) != null ? _ref3 : true;\n    };\n    row.getIsPinned = () => {\n      const rowIds = [row.id];\n      const {\n        top,\n        bottom\n      } = table.getState().rowPinning;\n      const isTop = rowIds.some(d => top == null ? void 0 : top.includes(d));\n      const isBottom = rowIds.some(d => bottom == null ? void 0 : bottom.includes(d));\n      return isTop ? 'top' : isBottom ? 'bottom' : false;\n    };\n    row.getPinnedIndex = () => {\n      var _ref4, _visiblePinnedRowIds$;\n      const position = row.getIsPinned();\n      if (!position) return -1;\n      const visiblePinnedRowIds = (_ref4 = position === 'top' ? table.getTopRows() : table.getBottomRows()) == null ? void 0 : _ref4.map(_ref5 => {\n        let {\n          id\n        } = _ref5;\n        return id;\n      });\n      return (_visiblePinnedRowIds$ = visiblePinnedRowIds == null ? void 0 : visiblePinnedRowIds.indexOf(row.id)) != null ? _visiblePinnedRowIds$ : -1;\n    };\n  },\n  createTable: table => {\n    table.setRowPinning = updater => table.options.onRowPinningChange == null ? void 0 : table.options.onRowPinningChange(updater);\n    table.resetRowPinning = defaultState => {\n      var _table$initialState$r, _table$initialState;\n      return table.setRowPinning(defaultState ? getDefaultRowPinningState() : (_table$initialState$r = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.rowPinning) != null ? _table$initialState$r : getDefaultRowPinningState());\n    };\n    table.getIsSomeRowsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().rowPinning;\n      if (!position) {\n        var _pinningState$top, _pinningState$bottom;\n        return Boolean(((_pinningState$top = pinningState.top) == null ? void 0 : _pinningState$top.length) || ((_pinningState$bottom = pinningState.bottom) == null ? void 0 : _pinningState$bottom.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      var _table$options$keepPi;\n      const rows = ((_table$options$keepPi = table.options.keepPinnedRows) != null ? _table$options$keepPi : true) ?\n      //get all rows that are pinned even if they would not be otherwise visible\n      //account for expanded parent rows, but not pagination or filtering\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => {\n        const row = table.getRow(rowId, true);\n        return row.getIsAllParentsExpanded() ? row : null;\n      }) :\n      //else get only visible rows that are pinned\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => visibleRows.find(row => row.id === rowId));\n      return rows.filter(Boolean).map(d => ({\n        ...d,\n        position\n      }));\n    };\n    table.getTopRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top], (allRows, topPinnedRowIds) => table._getPinnedRows(allRows, topPinnedRowIds, 'top'), getMemoOptions(table.options, 'debugRows', 'getTopRows'));\n    table.getBottomRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.bottom], (allRows, bottomPinnedRowIds) => table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'), getMemoOptions(table.options, 'debugRows', 'getBottomRows'));\n    table.getCenterRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top, table.getState().rowPinning.bottom], (allRows, top, bottom) => {\n      const topAndBottom = new Set([...(top != null ? top : []), ...(bottom != null ? bottom : [])]);\n      return allRows.filter(d => !topAndBottom.has(d.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterRows'));\n  }\n};\n\n//\n\nconst RowSelection = {\n  getInitialState: state => {\n    return {\n      rowSelection: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    };\n  },\n  createTable: table => {\n    table.setRowSelection = updater => table.options.onRowSelectionChange == null ? void 0 : table.options.onRowSelectionChange(updater);\n    table.resetRowSelection = defaultState => {\n      var _table$initialState$r;\n      return table.setRowSelection(defaultState ? {} : (_table$initialState$r = table.initialState.rowSelection) != null ? _table$initialState$r : {});\n    };\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected();\n        const rowSelection = {\n          ...old\n        };\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows;\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return;\n            }\n            rowSelection[row.id] = true;\n          });\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id];\n          });\n        }\n        return rowSelection;\n      });\n    };\n    table.toggleAllPageRowsSelected = value => table.setRowSelection(old => {\n      const resolvedValue = typeof value !== 'undefined' ? value : !table.getIsAllPageRowsSelected();\n      const rowSelection = {\n        ...old\n      };\n      table.getRowModel().rows.forEach(row => {\n        mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table);\n      });\n      return rowSelection;\n    });\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel();\n    table.getSelectedRowModel = memo(() => [table.getState().rowSelection, table.getCoreRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel'));\n    table.getFilteredSelectedRowModel = memo(() => [table.getState().rowSelection, table.getFilteredRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel'));\n    table.getGroupedSelectedRowModel = memo(() => [table.getState().rowSelection, table.getSortedRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel'));\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows;\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllRowsSelected = Boolean(preGroupedFlatRows.length && Object.keys(rowSelection).length);\n      if (isAllRowsSelected) {\n        if (preGroupedFlatRows.some(row => row.getCanSelect() && !rowSelection[row.id])) {\n          isAllRowsSelected = false;\n        }\n      }\n      return isAllRowsSelected;\n    };\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows.filter(row => row.getCanSelect());\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllPageRowsSelected = !!paginationFlatRows.length;\n      if (isAllPageRowsSelected && paginationFlatRows.some(row => !rowSelection[row.id])) {\n        isAllPageRowsSelected = false;\n      }\n      return isAllPageRowsSelected;\n    };\n    table.getIsSomeRowsSelected = () => {\n      var _table$getState$rowSe;\n      const totalSelected = Object.keys((_table$getState$rowSe = table.getState().rowSelection) != null ? _table$getState$rowSe : {}).length;\n      return totalSelected > 0 && totalSelected < table.getFilteredRowModel().flatRows.length;\n    };\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows;\n      return table.getIsAllPageRowsSelected() ? false : paginationFlatRows.filter(row => row.getCanSelect()).some(d => d.getIsSelected() || d.getIsSomeSelected());\n    };\n    table.getToggleAllRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllRowsSelected(e.target.checked);\n      };\n    };\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllPageRowsSelected(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected();\n      table.setRowSelection(old => {\n        var _opts$selectChildren;\n        value = typeof value !== 'undefined' ? value : !isSelected;\n        if (row.getCanSelect() && isSelected === value) {\n          return old;\n        }\n        const selectedRowIds = {\n          ...old\n        };\n        mutateRowIsSelected(selectedRowIds, row.id, value, (_opts$selectChildren = opts == null ? void 0 : opts.selectChildren) != null ? _opts$selectChildren : true, table);\n        return selectedRowIds;\n      });\n    };\n    row.getIsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isRowSelected(row, rowSelection);\n    };\n    row.getIsSomeSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'some';\n    };\n    row.getIsAllSubRowsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'all';\n    };\n    row.getCanSelect = () => {\n      var _table$options$enable;\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row);\n      }\n      return (_table$options$enable = table.options.enableRowSelection) != null ? _table$options$enable : true;\n    };\n    row.getCanSelectSubRows = () => {\n      var _table$options$enable2;\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row);\n      }\n      return (_table$options$enable2 = table.options.enableSubRowSelection) != null ? _table$options$enable2 : true;\n    };\n    row.getCanMultiSelect = () => {\n      var _table$options$enable3;\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row);\n      }\n      return (_table$options$enable3 = table.options.enableMultiRowSelection) != null ? _table$options$enable3 : true;\n    };\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect();\n      return e => {\n        var _target;\n        if (!canSelect) return;\n        row.toggleSelected((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nconst mutateRowIsSelected = (selectedRowIds, id, value, includeChildren, table) => {\n  var _row$subRows;\n  const row = table.getRow(id, true);\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key]);\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true;\n    }\n  } else {\n    delete selectedRowIds[id];\n  }\n  // }\n\n  if (includeChildren && (_row$subRows = row.subRows) != null && _row$subRows.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row => mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table));\n  }\n};\nfunction selectRowsFn(table, rowModel) {\n  const rowSelection = table.getState().rowSelection;\n  const newSelectedFlatRows = [];\n  const newSelectedRowsById = {};\n\n  // Filters top level and nested rows\n  const recurseRows = function (rows, depth) {\n    return rows.map(row => {\n      var _row$subRows2;\n      const isSelected = isRowSelected(row, rowSelection);\n      if (isSelected) {\n        newSelectedFlatRows.push(row);\n        newSelectedRowsById[row.id] = row;\n      }\n      if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length) {\n        row = {\n          ...row,\n          subRows: recurseRows(row.subRows)\n        };\n      }\n      if (isSelected) {\n        return row;\n      }\n    }).filter(Boolean);\n  };\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById\n  };\n}\nfunction isRowSelected(row, selection) {\n  var _selection$row$id;\n  return (_selection$row$id = selection[row.id]) != null ? _selection$row$id : false;\n}\nfunction isSubRowSelected(row, selection, table) {\n  var _row$subRows3;\n  if (!((_row$subRows3 = row.subRows) != null && _row$subRows3.length)) return false;\n  let allChildrenSelected = true;\n  let someSelected = false;\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return;\n    }\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection);\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true;\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true;\n        allChildrenSelected = false;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n  });\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false;\n}\n\nconst reSplitAlphaNumeric = /([0-9]+)/gm;\nconst alphanumeric = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\nconst alphanumericCaseSensitive = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\nconst datetime = (rowA, rowB, columnId) => {\n  const a = rowA.getValue(columnId);\n  const b = rowB.getValue(columnId);\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0;\n};\nconst basic = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId));\n};\n\n// Utils\n\nfunction compareBasic(a, b) {\n  return a === b ? 0 : a > b ? 1 : -1;\n}\nfunction toString(a) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return '';\n    }\n    return String(a);\n  }\n  if (typeof a === 'string') {\n    return a;\n  }\n  return '';\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr, bStr) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean);\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean);\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift();\n    const bb = b.shift();\n    const an = parseInt(aa, 10);\n    const bn = parseInt(bb, 10);\n    const combo = [an, bn].sort();\n\n    // Both are string\n    if (isNaN(combo[0])) {\n      if (aa > bb) {\n        return 1;\n      }\n      if (bb > aa) {\n        return -1;\n      }\n      continue;\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1])) {\n      return isNaN(an) ? -1 : 1;\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1;\n    }\n    if (bn > an) {\n      return -1;\n    }\n  }\n  return a.length - b.length;\n}\n\n// Exports\n\nconst sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic\n};\n\n//\n\nconst RowSorting = {\n  getInitialState: state => {\n    return {\n      sorting: [],\n      ...state\n    };\n  },\n  getDefaultColumnDef: () => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: e => {\n        return e.shiftKey;\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10);\n      let isString = false;\n      for (const row of firstRows) {\n        const value = row == null ? void 0 : row.getValue(column.id);\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime;\n        }\n        if (typeof value === 'string') {\n          isString = true;\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric;\n          }\n        }\n      }\n      if (isString) {\n        return sortingFns.text;\n      }\n      return sortingFns.basic;\n    };\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return 'asc';\n      }\n      return 'desc';\n    };\n    column.getSortingFn = () => {\n      var _table$options$sortin, _table$options$sortin2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.sortingFn) ? column.columnDef.sortingFn : column.columnDef.sortingFn === 'auto' ? column.getAutoSortingFn() : (_table$options$sortin = (_table$options$sortin2 = table.options.sortingFns) == null ? void 0 : _table$options$sortin2[column.columnDef.sortingFn]) != null ? _table$options$sortin : sortingFns[column.columnDef.sortingFn];\n    };\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder();\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null;\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old == null ? void 0 : old.find(d => d.id === column.id);\n        const existingIndex = old == null ? void 0 : old.findIndex(d => d.id === column.id);\n        let newSorting = [];\n\n        // What should we do with this sort action?\n        let sortAction;\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc';\n\n        // Multi-mode\n        if (old != null && old.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'add';\n          }\n        } else {\n          // Normal mode\n          if (old != null && old.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace';\n          } else if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'replace';\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove';\n            }\n          }\n        }\n        if (sortAction === 'add') {\n          var _table$options$maxMul;\n          newSorting = [...old, {\n            id: column.id,\n            desc: nextDesc\n          }];\n          // Take latest n columns\n          newSorting.splice(0, newSorting.length - ((_table$options$maxMul = table.options.maxMultiSortColCount) != null ? _table$options$maxMul : Number.MAX_SAFE_INTEGER));\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc\n              };\n            }\n            return d;\n          });\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id);\n        } else {\n          newSorting = [{\n            id: column.id,\n            desc: nextDesc\n          }];\n        }\n        return newSorting;\n      });\n    };\n    column.getFirstSortDir = () => {\n      var _ref, _column$columnDef$sor;\n      const sortDescFirst = (_ref = (_column$columnDef$sor = column.columnDef.sortDescFirst) != null ? _column$columnDef$sor : table.options.sortDescFirst) != null ? _ref : column.getAutoSortDir() === 'desc';\n      return sortDescFirst ? 'desc' : 'asc';\n    };\n    column.getNextSortingOrder = multi => {\n      var _table$options$enable, _table$options$enable2;\n      const firstSortDirection = column.getFirstSortDir();\n      const isSorted = column.getIsSorted();\n      if (!isSorted) {\n        return firstSortDirection;\n      }\n      if (isSorted !== firstSortDirection && ((_table$options$enable = table.options.enableSortingRemoval) != null ? _table$options$enable : true) && (\n      // If enableSortRemove, enable in general\n      multi ? (_table$options$enable2 = table.options.enableMultiRemove) != null ? _table$options$enable2 : true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false;\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc';\n    };\n    column.getCanSort = () => {\n      var _column$columnDef$ena, _table$options$enable3;\n      return ((_column$columnDef$ena = column.columnDef.enableSorting) != null ? _column$columnDef$ena : true) && ((_table$options$enable3 = table.options.enableSorting) != null ? _table$options$enable3 : true) && !!column.accessorFn;\n    };\n    column.getCanMultiSort = () => {\n      var _ref2, _column$columnDef$ena2;\n      return (_ref2 = (_column$columnDef$ena2 = column.columnDef.enableMultiSort) != null ? _column$columnDef$ena2 : table.options.enableMultiSort) != null ? _ref2 : !!column.accessorFn;\n    };\n    column.getIsSorted = () => {\n      var _table$getState$sorti;\n      const columnSort = (_table$getState$sorti = table.getState().sorting) == null ? void 0 : _table$getState$sorti.find(d => d.id === column.id);\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc';\n    };\n    column.getSortIndex = () => {\n      var _table$getState$sorti2, _table$getState$sorti3;\n      return (_table$getState$sorti2 = (_table$getState$sorti3 = table.getState().sorting) == null ? void 0 : _table$getState$sorti3.findIndex(d => d.id === column.id)) != null ? _table$getState$sorti2 : -1;\n    };\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old => old != null && old.length ? old.filter(d => d.id !== column.id) : []);\n    };\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort();\n      return e => {\n        if (!canSort) return;\n        e.persist == null || e.persist();\n        column.toggleSorting == null || column.toggleSorting(undefined, column.getCanMultiSort() ? table.options.isMultiSortEvent == null ? void 0 : table.options.isMultiSortEvent(e) : false);\n      };\n    };\n  },\n  createTable: table => {\n    table.setSorting = updater => table.options.onSortingChange == null ? void 0 : table.options.onSortingChange(updater);\n    table.resetSorting = defaultState => {\n      var _table$initialState$s, _table$initialState;\n      table.setSorting(defaultState ? [] : (_table$initialState$s = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.sorting) != null ? _table$initialState$s : []);\n    };\n    table.getPreSortedRowModel = () => table.getGroupedRowModel();\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table);\n      }\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel();\n      }\n      return table._getSortedRowModel();\n    };\n  }\n};\n\nconst builtInFeatures = [Headers, ColumnVisibility, ColumnOrdering, ColumnPinning, ColumnFaceting, ColumnFiltering, GlobalFaceting,\n//depends on ColumnFaceting\nGlobalFiltering,\n//depends on ColumnFiltering\nRowSorting, ColumnGrouping,\n//depends on RowSorting\nRowExpanding, RowPagination, RowPinning, RowSelection, ColumnSizing];\n\n//\n\nfunction createTable(options) {\n  var _options$_features, _options$initialState;\n  if (process.env.NODE_ENV !== 'production' && (options.debugAll || options.debugTable)) {\n    console.info('Creating Table Instance...');\n  }\n  const _features = [...builtInFeatures, ...((_options$_features = options._features) != null ? _options$_features : [])];\n  let table = {\n    _features\n  };\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions == null ? void 0 : feature.getDefaultOptions(table));\n  }, {});\n  const mergeOptions = options => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options);\n    }\n    return {\n      ...defaultOptions,\n      ...options\n    };\n  };\n  const coreInitialState = {};\n  let initialState = {\n    ...coreInitialState,\n    ...((_options$initialState = options.initialState) != null ? _options$initialState : {})\n  };\n  table._features.forEach(feature => {\n    var _feature$getInitialSt;\n    initialState = (_feature$getInitialSt = feature.getInitialState == null ? void 0 : feature.getInitialState(initialState)) != null ? _feature$getInitialSt : initialState;\n  });\n  const queued = [];\n  let queuedTimeout = false;\n  const coreInstance = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb);\n      if (!queuedTimeout) {\n        queuedTimeout = true;\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve().then(() => {\n          while (queued.length) {\n            queued.shift()();\n          }\n          queuedTimeout = false;\n        }).catch(error => setTimeout(() => {\n          throw error;\n        }));\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState);\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options);\n      table.options = mergeOptions(newOptions);\n    },\n    getState: () => {\n      return table.options.state;\n    },\n    setState: updater => {\n      table.options.onStateChange == null || table.options.onStateChange(updater);\n    },\n    _getRowId: (row, index, parent) => {\n      var _table$options$getRow;\n      return (_table$options$getRow = table.options.getRowId == null ? void 0 : table.options.getRowId(row, index, parent)) != null ? _table$options$getRow : `${parent ? [parent.id, index].join('.') : index}`;\n    },\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table);\n      }\n      return table._getCoreRowModel();\n    },\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel();\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id, searchAll) => {\n      let row = (searchAll ? table.getPrePaginationRowModel() : table.getRowModel()).rowsById[id];\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id];\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`);\n          }\n          throw new Error();\n        }\n      }\n      return row;\n    },\n    _getDefaultColumnDef: memo(() => [table.options.defaultColumn], defaultColumn => {\n      var _defaultColumn;\n      defaultColumn = (_defaultColumn = defaultColumn) != null ? _defaultColumn : {};\n      return {\n        header: props => {\n          const resolvedColumnDef = props.header.column.columnDef;\n          if (resolvedColumnDef.accessorKey) {\n            return resolvedColumnDef.accessorKey;\n          }\n          if (resolvedColumnDef.accessorFn) {\n            return resolvedColumnDef.id;\n          }\n          return null;\n        },\n        // footer: props => props.header.column.id,\n        cell: props => {\n          var _props$renderValue$to, _props$renderValue;\n          return (_props$renderValue$to = (_props$renderValue = props.renderValue()) == null || _props$renderValue.toString == null ? void 0 : _props$renderValue.toString()) != null ? _props$renderValue$to : null;\n        },\n        ...table._features.reduce((obj, feature) => {\n          return Object.assign(obj, feature.getDefaultColumnDef == null ? void 0 : feature.getDefaultColumnDef());\n        }, {}),\n        ...defaultColumn\n      };\n    }, getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')),\n    _getColumnDefs: () => table.options.columns,\n    getAllColumns: memo(() => [table._getColumnDefs()], columnDefs => {\n      const recurseColumns = function (columnDefs, parent, depth) {\n        if (depth === void 0) {\n          depth = 0;\n        }\n        return columnDefs.map(columnDef => {\n          const column = createColumn(table, columnDef, depth, parent);\n          const groupingColumnDef = columnDef;\n          column.columns = groupingColumnDef.columns ? recurseColumns(groupingColumnDef.columns, column, depth + 1) : [];\n          return column;\n        });\n      };\n      return recurseColumns(columnDefs);\n    }, getMemoOptions(options, 'debugColumns', 'getAllColumns')),\n    getAllFlatColumns: memo(() => [table.getAllColumns()], allColumns => {\n      return allColumns.flatMap(column => {\n        return column.getFlatColumns();\n      });\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')),\n    _getAllFlatColumnsById: memo(() => [table.getAllFlatColumns()], flatColumns => {\n      return flatColumns.reduce((acc, column) => {\n        acc[column.id] = column;\n        return acc;\n      }, {});\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')),\n    getAllLeafColumns: memo(() => [table.getAllColumns(), table._getOrderColumnsFn()], (allColumns, orderColumns) => {\n      let leafColumns = allColumns.flatMap(column => column.getLeafColumns());\n      return orderColumns(leafColumns);\n    }, getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')),\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId];\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`);\n      }\n      return column;\n    }\n  };\n  Object.assign(table, coreInstance);\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index];\n    feature == null || feature.createTable == null || feature.createTable(table);\n  }\n  return table;\n}\n\nfunction getCoreRowModel() {\n  return table => memo(() => [table.options.data], data => {\n    const rowModel = {\n      rows: [],\n      flatRows: [],\n      rowsById: {}\n    };\n    const accessRows = function (originalRows, depth, parentRow) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      const rows = [];\n      for (let i = 0; i < originalRows.length; i++) {\n        // This could be an expensive check at scale, so we should move it somewhere else, but where?\n        // if (!id) {\n        //   if (process.env.NODE_ENV !== 'production') {\n        //     throw new Error(`getRowId expected an ID, but got ${id}`)\n        //   }\n        // }\n\n        // Make the row\n        const row = createRow(table, table._getRowId(originalRows[i], i, parentRow), originalRows[i], i, depth, undefined, parentRow == null ? void 0 : parentRow.id);\n\n        // Keep track of every row in a flat array\n        rowModel.flatRows.push(row);\n        // Also keep track of every row by its ID\n        rowModel.rowsById[row.id] = row;\n        // Push table row into parent\n        rows.push(row);\n\n        // Get the original subrows\n        if (table.options.getSubRows) {\n          var _row$originalSubRows;\n          row.originalSubRows = table.options.getSubRows(originalRows[i], i);\n\n          // Then recursively access them\n          if ((_row$originalSubRows = row.originalSubRows) != null && _row$originalSubRows.length) {\n            row.subRows = accessRows(row.originalSubRows, depth + 1, row);\n          }\n        }\n      }\n      return rows;\n    };\n    rowModel.rows = accessRows(data);\n    return rowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getExpandedRowModel() {\n  return table => memo(() => [table.getState().expanded, table.getPreExpandedRowModel(), table.options.paginateExpandedRows], (expanded, rowModel, paginateExpandedRows) => {\n    if (!rowModel.rows.length || expanded !== true && !Object.keys(expanded != null ? expanded : {}).length) {\n      return rowModel;\n    }\n    if (!paginateExpandedRows) {\n      // Only expand rows at this point if they are being paginated\n      return rowModel;\n    }\n    return expandRows(rowModel);\n  }, getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel'));\n}\nfunction expandRows(rowModel) {\n  const expandedRows = [];\n  const handleRow = row => {\n    var _row$subRows;\n    expandedRows.push(row);\n    if ((_row$subRows = row.subRows) != null && _row$subRows.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow);\n    }\n  };\n  rowModel.rows.forEach(handleRow);\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById\n  };\n}\n\nfunction getFacetedMinMaxValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return undefined;\n    const uniqueValues = facetedRowModel.flatRows.flatMap(flatRow => {\n      var _flatRow$getUniqueVal;\n      return (_flatRow$getUniqueVal = flatRow.getUniqueValues(columnId)) != null ? _flatRow$getUniqueVal : [];\n    }).map(Number).filter(value => !Number.isNaN(value));\n    if (!uniqueValues.length) return;\n    let facetedMinValue = uniqueValues[0];\n    let facetedMaxValue = uniqueValues[uniqueValues.length - 1];\n    for (const value of uniqueValues) {\n      if (value < facetedMinValue) facetedMinValue = value;else if (value > facetedMaxValue) facetedMaxValue = value;\n    }\n    return [facetedMinValue, facetedMaxValue];\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues'));\n}\n\nfunction filterRows(rows, filterRowImpl, table) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table);\n  }\n  return filterRowModelFromRoot(rows, filterRowImpl, table);\n}\nfunction filterRowModelFromLeafs(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea : 100;\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    const rows = [];\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      var _row$subRows;\n      let row = rowsToFilter[i];\n      const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n      newRow.columnFilters = row.columnFilters;\n      if ((_row$subRows = row.subRows) != null && _row$subRows.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n        row = newRow;\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n      } else {\n        row = newRow;\n        if (filterRow(row)) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n        }\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\nfunction filterRowModelFromRoot(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea2;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea2 = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea2 : 100;\n\n  // Filters top level and nested rows\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    // Filter from parents downward first\n\n    const rows = [];\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i];\n      const pass = filterRow(row);\n      if (pass) {\n        var _row$subRows2;\n        if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length && depth < maxDepth) {\n          const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n          row = newRow;\n        }\n        rows.push(row);\n        newFilteredFlatRows.push(row);\n        newFilteredRowsById[row.id] = row;\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\n\nfunction getFacetedRowModel() {\n  return (table, columnId) => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter, table.getFilteredRowModel()], (preRowModel, columnFilters, globalFilter) => {\n    if (!preRowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      return preRowModel;\n    }\n    const filterableIds = [...columnFilters.map(d => d.id).filter(d => d !== columnId), globalFilter ? '__global__' : undefined].filter(Boolean);\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n    return filterRows(preRowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel'));\n}\n\nfunction getFacetedUniqueValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return new Map();\n    let facetedUniqueValues = new Map();\n    for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n      const values = facetedRowModel.flatRows[i].getUniqueValues(columnId);\n      for (let j = 0; j < values.length; j++) {\n        const value = values[j];\n        if (facetedUniqueValues.has(value)) {\n          var _facetedUniqueValues$;\n          facetedUniqueValues.set(value, ((_facetedUniqueValues$ = facetedUniqueValues.get(value)) != null ? _facetedUniqueValues$ : 0) + 1);\n        } else {\n          facetedUniqueValues.set(value, 1);\n        }\n      }\n    }\n    return facetedUniqueValues;\n  }, getMemoOptions(table.options, 'debugTable', `getFacetedUniqueValues_${columnId}`));\n}\n\nfunction getFilteredRowModel() {\n  return table => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter], (rowModel, columnFilters, globalFilter) => {\n    if (!rowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      for (let i = 0; i < rowModel.flatRows.length; i++) {\n        rowModel.flatRows[i].columnFilters = {};\n        rowModel.flatRows[i].columnFiltersMeta = {};\n      }\n      return rowModel;\n    }\n    const resolvedColumnFilters = [];\n    const resolvedGlobalFilters = [];\n    (columnFilters != null ? columnFilters : []).forEach(d => {\n      var _filterFn$resolveFilt;\n      const column = table.getColumn(d.id);\n      if (!column) {\n        return;\n      }\n      const filterFn = column.getFilterFn();\n      if (!filterFn) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`);\n        }\n        return;\n      }\n      resolvedColumnFilters.push({\n        id: d.id,\n        filterFn,\n        resolvedValue: (_filterFn$resolveFilt = filterFn.resolveFilterValue == null ? void 0 : filterFn.resolveFilterValue(d.value)) != null ? _filterFn$resolveFilt : d.value\n      });\n    });\n    const filterableIds = (columnFilters != null ? columnFilters : []).map(d => d.id);\n    const globalFilterFn = table.getGlobalFilterFn();\n    const globallyFilterableColumns = table.getAllLeafColumns().filter(column => column.getCanGlobalFilter());\n    if (globalFilter && globalFilterFn && globallyFilterableColumns.length) {\n      filterableIds.push('__global__');\n      globallyFilterableColumns.forEach(column => {\n        var _globalFilterFn$resol;\n        resolvedGlobalFilters.push({\n          id: column.id,\n          filterFn: globalFilterFn,\n          resolvedValue: (_globalFilterFn$resol = globalFilterFn.resolveFilterValue == null ? void 0 : globalFilterFn.resolveFilterValue(globalFilter)) != null ? _globalFilterFn$resol : globalFilter\n        });\n      });\n    }\n    let currentColumnFilter;\n    let currentGlobalFilter;\n\n    // Flag the prefiltered row model with each filter state\n    for (let j = 0; j < rowModel.flatRows.length; j++) {\n      const row = rowModel.flatRows[j];\n      row.columnFilters = {};\n      if (resolvedColumnFilters.length) {\n        for (let i = 0; i < resolvedColumnFilters.length; i++) {\n          currentColumnFilter = resolvedColumnFilters[i];\n          const id = currentColumnFilter.id;\n\n          // Tag the row with the column filter state\n          row.columnFilters[id] = currentColumnFilter.filterFn(row, id, currentColumnFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          });\n        }\n      }\n      if (resolvedGlobalFilters.length) {\n        for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n          currentGlobalFilter = resolvedGlobalFilters[i];\n          const id = currentGlobalFilter.id;\n          // Tag the row with the first truthy global filter state\n          if (currentGlobalFilter.filterFn(row, id, currentGlobalFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          })) {\n            row.columnFilters.__global__ = true;\n            break;\n          }\n        }\n        if (row.columnFilters.__global__ !== true) {\n          row.columnFilters.__global__ = false;\n        }\n      }\n    }\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    // Filter final rows using all of the active filters\n    return filterRows(rowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getGroupedRowModel() {\n  return table => memo(() => [table.getState().grouping, table.getPreGroupedRowModel()], (grouping, rowModel) => {\n    if (!rowModel.rows.length || !grouping.length) {\n      rowModel.rows.forEach(row => {\n        row.depth = 0;\n        row.parentId = undefined;\n      });\n      return rowModel;\n    }\n\n    // Filter the grouping list down to columns that exist\n    const existingGrouping = grouping.filter(columnId => table.getColumn(columnId));\n    const groupedFlatRows = [];\n    const groupedRowsById = {};\n    // const onlyGroupedFlatRows: Row[] = [];\n    // const onlyGroupedRowsById: Record<RowId, Row> = {};\n    // const nonGroupedFlatRows: Row[] = [];\n    // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n    // Recursively group the data\n    const groupUpRecursively = function (rows, depth, parentId) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      // Grouping depth has been been met\n      // Stop grouping and simply rewrite thd depth and row relationships\n      if (depth >= existingGrouping.length) {\n        return rows.map(row => {\n          row.depth = depth;\n          groupedFlatRows.push(row);\n          groupedRowsById[row.id] = row;\n          if (row.subRows) {\n            row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id);\n          }\n          return row;\n        });\n      }\n      const columnId = existingGrouping[depth];\n\n      // Group the rows together for this level\n      const rowGroupsMap = groupBy(rows, columnId);\n\n      // Perform aggregations for each group\n      const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map((_ref, index) => {\n        let [groupingValue, groupedRows] = _ref;\n        let id = `${columnId}:${groupingValue}`;\n        id = parentId ? `${parentId}>${id}` : id;\n\n        // First, Recurse to group sub rows before aggregation\n        const subRows = groupUpRecursively(groupedRows, depth + 1, id);\n        subRows.forEach(subRow => {\n          subRow.parentId = id;\n        });\n\n        // Flatten the leaf rows of the rows in this group\n        const leafRows = depth ? flattenBy(groupedRows, row => row.subRows) : groupedRows;\n        const row = createRow(table, id, leafRows[0].original, index, depth, undefined, parentId);\n        Object.assign(row, {\n          groupingColumnId: columnId,\n          groupingValue,\n          subRows,\n          leafRows,\n          getValue: columnId => {\n            // Don't aggregate columns that are in the grouping\n            if (existingGrouping.includes(columnId)) {\n              if (row._valuesCache.hasOwnProperty(columnId)) {\n                return row._valuesCache[columnId];\n              }\n              if (groupedRows[0]) {\n                var _groupedRows$0$getVal;\n                row._valuesCache[columnId] = (_groupedRows$0$getVal = groupedRows[0].getValue(columnId)) != null ? _groupedRows$0$getVal : undefined;\n              }\n              return row._valuesCache[columnId];\n            }\n            if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n              return row._groupingValuesCache[columnId];\n            }\n\n            // Aggregate the values\n            const column = table.getColumn(columnId);\n            const aggregateFn = column == null ? void 0 : column.getAggregationFn();\n            if (aggregateFn) {\n              row._groupingValuesCache[columnId] = aggregateFn(columnId, leafRows, groupedRows);\n              return row._groupingValuesCache[columnId];\n            }\n          }\n        });\n        subRows.forEach(subRow => {\n          groupedFlatRows.push(subRow);\n          groupedRowsById[subRow.id] = subRow;\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        });\n        return row;\n      });\n      return aggregatedGroupedRows;\n    };\n    const groupedRows = groupUpRecursively(rowModel.rows, 0);\n    groupedRows.forEach(subRow => {\n      groupedFlatRows.push(subRow);\n      groupedRowsById[subRow.id] = subRow;\n      // if (subRow.getIsGrouped?.()) {\n      //   onlyGroupedFlatRows.push(subRow);\n      //   onlyGroupedRowsById[subRow.id] = subRow;\n      // } else {\n      //   nonGroupedFlatRows.push(subRow);\n      //   nonGroupedRowsById[subRow.id] = subRow;\n      // }\n    });\n    return {\n      rows: groupedRows,\n      flatRows: groupedFlatRows,\n      rowsById: groupedRowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n    table._queue(() => {\n      table._autoResetExpanded();\n      table._autoResetPageIndex();\n    });\n  }));\n}\nfunction groupBy(rows, columnId) {\n  const groupMap = new Map();\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`;\n    const previous = map.get(resKey);\n    if (!previous) {\n      map.set(resKey, [row]);\n    } else {\n      previous.push(row);\n    }\n    return map;\n  }, groupMap);\n}\n\nfunction getPaginationRowModel(opts) {\n  return table => memo(() => [table.getState().pagination, table.getPrePaginationRowModel(), table.options.paginateExpandedRows ? undefined : table.getState().expanded], (pagination, rowModel) => {\n    if (!rowModel.rows.length) {\n      return rowModel;\n    }\n    const {\n      pageSize,\n      pageIndex\n    } = pagination;\n    let {\n      rows,\n      flatRows,\n      rowsById\n    } = rowModel;\n    const pageStart = pageSize * pageIndex;\n    const pageEnd = pageStart + pageSize;\n    rows = rows.slice(pageStart, pageEnd);\n    let paginatedRowModel;\n    if (!table.options.paginateExpandedRows) {\n      paginatedRowModel = expandRows({\n        rows,\n        flatRows,\n        rowsById\n      });\n    } else {\n      paginatedRowModel = {\n        rows,\n        flatRows,\n        rowsById\n      };\n    }\n    paginatedRowModel.flatRows = [];\n    const handleRow = row => {\n      paginatedRowModel.flatRows.push(row);\n      if (row.subRows.length) {\n        row.subRows.forEach(handleRow);\n      }\n    };\n    paginatedRowModel.rows.forEach(handleRow);\n    return paginatedRowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel'));\n}\n\nfunction getSortedRowModel() {\n  return table => memo(() => [table.getState().sorting, table.getPreSortedRowModel()], (sorting, rowModel) => {\n    if (!rowModel.rows.length || !(sorting != null && sorting.length)) {\n      return rowModel;\n    }\n    const sortingState = table.getState().sorting;\n    const sortedFlatRows = [];\n\n    // Filter out sortings that correspond to non existing columns\n    const availableSorting = sortingState.filter(sort => {\n      var _table$getColumn;\n      return (_table$getColumn = table.getColumn(sort.id)) == null ? void 0 : _table$getColumn.getCanSort();\n    });\n    const columnInfoById = {};\n    availableSorting.forEach(sortEntry => {\n      const column = table.getColumn(sortEntry.id);\n      if (!column) return;\n      columnInfoById[sortEntry.id] = {\n        sortUndefined: column.columnDef.sortUndefined,\n        invertSorting: column.columnDef.invertSorting,\n        sortingFn: column.getSortingFn()\n      };\n    });\n    const sortData = rows => {\n      // This will also perform a stable sorting using the row index\n      // if needed.\n      const sortedData = rows.map(row => ({\n        ...row\n      }));\n      sortedData.sort((rowA, rowB) => {\n        for (let i = 0; i < availableSorting.length; i += 1) {\n          var _sortEntry$desc;\n          const sortEntry = availableSorting[i];\n          const columnInfo = columnInfoById[sortEntry.id];\n          const sortUndefined = columnInfo.sortUndefined;\n          const isDesc = (_sortEntry$desc = sortEntry == null ? void 0 : sortEntry.desc) != null ? _sortEntry$desc : false;\n          let sortInt = 0;\n\n          // All sorting ints should always return in ascending order\n          if (sortUndefined) {\n            const aValue = rowA.getValue(sortEntry.id);\n            const bValue = rowB.getValue(sortEntry.id);\n            const aUndefined = aValue === undefined;\n            const bUndefined = bValue === undefined;\n            if (aUndefined || bUndefined) {\n              if (sortUndefined === 'first') return aUndefined ? -1 : 1;\n              if (sortUndefined === 'last') return aUndefined ? 1 : -1;\n              sortInt = aUndefined && bUndefined ? 0 : aUndefined ? sortUndefined : -sortUndefined;\n            }\n          }\n          if (sortInt === 0) {\n            sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id);\n          }\n\n          // If sorting is non-zero, take care of desc and inversion\n          if (sortInt !== 0) {\n            if (isDesc) {\n              sortInt *= -1;\n            }\n            if (columnInfo.invertSorting) {\n              sortInt *= -1;\n            }\n            return sortInt;\n          }\n        }\n        return rowA.index - rowB.index;\n      });\n\n      // If there are sub-rows, sort them\n      sortedData.forEach(row => {\n        var _row$subRows;\n        sortedFlatRows.push(row);\n        if ((_row$subRows = row.subRows) != null && _row$subRows.length) {\n          row.subRows = sortData(row.subRows);\n        }\n      });\n      return sortedData;\n    };\n    return {\n      rows: sortData(rowModel.rows),\n      flatRows: sortedFlatRows,\n      rowsById: rowModel.rowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () => table._autoResetPageIndex()));\n}\n\nexport { ColumnFaceting, ColumnFiltering, ColumnGrouping, ColumnOrdering, ColumnPinning, ColumnSizing, ColumnVisibility, GlobalFaceting, GlobalFiltering, Headers, RowExpanding, RowPagination, RowPinning, RowSelection, RowSorting, _getVisibleLeafColumns, aggregationFns, buildHeaderGroups, createCell, createColumn, createColumnHelper, createRow, createTable, defaultColumnSizing, expandRows, filterFns, flattenBy, functionalUpdate, getCoreRowModel, getExpandedRowModel, getFacetedMinMaxValues, getFacetedRowModel, getFacetedUniqueValues, getFilteredRowModel, getGroupedRowModel, getMemoOptions, getPaginationRowModel, getSortedRowModel, isFunction, isNumberArray, isRowSelected, isSubRowSelected, makeStateUpdater, memo, noop, orderColumns, passiveEventSupported, reSplitAlphaNumeric, selectRowsFn, shouldAutoRemoveFilter, sortingFns };\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | React.JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "names": ["functionalUpdate", "updater", "input", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "result", "deps", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "createCell", "table", "row", "column", "columnId", "cell", "id", "getValue", "renderValue", "_cell$getValue", "options", "renderFallbackValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "resolvedColumnDef", "_getDefaultColumnDef", "accessorKey", "accessorFn", "prototype", "replaceAll", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "headerGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "bottomHeaders", "recurseHeadersForSpans", "childRowSpans", "childColSpan", "childRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "_filterValue$toString", "search", "toString", "toLowerCase", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "_old$filter", "shouldAutoRemoveFilter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "aggregationFns", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "ColumnSizing", "columnSizing", "columnSizingInfo", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "document", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "noop", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "cells", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "autoResetAll", "autoResetExpanded", "manualExpanding", "_queue", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "RowPagination", "pagination", "pageIndex", "pageSize", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "RowPinning", "rowPinning", "top", "bottom", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "rowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "_old$top2", "_old$bottom2", "has", "enableRowPinning", "isTop", "isBottom", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "allRows", "topPinnedRowIds", "bottomPinnedRowIds", "getCenterRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "compareBasic", "compareAlphanumeric", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "alphanumeric", "rowA", "rowB", "alphanumericCaseSensitive", "text", "textCaseSensitive", "datetime", "basic", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "sortAction", "newSorting", "nextDesc", "_table$options$maxMul", "getCanMultiSort", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "_options$_features", "_options$initialState", "defaultOptions", "assign", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "mergeOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "defaultColumn", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "expandRows", "expandedRows", "handleRow", "filterRows", "filterRowImpl", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "filterRowModelFromLeafs", "_table$options$maxLea2", "filterRowModelFromRoot", "accessor", "display", "group", "Comp", "component", "proto", "getPrototypeOf", "isReactComponent", "isClassComponent", "$$typeof", "description", "isExoticComponent", "React", "createElement", "data", "accessRows", "originalRows", "_row$originalSubRows", "getSubRows", "originalSubRows", "_table$getColumn", "facetedRowModel", "uniqueValues", "flatRow", "_flatRow$getUniqueVal", "facetedMinValue", "facetedMaxValue", "preRowModel", "filterableIds", "facetedUniqueValues", "j", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "currentColumnFilter", "currentGlobalFilter", "_globalFilterFn$resol", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupMap", "res<PERSON>ey", "previous", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aUndefined", "bUndefined", "resolvedOptions", "tableRef", "useState", "current", "prev"], "mappings": ";;;;;;;;;;;;;;;;;;;;OAyEA,SAASA,EAAiBC,EAASC,GACjC,MAA0B,mBAAZD,EAAyBA,EAAQC,GAASD,CAC1D,CAIA,SAASE,EAAiBC,EAAKC,GAC7B,OAAOJ,IACLI,EAASC,UAASC,IACT,IACFA,EACHH,CAACA,GAAMJ,EAAiBC,EAASM,EAAIH,OAEvC,CAEN,CACA,SAASI,EAAWC,GAClB,OAAOA,aAAaC,QACtB,CACA,SAASC,EAAcF,GACrB,OAAOG,MAAMC,QAAQJ,IAAMA,EAAEK,OAAMC,GAAsB,iBAARA,GACnD,CACA,SAASC,EAAUC,EAAKC,GACtB,MAAMC,EAAO,GACPC,EAAUC,IACdA,EAAOC,SAAQC,IACbJ,EAAKK,KAAKD,GACV,MAAME,EAAWP,EAAYK,GACb,MAAZE,GAAoBA,EAASC,QAC/BN,EAAQK,EACT,GACD,EAGJ,OADAL,EAAQH,GACDE,CACT,CACA,SAASQ,EAAKC,EAASC,EAAIC,GACzB,IACIC,EADAC,EAAO,GAEX,OAAOC,IACL,IAAIC,EACAJ,EAAK1B,KAAO0B,EAAKK,QAAOD,EAAUE,KAAKC,OAC3C,MAAMC,EAAUV,EAAQK,GAExB,KADoBK,EAAQZ,SAAWM,EAAKN,QAAUY,EAAQC,MAAK,CAACC,EAAKC,IAAUT,EAAKS,KAAWD,KAEjG,OAAOT,EAGT,IAAIW,EAIJ,GALAV,EAAOM,EAEHR,EAAK1B,KAAO0B,EAAKK,QAAOO,EAAaN,KAAKC,OAC9CN,EAASF,KAAMS,GACP,MAARR,GAAiC,MAAjBA,EAAKa,UAAoBb,EAAKa,SAASZ,GACnDD,EAAK1B,KAAO0B,EAAKK,OACP,MAARL,GAAgBA,EAAKK,QAAS,CAChC,MAAMS,EAAaC,KAAKC,MAA+B,KAAxBV,KAAKC,MAAQH,IAAkB,IACxDa,EAAgBF,KAAKC,MAAkC,KAA3BV,KAAKC,MAAQK,IAAqB,IAC9DM,EAAsBD,EAAgB,GACtCE,EAAM,CAACC,EAAKC,KAEhB,IADAD,EAAME,OAAOF,GACNA,EAAIxB,OAASyB,GAClBD,EAAM,IAAMA,EAEd,OAAOA,CAAG,EAEZG,QAAQC,KAAK,OAAOL,EAAIF,EAAe,OAAOE,EAAIL,EAAY,QAAS,2FAGtDC,KAAKU,IAAI,EAAGV,KAAKW,IAAI,IAAM,IAAMR,EAAqB,sBAA+B,MAARlB,OAAe,EAASA,EAAK1B,IAC5H,CAEH,OAAO2B,CAAM,CAEjB,CACA,SAAS0B,EAAeC,EAAcC,EAAYvD,EAAKuC,GACrD,MAAO,CACLR,MAAO,KACL,IAAIyB,EACJ,OAA0F,OAAlFA,EAAwC,MAAhBF,OAAuB,EAASA,EAAaG,UAAoBD,EAAwBF,EAAaC,EAAW,EAEnJvD,KAAK,EACLuC,WAEJ,CAEA,SAASmB,EAAWC,EAAOC,EAAKC,EAAQC,GACtC,MAIMC,EAAO,CACXC,GAAI,GAAGJ,EAAII,MAAMH,EAAOG,KACxBJ,MACAC,SACAI,SAAU,IAAML,EAAIK,SAASH,GAC7BI,YATqB,KACrB,IAAIC,EACJ,OAA6C,OAArCA,EAAiBJ,EAAKE,YAAsBE,EAAiBR,EAAMS,QAAQC,mBAAmB,EAQtGC,WAAY/C,GAAK,IAAM,CAACoC,EAAOE,EAAQD,EAAKG,KAAO,CAACJ,EAAOE,EAAQD,EAAKG,KAAU,CAChFJ,QACAE,SACAD,MACAG,KAAMA,EACNE,SAAUF,EAAKE,SACfC,YAAaH,EAAKG,eAChBb,EAAeM,EAAMS,QAAS,gBAKpC,OAHAT,EAAMY,UAAUrD,SAAQsD,IACA,MAAtBA,EAAQd,YAAsBc,EAAQd,WAAWK,EAAMF,EAAQD,EAAKD,EAAM,GACzE,CAAE,GACEI,CACT,CAEA,SAASU,EAAad,EAAOe,EAAWC,EAAOC,GAC7C,IAAIC,EAAMC,EACV,MACMC,EAAoB,IADJpB,EAAMqB,0BAGvBN,GAECO,EAAcF,EAAkBE,YACtC,IACIC,EADAlB,EAAmP,OAA7Oa,EAAyD,OAAjDC,EAAwBC,EAAkBf,IAAcc,EAAwBG,EAAqD,mBAAhCjC,OAAOmC,UAAUC,WAA4BH,EAAYG,WAAW,IAAK,KAAOH,EAAYI,QAAQ,MAAO,UAAOC,GAAqBT,EAA2C,iBAA7BE,EAAkBQ,OAAsBR,EAAkBQ,YAASD,EAsB/U,GApBIP,EAAkBG,WACpBA,EAAaH,EAAkBG,WACtBD,IAGPC,EADED,EAAYO,SAAS,KACVC,IACX,IAAI9D,EAAS8D,EACb,IAAK,MAAMzF,KAAOiF,EAAYS,MAAM,KAAM,CACxC,IAAIC,EACJhE,EAA+B,OAArBgE,EAAUhE,QAAkB,EAASgE,EAAQ3F,EAIxD,CACD,OAAO2B,CAAM,EAGF8D,GAAeA,EAAYV,EAAkBE,eAGzDjB,EAIH,MAAM,IAAI4B,MAEZ,IAAI/B,EAAS,CACXG,GAAI,GAAGhB,OAAOgB,KACdkB,aACAN,OAAQA,EACRD,QACAD,UAAWK,EACXc,QAAS,GACTC,eAAgBvE,GAAK,IAAM,EAAC,KAAO,KACjC,IAAIwE,EACJ,MAAO,CAAClC,KAAkD,OAArCkC,EAAkBlC,EAAOgC,cAAmB,EAASE,EAAgBC,SAAQ3F,GAAKA,EAAEyF,mBAAmB,GAC3HzC,EAAeM,EAAMS,QAAS,iBACjC6B,eAAgB1E,GAAK,IAAM,CAACoC,EAAMuC,wBAAuBC,IACvD,IAAIC,EACJ,GAA2C,OAAtCA,EAAmBvC,EAAOgC,UAAoBO,EAAiB9E,OAAQ,CAC1E,IAAI+E,EAAcxC,EAAOgC,QAAQG,SAAQnC,GAAUA,EAAOoC,mBAC1D,OAAOE,EAAaE,EACrB,CACD,MAAO,CAACxC,EAAO,GACdR,EAAeM,EAAMS,QAAS,kBAEnC,IAAK,MAAMI,KAAWb,EAAMY,UACF,MAAxBC,EAAQC,cAAwBD,EAAQC,aAAaZ,EAAQF,GAI/D,OAAOE,CACT,CAEA,MAAM9B,EAAQ,eAGd,SAASuE,EAAa3C,EAAOE,EAAQO,GACnC,IAAImC,EAEJ,IAAIhB,EAAS,CACXvB,GAFuC,OAA7BuC,EAAcnC,EAAQJ,IAAcuC,EAAc1C,EAAOG,GAGnEH,SACAxB,MAAO+B,EAAQ/B,MACfmE,gBAAiBpC,EAAQoC,cACzBC,cAAerC,EAAQqC,cACvB9B,MAAOP,EAAQO,MACf+B,WAAY,GACZC,QAAS,EACTC,QAAS,EACTC,YAAa,KACbC,eAAgB,KACd,MAAMC,EAAc,GACdC,EAAgBC,IAChBA,EAAEP,YAAcO,EAAEP,WAAWpF,QAC/B2F,EAAEP,WAAWQ,IAAIF,GAEnBD,EAAY3F,KAAK6F,EAAE,EAGrB,OADAD,EAAczB,GACPwB,CAAW,EAEpBzC,WAAY,KAAO,CACjBX,QACA4B,OAAQA,EACR1B,YAMJ,OAHAF,EAAMY,UAAUrD,SAAQsD,IACE,MAAxBA,EAAQ8B,cAAwB9B,EAAQ8B,aAAaf,EAAQ5B,EAAM,IAE9D4B,CACT,CACK,MAAC4B,EAAU,CACdC,YAAazD,IAGXA,EAAM0D,gBAAkB9F,GAAK,IAAM,CAACoC,EAAM2D,gBAAiB3D,EAAM4D,wBAAyB5D,EAAM6D,WAAWC,cAAcC,KAAM/D,EAAM6D,WAAWC,cAAcE,SAAQ,CAACC,EAAYvB,EAAaqB,EAAMC,KACpM,IAAIE,EAAkBC,EACtB,MAAMC,EAA6I,OAA9HF,EAA2B,MAARH,OAAe,EAASA,EAAKR,KAAIpD,GAAYuC,EAAY2B,MAAK3H,GAAKA,EAAE2D,KAAOF,MAAWmE,OAAOC,UAAoBL,EAAmB,GACvKM,EAAiJ,OAAjIL,EAA6B,MAATH,OAAgB,EAASA,EAAMT,KAAIpD,GAAYuC,EAAY2B,MAAK3H,GAAKA,EAAE2D,KAAOF,MAAWmE,OAAOC,UAAoBJ,EAAoB,GAGlL,OADqBM,EAAkBR,EAAY,IAAIG,KADjC1B,EAAY4B,QAAOpE,KAAoB,MAAR6D,GAAgBA,EAAKlC,SAAS3B,EAAOG,KAAmB,MAAT2D,GAAiBA,EAAMnC,SAAS3B,EAAOG,UAClDmE,GAAexE,EACrF,GAClBN,EAAeM,EAAMS,QAASrC,IACjC4B,EAAM0E,sBAAwB9G,GAAK,IAAM,CAACoC,EAAM2D,gBAAiB3D,EAAM4D,wBAAyB5D,EAAM6D,WAAWC,cAAcC,KAAM/D,EAAM6D,WAAWC,cAAcE,SAAQ,CAACC,EAAYvB,EAAaqB,EAAMC,IAEnMS,EAAkBR,EADzBvB,EAAcA,EAAY4B,QAAOpE,KAAoB,MAAR6D,GAAgBA,EAAKlC,SAAS3B,EAAOG,KAAmB,MAAT2D,GAAiBA,EAAMnC,SAAS3B,EAAOG,OACjFL,EAAO,WACxDN,EAAeM,EAAMS,QAASrC,IACjC4B,EAAM2E,oBAAsB/G,GAAK,IAAM,CAACoC,EAAM2D,gBAAiB3D,EAAM4D,wBAAyB5D,EAAM6D,WAAWC,cAAcC,QAAO,CAACE,EAAYvB,EAAaqB,KAC5J,IAAIa,EAEJ,OAAOH,EAAkBR,EADkI,OAA/HW,EAA4B,MAARb,OAAe,EAASA,EAAKR,KAAIpD,GAAYuC,EAAY2B,MAAK3H,GAAKA,EAAE2D,KAAOF,MAAWmE,OAAOC,UAAoBK,EAAoB,GAC7H5E,EAAO,OAAO,GACtEN,EAAeM,EAAMS,QAASrC,IACjC4B,EAAM6E,qBAAuBjH,GAAK,IAAM,CAACoC,EAAM2D,gBAAiB3D,EAAM4D,wBAAyB5D,EAAM6D,WAAWC,cAAcE,SAAQ,CAACC,EAAYvB,EAAasB,KAC9J,IAAIc,EAEJ,OAAOL,EAAkBR,EADqI,OAAlIa,EAA8B,MAATd,OAAgB,EAASA,EAAMT,KAAIpD,GAAYuC,EAAY2B,MAAK3H,GAAKA,EAAE2D,KAAOF,MAAWmE,OAAOC,UAAoBO,EAAqB,GACjI9E,EAAO,QAAQ,GACvEN,EAAeM,EAAMS,QAASrC,IAIjC4B,EAAM+E,gBAAkBnH,GAAK,IAAM,CAACoC,EAAM0D,qBAAoBsB,GACrD,IAAIA,GAAcC,WACxBvF,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMkF,oBAAsBtH,GAAK,IAAM,CAACoC,EAAM2E,yBAAwBK,GAC7D,IAAIA,GAAcC,WACxBvF,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMmF,sBAAwBvH,GAAK,IAAM,CAACoC,EAAM0E,2BAA0BM,GACjE,IAAIA,GAAcC,WACxBvF,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMoF,qBAAuBxH,GAAK,IAAM,CAACoC,EAAM6E,0BAAyBG,GAC/D,IAAIA,GAAcC,WACxBvF,EAAeM,EAAMS,QAASrC,IAIjC4B,EAAMqF,eAAiBzH,GAAK,IAAM,CAACoC,EAAM0D,qBAAoBsB,GACpDA,EAAazB,KAAIL,GACfA,EAAYoC,UAClBlI,QACFsC,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMuF,mBAAqB3H,GAAK,IAAM,CAACoC,EAAM2E,yBAAwBZ,GAC5DA,EAAKR,KAAIL,GACPA,EAAYoC,UAClBlI,QACFsC,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMwF,qBAAuB5H,GAAK,IAAM,CAACoC,EAAM0E,2BAA0BX,GAChEA,EAAKR,KAAIL,GACPA,EAAYoC,UAClBlI,QACFsC,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMyF,oBAAsB7H,GAAK,IAAM,CAACoC,EAAM6E,0BAAyBd,GAC9DA,EAAKR,KAAIL,GACPA,EAAYoC,UAClBlI,QACFsC,EAAeM,EAAMS,QAASrC,IAIjC4B,EAAM0F,qBAAuB9H,GAAK,IAAM,CAACoC,EAAMwF,0BAAyBG,GAC/DA,EAAYrB,QAAO1C,IACxB,IAAIgE,EACJ,QAAqD,OAA3CA,EAAqBhE,EAAOmB,aAAuB6C,EAAmBjI,OAAO,KAExF+B,EAAeM,EAAMS,QAASrC,IACjC4B,EAAM6F,mBAAqBjI,GAAK,IAAM,CAACoC,EAAMuF,wBAAuBI,GAC3DA,EAAYrB,QAAO1C,IACxB,IAAIkE,EACJ,QAAsD,OAA5CA,EAAsBlE,EAAOmB,aAAuB+C,EAAoBnI,OAAO,KAE1F+B,EAAeM,EAAMS,QAASrC,IACjC4B,EAAM+F,oBAAsBnI,GAAK,IAAM,CAACoC,EAAMyF,yBAAwBE,GAC7DA,EAAYrB,QAAO1C,IACxB,IAAIoE,EACJ,QAAsD,OAA5CA,EAAsBpE,EAAOmB,aAAuBiD,EAAoBrI,OAAO,KAE1F+B,EAAeM,EAAMS,QAASrC,IACjC4B,EAAMmD,eAAiBvF,GAAK,IAAM,CAACoC,EAAM2E,sBAAuB3E,EAAM0E,wBAAyB1E,EAAM6E,0BAAyB,CAACd,EAAMkC,EAAQjC,KAC3I,IAAIkC,EAAiBC,EAAQC,EAAmBC,EAAUC,EAAkBC,EAC5E,MAAO,IAAiF,OAA3EL,EAAwC,OAArBC,EAASpC,EAAK,SAAc,EAASoC,EAAOb,SAAmBY,EAAkB,MAA6F,OAAnFE,EAA8C,OAAzBC,EAAWJ,EAAO,SAAc,EAASI,EAASf,SAAmBc,EAAoB,MAAyF,OAA/EE,EAA2C,OAAvBC,EAAUvC,EAAM,SAAc,EAASuC,EAAQjB,SAAmBgB,EAAmB,IAAK/C,KAAI3B,GAC5VA,EAAOuB,mBACb/F,MAAM,GACRsC,EAAeM,EAAMS,QAASrC,GAAyB,GAG9D,SAASqG,EAAkBR,EAAYuC,EAAgBxG,EAAOyG,GAC5D,IAAIC,EAAuBC,EAO3B,IAAIC,EAAW,EACf,MAAMC,EAAe,SAAU3E,EAASlB,QACxB,IAAVA,IACFA,EAAQ,GAEV4F,EAAW9H,KAAKU,IAAIoH,EAAU5F,GAC9BkB,EAAQoC,QAAOpE,GAAUA,EAAO4G,iBAAgBvJ,SAAQ2C,IACtD,IAAIkC,EACsC,OAArCA,EAAkBlC,EAAOgC,UAAoBE,EAAgBzE,QAChEkJ,EAAa3G,EAAOgC,QAASlB,EAAQ,EACtC,GACA,EACP,EACE6F,EAAa5C,GACb,IAAIe,EAAe,GACnB,MAAM+B,EAAoB,CAACC,EAAgBhG,KAEzC,MAAMkC,EAAc,CAClBlC,QACAX,GAAI,CAACoG,EAAc,GAAGzF,KAASsD,OAAOC,SAAS0C,KAAK,KACpD3B,QAAS,IAIL4B,EAAuB,GAG7BF,EAAezJ,SAAQ4J,IAGrB,MAAMC,EAA4B,IAAIF,GAAsBjC,UAAU,GAEtE,IAAI/E,EACA2C,GAAgB,EASpB,GAXqBsE,EAAcjH,OAAOc,QAAUkC,EAAYlC,OAG5CmG,EAAcjH,OAAOe,OAEvCf,EAASiH,EAAcjH,OAAOe,QAG9Bf,EAASiH,EAAcjH,OACvB2C,GAAgB,GAEduE,IAA2D,MAA7BA,OAAoC,EAASA,EAA0BlH,UAAYA,EAEnHkH,EAA0BrE,WAAWtF,KAAK0J,OACrC,CAEL,MAAMvF,EAASe,EAAa3C,EAAOE,EAAQ,CACzCG,GAAI,CAACoG,EAAczF,EAAOd,EAAOG,GAAqB,MAAjB8G,OAAwB,EAASA,EAAc9G,IAAIiE,OAAOC,SAAS0C,KAAK,KAC7GpE,gBACAC,cAAeD,EAAgB,GAAGqE,EAAqB5C,QAAO5H,GAAKA,EAAEwD,SAAWA,IAAQvC,cAAWgE,EACnGX,QACAtC,MAAOwI,EAAqBvJ,SAI9BiE,EAAOmB,WAAWtF,KAAK0J,GAGvBD,EAAqBzJ,KAAKmE,EAC3B,CACDsB,EAAYoC,QAAQ7H,KAAK0J,GACzBA,EAAcjE,YAAcA,CAAW,IAEzC8B,EAAavH,KAAKyF,GACdlC,EAAQ,GACV+F,EAAkBG,EAAsBlG,EAAQ,EACjD,EAEGqG,EAAgBb,EAAejD,KAAI,CAACrD,EAAQxB,IAAUiE,EAAa3C,EAAOE,EAAQ,CACtFc,MAAO4F,EACPlI,YAEFqI,EAAkBM,EAAeT,EAAW,GAC5C5B,EAAaC,UAMb,MAAMqC,EAAyBhC,GACLA,EAAQhB,QAAO1C,GAAUA,EAAO1B,OAAO4G,iBACxCvD,KAAI3B,IACzB,IAAIoB,EAAU,EACVC,EAAU,EACVsE,EAAgB,CAAC,GACjB3F,EAAOmB,YAAcnB,EAAOmB,WAAWpF,QACzC4J,EAAgB,GAChBD,EAAuB1F,EAAOmB,YAAYxF,SAAQ2D,IAChD,IACE8B,QAASwE,EACTvE,QAASwE,GACPvG,EACJ8B,GAAWwE,EACXD,EAAc9J,KAAKgK,EAAa,KAGlCzE,EAAU,EAMZ,OAHAC,GADwBnE,KAAKW,OAAO8H,GAEpC3F,EAAOoB,QAAUA,EACjBpB,EAAOqB,QAAUA,EACV,CACLD,UACAC,UACD,IAIL,OADAqE,EAAiI,OAAzGZ,EAA8D,OAArCC,EAAiB3B,EAAa,SAAc,EAAS2B,EAAerB,SAAmBoB,EAAwB,IACzJ1B,CACT,CAEK,MAAC0C,EAAY,CAAC1H,EAAOK,EAAIsH,EAAUC,EAAU5G,EAAO6G,EAASC,KAChE,IAAI7H,EAAM,CACRI,KACA3B,MAAOkJ,EACPD,WACA3G,QACA8G,WACAC,aAAc,CAAE,EAChBC,mBAAoB,CAAE,EACtB1H,SAAUH,IACR,GAAIF,EAAI8H,aAAaE,eAAe9H,GAClC,OAAOF,EAAI8H,aAAa5H,GAE1B,MAAMD,EAASF,EAAMkI,UAAU/H,GAC/B,OAAgB,MAAVD,GAAkBA,EAAOqB,YAG/BtB,EAAI8H,aAAa5H,GAAYD,EAAOqB,WAAWtB,EAAI0H,SAAUC,GACtD3H,EAAI8H,aAAa5H,SAJxB,CAIiC,EAEnCgI,gBAAiBhI,IACf,GAAIF,EAAI+H,mBAAmBC,eAAe9H,GACxC,OAAOF,EAAI+H,mBAAmB7H,GAEhC,MAAMD,EAASF,EAAMkI,UAAU/H,GAC/B,OAAgB,MAAVD,GAAkBA,EAAOqB,WAG1BrB,EAAOa,UAAUoH,iBAItBlI,EAAI+H,mBAAmB7H,GAAYD,EAAOa,UAAUoH,gBAAgBlI,EAAI0H,SAAUC,GAC3E3H,EAAI+H,mBAAmB7H,KAJ5BF,EAAI+H,mBAAmB7H,GAAY,CAACF,EAAIK,SAASH,IAC1CF,EAAI+H,mBAAmB7H,SALhC,CAQuC,EAEzCI,YAAaJ,IACX,IAAIiI,EACJ,OAAmD,OAA3CA,EAAgBnI,EAAIK,SAASH,IAAqBiI,EAAgBpI,EAAMS,QAAQC,mBAAmB,EAE7GmH,QAAoB,MAAXA,EAAkBA,EAAU,GACrCQ,YAAa,IAAMpL,EAAUgD,EAAI4H,SAASnL,GAAKA,EAAEmL,UACjDS,aAAc,IAAMrI,EAAI6H,SAAW9H,EAAMuI,OAAOtI,EAAI6H,UAAU,QAAQnG,EACtE6G,cAAe,KACb,IAAIC,EAAa,GACbC,EAAazI,EACjB,OAAa,CACX,MAAM0I,EAAYD,EAAWJ,eAC7B,IAAKK,EAAW,MAChBF,EAAWhL,KAAKkL,GAChBD,EAAaC,CACd,CACD,OAAOF,EAAWxD,SAAS,EAE7B2D,YAAahL,GAAK,IAAM,CAACoC,EAAM6I,uBAAsBnG,GAC5CA,EAAYa,KAAIrD,GACdH,EAAWC,EAAOC,EAAKC,EAAQA,EAAOG,OAE9CX,EAAeM,EAAMS,QAAS,cACjCqI,uBAAwBlL,GAAK,IAAM,CAACqC,EAAI2I,iBAAgBG,GAC/CA,EAASC,QAAO,CAACC,EAAK7I,KAC3B6I,EAAI7I,EAAKF,OAAOG,IAAMD,EACf6I,IACN,CAAE,IACJvJ,EAAeM,EAAMS,QAAS,eAEnC,IAAK,IAAIyI,EAAI,EAAGA,EAAIlJ,EAAMY,UAAUjD,OAAQuL,IAAK,CAC/C,MAAMrI,EAAUb,EAAMY,UAAUsI,GACrB,MAAXrI,GAAwC,MAArBA,EAAQ6G,WAAqB7G,EAAQ6G,UAAUzH,EAAKD,EACxE,CACD,OAAOC,CAAG,EAKNkJ,EAAiB,CACrBrI,aAAc,CAACZ,EAAQF,KACrBE,EAAOkJ,oBAAsBpJ,EAAMS,QAAQ4I,oBAAsBrJ,EAAMS,QAAQ4I,mBAAmBrJ,EAAOE,EAAOG,IAChHH,EAAOmJ,mBAAqB,IACrBnJ,EAAOkJ,oBAGLlJ,EAAOkJ,sBAFLpJ,EAAMsJ,yBAIjBpJ,EAAOqJ,wBAA0BvJ,EAAMS,QAAQ+I,wBAA0BxJ,EAAMS,QAAQ+I,uBAAuBxJ,EAAOE,EAAOG,IAC5HH,EAAOsJ,uBAAyB,IACzBtJ,EAAOqJ,wBAGLrJ,EAAOqJ,0BAFL,IAAIE,IAIfvJ,EAAOwJ,wBAA0B1J,EAAMS,QAAQkJ,wBAA0B3J,EAAMS,QAAQkJ,uBAAuB3J,EAAOE,EAAOG,IAC5HH,EAAOyJ,uBAAyB,KAC9B,GAAKzJ,EAAOwJ,wBAGZ,OAAOxJ,EAAOwJ,yBAAyB,CACxC,GAICE,EAAiB,CAAC3J,EAAKE,EAAU0J,KACrC,IAAIC,EAAuB1B,EAC3B,MAAM2B,EAAwB,MAAfF,GAA2E,OAAnDC,EAAwBD,EAAYG,iBAAsB,EAASF,EAAsBG,cAChI,OAAO1F,QAAoD,OAA3C6D,EAAgBnI,EAAIK,SAASH,KAAoE,OAA7CiI,EAAgBA,EAAc4B,aAAwE,OAAhD5B,EAAgBA,EAAc6B,oBAAyB,EAAS7B,EAAcvG,SAASkI,GAAQ,EAE3NH,EAAeM,WAAalN,GAAOmN,EAAWnN,GAC9C,MAAMoN,EAA0B,CAACnK,EAAKE,EAAU0J,KAC9C,IAAIQ,EACJ,OAAO9F,QAAqD,OAA5C8F,EAAiBpK,EAAIK,SAASH,KAAsE,OAA/CkK,EAAiBA,EAAeL,iBAAsB,EAASK,EAAexI,SAASgI,GAAa,EAE3KO,EAAwBF,WAAalN,GAAOmN,EAAWnN,GACvD,MAAMsN,EAAe,CAACrK,EAAKE,EAAU0J,KACnC,IAAIU,EACJ,OAAqD,OAA5CA,EAAiBtK,EAAIK,SAASH,KAAsE,OAA/CoK,EAAiBA,EAAeP,iBAAsB,EAASO,EAAeN,kBAAmC,MAAfJ,OAAsB,EAASA,EAAYI,cAAc,EAE3NK,EAAaJ,WAAalN,GAAOmN,EAAWnN,GAC5C,MAAMwN,EAAc,CAACvK,EAAKE,EAAU0J,KAClC,IAAIY,EACJ,OAAoD,OAA5CA,EAAiBxK,EAAIK,SAASH,SAAqB,EAASsK,EAAe5I,SAASgI,EAAY,EAE1GW,EAAYN,WAAalN,GAAOmN,EAAWnN,GAC3C,MAAM0N,EAAiB,CAACzK,EAAKE,EAAU0J,KAC7BA,EAAYrL,MAAKxB,IACvB,IAAI2N,EACJ,QAAsD,OAA5CA,EAAiB1K,EAAIK,SAASH,KAAsBwK,EAAe9I,SAAS7E,GAAK,IAG/F0N,EAAeR,WAAalN,GAAOmN,EAAWnN,MAAiB,MAAPA,GAAeA,EAAIW,QAC3E,MAAMiN,EAAkB,CAAC3K,EAAKE,EAAU0J,IAC/BA,EAAYrL,MAAKxB,IACtB,IAAI6N,EACJ,OAAoD,OAA5CA,EAAiB5K,EAAIK,SAASH,SAAqB,EAAS0K,EAAehJ,SAAS7E,EAAI,IAGpG4N,EAAgBV,WAAalN,GAAOmN,EAAWnN,MAAiB,MAAPA,GAAeA,EAAIW,QAC5E,MAAMmN,EAAS,CAAC7K,EAAKE,EAAU0J,IACtB5J,EAAIK,SAASH,KAAc0J,EAEpCiB,EAAOZ,WAAalN,GAAOmN,EAAWnN,GACtC,MAAM+N,EAAa,CAAC9K,EAAKE,EAAU0J,IAC1B5J,EAAIK,SAASH,IAAa0J,EAEnCkB,EAAWb,WAAalN,GAAOmN,EAAWnN,GAC1C,MAAMgO,EAAgB,CAAC/K,EAAKE,EAAU0J,KACpC,IAAKpK,EAAKD,GAAOqK,EACjB,MAAMoB,EAAWhL,EAAIK,SAASH,GAC9B,OAAO8K,GAAYxL,GAAOwL,GAAYzL,CAAG,EAE3CwL,EAAcE,mBAAqBlO,IACjC,IAAKmO,EAAWC,GAAapO,EACzBqO,EAAiC,iBAAdF,EAAyBG,WAAWH,GAAaA,EACpEI,EAAiC,iBAAdH,EAAyBE,WAAWF,GAAaA,EACpE3L,EAAoB,OAAd0L,GAAsBK,OAAOC,MAAMJ,IAAcK,IAAWL,EAClE7L,EAAoB,OAAd4L,GAAsBI,OAAOC,MAAMF,GAAaG,IAAWH,EACrE,GAAI9L,EAAMD,EAAK,CACb,MAAMmM,EAAOlM,EACbA,EAAMD,EACNA,EAAMmM,CACP,CACD,MAAO,CAAClM,EAAKD,EAAI,EAEnBwL,EAAcd,WAAalN,GAAOmN,EAAWnN,IAAQmN,EAAWnN,EAAI,KAAOmN,EAAWnN,EAAI,IAIrF,MAAC4O,EAAY,CAChBhC,iBACAQ,0BACAE,eACAE,cACAE,iBACAE,kBACAE,SACAC,aACAC,iBAIF,SAASb,EAAWnN,GAClB,OAAOA,SAA6C,KAARA,CAC9C,CAIK,MAAC6O,EAAkB,CACtBC,oBAAqB,KACZ,CACLC,SAAU,SAGdC,gBAAiBC,IACR,CACLC,cAAe,MACZD,IAGPE,kBAAmBnM,IACV,CACLoM,sBAAuBhQ,EAAiB,gBAAiB4D,GACzDqM,oBAAoB,EACpBC,sBAAuB,MAG3BxL,aAAc,CAACZ,EAAQF,KACrBE,EAAOqM,gBAAkB,KACvB,MAAMC,EAAWxM,EAAMyM,kBAAkBC,SAAS,GAC5CC,EAAoB,MAAZH,OAAmB,EAASA,EAASlM,SAASJ,EAAOG,IACnE,MAAqB,iBAAVsM,EACFf,EAAUhC,eAEE,iBAAV+C,EACFf,EAAUZ,cAEE,kBAAV2B,GAGG,OAAVA,GAAmC,iBAAVA,EAFpBf,EAAUd,OAKfjO,MAAMC,QAAQ6P,GACTf,EAAUpB,YAEZoB,EAAUb,UAAU,EAE7B7K,EAAO0M,YAAc,KACnB,IAAIC,EAAuBC,EAC3B,OAAOrQ,EAAWyD,EAAOa,UAAUgL,UAAY7L,EAAOa,UAAUgL,SAAyC,SAA9B7L,EAAOa,UAAUgL,SAAsB7L,EAAOqM,kBAC4B,OAApJM,EAA8E,OAArDC,EAAyB9M,EAAMS,QAAQmL,gBAAqB,EAASkB,EAAuB5M,EAAOa,UAAUgL,WAAqBc,EAAwBjB,EAAU1L,EAAOa,UAAUgL,SAAS,EAE1N7L,EAAO6M,aAAe,KACpB,IAAIC,EAAuBC,EAAuBC,EAClD,OAAyE,OAAhEF,EAAwB9M,EAAOa,UAAUoM,qBAA8BH,KAAiG,OAA9DC,EAAwBjN,EAAMS,QAAQ2M,sBAA+BH,KAA4F,OAAzDC,EAAyBlN,EAAMS,QAAQ4M,gBAAyBH,MAAoChN,EAAOqB,UAAU,EAElVrB,EAAOoN,cAAgB,IAAMpN,EAAOqN,kBAAoB,EACxDrN,EAAOsN,eAAiB,KACtB,IAAIC,EACJ,OAAmE,OAA3DA,EAAwBzN,EAAM6D,WAAWqI,gBAA2G,OAAhFuB,EAAwBA,EAAsBpJ,MAAK3H,GAAKA,EAAE2D,KAAOH,EAAOG,WAAe,EAASoN,EAAsBd,KAAK,EAEzMzM,EAAOqN,eAAiB,KACtB,IAAIG,EAAwBC,EAC5B,OAA4K,OAApKD,EAAsF,OAA5DC,EAAyB3N,EAAM6D,WAAWqI,oBAAyB,EAASyB,EAAuBC,WAAUlR,GAAKA,EAAE2D,KAAOH,EAAOG,MAAeqN,GAA0B,CAAC,EAEhNxN,EAAO2N,eAAiBlB,IACtB3M,EAAM8N,kBAAiBtR,IACrB,MAAMuP,EAAW7L,EAAO0M,cAClBmB,EAAwB,MAAPvR,OAAc,EAASA,EAAI6H,MAAK3H,GAAKA,EAAE2D,KAAOH,EAAOG,KACtE2N,EAAY/R,EAAiB0Q,EAAOoB,EAAiBA,EAAepB,WAAQhL,GAIhF,IAAIsM,EADN,GAAIC,EAAuBnC,EAAUiC,EAAW9N,GAE9C,OAAqF,OAA7E+N,EAAqB,MAAPzR,OAAc,EAASA,EAAI8H,QAAO5H,GAAKA,EAAE2D,KAAOH,EAAOG,MAAe4N,EAAc,GAE5G,MAAME,EAAe,CACnB9N,GAAIH,EAAOG,GACXsM,MAAOqB,GAGP,IAAII,EADN,OAAIL,EAOK,OALCK,EAAkB,MAAP5R,OAAc,EAASA,EAAI+G,KAAI7G,GAC5CA,EAAE2D,KAAOH,EAAOG,GACX8N,EAEFzR,KACK0R,EAAW,GAEhB,MAAP5R,GAAeA,EAAImB,OACd,IAAInB,EAAK2R,GAEX,CAACA,EAAa,GACrB,CACH,EAEHzG,UAAW,CAACzH,EAAKoO,KACfpO,EAAIiM,cAAgB,GACpBjM,EAAIqO,kBAAoB,EAAE,EAE5B7K,YAAazD,IACXA,EAAM8N,iBAAmB5R,IACvB,MAAMwG,EAAc1C,EAAM6I,oBAca,MAAvC7I,EAAMS,QAAQ2L,uBAAiCpM,EAAMS,QAAQ2L,uBAb5C5P,IACf,IAAI+R,EACJ,OAA+D,OAAvDA,EAAoBtS,EAAiBC,EAASM,SAAgB,EAAS+R,EAAkBjK,QAAOA,IACtG,MAAMpE,EAASwC,EAAY2B,MAAK3H,GAAKA,EAAE2D,KAAOiE,EAAOjE,KACrD,GAAIH,EAAQ,CAEV,GAAIgO,EADahO,EAAO0M,cACatI,EAAOqI,MAAOzM,GACjD,OAAO,CAEV,CACD,OAAO,CAAI,GACX,GAEwF,EAE9FF,EAAMwO,mBAAqBC,IACzB,IAAIC,EAAuBC,EAC3B3O,EAAM8N,iBAAiBW,EAAe,GAAkI,OAA5HC,EAAsE,OAA7CC,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoBzC,eAAyBwC,EAAwB,GAAG,EAE5M1O,EAAMsJ,uBAAyB,IAAMtJ,EAAMyM,kBAC3CzM,EAAM6O,oBAAsB,MACrB7O,EAAM8O,sBAAwB9O,EAAMS,QAAQoO,sBAC/C7O,EAAM8O,qBAAuB9O,EAAMS,QAAQoO,oBAAoB7O,IAE7DA,EAAMS,QAAQsO,kBAAoB/O,EAAM8O,qBACnC9O,EAAMsJ,yBAERtJ,EAAM8O,uBACd,GAGL,SAASZ,EAAuBnC,EAAUY,EAAOzM,GAC/C,SAAQ6L,IAAYA,EAAS7B,aAAa6B,EAAS7B,WAAWyC,EAAOzM,SAAqC,IAAVyM,GAA0C,iBAAVA,IAAuBA,CACzJ,CAEA,MAgFMqC,EAAiB,CACrBC,IAjFU,CAAC9O,EAAU+O,EAAWC,IAGzBA,EAAUnG,QAAO,CAACiG,EAAKG,KAC5B,MAAMC,EAAYD,EAAK9O,SAASH,GAChC,OAAO8O,GAA4B,iBAAdI,EAAyBA,EAAY,EAAE,GAC3D,GA4EH5P,IA1EU,CAACU,EAAU+O,EAAWC,KAChC,IAAI1P,EAOJ,OANA0P,EAAU5R,SAAQ0C,IAChB,MAAM0M,EAAQ1M,EAAIK,SAASH,GACd,MAATwM,IAAkBlN,EAAMkN,QAAiBhL,IAARlC,GAAqBkN,GAASA,KACjElN,EAAMkN,EACP,IAEIlN,CAAG,EAmEVD,IAjEU,CAACW,EAAU+O,EAAWC,KAChC,IAAI3P,EAOJ,OANA2P,EAAU5R,SAAQ0C,IAChB,MAAM0M,EAAQ1M,EAAIK,SAASH,GACd,MAATwM,IAAkBnN,EAAMmN,QAAiBhL,IAARnC,GAAqBmN,GAASA,KACjEnN,EAAMmN,EACP,IAEInN,CAAG,EA0DV8P,OAxDa,CAACnP,EAAU+O,EAAWC,KACnC,IAAI1P,EACAD,EAYJ,OAXA2P,EAAU5R,SAAQ0C,IAChB,MAAM0M,EAAQ1M,EAAIK,SAASH,GACd,MAATwM,SACUhL,IAARlC,EACEkN,GAASA,IAAOlN,EAAMD,EAAMmN,IAE5BlN,EAAMkN,IAAOlN,EAAMkN,GACnBnN,EAAMmN,IAAOnN,EAAMmN,IAE1B,IAEI,CAAClN,EAAKD,EAAI,EA2CjB+P,KAzCW,CAACpP,EAAUqP,KACtB,IAAIC,EAAQ,EACRR,EAAM,EAOV,GANAO,EAASjS,SAAQ0C,IACf,IAAI0M,EAAQ1M,EAAIK,SAASH,GACZ,MAATwM,IAAkBA,GAASA,IAAUA,MACrC8C,EAAOR,GAAOtC,EACjB,IAEC8C,EAAO,OAAOR,EAAMQ,CACjB,EAgCPC,OA9Ba,CAACvP,EAAUqP,KACxB,IAAKA,EAAS7R,OACZ,OAEF,MAAMgS,EAASH,EAASjM,KAAItD,GAAOA,EAAIK,SAASH,KAChD,IAAKvD,EAAc+S,GACjB,OAEF,GAAsB,IAAlBA,EAAOhS,OACT,OAAOgS,EAAO,GAEhB,MAAMC,EAAM9Q,KAAK+Q,MAAMF,EAAOhS,OAAS,GACjCmS,EAAOH,EAAOI,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IACvC,OAAON,EAAOhS,OAAS,GAAM,EAAImS,EAAKF,IAAQE,EAAKF,EAAM,GAAKE,EAAKF,IAAQ,CAAC,EAkB5EM,OAhBa,CAAC/P,EAAUqP,IACjB3S,MAAMsT,KAAK,IAAIC,IAAIZ,EAASjM,KAAI7G,GAAKA,EAAE4D,SAASH,MAAYwP,UAgBnEU,YAdkB,CAAClQ,EAAUqP,IACtB,IAAIY,IAAIZ,EAASjM,KAAI7G,GAAKA,EAAE4D,SAASH,MAAYmQ,KAcxDb,MAZY,CAACc,EAAWf,IACjBA,EAAS7R,QAgBZ6S,EAAiB,CACrB1E,oBAAqB,KACZ,CACL2E,eAAgBC,IACd,IAAIC,EAAWC,EACf,OAA+I,OAAvID,EAAoD,OAAvCC,EAAkBF,EAAMpQ,aAAmD,MAA5BsQ,EAAgB5G,cAAmB,EAAS4G,EAAgB5G,YAAsB2G,EAAY,IAAI,EAExKE,cAAe,SAGnB7E,gBAAiBC,IACR,CACL6E,SAAU,MACP7E,IAGPE,kBAAmBnM,IACV,CACL+Q,iBAAkB3U,EAAiB,WAAY4D,GAC/CgR,kBAAmB,YAGvBlQ,aAAc,CAACZ,EAAQF,KACrBE,EAAO+Q,eAAiB,KACtBjR,EAAMkR,aAAY1U,GAEL,MAAPA,GAAeA,EAAIqF,SAAS3B,EAAOG,IAC9B7D,EAAI8H,QAAO5H,GAAKA,IAAMwD,EAAOG,KAE/B,IAAY,MAAP7D,EAAcA,EAAM,GAAK0D,EAAOG,KAC5C,EAEJH,EAAOiR,YAAc,KACnB,IAAInE,EAAuBC,EAC3B,OAAqE,OAA5DD,EAAwB9M,EAAOa,UAAUqQ,iBAA0BpE,KAA4F,OAAzDC,EAAwBjN,EAAMS,QAAQ2Q,iBAA0BnE,OAAoC/M,EAAOqB,cAAgBrB,EAAOa,UAAUsQ,iBAAiB,EAE9QnR,EAAOoR,aAAe,KACpB,IAAIC,EACJ,OAA8D,OAAtDA,EAAwBvR,EAAM6D,WAAWiN,eAAoB,EAASS,EAAsB1P,SAAS3B,EAAOG,GAAG,EAEzHH,EAAOsR,gBAAkB,KACvB,IAAIC,EACJ,OAA+D,OAAvDA,EAAyBzR,EAAM6D,WAAWiN,eAAoB,EAASW,EAAuBC,QAAQxR,EAAOG,GAAG,EAE1HH,EAAOyR,yBAA2B,KAChC,MAAMC,EAAW1R,EAAOiR,cACxB,MAAO,KACAS,GACL1R,EAAO+Q,gBAAgB,CACxB,EAEH/Q,EAAO2R,qBAAuB,KAC5B,MAAMrF,EAAWxM,EAAMyM,kBAAkBC,SAAS,GAC5CC,EAAoB,MAAZH,OAAmB,EAASA,EAASlM,SAASJ,EAAOG,IACnE,MAAqB,iBAAVsM,EACFqC,EAAeC,IAEsB,kBAA1C6C,OAAOtQ,UAAUwI,SAAS+H,KAAKpF,GAC1BqC,EAAeM,YADxB,CAEC,EAEHpP,EAAO8R,iBAAmB,KACxB,IAAIC,EAAuBC,EAC3B,IAAKhS,EACH,MAAM,IAAI+B,MAEZ,OAAOxF,EAAWyD,EAAOa,UAAU8P,eAAiB3Q,EAAOa,UAAU8P,cAAmD,SAAnC3Q,EAAOa,UAAU8P,cAA2B3Q,EAAO2R,uBAAwL,OAA9JI,EAAmF,OAA1DC,EAAyBlS,EAAMS,QAAQuO,qBAA0B,EAASkD,EAAuBhS,EAAOa,UAAU8P,gBAA0BoB,EAAwBjD,EAAe9O,EAAOa,UAAU8P,cAAc,CAC9Y,EAEHpN,YAAazD,IACXA,EAAMkR,YAAchV,GAA6C,MAAlC8D,EAAMS,QAAQsQ,sBAA2B,EAAS/Q,EAAMS,QAAQsQ,iBAAiB7U,GAChH8D,EAAMmS,cAAgB1D,IACpB,IAAI2D,EAAuBzD,EAC3B3O,EAAMkR,YAAYzC,EAAe,GAA6H,OAAvH2D,EAAsE,OAA7CzD,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoBmC,UAAoBsB,EAAwB,GAAG,EAElMpS,EAAMqS,sBAAwB,IAAMrS,EAAM6O,sBAC1C7O,EAAMsS,mBAAqB,MACpBtS,EAAMuS,qBAAuBvS,EAAMS,QAAQ6R,qBAC9CtS,EAAMuS,oBAAsBvS,EAAMS,QAAQ6R,mBAAmBtS,IAE3DA,EAAMS,QAAQ+R,iBAAmBxS,EAAMuS,oBAClCvS,EAAMqS,wBAERrS,EAAMuS,sBACd,EAEH7K,UAAW,CAACzH,EAAKD,KACfC,EAAIqR,aAAe,MAAQrR,EAAIwS,iBAC/BxS,EAAIoR,iBAAmBlR,IACrB,GAAIF,EAAIyS,qBAAqBzK,eAAe9H,GAC1C,OAAOF,EAAIyS,qBAAqBvS,GAElC,MAAMD,EAASF,EAAMkI,UAAU/H,GAC/B,OAAgB,MAAVD,GAAkBA,EAAOa,UAAUsQ,kBAGzCpR,EAAIyS,qBAAqBvS,GAAYD,EAAOa,UAAUsQ,iBAAiBpR,EAAI0H,UACpE1H,EAAIyS,qBAAqBvS,IAHvBF,EAAIK,SAASH,EAGmB,EAE3CF,EAAIyS,qBAAuB,EAAE,EAE/B3S,WAAY,CAACK,EAAMF,EAAQD,EAAKD,KAC9BI,EAAKkR,aAAe,IAAMpR,EAAOoR,gBAAkBpR,EAAOG,KAAOJ,EAAIwS,iBACrErS,EAAKuS,iBAAmB,KAAOvS,EAAKkR,gBAAkBpR,EAAOoR,eAC7DlR,EAAKwS,gBAAkB,KACrB,IAAIC,EACJ,OAAQzS,EAAKkR,iBAAmBlR,EAAKuS,sBAAyD,OAA/BE,EAAe5S,EAAI4H,WAAoBgL,EAAalV,OAAO,CAC3H,GAGL,SAAS6E,EAAaE,EAAaoO,EAAUE,GAC3C,GAAkB,MAAZF,IAAoBA,EAASnT,SAAYqT,EAC7C,OAAOtO,EAET,MAAMoQ,EAAqBpQ,EAAY4B,QAAOyO,IAAQjC,EAASjP,SAASkR,EAAI1S,MAC5E,GAA0B,WAAtB2Q,EACF,OAAO8B,EAGT,MAAO,IADiBhC,EAASvN,KAAIyP,GAAKtQ,EAAY2B,MAAK0O,GAAOA,EAAI1S,KAAO2S,MAAI1O,OAAOC,YACzDuO,EACjC,CAIK,MAACG,EAAiB,CACrBjH,gBAAiBC,IACR,CACLiH,YAAa,MACVjH,IAGPE,kBAAmBnM,IACV,CACLmT,oBAAqB/W,EAAiB,cAAe4D,KAGzDc,aAAc,CAACZ,EAAQF,KACrBE,EAAOkT,SAAWxV,GAAKyV,GAAY,CAACC,EAAuBtT,EAAOqT,MAAYnR,GAAWA,EAAQ0L,WAAUlR,GAAKA,EAAE2D,KAAOH,EAAOG,MAAKX,EAAeM,EAAMS,QAAS,iBACnKP,EAAOqT,iBAAmBF,IACxB,IAAIG,EAEJ,OAAoC,OAA3BA,EADOF,EAAuBtT,EAAOqT,GACjB,SAAc,EAASG,EAAUnT,MAAQH,EAAOG,EAAE,EAEjFH,EAAOuT,gBAAkBJ,IACvB,IAAIK,EACJ,MAAMxR,EAAUoR,EAAuBtT,EAAOqT,GAC9C,OAAoD,OAA3CK,EAAWxR,EAAQA,EAAQvE,OAAS,SAAc,EAAS+V,EAASrT,MAAQH,EAAOG,EAAE,CAC/F,EAEHoD,YAAazD,IACXA,EAAM2T,eAAiBzX,GAAgD,MAArC8D,EAAMS,QAAQ0S,yBAA8B,EAASnT,EAAMS,QAAQ0S,oBAAoBjX,GACzH8D,EAAM4T,iBAAmBnF,IACvB,IAAIC,EACJ1O,EAAM2T,eAAelF,EAAe,GAAiE,OAA3DC,EAAwB1O,EAAM4O,aAAasE,aAAuBxE,EAAwB,GAAG,EAEzI1O,EAAMuC,mBAAqB3E,GAAK,IAAM,CAACoC,EAAM6D,WAAWqP,YAAalT,EAAM6D,WAAWiN,SAAU9Q,EAAMS,QAAQuQ,qBAAoB,CAACkC,EAAapC,EAAUE,IAAsB9O,IAG9K,IAAI2R,EAAiB,GAGrB,GAAqB,MAAfX,GAAuBA,EAAYvV,OAElC,CACL,MAAMmW,EAAkB,IAAIZ,GAGtBa,EAAc,IAAI7R,GAKxB,KAAO6R,EAAYpW,QAAUmW,EAAgBnW,QAAQ,CACnD,MAAMqW,EAAiBF,EAAgBG,QACjCC,EAAaH,EAAYnG,WAAUlR,GAAKA,EAAE2D,KAAO2T,IACnDE,GAAc,GAChBL,EAAepW,KAAKsW,EAAYI,OAAOD,EAAY,GAAG,GAEzD,CAGDL,EAAiB,IAAIA,KAAmBE,EACzC,MApBCF,EAAiB3R,EAqBnB,OAAOM,EAAaqR,EAAgB/C,EAAUE,EAAkB,GAC/DtR,EAAeM,EAAMS,QAAS,cAAoC,GAUnE2T,EAAgB,CACpBpI,gBAAiBC,IACR,CACLnI,cAPsC,CAC1CC,KAAM,GACNC,MAAO,OAMAiI,IAGPE,kBAAmBnM,IACV,CACLqU,sBAAuBjY,EAAiB,gBAAiB4D,KAG7Dc,aAAc,CAACZ,EAAQF,KACrBE,EAAOoU,IAAMjB,IACX,MAAMkB,EAAYrU,EAAOoC,iBAAiBiB,KAAI7G,GAAKA,EAAE2D,KAAIiE,OAAOC,SAChEvE,EAAMwU,kBAAiBhY,IACrB,IAAIiY,EAAYC,EAEVC,EAAWC,EAOXC,EAAYC,EARlB,MAAiB,UAAbzB,EAEK,CACLtP,MAAwD,OAAhD4Q,EAAmB,MAAPnY,OAAc,EAASA,EAAIuH,MAAgB4Q,EAAY,IAAIrQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,MACrIsH,MAAO,KAAwD,OAAlD4Q,EAAoB,MAAPpY,OAAc,EAASA,EAAIwH,OAAiB4Q,EAAa,IAAItQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,SAAS6X,IAGzI,SAAblB,EAEK,CACLtP,KAAM,KAAuD,OAAjD8Q,EAAoB,MAAPrY,OAAc,EAASA,EAAIuH,MAAgB8Q,EAAa,IAAIvQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,SAAS6X,GACpJvQ,OAA4D,OAAnD8Q,EAAqB,MAAPtY,OAAc,EAASA,EAAIwH,OAAiB8Q,EAAc,IAAIxQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,OAGxI,CACLqH,MAAyD,OAAjD0Q,EAAoB,MAAPjY,OAAc,EAASA,EAAIuH,MAAgB0Q,EAAa,IAAInQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,MACvIsH,OAA4D,OAAnD0Q,EAAqB,MAAPlY,OAAc,EAASA,EAAIwH,OAAiB0Q,EAAc,IAAIpQ,QAAO5H,KAAoB,MAAb6X,GAAqBA,EAAU1S,SAASnF,MAC5I,GACD,EAEJwD,EAAO6U,UAAY,IACG7U,EAAOoC,iBACR9D,MAAK9B,IACtB,IAAIsY,EAAuB9T,EAAM+L,EACjC,OAA+D,OAAtD+H,EAAwBtY,EAAEqE,UAAUkU,gBAAyBD,KAAwK,OAArI9T,EAAsE,OAA9D+L,EAAwBjN,EAAMS,QAAQyU,qBAA+BjI,EAAwBjN,EAAMS,QAAQwU,gBAAyB/T,EAAY,IAGrQhB,EAAOiV,YAAc,KACnB,MAAMC,EAAgBlV,EAAOoC,iBAAiBiB,KAAI7G,GAAKA,EAAE2D,MACnD0D,KACJA,EAAIC,MACJA,GACEhE,EAAM6D,WAAWC,cACfuR,EAASD,EAAc5W,MAAK9B,GAAa,MAARqH,OAAe,EAASA,EAAKlC,SAASnF,KACvE4Y,EAAUF,EAAc5W,MAAK9B,GAAc,MAATsH,OAAgB,EAASA,EAAMnC,SAASnF,KAChF,OAAO2Y,EAAS,SAASC,GAAU,OAAe,EAEpDpV,EAAOqV,eAAiB,KACtB,IAAI9H,EAAuBC,EAC3B,MAAM2F,EAAWnT,EAAOiV,cACxB,OAAO9B,EAAsO,OAA1N5F,EAAqF,OAA5DC,EAAyB1N,EAAM6D,WAAWC,gBAAyF,OAA9D4J,EAAyBA,EAAuB2F,SAAqB,EAAS3F,EAAuBgE,QAAQxR,EAAOG,KAAeoN,GAAyB,EAAI,CAAC,CACnR,EAEH/F,UAAW,CAACzH,EAAKD,KACfC,EAAIuV,sBAAwB5X,GAAK,IAAM,CAACqC,EAAIwV,sBAAuBzV,EAAM6D,WAAWC,cAAcC,KAAM/D,EAAM6D,WAAWC,cAAcE,SAAQ,CAAC+E,EAAUhF,EAAMC,KAC9J,MAAM0R,EAAe,IAAa,MAAR3R,EAAeA,EAAO,MAAkB,MAATC,EAAgBA,EAAQ,IACjF,OAAO+E,EAASzE,QAAO5H,IAAMgZ,EAAa7T,SAASnF,EAAEwD,OAAOG,KAAI,GAC/DX,EAAeM,EAAMS,QAAS,cACjCR,EAAI0V,oBAAsB/X,GAAK,IAAM,CAACqC,EAAIwV,sBAAuBzV,EAAM6D,WAAWC,cAAcC,QAAO,CAACgF,EAAUhF,KACzF,MAARA,EAAeA,EAAO,IAAIR,KAAIpD,GAAY4I,EAAS1E,MAAKjE,GAAQA,EAAKF,OAAOG,KAAOF,MAAWmE,OAAOC,SAAShB,KAAI7G,IAAM,IAClIA,EACH2W,SAAU,YAGX3T,EAAeM,EAAMS,QAAS,cACjCR,EAAI2V,qBAAuBhY,GAAK,IAAM,CAACqC,EAAIwV,sBAAuBzV,EAAM6D,WAAWC,cAAcE,SAAQ,CAAC+E,EAAU/E,KAC1F,MAATA,EAAgBA,EAAQ,IAAIT,KAAIpD,GAAY4I,EAAS1E,MAAKjE,GAAQA,EAAKF,OAAOG,KAAOF,MAAWmE,OAAOC,SAAShB,KAAI7G,IAAM,IACpIA,EACH2W,SAAU,aAGX3T,EAAeM,EAAMS,QAAS,aAAqC,EAExEgD,YAAazD,IACXA,EAAMwU,iBAAmBtY,GAAkD,MAAvC8D,EAAMS,QAAQ4T,2BAAgC,EAASrU,EAAMS,QAAQ4T,sBAAsBnY,GAC/H8D,EAAM6V,mBAAqBpH,IACzB,IAAIC,EAAuBC,EAC3B,OAAO3O,EAAMwU,iBAAiB/F,EAxFQ,CAC1C1K,KAAM,GACNC,MAAO,IAsFwM,OAA5H0K,EAAsE,OAA7CC,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoB7K,eAAyB4K,EAxF5K,CAC1C3K,KAAM,GACNC,MAAO,IAsFsQ,EAE3QhE,EAAM8V,uBAAyBzC,IAC7B,IAAI0C,EACJ,MAAMC,EAAehW,EAAM6D,WAAWC,cAEpC,IAAImS,EAAoBC,EAD1B,OAAK7C,EAIE9O,QAA4D,OAAnDwR,EAAwBC,EAAa3C,SAAqB,EAAS0C,EAAsBpY,QAFhG4G,SAAqD,OAA3C0R,EAAqBD,EAAajS,WAAgB,EAASkS,EAAmBtY,UAA0D,OAA7CuY,EAAsBF,EAAahS,YAAiB,EAASkS,EAAoBvY,QAE/E,EAElHqC,EAAMmW,mBAAqBvY,GAAK,IAAM,CAACoC,EAAM6I,oBAAqB7I,EAAM6D,WAAWC,cAAcC,QAAO,CAACE,EAAYF,KACnG,MAARA,EAAeA,EAAO,IAAIR,KAAIpD,GAAY8D,EAAWI,MAAKnE,GAAUA,EAAOG,KAAOF,MAAWmE,OAAOC,UAC3G7E,EAAeM,EAAMS,QAAS,iBACjCT,EAAMoW,oBAAsBxY,GAAK,IAAM,CAACoC,EAAM6I,oBAAqB7I,EAAM6D,WAAWC,cAAcE,SAAQ,CAACC,EAAYD,KACpG,MAATA,EAAgBA,EAAQ,IAAIT,KAAIpD,GAAY8D,EAAWI,MAAKnE,GAAUA,EAAOG,KAAOF,MAAWmE,OAAOC,UAC7G7E,EAAeM,EAAMS,QAAS,iBACjCT,EAAMqW,qBAAuBzY,GAAK,IAAM,CAACoC,EAAM6I,oBAAqB7I,EAAM6D,WAAWC,cAAcC,KAAM/D,EAAM6D,WAAWC,cAAcE,SAAQ,CAACC,EAAYF,EAAMC,KACjK,MAAM0R,EAAe,IAAa,MAAR3R,EAAeA,EAAO,MAAkB,MAATC,EAAgBA,EAAQ,IACjF,OAAOC,EAAWK,QAAO5H,IAAMgZ,EAAa7T,SAASnF,EAAE2D,KAAI,GAC1DX,EAAeM,EAAMS,QAAS,gBAAwC,GAYxE,MAAC6V,EAAsB,CAC1BhG,KAAM,IACNiG,QAAS,GACTC,QAAShL,OAAOiL,kBAUZC,EAAe,CACnB5K,oBAAqB,IACZwK,EAETtK,gBAAiBC,IACR,CACL0K,aAAc,CAAE,EAChBC,iBAfyC,CAC7CC,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,OAUZjL,IAGPE,kBAAmBnM,IACV,CACLmX,iBAAkB,QAClBC,sBAAuB,MACvBC,qBAAsBjb,EAAiB,eAAgB4D,GACvDsX,yBAA0Blb,EAAiB,mBAAoB4D,KAGnEc,aAAc,CAACZ,EAAQF,KACrBE,EAAOqX,QAAU,KACf,IAAIC,EAAuBtW,EAAMuW,EACjC,MAAMC,EAAa1X,EAAM6D,WAAW8S,aAAazW,EAAOG,IACxD,OAAOvB,KAAKW,IAAIX,KAAKU,IAA0D,OAArDgY,EAAwBtX,EAAOa,UAAUwV,SAAmBiB,EAAwBlB,EAAoBC,QAA6E,OAAnErV,EAAqB,MAAdwW,EAAqBA,EAAaxX,EAAOa,UAAUuP,MAAgBpP,EAAOoV,EAAoBhG,MAA6D,OAArDmH,EAAwBvX,EAAOa,UAAUyV,SAAmBiB,EAAwBnB,EAAoBE,QAAQ,EAE3WtW,EAAOyX,SAAW/Z,GAAKyV,GAAY,CAACA,EAAUC,EAAuBtT,EAAOqT,GAAWrT,EAAM6D,WAAW8S,gBAAe,CAACtD,EAAUnR,IAAYA,EAAQ0V,MAAM,EAAG1X,EAAOkT,SAASC,IAAWrK,QAAO,CAACiG,EAAK/O,IAAW+O,EAAM/O,EAAOqX,WAAW,IAAI7X,EAAeM,EAAMS,QAAS,iBAC5QP,EAAO2X,SAAWja,GAAKyV,GAAY,CAACA,EAAUC,EAAuBtT,EAAOqT,GAAWrT,EAAM6D,WAAW8S,gBAAe,CAACtD,EAAUnR,IAAYA,EAAQ0V,MAAM1X,EAAOkT,SAASC,GAAY,GAAGrK,QAAO,CAACiG,EAAK/O,IAAW+O,EAAM/O,EAAOqX,WAAW,IAAI7X,EAAeM,EAAMS,QAAS,iBAC7QP,EAAO4X,UAAY,KACjB9X,EAAM+X,iBAAgBC,IACpB,IACE,CAAC9X,EAAOG,IAAK4X,KACVC,GACDF,EACJ,OAAOE,CAAI,GACX,EAEJhY,EAAOiY,aAAe,KACpB,IAAInL,EAAuBC,EAC3B,OAAqE,OAA5DD,EAAwB9M,EAAOa,UAAUqX,iBAA0BpL,KAAkG,OAA/DC,EAAwBjN,EAAMS,QAAQ4X,uBAAgCpL,EAA6B,EAEpN/M,EAAOoY,cAAgB,IACdtY,EAAM6D,WAAW+S,iBAAiBK,mBAAqB/W,EAAOG,EACtE,EAEHsC,aAAc,CAACf,EAAQ5B,KACrB4B,EAAO2V,QAAU,KACf,IAAItI,EAAM,EACV,MAAM5R,EAAUuE,IAIZ,IAAI2W,EAHF3W,EAAOmB,WAAWpF,OACpBiE,EAAOmB,WAAWxF,QAAQF,GAG1B4R,GAA4D,OAApDsJ,EAAwB3W,EAAO1B,OAAOqX,WAAqBgB,EAAwB,CAC5F,EAGH,OADAlb,EAAQuE,GACDqN,CAAG,EAEZrN,EAAO+V,SAAW,KAChB,GAAI/V,EAAOlD,MAAQ,EAAG,CACpB,MAAM8Z,EAAoB5W,EAAOsB,YAAYoC,QAAQ1D,EAAOlD,MAAQ,GACpE,OAAO8Z,EAAkBb,WAAaa,EAAkBjB,SACzD,CACD,OAAO,CAAC,EAEV3V,EAAO6W,iBAAmBC,IACxB,MAAMxY,EAASF,EAAMkI,UAAUtG,EAAO1B,OAAOG,IACvCsY,EAAsB,MAAVzY,OAAiB,EAASA,EAAOiY,eACnD,OAAOS,IACL,IAAK1Y,IAAWyY,EACd,OAGF,GADa,MAAbC,EAAEC,SAAmBD,EAAEC,UACnBC,EAAkBF,IAEhBA,EAAEG,SAAWH,EAAEG,QAAQpb,OAAS,EAClC,OAGJ,MAAMmZ,EAAYlV,EAAO2V,UACnBL,EAAoBtV,EAASA,EAAOuB,iBAAiBI,KAAI7G,GAAK,CAACA,EAAEwD,OAAOG,GAAI3D,EAAEwD,OAAOqX,aAAc,CAAC,CAACrX,EAAOG,GAAIH,EAAOqX,YACvHyB,EAAUF,EAAkBF,GAAK9Z,KAAKC,MAAM6Z,EAAEG,QAAQ,GAAGC,SAAWJ,EAAEI,QACtEC,EAAkB,CAAA,EAClBC,EAAe,CAACC,EAAWC,KACL,iBAAfA,IAGXpZ,EAAMqZ,qBAAoB7c,IACxB,IAAI8c,EAAkBC,EACtB,MAAMC,EAAyD,QAAxCxZ,EAAMS,QAAQ2W,uBAAmC,EAAI,EACtEL,GAAeqC,GAA6E,OAA9DE,EAA0B,MAAP9c,OAAc,EAASA,EAAIqa,aAAuByC,EAAmB,IAAME,EAC5HxC,EAAkBlY,KAAKU,IAAIuX,GAA0E,OAA1DwC,EAAwB,MAAP/c,OAAc,EAASA,EAAIsa,WAAqByC,EAAiB,IAAK,SAKxI,OAJA/c,EAAI0a,kBAAkB3Z,SAAQkc,IAC5B,IAAKtZ,EAAUuZ,GAAcD,EAC7BR,EAAgB9Y,GAAYrB,KAAKC,MAA+D,IAAzDD,KAAKU,IAAIka,EAAaA,EAAa1C,EAAiB,IAAY,GAAG,IAErG,IACFxa,EACHua,cACAC,kBACD,IAEoC,aAAnChX,EAAMS,QAAQ0W,kBAAiD,QAAdgC,GACnDnZ,EAAM+X,iBAAgBvb,IAAQ,IACzBA,KACAyc,MAEN,EAEGU,EAASP,GAAcF,EAAa,OAAQE,GAC5CQ,EAAQR,IACZF,EAAa,MAAOE,GACpBpZ,EAAMqZ,qBAAoB7c,IAAQ,IAC7BA,EACHya,kBAAkB,EAClBJ,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBE,kBAAmB,MAClB,EAEC2C,EAAuCnB,IA9IV,oBAAboB,SAA2BA,SAAW,MA+I5D,MAAMC,EAAc,CAClBC,YAAapB,GAAKe,EAAOf,EAAEI,SAC3BiB,UAAWrB,IACU,MAAnBiB,GAA2BA,EAAgBK,oBAAoB,YAAaH,EAAYC,aACrE,MAAnBH,GAA2BA,EAAgBK,oBAAoB,UAAWH,EAAYE,WACtFL,EAAMhB,EAAEI,QAAQ,GAGdmB,EAAc,CAClBH,YAAapB,IACPA,EAAEwB,aACJxB,EAAEyB,iBACFzB,EAAE0B,mBAEJX,EAAOf,EAAEG,QAAQ,GAAGC,UACb,GAETiB,UAAWrB,IACT,IAAI2B,EACe,MAAnBV,GAA2BA,EAAgBK,oBAAoB,YAAaC,EAAYH,aACrE,MAAnBH,GAA2BA,EAAgBK,oBAAoB,WAAYC,EAAYF,WACnFrB,EAAEwB,aACJxB,EAAEyB,iBACFzB,EAAE0B,mBAEJV,EAAsC,OAA/BW,EAAc3B,EAAEG,QAAQ,SAAc,EAASwB,EAAYvB,QAAQ,GAGxEwB,IAAqBC,KAA0B,CACnDC,SAAS,GAEP5B,EAAkBF,IACD,MAAnBiB,GAA2BA,EAAgBc,iBAAiB,YAAaR,EAAYH,YAAaQ,GAC/E,MAAnBX,GAA2BA,EAAgBc,iBAAiB,WAAYR,EAAYF,UAAWO,KAE5E,MAAnBX,GAA2BA,EAAgBc,iBAAiB,YAAaZ,EAAYC,YAAaQ,GAC/E,MAAnBX,GAA2BA,EAAgBc,iBAAiB,UAAWZ,EAAYE,UAAWO,IAEhGxa,EAAMqZ,qBAAoB7c,IAAQ,IAC7BA,EACHqa,YAAamC,EACblC,YACAC,YAAa,EACbC,gBAAiB,EACjBE,oBACAD,iBAAkB/W,EAAOG,MACxB,CACJ,CACF,EAEHoD,YAAazD,IACXA,EAAM+X,gBAAkB7b,GAAiD,MAAtC8D,EAAMS,QAAQ4W,0BAA+B,EAASrX,EAAMS,QAAQ4W,qBAAqBnb,GAC5H8D,EAAMqZ,oBAAsBnd,GAAqD,MAA1C8D,EAAMS,QAAQ6W,8BAAmC,EAAStX,EAAMS,QAAQ6W,yBAAyBpb,GACxI8D,EAAM4a,kBAAoBnM,IACxB,IAAIC,EACJ1O,EAAM+X,gBAAgBtJ,EAAe,CAAA,EAAkE,OAA5DC,EAAwB1O,EAAM4O,aAAa+H,cAAwBjI,EAAwB,CAAE,EAAC,EAE3I1O,EAAM6a,oBAAsBpM,IAC1B,IAAIqM,EACJ9a,EAAMqZ,oBAAoB5K,EA9Le,CAC7CoI,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IAwLgI,OAAjE4D,EAAyB9a,EAAM4O,aAAagI,kBAA4BkE,EA9L7G,CAC7CjE,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IAwLkM,EAEnNlX,EAAM+a,aAAe,KACnB,IAAIC,EAAuBC,EAC3B,OAEU,OAFFD,EAAiF,OAAxDC,EAAyBjb,EAAM0D,kBAAkB,SAAc,EAASuX,EAAuB3V,QAAQ0D,QAAO,CAACiG,EAAKrN,IAC5IqN,EAAMrN,EAAO2V,WACnB,IAAcyD,EAAwB,CAAC,EAE5Chb,EAAMkb,iBAAmB,KACvB,IAAIC,EAAuBC,EAC3B,OAEU,OAFFD,EAAqF,OAA5DC,EAAyBpb,EAAM2E,sBAAsB,SAAc,EAASyW,EAAuB9V,QAAQ0D,QAAO,CAACiG,EAAKrN,IAChJqN,EAAMrN,EAAO2V,WACnB,IAAc4D,EAAwB,CAAC,EAE5Cnb,EAAMqb,mBAAqB,KACzB,IAAIC,EAAuBC,EAC3B,OAEU,OAFFD,EAAuF,OAA9DC,EAAyBvb,EAAM0E,wBAAwB,SAAc,EAAS6W,EAAuBjW,QAAQ0D,QAAO,CAACiG,EAAKrN,IAClJqN,EAAMrN,EAAO2V,WACnB,IAAc+D,EAAwB,CAAC,EAE5Ctb,EAAMwb,kBAAoB,KACxB,IAAIC,EAAuBC,EAC3B,OAEU,OAFFD,EAAsF,OAA7DC,EAAyB1b,EAAM6E,uBAAuB,SAAc,EAAS6W,EAAuBpW,QAAQ0D,QAAO,CAACiG,EAAKrN,IACjJqN,EAAMrN,EAAO2V,WACnB,IAAckE,EAAwB,CAAC,CAC3C,GAGL,IAAIE,EAAmB,KACvB,SAASlB,IACP,GAAgC,kBAArBkB,EAAgC,OAAOA,EAClD,IAAIC,GAAY,EAChB,IACE,MAAMnb,EAAU,CACd,WAAIia,GAEF,OADAkB,GAAY,GACL,CACR,GAEGC,EAAO,OACbC,OAAOnB,iBAAiB,OAAQkB,EAAMpb,GACtCqb,OAAO5B,oBAAoB,OAAQ2B,EACpC,CAAC,MAAOE,GACPH,GAAY,CACb,CAED,OADAD,EAAmBC,EACZD,CACT,CACA,SAAS7C,EAAkBF,GACzB,MAAkB,eAAXA,EAAEoD,IACX,CAIK,MAACC,EAAmB,CACvBjQ,gBAAiBC,IACR,CACLiQ,iBAAkB,CAAE,KACjBjQ,IAGPE,kBAAmBnM,IACV,CACLmc,yBAA0B/f,EAAiB,mBAAoB4D,KAGnEc,aAAc,CAACZ,EAAQF,KACrBE,EAAOkc,iBAAmBzP,IACpBzM,EAAOmc,cACTrc,EAAMsc,qBAAoB9f,IAAQ,IAC7BA,EACH,CAAC0D,EAAOG,IAAc,MAATsM,EAAgBA,GAASzM,EAAO4G,kBAEhD,EAEH5G,EAAO4G,aAAe,KACpB,IAAI5F,EAAMuM,EACV,MAAM8O,EAAerc,EAAOgC,QAC5B,OAAoM,OAA5LhB,EAAOqb,EAAa5e,OAAS4e,EAAa/d,MAAKge,GAAKA,EAAE1V,iBAAiF,OAA9D2G,EAAwBzN,EAAM6D,WAAWqY,uBAA4B,EAASzO,EAAsBvN,EAAOG,MAAea,CAAW,EAExNhB,EAAOmc,WAAa,KAClB,IAAIrP,EAAuBC,EAC3B,OAAmE,OAA1DD,EAAwB9M,EAAOa,UAAU0b,eAAwBzP,KAA0F,OAAvDC,EAAwBjN,EAAMS,QAAQgc,eAAwBxP,EAA6B,EAE1M/M,EAAOwc,2BAA6B,IAC3B9D,IACsB,MAA3B1Y,EAAOkc,kBAA4Blc,EAAOkc,iBAAiBxD,EAAE+D,OAAOC,QAAQ,CAE/E,EAEHlV,UAAW,CAACzH,EAAKD,KACfC,EAAIwV,oBAAsB7X,GAAK,IAAM,CAACqC,EAAI2I,cAAe5I,EAAM6D,WAAWqY,oBAAmBW,GACpFA,EAAMvY,QAAOlE,GAAQA,EAAKF,OAAO4G,kBACvCpH,EAAeM,EAAMS,QAAS,cACjCR,EAAI6c,gBAAkBlf,GAAK,IAAM,CAACqC,EAAI0V,sBAAuB1V,EAAIuV,wBAAyBvV,EAAI2V,0BAAyB,CAAC7R,EAAMkC,EAAQjC,IAAU,IAAID,KAASkC,KAAWjC,IAAQtE,EAAeM,EAAMS,QAAS,aAAgC,EAEhPgD,YAAazD,IACX,MAAM+c,EAA2B,CAAC1gB,EAAK2gB,IAC9Bpf,GAAK,IAAM,CAACof,IAAcA,IAAa1Y,QAAO5H,GAAKA,EAAEoK,iBAAgBvD,KAAI7G,GAAKA,EAAE2D,KAAI4G,KAAK,QAAO/E,GAC9FA,EAAQoC,QAAO5H,GAAuB,MAAlBA,EAAEoK,kBAAuB,EAASpK,EAAEoK,kBAC9DpH,EAAeM,EAAMS,QAAS,iBAEnCT,EAAMid,sBAAwBF,EAAyB,GAAyB,IAAM/c,EAAMkd,sBAC5Fld,EAAM4D,sBAAwBmZ,EAAyB,GAAyB,IAAM/c,EAAM6I,sBAC5F7I,EAAMmd,0BAA4BJ,EAAyB,GAA6B,IAAM/c,EAAMmW,uBACpGnW,EAAMod,2BAA6BL,EAAyB,GAA8B,IAAM/c,EAAMoW,wBACtGpW,EAAMqd,4BAA8BN,EAAyB,GAA+B,IAAM/c,EAAMqW,yBACxGrW,EAAMsc,oBAAsBpgB,GAAqD,MAA1C8D,EAAMS,QAAQ0b,8BAAmC,EAASnc,EAAMS,QAAQ0b,yBAAyBjgB,GACxI8D,EAAMsd,sBAAwB7O,IAC5B,IAAIC,EACJ1O,EAAMsc,oBAAoB7N,EAAe,CAAA,EAAsE,OAAhEC,EAAwB1O,EAAM4O,aAAasN,kBAA4BxN,EAAwB,CAAE,EAAC,EAEnJ1O,EAAMud,wBAA0B5Q,IAC9B,IAAI6Q,EACJ7Q,EAA4B,OAAnB6Q,EAAS7Q,GAAiB6Q,GAAUxd,EAAMyd,yBACnDzd,EAAMsc,oBAAoBtc,EAAM6I,oBAAoBG,QAAO,CAAC0U,EAAKxd,KAAY,IACxEwd,EACH,CAACxd,EAAOG,IAAMsM,KAA+B,MAArBzM,EAAOmc,YAAsBnc,EAAOmc,iBAC1D,CAAE,GAAE,EAEVrc,EAAMyd,uBAAyB,KAAOzd,EAAM6I,oBAAoBrK,MAAK0B,KAAmC,MAAvBA,EAAO4G,cAAwB5G,EAAO4G,kBACvH9G,EAAM2d,wBAA0B,IAAM3d,EAAM6I,oBAAoBrK,MAAK0B,GAAiC,MAAvBA,EAAO4G,kBAAuB,EAAS5G,EAAO4G,iBAC7H9G,EAAM4d,qCAAuC,IACpChF,IACL,IAAIiF,EACJ7d,EAAMud,wBAAgD,OAAvBM,EAAUjF,EAAE+D,aAAkB,EAASkB,EAAQjB,QAAQ,CAEzF,GAGL,SAAStJ,EAAuBtT,EAAOqT,GACrC,OAAQA,EAAwD,WAAbA,EAAwBrT,EAAMqd,8BAA6C,SAAbhK,EAAsBrT,EAAMmd,4BAA8Bnd,EAAMod,6BAA9Jpd,EAAM4D,uBAC3B,CAIK,MAACka,EAAiB,CACrBra,YAAazD,IACXA,EAAM+d,0BAA4B/d,EAAMS,QAAQ4I,oBAAsBrJ,EAAMS,QAAQ4I,mBAAmBrJ,EAAO,cAC9GA,EAAMge,yBAA2B,IAC3Bhe,EAAMS,QAAQsO,kBAAoB/O,EAAM+d,0BACnC/d,EAAMsJ,yBAERtJ,EAAM+d,4BAEf/d,EAAMie,8BAAgCje,EAAMS,QAAQ+I,wBAA0BxJ,EAAMS,QAAQ+I,uBAAuBxJ,EAAO,cAC1HA,EAAMke,6BAA+B,IAC9Ble,EAAMie,8BAGJje,EAAMie,gCAFJ,IAAIxU,IAIfzJ,EAAMme,8BAAgCne,EAAMS,QAAQkJ,wBAA0B3J,EAAMS,QAAQkJ,uBAAuB3J,EAAO,cAC1HA,EAAMoe,6BAA+B,KACnC,GAAKpe,EAAMme,8BAGX,OAAOne,EAAMme,+BAA+B,CAC7C,GAMCE,EAAkB,CACtBrS,gBAAiBC,IACR,CACLqS,kBAAc3c,KACXsK,IAGPE,kBAAmBnM,IACV,CACLue,qBAAsBniB,EAAiB,eAAgB4D,GACvDwe,eAAgB,OAChBC,yBAA0Bve,IACxB,IAAIwe,EACJ,MAAM/R,EAAyE,OAAhE+R,EAAwB1e,EAAMyM,kBAAkBC,SAAS,KAAsG,OAAtFgS,EAAwBA,EAAsB5V,yBAAyB5I,EAAOG,UAAe,EAASqe,EAAsBpe,WACpN,MAAwB,iBAAVqM,GAAuC,iBAAVA,CAAkB,IAInE7L,aAAc,CAACZ,EAAQF,KACrBE,EAAOye,mBAAqB,KAC1B,IAAI3R,EAAuBC,EAAuBC,EAAwB0R,EAC1E,OAAyE,OAAhE5R,EAAwB9M,EAAOa,UAAU8d,qBAA8B7R,KAAgG,OAA7DC,EAAwBjN,EAAMS,QAAQoe,qBAA8B5R,KAA4F,OAAzDC,EAAyBlN,EAAMS,QAAQ4M,gBAAyBH,KAAyK,OAArI0R,EAAkE,MAA1C5e,EAAMS,QAAQge,8BAAmC,EAASze,EAAMS,QAAQge,yBAAyBve,KAAmB0e,MAAmC1e,EAAOqB,UAAU,CAC/f,EAEHkC,YAAazD,IACXA,EAAM8e,sBAAwB,IACrBlT,EAAUhC,eAEnB5J,EAAM+e,kBAAoB,KACxB,IAAIlS,EAAuBC,EAC3B,MACE0R,eAAgBA,GACdxe,EAAMS,QACV,OAAOhE,EAAW+hB,GAAkBA,EAAoC,SAAnBA,EAA4Bxe,EAAM8e,wBAAoK,OAAzIjS,EAA8E,OAArDC,EAAyB9M,EAAMS,QAAQmL,gBAAqB,EAASkB,EAAuB0R,IAA2B3R,EAAwBjB,EAAU4S,EAAe,EAErTxe,EAAMgf,gBAAkB9iB,IACgB,MAAtC8D,EAAMS,QAAQ8d,sBAAgCve,EAAMS,QAAQ8d,qBAAqBriB,EAAQ,EAE3F8D,EAAMif,kBAAoBxQ,IACxBzO,EAAMgf,gBAAgBvQ,OAAe9M,EAAY3B,EAAM4O,aAAa0P,aAAa,CAClF,GAMCY,EAAe,CACnBlT,gBAAiBC,IACR,CACLkT,SAAU,CAAE,KACTlT,IAGPE,kBAAmBnM,IACV,CACLof,iBAAkBhjB,EAAiB,WAAY4D,GAC/Cqf,sBAAsB,IAG1B5b,YAAazD,IACX,IAAIsf,GAAa,EACbC,GAAS,EACbvf,EAAMwf,mBAAqB,KACzB,IAAIte,EAAMue,EACV,GAAKH,GAML,GAAuI,OAAlIpe,EAA+D,OAAvDue,EAAwBzf,EAAMS,QAAQif,cAAwBD,EAAwBzf,EAAMS,QAAQkf,mBAA6Bze,GAAQlB,EAAMS,QAAQmf,gBAAiB,CACnL,GAAIL,EAAQ,OACZA,GAAS,EACTvf,EAAM6f,QAAO,KACX7f,EAAM8f,gBACNP,GAAS,CAAK,GAEjB,OAZCvf,EAAM6f,QAAO,KACXP,GAAa,CAAI,GAWpB,EAEHtf,EAAM+f,YAAc7jB,GAA6C,MAAlC8D,EAAMS,QAAQ2e,sBAA2B,EAASpf,EAAMS,QAAQ2e,iBAAiBljB,GAChH8D,EAAMggB,sBAAwBb,KACZ,MAAZA,EAAmBA,GAAYnf,EAAMigB,wBACvCjgB,EAAM+f,aAAY,GAElB/f,EAAM+f,YAAY,CAAA,EACnB,EAEH/f,EAAM8f,cAAgBrR,IACpB,IAAIyR,EAAuBvR,EAC3B3O,EAAM+f,YAAYtR,EAAe,CAAA,EAA6H,OAAvHyR,EAAsE,OAA7CvR,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoBwQ,UAAoBe,EAAwB,CAAA,EAAG,EAElMlgB,EAAMmgB,qBAAuB,IACpBngB,EAAMogB,2BAA2B1T,SAASlO,MAAKyB,GAAOA,EAAIogB,iBAEnErgB,EAAMsgB,gCAAkC,IAC/B1H,IACQ,MAAbA,EAAEC,SAAmBD,EAAEC,UACvB7Y,EAAMggB,uBAAuB,EAGjChgB,EAAMugB,sBAAwB,KAC5B,MAAMpB,EAAWnf,EAAM6D,WAAWsb,SAClC,OAAoB,IAAbA,GAAqBrN,OAAOnC,OAAOwP,GAAU3gB,KAAK+F,QAAQ,EAEnEvE,EAAMigB,qBAAuB,KAC3B,MAAMd,EAAWnf,EAAM6D,WAAWsb,SAGlC,MAAwB,kBAAbA,GACW,IAAbA,IAEJrN,OAAO0O,KAAKrB,GAAUxhB,SAKvBqC,EAAMygB,cAAc/T,SAASlO,MAAKyB,IAAQA,EAAIygB,iBAKvC,EAEb1gB,EAAM2gB,iBAAmB,KACvB,IAAI/Z,EAAW,EAMf,QAL6C,IAA9B5G,EAAM6D,WAAWsb,SAAoBrN,OAAO0O,KAAKxgB,EAAMygB,cAAcG,UAAY9O,OAAO0O,KAAKxgB,EAAM6D,WAAWsb,WACtH5hB,SAAQ8C,IACb,MAAMwgB,EAAUxgB,EAAG0B,MAAM,KACzB6E,EAAW9H,KAAKU,IAAIoH,EAAUia,EAAQljB,OAAO,IAExCiJ,CAAQ,EAEjB5G,EAAM8gB,uBAAyB,IAAM9gB,EAAM+gB,oBAC3C/gB,EAAMghB,oBAAsB,MACrBhhB,EAAMihB,sBAAwBjhB,EAAMS,QAAQugB,sBAC/ChhB,EAAMihB,qBAAuBjhB,EAAMS,QAAQugB,oBAAoBhhB,IAE7DA,EAAMS,QAAQmf,kBAAoB5f,EAAMihB,qBACnCjhB,EAAM8gB,yBAER9gB,EAAMihB,uBACd,EAEHvZ,UAAW,CAACzH,EAAKD,KACfC,EAAIihB,eAAiB/B,IACnBnf,EAAM+f,aAAYvjB,IAChB,IAAI2kB,EACJ,MAAMC,GAAiB,IAAR5kB,KAAgC,MAAPA,IAAeA,EAAIyD,EAAII,KAC/D,IAAIghB,EAAc,CAAA,EASlB,IARY,IAAR7kB,EACFsV,OAAO0O,KAAKxgB,EAAMygB,cAAcG,UAAUrjB,SAAQ+jB,IAChDD,EAAYC,IAAS,CAAI,IAG3BD,EAAc7kB,EAEhB2iB,EAAqC,OAAzBgC,EAAYhC,GAAoBgC,GAAaC,GACpDA,GAAUjC,EACb,MAAO,IACFkC,EACH,CAACphB,EAAII,KAAK,GAGd,GAAI+gB,IAAWjC,EAAU,CACvB,MACE,CAAClf,EAAII,IAAK4X,KACPC,GACDmJ,EACJ,OAAOnJ,CACR,CACD,OAAO1b,CAAG,GACV,EAEJyD,EAAIygB,cAAgB,KAClB,IAAIa,EACJ,MAAMpC,EAAWnf,EAAM6D,WAAWsb,SAClC,SAA6H,OAAlHoC,EAA0D,MAAlCvhB,EAAMS,QAAQ+gB,sBAA2B,EAASxhB,EAAMS,QAAQ+gB,iBAAiBvhB,IAAgBshB,GAAqC,IAAbpC,IAAkC,MAAZA,OAAmB,EAASA,EAASlf,EAAII,KAAK,EAElOJ,EAAIogB,aAAe,KACjB,IAAIoB,EAAuBxU,EAAuB4F,EAClD,OAAwH,OAAhH4O,EAAyD,MAAjCzhB,EAAMS,QAAQihB,qBAA0B,EAAS1hB,EAAMS,QAAQihB,gBAAgBzhB,IAAgBwhB,GAAoF,OAA1DxU,EAAwBjN,EAAMS,QAAQkhB,kBAA2B1U,MAAoE,OAA/B4F,EAAe5S,EAAI4H,WAAoBgL,EAAalV,OAAO,EAE5TsC,EAAI2hB,wBAA0B,KAC5B,IAAIC,GAAkB,EAClBnZ,EAAazI,EACjB,KAAO4hB,GAAmBnZ,EAAWZ,UACnCY,EAAa1I,EAAMuI,OAAOG,EAAWZ,UAAU,GAC/C+Z,EAAkBnZ,EAAWgY,gBAE/B,OAAOmB,CAAe,EAExB5hB,EAAI6hB,yBAA2B,KAC7B,MAAMC,EAAY9hB,EAAIogB,eACtB,MAAO,KACA0B,GACL9hB,EAAIihB,gBAAgB,CACrB,CACF,GAYCc,EAAgB,CACpBhW,gBAAiBC,IACR,IACFA,EACHgW,WAAY,CAPhBC,UAHuB,EAIvBC,SAHsB,MAWH,MAATlW,OAAgB,EAASA,EAAMgW,cAIzC9V,kBAAmBnM,IACV,CACLoiB,mBAAoBhmB,EAAiB,aAAc4D,KAGvDyD,YAAazD,IACX,IAAIsf,GAAa,EACbC,GAAS,EACbvf,EAAMqiB,oBAAsB,KAC1B,IAAInhB,EAAMue,EACV,GAAKH,GAML,GAAwI,OAAnIpe,EAA+D,OAAvDue,EAAwBzf,EAAMS,QAAQif,cAAwBD,EAAwBzf,EAAMS,QAAQ6hB,oBAA8BphB,GAAQlB,EAAMS,QAAQ8hB,iBAAkB,CACrL,GAAIhD,EAAQ,OACZA,GAAS,EACTvf,EAAM6f,QAAO,KACX7f,EAAMwiB,iBACNjD,GAAS,CAAK,GAEjB,OAZCvf,EAAM6f,QAAO,KACXP,GAAa,CAAI,GAWpB,EAEHtf,EAAMyiB,cAAgBvmB,GAKuB,MAApC8D,EAAMS,QAAQ2hB,wBAA6B,EAASpiB,EAAMS,QAAQ2hB,oBAJrD5lB,GACHP,EAAiBC,EAASM,KAK7CwD,EAAM0iB,gBAAkBjU,IACtB,IAAIkU,EACJ3iB,EAAMyiB,cAAchU,EAhDe,CACvCyT,UAHuB,EAIvBC,SAHsB,IAiD0G,OAA1DQ,EAAwB3iB,EAAM4O,aAAaqT,YAAsBU,EAhDhG,CACvCT,UAHuB,EAIvBC,SAHsB,IAiDqK,EAEzLniB,EAAM4iB,aAAe1mB,IACnB8D,EAAMyiB,eAAcjmB,IAClB,IAAI0lB,EAAYjmB,EAAiBC,EAASM,EAAI0lB,WAC9C,MAAMW,OAAkD,IAA5B7iB,EAAMS,QAAQqiB,YAA0D,IAA7B9iB,EAAMS,QAAQqiB,UAAmBtX,OAAOiL,iBAAmBzW,EAAMS,QAAQqiB,UAAY,EAE5J,OADAZ,EAAYpjB,KAAKU,IAAI,EAAGV,KAAKW,IAAIyiB,EAAWW,IACrC,IACFrmB,EACH0lB,YACD,GACD,EAEJliB,EAAMwiB,eAAiB/T,IACrB,IAAIsU,EAAwBpU,EAC5B3O,EAAM4iB,aAAanU,EAjEA,EAiE8N,OAA3LsU,EAAuE,OAA7CpU,EAAsB3O,EAAM4O,eAAmF,OAAzDD,EAAsBA,EAAoBsT,iBAAsB,EAAStT,EAAoBuT,WAAqBa,EAjErO,EAiE+Q,EAEpS/iB,EAAMgjB,cAAgBvU,IACpB,IAAIwU,EAAwBC,EAC5BljB,EAAMmjB,YAAY1U,EApEA,GAoEgO,OAA9LwU,EAAwE,OAA9CC,EAAuBljB,EAAM4O,eAAqF,OAA3DsU,EAAuBA,EAAqBjB,iBAAsB,EAASiB,EAAqBf,UAAoBc,EApEvO,GAoEgR,EAEpSjjB,EAAMmjB,YAAcjnB,IAClB8D,EAAMyiB,eAAcjmB,IAClB,MAAM2lB,EAAWrjB,KAAKU,IAAI,EAAGvD,EAAiBC,EAASM,EAAI2lB,WACrDiB,EAAc5mB,EAAI2lB,SAAW3lB,EAAI0lB,UACjCA,EAAYpjB,KAAK+Q,MAAMuT,EAAcjB,GAC3C,MAAO,IACF3lB,EACH0lB,YACAC,WACD,GACD,EAGJniB,EAAMqjB,aAAennB,GAAW8D,EAAMyiB,eAAcjmB,IAClD,IAAI8mB,EACJ,IAAIC,EAAetnB,EAAiBC,EAA8D,OAApDonB,EAAwBtjB,EAAMS,QAAQqiB,WAAqBQ,GAAyB,GAIlI,MAH4B,iBAAjBC,IACTA,EAAezkB,KAAKU,KAAK,EAAG+jB,IAEvB,IACF/mB,EACHsmB,UAAWS,EACZ,IAEHvjB,EAAMwjB,eAAiB5lB,GAAK,IAAM,CAACoC,EAAMyjB,kBAAiBX,IACxD,IAAIY,EAAc,GAIlB,OAHIZ,GAAaA,EAAY,IAC3BY,EAAc,IAAI,IAAI7mB,MAAMimB,IAAYa,KAAK,MAAMpgB,KAAI,CAAC0U,EAAG/O,IAAMA,KAE5Dwa,CAAW,GACjBhkB,EAAeM,EAAMS,QAAS,eACjCT,EAAM4jB,mBAAqB,IAAM5jB,EAAM6D,WAAWoe,WAAWC,UAAY,EACzEliB,EAAM6jB,eAAiB,KACrB,MAAM3B,UACJA,GACEliB,EAAM6D,WAAWoe,WACfa,EAAY9iB,EAAMyjB,eACxB,OAAmB,IAAfX,GAGc,IAAdA,GAGGZ,EAAYY,EAAY,CAAC,EAElC9iB,EAAM8jB,aAAe,IACZ9jB,EAAM4iB,cAAapmB,GAAOA,EAAM,IAEzCwD,EAAM+jB,SAAW,IACR/jB,EAAM4iB,cAAapmB,GACjBA,EAAM,IAGjBwD,EAAMgkB,UAAY,IACThkB,EAAM4iB,aAAa,GAE5B5iB,EAAMikB,SAAW,IACRjkB,EAAM4iB,aAAa5iB,EAAMyjB,eAAiB,GAEnDzjB,EAAMogB,yBAA2B,IAAMpgB,EAAMghB,sBAC7ChhB,EAAMkkB,sBAAwB,MACvBlkB,EAAMmkB,wBAA0BnkB,EAAMS,QAAQyjB,wBACjDlkB,EAAMmkB,uBAAyBnkB,EAAMS,QAAQyjB,sBAAsBlkB,IAEjEA,EAAMS,QAAQ8hB,mBAAqBviB,EAAMmkB,uBACpCnkB,EAAMogB,2BAERpgB,EAAMmkB,0BAEfnkB,EAAMyjB,aAAe,KACnB,IAAIW,EACJ,OAA6D,OAArDA,EAAyBpkB,EAAMS,QAAQqiB,WAAqBsB,EAAyBtlB,KAAKulB,KAAKrkB,EAAMskB,cAAgBtkB,EAAM6D,WAAWoe,WAAWE,SAAS,EAEpKniB,EAAMskB,YAAc,KAClB,IAAIC,EACJ,OAA2D,OAAnDA,EAAwBvkB,EAAMS,QAAQ+jB,UAAoBD,EAAwBvkB,EAAMogB,2BAA2BqE,KAAK9mB,MAAM,CACvI,GAUC+mB,EAAa,CACjB1Y,gBAAiBC,IACR,CACL0Y,WAPmC,CACvCC,IAAK,GACLC,OAAQ,OAMD5Y,IAGPE,kBAAmBnM,IACV,CACL8kB,mBAAoB1oB,EAAiB,aAAc4D,KAGvD0H,UAAW,CAACzH,EAAKD,KACfC,EAAIqU,IAAM,CAACjB,EAAU0R,EAAiBC,KACpC,MAAMC,EAAaF,EAAkB9kB,EAAIoI,cAAc9E,KAAIrC,IACzD,IAAIb,GACFA,GACEa,EACJ,OAAOb,CAAE,IACN,GACC6kB,EAAeF,EAAoB/kB,EAAIuI,gBAAgBjF,KAAIyU,IAC/D,IAAI3X,GACFA,GACE2X,EACJ,OAAO3X,CAAE,IACN,GACC8kB,EAAS,IAAI/U,IAAI,IAAI8U,EAAcjlB,EAAII,MAAO4kB,IACpDjlB,EAAMolB,eAAc5oB,IAClB,IAAI6oB,EAAWC,EAETC,EAAUC,EAOVC,EAAWC,EARjB,MAAiB,WAAbrS,EAEK,CACLuR,KAAqD,OAA9CW,EAAkB,MAAP/oB,OAAc,EAASA,EAAIooB,KAAeW,EAAW,IAAIjhB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,MACtHmoB,OAAQ,KAA0D,OAApDW,EAAqB,MAAPhpB,OAAc,EAASA,EAAIqoB,QAAkBW,EAAc,IAAIlhB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,SAASG,MAAMsT,KAAKgV,KAG7I,QAAb9R,EAEK,CACLuR,IAAK,KAAqD,OAA/Ca,EAAmB,MAAPjpB,OAAc,EAASA,EAAIooB,KAAea,EAAY,IAAInhB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,SAASG,MAAMsT,KAAKgV,IAChJN,QAA+D,OAArDa,EAAsB,MAAPlpB,OAAc,EAASA,EAAIqoB,QAAkBa,EAAe,IAAIphB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,OAGjI,CACLkoB,KAAsD,OAA/CS,EAAmB,MAAP7oB,OAAc,EAASA,EAAIooB,KAAeS,EAAY,IAAI/gB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,MACxHmoB,QAA+D,OAArDS,EAAsB,MAAP9oB,OAAc,EAASA,EAAIqoB,QAAkBS,EAAe,IAAIhhB,QAAO5H,KAAiB,MAAVyoB,GAAkBA,EAAOQ,IAAIjpB,MACrI,GACD,EAEJuD,EAAI8U,UAAY,KACd,IAAI0E,EACJ,MAAMmM,iBACJA,EAAgB3Q,cAChBA,GACEjV,EAAMS,QACV,MAAgC,mBAArBmlB,EACFA,EAAiB3lB,GAEsD,OAAxEwZ,EAA4B,MAApBmM,EAA2BA,EAAmB3Q,IAAyBwE,CAAY,EAErGxZ,EAAIkV,YAAc,KAChB,MAAMgQ,EAAS,CAACllB,EAAII,KACdukB,IACJA,EAAGC,OACHA,GACE7kB,EAAM6D,WAAW8gB,WACfkB,EAAQV,EAAO3mB,MAAK9B,GAAY,MAAPkoB,OAAc,EAASA,EAAI/iB,SAASnF,KAC7DopB,EAAWX,EAAO3mB,MAAK9B,GAAe,MAAVmoB,OAAiB,EAASA,EAAOhjB,SAASnF,KAC5E,OAAOmpB,EAAQ,QAAQC,GAAW,QAAgB,EAEpD7lB,EAAIsV,eAAiB,KACnB,IAAIwQ,EAAOC,EACX,MAAM3S,EAAWpT,EAAIkV,cACrB,IAAK9B,EAAU,OAAQ,EACvB,MAAM4S,EAAmG,OAA5EF,EAAqB,QAAb1S,EAAqBrT,EAAMkmB,aAAelmB,EAAMmmB,sBAA2B,EAASJ,EAAMxiB,KAAI6iB,IACjI,IAAI/lB,GACFA,GACE+lB,EACJ,OAAO/lB,CAAE,IAEX,OAA+G,OAAvG2lB,EAA+C,MAAvBC,OAA8B,EAASA,EAAoBvU,QAAQzR,EAAII,KAAe2lB,GAAyB,CAAC,CACjJ,EAEHviB,YAAazD,IACXA,EAAMolB,cAAgBlpB,GAA+C,MAApC8D,EAAMS,QAAQqkB,wBAA6B,EAAS9kB,EAAMS,QAAQqkB,mBAAmB5oB,GACtH8D,EAAMqmB,gBAAkB5X,IACtB,IAAI6X,EAAuB3X,EAC3B,OAAO3O,EAAMolB,cAAc3W,EA3FQ,CACvCmW,IAAK,GACLC,OAAQ,IAyF8L,OAAzHyB,EAAsE,OAA7C3X,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoBgW,YAAsB2B,EA3FtK,CACvC1B,IAAK,GACLC,OAAQ,IAyFyP,EAE/P7kB,EAAMumB,oBAAsBlT,IAC1B,IAAI0C,EACJ,MAAMC,EAAehW,EAAM6D,WAAW8gB,WAEpC,IAAI6B,EAAmBC,EADzB,OAAKpT,EAIE9O,QAA4D,OAAnDwR,EAAwBC,EAAa3C,SAAqB,EAAS0C,EAAsBpY,QAFhG4G,SAAmD,OAAzCiiB,EAAoBxQ,EAAa4O,UAAe,EAAS4B,EAAkB7oB,UAA4D,OAA/C8oB,EAAuBzQ,EAAa6O,aAAkB,EAAS4B,EAAqB9oB,QAE/E,EAElHqC,EAAM0mB,eAAiB,CAACC,EAAaC,EAAcvT,KACjD,IAAIwT,EAUJ,OATwE,OAAzDA,EAAwB7mB,EAAMS,QAAQqmB,iBAA0BD,GAG9D,MAAhBD,EAAuBA,EAAe,IAAIrjB,KAAI+d,IAC7C,MAAMrhB,EAAMD,EAAMuI,OAAO+Y,GAAO,GAChC,OAAOrhB,EAAI2hB,0BAA4B3hB,EAAM,IAAI,KAGlC,MAAhB2mB,EAAuBA,EAAe,IAAIrjB,KAAI+d,GAASqF,EAAYtiB,MAAKpE,GAAOA,EAAII,KAAOihB,OAC/Ehd,OAAOC,SAAShB,KAAI7G,IAAM,IACjCA,EACH2W,cACC,EAELrT,EAAMkmB,WAAatoB,GAAK,IAAM,CAACoC,EAAMygB,cAAcgE,KAAMzkB,EAAM6D,WAAW8gB,WAAWC,OAAM,CAACmC,EAASC,IAAoBhnB,EAAM0mB,eAAeK,EAASC,EAAiB,QAAQtnB,EAAeM,EAAMS,QAAS,cAC9MT,EAAMmmB,cAAgBvoB,GAAK,IAAM,CAACoC,EAAMygB,cAAcgE,KAAMzkB,EAAM6D,WAAW8gB,WAAWE,UAAS,CAACkC,EAASE,IAAuBjnB,EAAM0mB,eAAeK,EAASE,EAAoB,WAAWvnB,EAAeM,EAAMS,QAAS,cAC7NT,EAAMknB,cAAgBtpB,GAAK,IAAM,CAACoC,EAAMygB,cAAcgE,KAAMzkB,EAAM6D,WAAW8gB,WAAWC,IAAK5kB,EAAM6D,WAAW8gB,WAAWE,UAAS,CAACkC,EAASnC,EAAKC,KAC/I,MAAMsC,EAAe,IAAI/W,IAAI,IAAY,MAAPwU,EAAcA,EAAM,MAAmB,MAAVC,EAAiBA,EAAS,KACzF,OAAOkC,EAAQziB,QAAO5H,IAAMyqB,EAAaxB,IAAIjpB,EAAE2D,KAAI,GAClDX,EAAeM,EAAMS,QAAS,aAA8B,GAM7D2mB,EAAe,CACnBpb,gBAAiBC,IACR,CACLob,aAAc,CAAE,KACbpb,IAGPE,kBAAmBnM,IACV,CACLsnB,qBAAsBlrB,EAAiB,eAAgB4D,GACvDunB,oBAAoB,EACpBC,yBAAyB,EACzBC,uBAAuB,IAM3BhkB,YAAazD,IACXA,EAAM0nB,gBAAkBxrB,GAAiD,MAAtC8D,EAAMS,QAAQ6mB,0BAA+B,EAAStnB,EAAMS,QAAQ6mB,qBAAqBprB,GAC5H8D,EAAM2nB,kBAAoBlZ,IACxB,IAAI6X,EACJ,OAAOtmB,EAAM0nB,gBAAgBjZ,EAAe,CAAA,EAAkE,OAA5D6X,EAAwBtmB,EAAM4O,aAAayY,cAAwBf,EAAwB,CAAE,EAAC,EAElJtmB,EAAM4nB,sBAAwBjb,IAC5B3M,EAAM0nB,iBAAgBlrB,IACpBmQ,OAAyB,IAAVA,EAAwBA,GAAS3M,EAAM6nB,uBACtD,MAAMR,EAAe,IAChB7qB,GAECsrB,EAAqB9nB,EAAMqS,wBAAwB3F,SAgBzD,OAZIC,EACFmb,EAAmBvqB,SAAQ0C,IACpBA,EAAI8nB,iBAGTV,EAAapnB,EAAII,KAAM,EAAI,IAG7BynB,EAAmBvqB,SAAQ0C,WAClBonB,EAAapnB,EAAII,GAAG,IAGxBgnB,CAAY,GACnB,EAEJrnB,EAAMgoB,0BAA4Brb,GAAS3M,EAAM0nB,iBAAgBlrB,IAC/D,MAAMyrB,OAAiC,IAAVtb,EAAwBA,GAAS3M,EAAMkoB,2BAC9Db,EAAe,IAChB7qB,GAKL,OAHAwD,EAAMygB,cAAcgE,KAAKlnB,SAAQ0C,IAC/BkoB,EAAoBd,EAAcpnB,EAAII,GAAI4nB,GAAe,EAAMjoB,EAAM,IAEhEqnB,CAAY,IA6DrBrnB,EAAMooB,uBAAyB,IAAMpoB,EAAMyM,kBAC3CzM,EAAMqoB,oBAAsBzqB,GAAK,IAAM,CAACoC,EAAM6D,WAAWwjB,aAAcrnB,EAAMyM,qBAAoB,CAAC4a,EAAciB,IACzGxW,OAAO0O,KAAK6G,GAAc1pB,OAOxB4qB,EAAavoB,EAAOsoB,GANlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAE,IAIflhB,EAAeM,EAAMS,QAAS,eACjCT,EAAMwoB,4BAA8B5qB,GAAK,IAAM,CAACoC,EAAM6D,WAAWwjB,aAAcrnB,EAAM6O,yBAAwB,CAACwY,EAAciB,IACrHxW,OAAO0O,KAAK6G,GAAc1pB,OAOxB4qB,EAAavoB,EAAOsoB,GANlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAE,IAIflhB,EAAeM,EAAMS,QAAS,eACjCT,EAAMyoB,2BAA6B7qB,GAAK,IAAM,CAACoC,EAAM6D,WAAWwjB,aAAcrnB,EAAM+gB,uBAAsB,CAACsG,EAAciB,IAClHxW,OAAO0O,KAAK6G,GAAc1pB,OAOxB4qB,EAAavoB,EAAOsoB,GANlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAE,IAIflhB,EAAeM,EAAMS,QAAS,eAkBjCT,EAAM6nB,qBAAuB,KAC3B,MAAMC,EAAqB9nB,EAAM6O,sBAAsBnC,UACjD2a,aACJA,GACErnB,EAAM6D,WACV,IAAI6kB,EAAoBnkB,QAAQujB,EAAmBnqB,QAAUmU,OAAO0O,KAAK6G,GAAc1pB,QAMvF,OALI+qB,GACEZ,EAAmBtpB,MAAKyB,GAAOA,EAAI8nB,iBAAmBV,EAAapnB,EAAII,QACzEqoB,GAAoB,GAGjBA,CAAiB,EAE1B1oB,EAAMkoB,yBAA2B,KAC/B,MAAMS,EAAqB3oB,EAAMkkB,wBAAwBxX,SAASpI,QAAOrE,GAAOA,EAAI8nB,kBAC9EV,aACJA,GACErnB,EAAM6D,WACV,IAAI+kB,IAA0BD,EAAmBhrB,OAIjD,OAHIirB,GAAyBD,EAAmBnqB,MAAKyB,IAAQonB,EAAapnB,EAAII,QAC5EuoB,GAAwB,GAEnBA,CAAqB,EAE9B5oB,EAAM6oB,sBAAwB,KAC5B,IAAIC,EACJ,MAAMC,EAAgBjX,OAAO0O,KAAgE,OAA1DsI,EAAwB9oB,EAAM6D,WAAWwjB,cAAwByB,EAAwB,CAAE,GAAEnrB,OAChI,OAAOorB,EAAgB,GAAKA,EAAgB/oB,EAAM6O,sBAAsBnC,SAAS/O,MAAM,EAEzFqC,EAAMgpB,0BAA4B,KAChC,MAAML,EAAqB3oB,EAAMkkB,wBAAwBxX,SACzD,OAAO1M,EAAMkoB,4BAAqCS,EAAmBrkB,QAAOrE,GAAOA,EAAI8nB,iBAAgBvpB,MAAK9B,GAAKA,EAAEusB,iBAAmBvsB,EAAEwsB,qBAAoB,EAE9JlpB,EAAMmpB,gCAAkC,IAC/BvQ,IACL5Y,EAAM4nB,sBAAsBhP,EAAE+D,OAAOC,QAAQ,EAGjD5c,EAAMopB,oCAAsC,IACnCxQ,IACL5Y,EAAMgoB,0BAA0BpP,EAAE+D,OAAOC,QAAQ,CAEpD,EAEHlV,UAAW,CAACzH,EAAKD,KACfC,EAAIopB,eAAiB,CAAC1c,EAAO5O,KAC3B,MAAMurB,EAAarpB,EAAIgpB,gBACvBjpB,EAAM0nB,iBAAgBlrB,IACpB,IAAI+sB,EAEJ,GADA5c,OAAyB,IAAVA,EAAwBA,GAAS2c,EAC5CrpB,EAAI8nB,gBAAkBuB,IAAe3c,EACvC,OAAOnQ,EAET,MAAMgtB,EAAiB,IAClBhtB,GAGL,OADA2rB,EAAoBqB,EAAgBvpB,EAAII,GAAIsM,EAA+E,OAAvE4c,EAA+B,MAARxrB,OAAe,EAASA,EAAK0rB,iBAA0BF,EAA6BvpB,GACxJwpB,CAAc,GACrB,EAEJvpB,EAAIgpB,cAAgB,KAClB,MAAM5B,aACJA,GACErnB,EAAM6D,WACV,OAAO6lB,EAAczpB,EAAKonB,EAAa,EAEzCpnB,EAAIipB,kBAAoB,KACtB,MAAM7B,aACJA,GACErnB,EAAM6D,WACV,MAA+C,SAAxC8lB,EAAiB1pB,EAAKonB,EAAwB,EAEvDpnB,EAAI2pB,wBAA0B,KAC5B,MAAMvC,aACJA,GACErnB,EAAM6D,WACV,MAA+C,QAAxC8lB,EAAiB1pB,EAAKonB,EAAuB,EAEtDpnB,EAAI8nB,aAAe,KACjB,IAAI9a,EACJ,MAAgD,mBAArCjN,EAAMS,QAAQ8mB,mBAChBvnB,EAAMS,QAAQ8mB,mBAAmBtnB,GAE2B,OAA7DgN,EAAwBjN,EAAMS,QAAQ8mB,qBAA8Bta,CAA4B,EAE1GhN,EAAI4pB,oBAAsB,KACxB,IAAI3c,EACJ,MAAmD,mBAAxClN,EAAMS,QAAQgnB,sBAChBznB,EAAMS,QAAQgnB,sBAAsBxnB,GAE4B,OAAjEiN,EAAyBlN,EAAMS,QAAQgnB,wBAAiCva,CAA6B,EAE/GjN,EAAI6pB,kBAAoB,KACtB,IAAIC,EACJ,MAAqD,mBAA1C/pB,EAAMS,QAAQ+mB,wBAChBxnB,EAAMS,QAAQ+mB,wBAAwBvnB,GAE4B,OAAnE8pB,EAAyB/pB,EAAMS,QAAQ+mB,0BAAmCuC,CAA6B,EAEjH9pB,EAAI+pB,yBAA2B,KAC7B,MAAMC,EAAYhqB,EAAI8nB,eACtB,OAAOnP,IACL,IAAIiF,EACCoM,GACLhqB,EAAIopB,eAAuC,OAAvBxL,EAAUjF,EAAE+D,aAAkB,EAASkB,EAAQjB,QAAQ,CAC5E,CACF,GAGCuL,EAAsB,CAACqB,EAAgBnpB,EAAIsM,EAAOud,EAAiBlqB,KACvE,IAAI6S,EACJ,MAAM5S,EAAMD,EAAMuI,OAAOlI,GAAI,GAQzBsM,GACG1M,EAAI6pB,qBACPhY,OAAO0O,KAAKgJ,GAAgBjsB,SAAQlB,UAAcmtB,EAAentB,KAE/D4D,EAAI8nB,iBACNyB,EAAenpB,IAAM,WAGhBmpB,EAAenpB,GAIpB6pB,GAAmD,OAA/BrX,EAAe5S,EAAI4H,UAAoBgL,EAAalV,QAAUsC,EAAI4pB,uBACxF5pB,EAAI4H,QAAQtK,SAAQ0C,GAAOkoB,EAAoBqB,EAAgBvpB,EAAII,GAAIsM,EAAOud,EAAiBlqB,IAChG,EAEH,SAASuoB,EAAavoB,EAAOsoB,GAC3B,MAAMjB,EAAernB,EAAM6D,WAAWwjB,aAChC8C,EAAsB,GACtBC,EAAsB,CAAA,EAGtBC,EAAc,SAAU5F,EAAMzjB,GAClC,OAAOyjB,EAAKlhB,KAAItD,IACd,IAAIqqB,EACJ,MAAMhB,EAAaI,EAAczpB,EAAKonB,GAWtC,GAVIiC,IACFa,EAAoB1sB,KAAKwC,GACzBmqB,EAAoBnqB,EAAII,IAAMJ,GAEK,OAAhCqqB,EAAgBrqB,EAAI4H,UAAoByiB,EAAc3sB,SACzDsC,EAAM,IACDA,EACH4H,QAASwiB,EAAYpqB,EAAI4H,WAGzByhB,EACF,OAAOrpB,CACR,IACAqE,OAAOC,QACd,EACE,MAAO,CACLkgB,KAAM4F,EAAY/B,EAAS7D,MAC3B/X,SAAUyd,EACVvJ,SAAUwJ,EAEd,CACA,SAASV,EAAczpB,EAAKsqB,GAC1B,IAAIC,EACJ,OAAkD,OAA1CA,EAAoBD,EAAUtqB,EAAII,MAAemqB,CAC3D,CACA,SAASb,EAAiB1pB,EAAKsqB,EAAWvqB,GACxC,IAAIyqB,EACJ,GAAuC,OAAhCA,EAAgBxqB,EAAI4H,WAAoB4iB,EAAc9sB,OAAS,OAAO,EAC7E,IAAI+sB,GAAsB,EACtBC,GAAe,EA2BnB,OA1BA1qB,EAAI4H,QAAQtK,SAAQqtB,IAElB,KAAID,GAAiBD,KAGjBE,EAAO7C,iBACL2B,EAAckB,EAAQL,GACxBI,GAAe,EAEfD,GAAsB,GAKtBE,EAAO/iB,SAAW+iB,EAAO/iB,QAAQlK,QAAQ,CAC3C,MAAMktB,EAAyBlB,EAAiBiB,EAAQL,GACzB,QAA3BM,EACFF,GAAe,EACqB,SAA3BE,GACTF,GAAe,EACfD,GAAsB,GAEtBA,GAAsB,CAEzB,KAEIA,EAAsB,QAAQC,GAAe,MACtD,CAEM,MAAAG,GAAsB,aAkC5B,SAASC,GAAa/a,EAAGC,GACvB,OAAOD,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,CACpC,CACA,SAASjG,GAASgG,GAChB,MAAiB,iBAANA,EACLvE,MAAMuE,IAAMA,IAAMtE,KAAYsE,KAAOtE,IAChC,GAEFrM,OAAO2Q,GAEC,iBAANA,EACFA,EAEF,EACT,CAKA,SAASgb,GAAoBC,EAAMC,GAGjC,MAAMlb,EAAIib,EAAKlpB,MAAM+oB,IAAqBxmB,OAAOC,SAC3C0L,EAAIib,EAAKnpB,MAAM+oB,IAAqBxmB,OAAOC,SAGjD,KAAOyL,EAAErS,QAAUsS,EAAEtS,QAAQ,CAC3B,MAAMwtB,EAAKnb,EAAEiE,QACPmX,EAAKnb,EAAEgE,QACPoX,EAAKC,SAASH,EAAI,IAClBI,EAAKD,SAASF,EAAI,IAClBI,EAAQ,CAACH,EAAIE,GAAIxb,OAGvB,GAAItE,MAAM+f,EAAM,IAAhB,CACE,GAAIL,EAAKC,EACP,OAAO,EAET,GAAIA,EAAKD,EACP,OAAQ,CAGX,KARD,CAWA,GAAI1f,MAAM+f,EAAM,IACd,OAAO/f,MAAM4f,IAAO,EAAI,EAI1B,GAAIA,EAAKE,EACP,OAAO,EAET,GAAIA,EAAKF,EACP,OAAQ,CAZT,CAcF,CACD,OAAOrb,EAAErS,OAASsS,EAAEtS,MACtB,CAIK,MAAC8tB,GAAa,CACjBC,aAhGmB,CAACC,EAAMC,EAAMzrB,IACzB6qB,GAAoBhhB,GAAS2hB,EAAKrrB,SAASH,IAAW8J,cAAeD,GAAS4hB,EAAKtrB,SAASH,IAAW8J,eAgG9G4hB,0BA9FgC,CAACF,EAAMC,EAAMzrB,IACtC6qB,GAAoBhhB,GAAS2hB,EAAKrrB,SAASH,IAAY6J,GAAS4hB,EAAKtrB,SAASH,KA8FrF2rB,KAzFW,CAACH,EAAMC,EAAMzrB,IACjB4qB,GAAa/gB,GAAS2hB,EAAKrrB,SAASH,IAAW8J,cAAeD,GAAS4hB,EAAKtrB,SAASH,IAAW8J,eAyFvG8hB,kBApFwB,CAACJ,EAAMC,EAAMzrB,IAC9B4qB,GAAa/gB,GAAS2hB,EAAKrrB,SAASH,IAAY6J,GAAS4hB,EAAKtrB,SAASH,KAoF9E6rB,SAlFe,CAACL,EAAMC,EAAMzrB,KAC5B,MAAM6P,EAAI2b,EAAKrrB,SAASH,GAClB8P,EAAI2b,EAAKtrB,SAASH,GAKxB,OAAO6P,EAAIC,EAAI,EAAID,EAAIC,GAAK,EAAI,CAAC,EA4EjCgc,MA1EY,CAACN,EAAMC,EAAMzrB,IAClB4qB,GAAaY,EAAKrrB,SAASH,GAAWyrB,EAAKtrB,SAASH,KA8EvD+rB,GAAa,CACjBlgB,gBAAiBC,IACR,CACLkgB,QAAS,MACNlgB,IAGPH,oBAAqB,KACZ,CACLsgB,UAAW,OACXC,cAAe,IAGnBlgB,kBAAmBnM,IACV,CACLssB,gBAAiBlwB,EAAiB,UAAW4D,GAC7CusB,iBAAkB3T,GACTA,EAAE4T,WAIf1rB,aAAc,CAACZ,EAAQF,KACrBE,EAAOusB,iBAAmB,KACxB,MAAMC,EAAY1sB,EAAM6O,sBAAsBnC,SAASkL,MAAM,IAC7D,IAAI+U,GAAW,EACf,IAAK,MAAM1sB,KAAOysB,EAAW,CAC3B,MAAM/f,EAAe,MAAP1M,OAAc,EAASA,EAAIK,SAASJ,EAAOG,IACzD,GAA8C,kBAA1CyR,OAAOtQ,UAAUwI,SAAS+H,KAAKpF,GACjC,OAAO8e,GAAWO,SAEpB,GAAqB,iBAAVrf,IACTggB,GAAW,EACPhgB,EAAM5K,MAAM+oB,IAAqBntB,OAAS,GAC5C,OAAO8tB,GAAWC,YAGvB,CACD,OAAIiB,EACKlB,GAAWK,KAEbL,GAAWQ,KAAK,EAEzB/rB,EAAO0sB,eAAiB,KACtB,MAAMpgB,EAAWxM,EAAM6O,sBAAsBnC,SAAS,GAEtD,MAAqB,iBADK,MAAZF,OAAmB,EAASA,EAASlM,SAASJ,EAAOG,KAE1D,MAEF,MAAM,EAEfH,EAAO2sB,aAAe,KACpB,IAAIC,EAAuBC,EAC3B,IAAK7sB,EACH,MAAM,IAAI+B,MAEZ,OAAOxF,EAAWyD,EAAOa,UAAUqrB,WAAalsB,EAAOa,UAAUqrB,UAA2C,SAA/BlsB,EAAOa,UAAUqrB,UAAuBlsB,EAAOusB,mBAA4K,OAAtJK,EAA+E,OAAtDC,EAAyB/sB,EAAMS,QAAQgrB,iBAAsB,EAASsB,EAAuB7sB,EAAOa,UAAUqrB,YAAsBU,EAAwBrB,GAAWvrB,EAAOa,UAAUqrB,UAAU,EAE/WlsB,EAAO8sB,cAAgB,CAACC,EAAMC,KAW5B,MAAMC,EAAmBjtB,EAAOktB,sBAC1BC,EAAiB,MAAOJ,EAC9BjtB,EAAMstB,YAAW9wB,IAEf,MAAM+wB,EAAyB,MAAP/wB,OAAc,EAASA,EAAI6H,MAAK3H,GAAKA,EAAE2D,KAAOH,EAAOG,KACvEmtB,EAAuB,MAAPhxB,OAAc,EAASA,EAAIoR,WAAUlR,GAAKA,EAAE2D,KAAOH,EAAOG,KAChF,IAGIotB,EAHAC,EAAa,GAIbC,EAAWN,EAAiBJ,EAA4B,SAArBE,EA+BrC,IAAIS,GA1BFH,EAFO,MAAPjxB,GAAeA,EAAImB,QAAUuC,EAAO2tB,mBAAqBX,EACvDK,EACW,SAEA,MAIJ,MAAP/wB,GAAeA,EAAImB,QAAU6vB,IAAkBhxB,EAAImB,OAAS,EACjD,UACJ4vB,EACI,SAEA,UAKE,WAAfE,IAEGJ,GAEEF,IACHM,EAAa,WAIA,QAAfA,IAEFC,EAAa,IAAIlxB,EAAK,CACpB6D,GAAIH,EAAOG,GACX4sB,KAAMU,IAGRD,EAAWvZ,OAAO,EAAGuZ,EAAW/vB,QAA0E,OAA/DiwB,EAAwB5tB,EAAMS,QAAQqtB,sBAAgCF,EAAwBpiB,OAAOiL,oBAGhJiX,EAFwB,WAAfD,EAEIjxB,EAAI+G,KAAI7G,GACfA,EAAE2D,KAAOH,EAAOG,GACX,IACF3D,EACHuwB,KAAMU,GAGHjxB,IAEe,WAAf+wB,EACIjxB,EAAI8H,QAAO5H,GAAKA,EAAE2D,KAAOH,EAAOG,KAEhC,CAAC,CACZA,GAAIH,EAAOG,GACX4sB,KAAMU,IAGV,OAAOD,CAAU,GACjB,EAEJxtB,EAAO6tB,gBAAkB,KACvB,IAAI7sB,EAAM8sB,EAEV,OADyJ,OAAlI9sB,EAAmE,OAA3D8sB,EAAwB9tB,EAAOa,UAAUktB,eAAyBD,EAAwBhuB,EAAMS,QAAQwtB,eAAyB/sB,EAAmC,SAA5BhB,EAAO0sB,kBACvJ,OAAS,KAAK,EAEvC1sB,EAAOktB,oBAAsBF,IAC3B,IAAIjgB,EAAuBC,EAC3B,MAAMghB,EAAqBhuB,EAAO6tB,kBAC5BI,EAAWjuB,EAAOkuB,cACxB,OAAKD,KAGDA,IAAaD,GAAuF,OAA/DjhB,EAAwBjN,EAAMS,QAAQ4tB,wBAAgCphB,GAE/GigB,GAAsE,OAA7DhgB,EAAyBlN,EAAMS,QAAQ6tB,qBAA6BphB,KAIzD,SAAbihB,EAAsB,MAAQ,QAR5BD,CAQkC,EAE7ChuB,EAAOquB,WAAa,KAClB,IAAIvhB,EAAuB+c,EAC3B,OAAoE,OAA3D/c,EAAwB9M,EAAOa,UAAUytB,gBAAyBxhB,KAA4F,OAAzD+c,EAAyB/pB,EAAMS,QAAQ+tB,gBAAyBzE,MAAoC7pB,EAAOqB,UAAU,EAErOrB,EAAO2tB,gBAAkB,KACvB,IAAI7V,EAAOyW,EACX,OAAiJ,OAAzIzW,EAAuE,OAA9DyW,EAAyBvuB,EAAOa,UAAU2tB,iBAA2BD,EAAyBzuB,EAAMS,QAAQiuB,iBAA2B1W,IAAU9X,EAAOqB,UAAU,EAErLrB,EAAOkuB,YAAc,KACnB,IAAIO,EACJ,MAAMC,EAAmE,OAArDD,EAAwB3uB,EAAM6D,WAAWsoB,cAAmB,EAASwC,EAAsBtqB,MAAK3H,GAAKA,EAAE2D,KAAOH,EAAOG,KACzI,QAAQuuB,IAAqBA,EAAW3B,KAAO,OAAS,MAAK,EAE/D/sB,EAAO2uB,aAAe,KACpB,IAAIC,EAAwBC,EAC5B,OAAsK,OAA9JD,EAAgF,OAAtDC,EAAyB/uB,EAAM6D,WAAWsoB,cAAmB,EAAS4C,EAAuBnhB,WAAUlR,GAAKA,EAAE2D,KAAOH,EAAOG,MAAeyuB,GAA0B,CAAC,EAE1M5uB,EAAO8uB,aAAe,KAEpBhvB,EAAMstB,YAAW9wB,GAAc,MAAPA,GAAeA,EAAImB,OAASnB,EAAI8H,QAAO5H,GAAKA,EAAE2D,KAAOH,EAAOG,KAAM,IAAG,EAE/FH,EAAO+uB,wBAA0B,KAC/B,MAAMC,EAAUhvB,EAAOquB,aACvB,OAAO3V,IACAsW,IACQ,MAAbtW,EAAEC,SAAmBD,EAAEC,UACC,MAAxB3Y,EAAO8sB,eAAyB9sB,EAAO8sB,mBAAcrrB,IAAWzB,EAAO2tB,oBAAsD,MAAlC7tB,EAAMS,QAAQ8rB,sBAA2B,EAASvsB,EAAMS,QAAQ8rB,iBAAiB3T,KAAW,CACxL,CACF,EAEHnV,YAAazD,IACXA,EAAMstB,WAAapxB,GAA4C,MAAjC8D,EAAMS,QAAQ6rB,qBAA0B,EAAStsB,EAAMS,QAAQ6rB,gBAAgBpwB,GAC7G8D,EAAMmvB,aAAe1gB,IACnB,IAAI2gB,EAAuBzgB,EAC3B3O,EAAMstB,WAAW7e,EAAe,GAA4H,OAAtH2gB,EAAsE,OAA7CzgB,EAAsB3O,EAAM4O,mBAAwB,EAASD,EAAoBwd,SAAmBiD,EAAwB,GAAG,EAEhMpvB,EAAMqvB,qBAAuB,IAAMrvB,EAAMsS,qBACzCtS,EAAM+gB,kBAAoB,MACnB/gB,EAAMsvB,oBAAsBtvB,EAAMS,QAAQsgB,oBAC7C/gB,EAAMsvB,mBAAqBtvB,EAAMS,QAAQsgB,kBAAkB/gB,IAEzDA,EAAMS,QAAQ8uB,gBAAkBvvB,EAAMsvB,mBACjCtvB,EAAMqvB,uBAERrvB,EAAMsvB,qBACd,GAICE,GAAkB,CAAChsB,EAASyY,EAAkBhJ,EAAgBmB,EAAejL,EAAgB0C,EAAiBiS,EAEpHO,EAEA6N,GAAY1b,EAEZ0O,EAAc8C,EAAe0C,EAAY0C,EAAc1Q,GAIvD,SAASjT,GAAYhD,GACnB,IAAIgvB,EAAoBC,EAIxB,MAAM9uB,EAAY,IAAI4uB,MAAiE,OAA3CC,EAAqBhvB,EAAQG,WAAqB6uB,EAAqB,IACnH,IAAIzvB,EAAQ,CACVY,aAEF,MAAM+uB,EAAiB3vB,EAAMY,UAAUoI,QAAO,CAAC0U,EAAK7c,IAC3CiR,OAAO8d,OAAOlS,EAAkC,MAA7B7c,EAAQsL,uBAA4B,EAAStL,EAAQsL,kBAAkBnM,KAChG,CAAE,GAWL,IAAI4O,EAAe,IAEqC,OAAjD8gB,EAAwBjvB,EAAQmO,cAAwB8gB,EAAwB,CAAE,GAEzF1vB,EAAMY,UAAUrD,SAAQsD,IACtB,IAAIgvB,EACJjhB,EAA6H,OAA7GihB,EAAmD,MAA3BhvB,EAAQmL,qBAA0B,EAASnL,EAAQmL,gBAAgB4C,IAAyBihB,EAAwBjhB,CAAY,IAE1K,MAAM2Q,EAAS,GACf,IAAIuQ,GAAgB,EACpB,MAAMC,EAAe,CACnBnvB,YACAH,QAAS,IACJkvB,KACAlvB,GAELmO,eACAiR,OAAQmQ,IACNzQ,EAAO9hB,KAAKuyB,GACPF,IACHA,GAAgB,EAIhBG,QAAQC,UAAUC,MAAK,KACrB,KAAO5Q,EAAO5hB,QACZ4hB,EAAOtL,OAAPsL,GAEFuQ,GAAgB,CAAK,IACpBM,OAAMC,GAASC,YAAW,KAC3B,MAAMD,CAAK,MAEd,EAEHE,MAAO,KACLvwB,EAAMzD,SAASyD,EAAM4O,aAAa,EAEpC4hB,WAAYt0B,IACV,MAAMu0B,EAAax0B,EAAiBC,EAAS8D,EAAMS,SACnDT,EAAMS,QAjDWA,IACfT,EAAMS,QAAQiwB,aACT1wB,EAAMS,QAAQiwB,aAAaf,EAAgBlvB,GAE7C,IACFkvB,KACAlvB,GA2CaiwB,CAAaD,EAAW,EAE1C5sB,SAAU,IACD7D,EAAMS,QAAQwL,MAEvB1P,SAAUL,IACuB,MAA/B8D,EAAMS,QAAQkwB,eAAyB3wB,EAAMS,QAAQkwB,cAAcz0B,EAAQ,EAE7E00B,UAAW,CAAC3wB,EAAKvB,EAAOuC,KACtB,IAAIwgB,EACJ,OAAyH,OAAjHA,EAAkD,MAA1BzhB,EAAMS,QAAQowB,cAAmB,EAAS7wB,EAAMS,QAAQowB,SAAS5wB,EAAKvB,EAAOuC,IAAmBwgB,EAAwB,GAAGxgB,EAAS,CAACA,EAAOZ,GAAI3B,GAAOuI,KAAK,KAAOvI,GAAO,EAE5M+N,gBAAiB,KACVzM,EAAM8wB,mBACT9wB,EAAM8wB,iBAAmB9wB,EAAMS,QAAQgM,gBAAgBzM,IAElDA,EAAM8wB,oBAKfrQ,YAAa,IACJzgB,EAAMkkB,wBAGf3b,OAAQ,CAAClI,EAAI0wB,KACX,IAAI9wB,GAAO8wB,EAAY/wB,EAAMogB,2BAA6BpgB,EAAMygB,eAAeG,SAASvgB,GACxF,IAAKJ,IACHA,EAAMD,EAAMyM,kBAAkBmU,SAASvgB,IAClCJ,GAIH,MAAM,IAAIgC,MAGd,OAAOhC,CAAG,EAEZoB,qBAAsBzD,GAAK,IAAM,CAACoC,EAAMS,QAAQuwB,iBAAgBA,IAC9D,IAAIC,EAEJ,OADAD,EAAoD,OAAnCC,EAAiBD,GAAyBC,EAAiB,CAAA,EACrE,CACLrvB,OAAQ8O,IACN,MAAMtP,EAAoBsP,EAAM9O,OAAO1B,OAAOa,UAC9C,OAAIK,EAAkBE,YACbF,EAAkBE,YAEvBF,EAAkBG,WACbH,EAAkBf,GAEpB,IAAI,EAGbD,KAAMsQ,IACJ,IAAIwgB,EAAuBC,EAC3B,OAAuK,OAA/JD,EAAsE,OAA7CC,EAAqBzgB,EAAMnQ,gBAAyD,MAA/B4wB,EAAmBnnB,cAAmB,EAASmnB,EAAmBnnB,YAAsBknB,EAAwB,IAAI,KAEzMlxB,EAAMY,UAAUoI,QAAO,CAAC0U,EAAK7c,IACvBiR,OAAO8d,OAAOlS,EAAoC,MAA/B7c,EAAQiL,yBAA8B,EAASjL,EAAQiL,wBAChF,OACAklB,EACJ,GACAtxB,EAAee,EAAS,iBAC3B2wB,eAAgB,IAAMpxB,EAAMS,QAAQyB,QACpCyB,cAAe/F,GAAK,IAAM,CAACoC,EAAMoxB,oBAAmBC,IAClD,MAAMC,EAAiB,SAAUD,EAAYpwB,EAAQD,GAInD,YAHc,IAAVA,IACFA,EAAQ,GAEHqwB,EAAW9tB,KAAIxC,IACpB,MAAMb,EAASY,EAAad,EAAOe,EAAWC,EAAOC,GAC/CswB,EAAoBxwB,EAE1B,OADAb,EAAOgC,QAAUqvB,EAAkBrvB,QAAUovB,EAAeC,EAAkBrvB,QAAShC,EAAQc,EAAQ,GAAK,GACrGd,CAAM,GAEvB,EACM,OAAOoxB,EAAeD,EAAW,GAChC3xB,EAAee,EAAS,iBAC3Byc,kBAAmBtf,GAAK,IAAM,CAACoC,EAAM2D,mBAAkBM,GAC9CA,EAAW5B,SAAQnC,GACjBA,EAAOiC,oBAEfzC,EAAee,EAAS,iBAC3B+wB,uBAAwB5zB,GAAK,IAAM,CAACoC,EAAMkd,uBAAsBuU,GACvDA,EAAYzoB,QAAO,CAACC,EAAK/I,KAC9B+I,EAAI/I,EAAOG,IAAMH,EACV+I,IACN,CAAE,IACJvJ,EAAee,EAAS,iBAC3BoI,kBAAmBjL,GAAK,IAAM,CAACoC,EAAM2D,gBAAiB3D,EAAMuC,wBAAuB,CAAC0B,EAAYzB,IAEvFA,EADWyB,EAAW5B,SAAQnC,GAAUA,EAAOoC,qBAErD5C,EAAee,EAAS,iBAC3ByH,UAAW/H,GACMH,EAAMwxB,yBAAyBrxB,IAOlD2R,OAAO8d,OAAO5vB,EAAO+vB,GACrB,IAAK,IAAIrxB,EAAQ,EAAGA,EAAQsB,EAAMY,UAAUjD,OAAQe,IAAS,CAC3D,MAAMmC,EAAUb,EAAMY,UAAUlC,GACrB,MAAXmC,GAA0C,MAAvBA,EAAQ4C,aAAuB5C,EAAQ4C,YAAYzD,EACvE,CACD,OAAOA,CACT,CA8DA,SAAS0xB,GAAWpJ,GAClB,MAAMqJ,EAAe,GACfC,EAAY3xB,IAChB,IAAI4S,EACJ8e,EAAal0B,KAAKwC,GACkB,OAA/B4S,EAAe5S,EAAI4H,UAAoBgL,EAAalV,QAAUsC,EAAIygB,iBACrEzgB,EAAI4H,QAAQtK,QAAQq0B,EACrB,EAGH,OADAtJ,EAAS7D,KAAKlnB,QAAQq0B,GACf,CACLnN,KAAMkN,EACNjlB,SAAU4b,EAAS5b,SACnBkU,SAAU0H,EAAS1H,SAEvB,CAsBA,SAASiR,GAAWpN,EAAMqN,EAAe9xB,GACvC,OAAIA,EAAMS,QAAQ4L,mBAKpB,SAAiC0lB,EAAcC,EAAWhyB,GACxD,IAAIiyB,EACJ,MAAMC,EAAsB,GACtBC,EAAsB,CAAA,EACtBvrB,EAA4E,OAAhEqrB,EAAwBjyB,EAAMS,QAAQ6L,uBAAiC2lB,EAAwB,IAC3GG,EAAoB,SAAUL,EAAc/wB,QAClC,IAAVA,IACFA,EAAQ,GAEV,MAAMyjB,EAAO,GAGb,IAAK,IAAIvb,EAAI,EAAGA,EAAI6oB,EAAap0B,OAAQuL,IAAK,CAC5C,IAAI2J,EACJ,IAAI5S,EAAM8xB,EAAa7oB,GACvB,MAAMmpB,EAAS3qB,EAAU1H,EAAOC,EAAII,GAAIJ,EAAI0H,SAAU1H,EAAIvB,MAAOuB,EAAIe,WAAOW,EAAW1B,EAAI6H,UAE3F,GADAuqB,EAAOnmB,cAAgBjM,EAAIiM,cACS,OAA/B2G,EAAe5S,EAAI4H,UAAoBgL,EAAalV,QAAUqD,EAAQ4F,EAAU,CAGnF,GAFAyrB,EAAOxqB,QAAUuqB,EAAkBnyB,EAAI4H,QAAS7G,EAAQ,GACxDf,EAAMoyB,EACFL,EAAU/xB,KAASoyB,EAAOxqB,QAAQlK,OAAQ,CAC5C8mB,EAAKhnB,KAAKwC,GACVkyB,EAAoBlyB,EAAII,IAAMJ,EAC9BiyB,EAAoBz0B,KAAKwC,GACzB,QACD,CACD,GAAI+xB,EAAU/xB,IAAQoyB,EAAOxqB,QAAQlK,OAAQ,CAC3C8mB,EAAKhnB,KAAKwC,GACVkyB,EAAoBlyB,EAAII,IAAMJ,EAC9BiyB,EAAoBz0B,KAAKwC,GACzB,QACD,CACT,MACQA,EAAMoyB,EACFL,EAAU/xB,KACZwkB,EAAKhnB,KAAKwC,GACVkyB,EAAoBlyB,EAAII,IAAMJ,EAC9BiyB,EAAoBz0B,KAAKwC,GAG9B,CACD,OAAOwkB,CACX,EACE,MAAO,CACLA,KAAM2N,EAAkBL,GACxBrlB,SAAUwlB,EACVtR,SAAUuR,EAEd,CApDWG,CAAwB7N,EAAMqN,EAAe9xB,GAqDxD,SAAgC+xB,EAAcC,EAAWhyB,GACvD,IAAIuyB,EACJ,MAAML,EAAsB,GACtBC,EAAsB,CAAA,EACtBvrB,EAA6E,OAAjE2rB,EAAyBvyB,EAAMS,QAAQ6L,uBAAiCimB,EAAyB,IAG7GH,EAAoB,SAAUL,EAAc/wB,QAClC,IAAVA,IACFA,EAAQ,GAIV,MAAMyjB,EAAO,GAGb,IAAK,IAAIvb,EAAI,EAAGA,EAAI6oB,EAAap0B,OAAQuL,IAAK,CAC5C,IAAIjJ,EAAM8xB,EAAa7oB,GAEvB,GADa8oB,EAAU/xB,GACb,CACR,IAAIqqB,EACJ,GAAqC,OAAhCA,EAAgBrqB,EAAI4H,UAAoByiB,EAAc3sB,QAAUqD,EAAQ4F,EAAU,CACrF,MAAMyrB,EAAS3qB,EAAU1H,EAAOC,EAAII,GAAIJ,EAAI0H,SAAU1H,EAAIvB,MAAOuB,EAAIe,WAAOW,EAAW1B,EAAI6H,UAC3FuqB,EAAOxqB,QAAUuqB,EAAkBnyB,EAAI4H,QAAS7G,EAAQ,GACxDf,EAAMoyB,CACP,CACD5N,EAAKhnB,KAAKwC,GACViyB,EAAoBz0B,KAAKwC,GACzBkyB,EAAoBlyB,EAAII,IAAMJ,CAC/B,CACF,CACD,OAAOwkB,CACX,EACE,MAAO,CACLA,KAAM2N,EAAkBL,GACxBrlB,SAAUwlB,EACVtR,SAAUuR,EAEd,CAzFSK,CAAuB/N,EAAMqN,EAAe9xB,EACrD,qYAl6FA,WACE,MAAO,CACLyyB,SAAU,CAACA,EAAUvyB,IACQ,mBAAbuyB,EAA0B,IACnCvyB,EACHqB,WAAYkxB,GACV,IACCvyB,EACHoB,YAAamxB,GAGjBC,QAASxyB,GAAUA,EACnByyB,MAAOzyB,GAAUA,EAErB,kHChDO,SACL0yB,EACAliB,GAEA,OAAQkiB,EAiBV,SAA0BC,GACxB,MACuB,mBAAdA,GACP,MACE,MAAMC,EAAQhhB,OAAOihB,eAAeF,GACpC,OAAOC,EAAMtxB,WAAasxB,EAAMtxB,UAAUwxB,gBAC3C,EAHD,EAKJ,CAdIC,CAHFJ,EAR+CD,IAYxB,mBAAdC,GAeX,SAA2BA,GACzB,MACuB,iBAAdA,GACuB,iBAAvBA,EAAUK,UACjB,CAAC,aAAc,qBAAqBrxB,SAASgxB,EAAUK,SAASC,YAEpE,CApBIC,CAAkBP,GAZlBQ,EAAAC,cAACV,EAASliB,GAEVkiB,EAHa,KAOjB,IACEC,CAHF,yCDq1FA,WACE,OAAO7yB,GAASpC,GAAK,IAAM,CAACoC,EAAMS,QAAQ8yB,QAAOA,IAC/C,MAAMjL,EAAW,CACf7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAE,GAER4S,EAAa,SAAUC,EAAczyB,EAAO2H,QAClC,IAAV3H,IACFA,EAAQ,GAEV,MAAMyjB,EAAO,GACb,IAAK,IAAIvb,EAAI,EAAGA,EAAIuqB,EAAa91B,OAAQuL,IAAK,CAS5C,MAAMjJ,EAAMyH,EAAU1H,EAAOA,EAAM4wB,UAAU6C,EAAavqB,GAAIA,EAAGP,GAAY8qB,EAAavqB,GAAIA,EAAGlI,OAAOW,EAAwB,MAAbgH,OAAoB,EAASA,EAAUtI,IAWxJ,IAAIqzB,EADN,GAPApL,EAAS5b,SAASjP,KAAKwC,GAEvBqoB,EAAS1H,SAAS3gB,EAAII,IAAMJ,EAE5BwkB,EAAKhnB,KAAKwC,GAGND,EAAMS,QAAQkzB,WAEhB1zB,EAAI2zB,gBAAkB5zB,EAAMS,QAAQkzB,WAAWF,EAAavqB,GAAIA,GAGZ,OAA/CwqB,EAAuBzzB,EAAI2zB,kBAA4BF,EAAqB/1B,SAC/EsC,EAAI4H,QAAU2rB,EAAWvzB,EAAI2zB,gBAAiB5yB,EAAQ,EAAGf,GAG9D,CACD,OAAOwkB,CACb,EAEI,OADA6D,EAAS7D,KAAO+O,EAAWD,GACpBjL,CAAQ,GACd5oB,EAAeM,EAAMS,QAAS,aAAc,GAAe,IAAMT,EAAMqiB,wBAC5E,wBAEA,WACE,OAAOriB,GAASpC,GAAK,IAAM,CAACoC,EAAM6D,WAAWsb,SAAUnf,EAAM8gB,yBAA0B9gB,EAAMS,QAAQ4e,wBAAuB,CAACF,EAAUmJ,EAAUjJ,KAC1IiJ,EAAS7D,KAAK9mB,SAAuB,IAAbwhB,IAAsBrN,OAAO0O,KAAiB,MAAZrB,EAAmBA,EAAW,CAAE,GAAExhB,OACxF2qB,EAEJjJ,EAIEqS,GAAWpJ,GAFTA,GAGR5oB,EAAeM,EAAMS,QAAS,cACnC,2BAkBA,WACE,MAAO,CAACT,EAAOG,IAAavC,GAAK,KAC/B,IAAIi2B,EACJ,MAAO,CAAmD,OAAjDA,EAAmB7zB,EAAMkI,UAAU/H,SAAqB,EAAS0zB,EAAiBxqB,qBAAqB,IAC/GyqB,IACD,IAAKA,EAAiB,OACtB,MAAMC,EAAeD,EAAgBpnB,SAASrK,SAAQ2xB,IACpD,IAAIC,EACJ,OAAsE,OAA9DA,EAAwBD,EAAQ7rB,gBAAgBhI,IAAqB8zB,EAAwB,EAAE,IACtG1wB,IAAIiI,QAAQlH,QAAOqI,IAAUnB,OAAOC,MAAMkB,KAC7C,IAAKonB,EAAap2B,OAAQ,OAC1B,IAAIu2B,EAAkBH,EAAa,GAC/BI,EAAkBJ,EAAaA,EAAap2B,OAAS,GACzD,IAAK,MAAMgP,KAASonB,EACdpnB,EAAQunB,EAAiBA,EAAkBvnB,EAAeA,EAAQwnB,IAAiBA,EAAkBxnB,GAE3G,MAAO,CAACunB,EAAiBC,EAAgB,GACxCz0B,EAAeM,EAAMS,QAAS,cACnC,uBAiGA,WACE,MAAO,CAACT,EAAOG,IAAavC,GAAK,IAAM,CAACoC,EAAMsJ,yBAA0BtJ,EAAM6D,WAAWqI,cAAelM,EAAM6D,WAAWya,aAActe,EAAM6O,yBAAwB,CAACulB,EAAaloB,EAAeoS,KAChM,IAAK8V,EAAY3P,KAAK9mB,SAA6B,MAAjBuO,IAAyBA,EAAcvO,UAAY2gB,EACnF,OAAO8V,EAET,MAAMC,EAAgB,IAAInoB,EAAc3I,KAAI7G,GAAKA,EAAE2D,KAAIiE,QAAO5H,GAAKA,IAAMyD,IAAWme,EAAe,kBAAe3c,GAAW2C,OAAOC,SAUpI,OAAOstB,GAAWuC,EAAY3P,MATPxkB,IAErB,IAAK,IAAIiJ,EAAI,EAAGA,EAAImrB,EAAc12B,OAAQuL,IACxC,IAA4C,IAAxCjJ,EAAIiM,cAAcmoB,EAAcnrB,IAClC,OAAO,EAGX,OAAO,CAAI,GAEuClJ,EAAM,GACzDN,EAAeM,EAAMS,QAAS,cACnC,2BAEA,WACE,MAAO,CAACT,EAAOG,IAAavC,GAAK,KAC/B,IAAIi2B,EACJ,MAAO,CAAmD,OAAjDA,EAAmB7zB,EAAMkI,UAAU/H,SAAqB,EAAS0zB,EAAiBxqB,qBAAqB,IAC/GyqB,IACD,IAAKA,EAAiB,OAAO,IAAIrqB,IACjC,IAAI6qB,EAAsB,IAAI7qB,IAC9B,IAAK,IAAIP,EAAI,EAAGA,EAAI4qB,EAAgBpnB,SAAS/O,OAAQuL,IAAK,CACxD,MAAMyG,EAASmkB,EAAgBpnB,SAASxD,GAAGf,gBAAgBhI,GAC3D,IAAK,IAAIo0B,EAAI,EAAGA,EAAI5kB,EAAOhS,OAAQ42B,IAAK,CACtC,MAAM5nB,EAAQgD,EAAO4kB,GAEnB,IAAIC,EADN,GAAIF,EAAoB3O,IAAIhZ,GAE1B2nB,EAAoBG,IAAI9nB,GAAoE,OAA3D6nB,EAAwBF,EAAoBI,IAAI/nB,IAAkB6nB,EAAwB,GAAK,QAEhIF,EAAoBG,IAAI9nB,EAAO,EAElC,CACF,CACD,OAAO2nB,CAAmB,GACzB50B,EAAeM,EAAMS,QAAS,cACnC,wBAEA,WACE,OAAOT,GAASpC,GAAK,IAAM,CAACoC,EAAMsJ,yBAA0BtJ,EAAM6D,WAAWqI,cAAelM,EAAM6D,WAAWya,gBAAe,CAACgK,EAAUpc,EAAeoS,KACpJ,IAAKgK,EAAS7D,KAAK9mB,SAA6B,MAAjBuO,IAAyBA,EAAcvO,UAAY2gB,EAAc,CAC9F,IAAK,IAAIpV,EAAI,EAAGA,EAAIof,EAAS5b,SAAS/O,OAAQuL,IAC5Cof,EAAS5b,SAASxD,GAAGgD,cAAgB,CAAA,EACrCoc,EAAS5b,SAASxD,GAAGoF,kBAAoB,CAAA,EAE3C,OAAOga,CACR,CACD,MAAMqM,EAAwB,GACxBC,EAAwB,IACZ,MAAjB1oB,EAAwBA,EAAgB,IAAI3O,SAAQb,IACnD,IAAIm4B,EACJ,MAAM30B,EAASF,EAAMkI,UAAUxL,EAAE2D,IACjC,IAAKH,EACH,OAEF,MAAM6L,EAAW7L,EAAO0M,cACnBb,GAML4oB,EAAsBl3B,KAAK,CACzB4C,GAAI3D,EAAE2D,GACN0L,WACAkc,cAAgI,OAAhH4M,EAAuD,MAA/B9oB,EAASb,wBAA6B,EAASa,EAASb,mBAAmBxO,EAAEiQ,QAAkBkoB,EAAwBn4B,EAAEiQ,OACjK,IAEJ,MAAM0nB,GAAkC,MAAjBnoB,EAAwBA,EAAgB,IAAI3I,KAAI7G,GAAKA,EAAE2D,KACxEme,EAAiBxe,EAAM+e,oBACvB+V,EAA4B90B,EAAM6I,oBAAoBvE,QAAOpE,GAAUA,EAAOye,uBAYpF,IAAIoW,EACAC,EAZA1W,GAAgBE,GAAkBsW,EAA0Bn3B,SAC9D02B,EAAc52B,KAAK,cACnBq3B,EAA0Bv3B,SAAQ2C,IAChC,IAAI+0B,EACJL,EAAsBn3B,KAAK,CACzB4C,GAAIH,EAAOG,GACX0L,SAAUyS,EACVyJ,cAAiJ,OAAjIgN,EAA6D,MAArCzW,EAAetT,wBAA6B,EAASsT,EAAetT,mBAAmBoT,IAAyB2W,EAAwB3W,GAChL,KAON,IAAK,IAAIiW,EAAI,EAAGA,EAAIjM,EAAS5b,SAAS/O,OAAQ42B,IAAK,CACjD,MAAMt0B,EAAMqoB,EAAS5b,SAAS6nB,GAE9B,GADAt0B,EAAIiM,cAAgB,GAChByoB,EAAsBh3B,OACxB,IAAK,IAAIuL,EAAI,EAAGA,EAAIyrB,EAAsBh3B,OAAQuL,IAAK,CACrD6rB,EAAsBJ,EAAsBzrB,GAC5C,MAAM7I,EAAK00B,EAAoB10B,GAG/BJ,EAAIiM,cAAc7L,GAAM00B,EAAoBhpB,SAAS9L,EAAKI,EAAI00B,EAAoB9M,eAAeiN,IAC/Fj1B,EAAIqO,kBAAkBjO,GAAM60B,CAAU,GAEzC,CAEH,GAAIN,EAAsBj3B,OAAQ,CAChC,IAAK,IAAIuL,EAAI,EAAGA,EAAI0rB,EAAsBj3B,OAAQuL,IAAK,CACrD8rB,EAAsBJ,EAAsB1rB,GAC5C,MAAM7I,EAAK20B,EAAoB30B,GAE/B,GAAI20B,EAAoBjpB,SAAS9L,EAAKI,EAAI20B,EAAoB/M,eAAeiN,IAC3Ej1B,EAAIqO,kBAAkBjO,GAAM60B,CAAU,IACpC,CACFj1B,EAAIiM,cAAcipB,YAAa,EAC/B,KACD,CACF,EACoC,IAAjCl1B,EAAIiM,cAAcipB,aACpBl1B,EAAIiM,cAAcipB,YAAa,EAElC,CACF,CAYD,OAAOtD,GAAWvJ,EAAS7D,MAXJxkB,IAErB,IAAK,IAAIiJ,EAAI,EAAGA,EAAImrB,EAAc12B,OAAQuL,IACxC,IAA4C,IAAxCjJ,EAAIiM,cAAcmoB,EAAcnrB,IAClC,OAAO,EAGX,OAAO,CAAI,GAIoClJ,EAAM,GACtDN,EAAeM,EAAMS,QAAS,aAAc,GAAuB,IAAMT,EAAMqiB,wBACpF,uBAEA,WACE,OAAOriB,GAASpC,GAAK,IAAM,CAACoC,EAAM6D,WAAWiN,SAAU9Q,EAAMqS,2BAA0B,CAACvB,EAAUwX,KAChG,IAAKA,EAAS7D,KAAK9mB,SAAWmT,EAASnT,OAKrC,OAJA2qB,EAAS7D,KAAKlnB,SAAQ0C,IACpBA,EAAIe,MAAQ,EACZf,EAAI6H,cAAWnG,CAAS,IAEnB2mB,EAIT,MAAM8M,EAAmBtkB,EAASxM,QAAOnE,GAAYH,EAAMkI,UAAU/H,KAC/Dk1B,EAAkB,GAClBC,EAAkB,CAAA,EAOlBC,EAAqB,SAAU9Q,EAAMzjB,EAAO8G,GAMhD,QALc,IAAV9G,IACFA,EAAQ,GAINA,GAASo0B,EAAiBz3B,OAC5B,OAAO8mB,EAAKlhB,KAAItD,IACdA,EAAIe,MAAQA,EACZq0B,EAAgB53B,KAAKwC,GACrBq1B,EAAgBr1B,EAAII,IAAMJ,EACtBA,EAAI4H,UACN5H,EAAI4H,QAAU0tB,EAAmBt1B,EAAI4H,QAAS7G,EAAQ,EAAGf,EAAII,KAExDJ,KAGX,MAAME,EAAWi1B,EAAiBp0B,GAG5Bw0B,EAsFZ,SAAiB/Q,EAAMtkB,GACrB,MAAMs1B,EAAW,IAAIhsB,IACrB,OAAOgb,EAAKzb,QAAO,CAACzF,EAAKtD,KACvB,MAAMy1B,EAAS,GAAGz1B,EAAIoR,iBAAiBlR,KACjCw1B,EAAWpyB,EAAImxB,IAAIgB,GAMzB,OALKC,EAGHA,EAASl4B,KAAKwC,GAFdsD,EAAIkxB,IAAIiB,EAAQ,CAACz1B,IAIZsD,CAAG,GACTkyB,EACL,CAlG2BG,CAAQnR,EAAMtkB,GAG7B01B,EAAwBh5B,MAAMsT,KAAKqlB,EAAaM,WAAWvyB,KAAI,CAACrC,EAAMxC,KAC1E,IAAKq3B,EAAeC,GAAe90B,EAC/Bb,EAAK,GAAGF,KAAY41B,IACxB11B,EAAKyH,EAAW,GAAGA,KAAYzH,IAAOA,EAGtC,MAAMwH,EAAU0tB,EAAmBS,EAAah1B,EAAQ,EAAGX,GAC3DwH,EAAQtK,SAAQqtB,IACdA,EAAO9iB,SAAWzH,CAAE,IAItB,MAAMmP,EAAWxO,EAAQ/D,EAAU+4B,GAAa/1B,GAAOA,EAAI4H,UAAWmuB,EAChE/1B,EAAMyH,EAAU1H,EAAOK,EAAImP,EAAS,GAAG7H,SAAUjJ,EAAOsC,OAAOW,EAAWmG,GA0ChF,OAzCAgK,OAAO8d,OAAO3vB,EAAK,CACjBwS,iBAAkBtS,EAClB41B,gBACAluB,UACA2H,WACAlP,SAAUH,IAER,GAAIi1B,EAAiBvzB,SAAS1B,GAAW,CACvC,GAAIF,EAAI8H,aAAaE,eAAe9H,GAClC,OAAOF,EAAI8H,aAAa5H,GAGxB,IAAI81B,EADN,GAAID,EAAY,GAEd/1B,EAAI8H,aAAa5H,GAA2E,OAA9D81B,EAAwBD,EAAY,GAAG11B,SAASH,IAAqB81B,OAAwBt0B,EAE7H,OAAO1B,EAAI8H,aAAa5H,EACzB,CACD,GAAIF,EAAIyS,qBAAqBzK,eAAe9H,GAC1C,OAAOF,EAAIyS,qBAAqBvS,GAIlC,MAAMD,EAASF,EAAMkI,UAAU/H,GACzB+1B,EAAwB,MAAVh2B,OAAiB,EAASA,EAAO8R,mBACrD,OAAIkkB,GACFj2B,EAAIyS,qBAAqBvS,GAAY+1B,EAAY/1B,EAAUqP,EAAUwmB,GAC9D/1B,EAAIyS,qBAAqBvS,SAFlC,CAGC,IAGL0H,EAAQtK,SAAQqtB,IACdyK,EAAgB53B,KAAKmtB,GACrB0K,EAAgB1K,EAAOvqB,IAAMuqB,CAAM,IAS9B3qB,CAAG,IAEZ,OAAO41B,CACb,EACUG,EAAcT,EAAmBjN,EAAS7D,KAAM,GAYtD,OAXAuR,EAAYz4B,SAAQqtB,IAClByK,EAAgB53B,KAAKmtB,GACrB0K,EAAgB1K,EAAOvqB,IAAMuqB,CAAM,IAS9B,CACLnG,KAAMuR,EACNtpB,SAAU2oB,EACVzU,SAAU0U,EACX,GACA51B,EAAeM,EAAMS,QAAS,aAAc,GAAsB,KACnET,EAAM6f,QAAO,KACX7f,EAAMwf,qBACNxf,EAAMqiB,qBAAqB,GAC3B,IAEN,6CAeA,SAA+BtkB,GAC7B,OAAOiC,GAASpC,GAAK,IAAM,CAACoC,EAAM6D,WAAWoe,WAAYjiB,EAAMogB,2BAA4BpgB,EAAMS,QAAQ4e,0BAAuB1d,EAAY3B,EAAM6D,WAAWsb,YAAW,CAAC8C,EAAYqG,KACnL,IAAKA,EAAS7D,KAAK9mB,OACjB,OAAO2qB,EAET,MAAMnG,SACJA,EAAQD,UACRA,GACED,EACJ,IAAIwC,KACFA,EAAI/X,SACJA,EAAQkU,SACRA,GACE0H,EACJ,MAAM6N,EAAYhU,EAAWD,EACvBkU,EAAUD,EAAYhU,EAE5B,IAAIkU,EADJ5R,EAAOA,EAAK7M,MAAMue,EAAWC,GAS3BC,EAPGr2B,EAAMS,QAAQ4e,qBAOG,CAClBoF,OACA/X,WACAkU,YATkB8Q,GAAW,CAC7BjN,OACA/X,WACAkU,aASJyV,EAAkB3pB,SAAW,GAC7B,MAAMklB,EAAY3xB,IAChBo2B,EAAkB3pB,SAASjP,KAAKwC,GAC5BA,EAAI4H,QAAQlK,QACdsC,EAAI4H,QAAQtK,QAAQq0B,EACrB,EAGH,OADAyE,EAAkB5R,KAAKlnB,QAAQq0B,GACxByE,CAAiB,GACvB32B,EAAeM,EAAMS,QAAS,cACnC,sBAEA,WACE,OAAOT,GAASpC,GAAK,IAAM,CAACoC,EAAM6D,WAAWsoB,QAASnsB,EAAMqvB,0BAAyB,CAAClD,EAAS7D,KAC7F,IAAKA,EAAS7D,KAAK9mB,QAAuB,MAAXwuB,IAAmBA,EAAQxuB,OACxD,OAAO2qB,EAET,MAAMgO,EAAet2B,EAAM6D,WAAWsoB,QAChCoK,EAAiB,GAGjBC,EAAmBF,EAAahyB,QAAOyL,IAC3C,IAAI8jB,EACJ,OAAwD,OAAhDA,EAAmB7zB,EAAMkI,UAAU6H,EAAK1P,UAAe,EAASwzB,EAAiBtF,YAAY,IAEjGkI,EAAiB,CAAA,EACvBD,EAAiBj5B,SAAQm5B,IACvB,MAAMx2B,EAASF,EAAMkI,UAAUwuB,EAAUr2B,IACpCH,IACLu2B,EAAeC,EAAUr2B,IAAM,CAC7BgsB,cAAensB,EAAOa,UAAUsrB,cAChCsK,cAAez2B,EAAOa,UAAU41B,cAChCvK,UAAWlsB,EAAO2sB,gBACnB,IAEH,MAAM+J,EAAWnS,IAGf,MAAMoS,EAAapS,EAAKlhB,KAAItD,IAAQ,IAC/BA,MAiDL,OA/CA42B,EAAW9mB,MAAK,CAAC4b,EAAMC,KACrB,IAAK,IAAI1iB,EAAI,EAAGA,EAAIstB,EAAiB74B,OAAQuL,GAAK,EAAG,CACnD,IAAI4tB,EACJ,MAAMJ,EAAYF,EAAiBttB,GAC7B6tB,EAAaN,EAAeC,EAAUr2B,IACtCgsB,EAAgB0K,EAAW1K,cAC3B2K,EAA4E,OAAlEF,EAA+B,MAAbJ,OAAoB,EAASA,EAAUzJ,OAAgB6J,EACzF,IAAIG,EAAU,EAGd,GAAI5K,EAAe,CACjB,MAEM6K,OAAwBv1B,IAFfgqB,EAAKrrB,SAASo2B,EAAUr2B,IAGjC82B,OAAwBx1B,IAFfiqB,EAAKtrB,SAASo2B,EAAUr2B,IAGvC,GAAI62B,GAAcC,EAAY,CAC5B,GAAsB,UAAlB9K,EAA2B,OAAO6K,GAAc,EAAI,EACxD,GAAsB,SAAlB7K,EAA0B,OAAO6K,EAAa,GAAK,EACvDD,EAAUC,GAAcC,EAAa,EAAID,EAAa7K,GAAiBA,CACxE,CACF,CAMD,GALgB,IAAZ4K,IACFA,EAAUF,EAAW3K,UAAUT,EAAMC,EAAM8K,EAAUr2B,KAIvC,IAAZ42B,EAOF,OANID,IACFC,IAAY,GAEVF,EAAWJ,gBACbM,IAAY,GAEPA,CAEV,CACD,OAAOtL,EAAKjtB,MAAQktB,EAAKltB,KAAK,IAIhCm4B,EAAWt5B,SAAQ0C,IACjB,IAAI4S,EACJ0jB,EAAe94B,KAAKwC,GACgB,OAA/B4S,EAAe5S,EAAI4H,UAAoBgL,EAAalV,SACvDsC,EAAI4H,QAAU+uB,EAAS32B,EAAI4H,SAC5B,IAEIgvB,CAAU,EAEnB,MAAO,CACLpS,KAAMmS,EAAStO,EAAS7D,MACxB/X,SAAU6pB,EACV3V,SAAU0H,EAAS1H,SACpB,GACAlhB,EAAeM,EAAMS,QAAS,aAAc,GAAqB,IAAMT,EAAMqiB,wBAClF,+GAt3GA,WAEA,kJCtBO,SACL5hB,GAGA,MAAM22B,EAA+C,CACnDnrB,MAAO,CAAE,EACT0kB,cAAeA,OACfjwB,oBAAqB,QAClBD,IAIE42B,GAAYhE,EAAMiE,UAAS,KAAO,CACvCC,QAAS9zB,GAAmB2zB,QAIvBnrB,EAAO1P,GAAY82B,EAAMiE,UAAS,IAAMD,EAASE,QAAQ3oB,eAmBhE,OAfAyoB,EAASE,QAAQ/G,YAAWgH,IAAS,IAChCA,KACA/2B,EACHwL,MAAO,IACFA,KACAxL,EAAQwL,OAIb0kB,cAAez0B,IACbK,EAASL,GACTuE,MAAAA,EAAQkwB,eAARlwB,EAAQkwB,cAAgBz0B,EAAQ,MAI7Bm7B,EAASE,OAClB"}