import { addPointerEvent } from '../events/add-pointer-event.mjs';
import { pipe } from '../utils/pipe.mjs';
import { isDragActive } from './drag/utils/lock.mjs';
import { Feature } from '../motion/features/Feature.mjs';
import { frame } from '../frameloop/frame.mjs';

function addHoverEvent(node, isActive) {
    const eventName = "pointer" + (isActive ? "enter" : "leave");
    const callbackName = "onHover" + (isActive ? "Start" : "End");
    const handleEvent = (event, info) => {
        if (event.pointerType === "touch" || isDragActive())
            return;
        const props = node.getProps();
        if (node.animationState && props.whileHover) {
            node.animationState.setActive("whileHover", isActive);
        }
        if (props[callbackName]) {
            frame.update(() => props[callbackName](event, info));
        }
    };
    return addPointerEvent(node.current, eventName, handleEvent, {
        passive: !node.getProps()[callbackName],
    });
}
class HoverGesture extends Feature {
    mount() {
        this.unmount = pipe(addHoverEvent(this.node, true), addHoverEvent(this.node, false));
    }
    unmount() { }
}

export { HoverGesture };
