'use client';

import { useState } from 'react';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  TagIcon,
  ChartBarIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingState';
import { EmptyState } from '@/components/ui/EmptyState';

interface Category {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  icon: string;
  color: string;
  serviceCount: number;
  totalRevenue: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Mock data for categories
const mockCategories: Category[] = [
  {
    id: '1',
    name: 'Web Development',
    nameAr: 'تطوير الويب',
    description: 'Website and web application development services',
    descriptionAr: 'خدمات تطوير المواقع وتطبيقات الويب',
    icon: '💻',
    color: '#3B82F6',
    serviceCount: 45,
    totalRevenue: 125000,
    isActive: true,
    createdAt: '2024-01-15',
    updatedAt: '2024-06-10',
  },
  {
    id: '2',
    name: 'Mobile Development',
    nameAr: 'تطوير التطبيقات',
    description: 'iOS and Android mobile application development',
    descriptionAr: 'تطوير تطبيقات الهواتف الذكية لنظامي iOS و Android',
    icon: '📱',
    color: '#10B981',
    serviceCount: 32,
    totalRevenue: 98000,
    isActive: true,
    createdAt: '2024-01-20',
    updatedAt: '2024-06-08',
  },
  {
    id: '3',
    name: 'UI/UX Design',
    nameAr: 'تصميم واجهات المستخدم',
    description: 'User interface and user experience design',
    descriptionAr: 'تصميم واجهات المستخدم وتجربة المستخدم',
    icon: '🎨',
    color: '#F59E0B',
    serviceCount: 28,
    totalRevenue: 67000,
    isActive: true,
    createdAt: '2024-02-01',
    updatedAt: '2024-06-05',
  },
  {
    id: '4',
    name: 'Digital Marketing',
    nameAr: 'التسويق الرقمي',
    description: 'Digital marketing and social media management',
    descriptionAr: 'التسويق الرقمي وإدارة وسائل التواصل الاجتماعي',
    icon: '📈',
    color: '#EF4444',
    serviceCount: 21,
    totalRevenue: 45000,
    isActive: true,
    createdAt: '2024-02-10',
    updatedAt: '2024-06-03',
  },
  {
    id: '5',
    name: 'Content Writing',
    nameAr: 'كتابة المحتوى',
    description: 'Content writing and copywriting services',
    descriptionAr: 'خدمات كتابة المحتوى والنصوص الإعلانية',
    icon: '✍️',
    color: '#8B5CF6',
    serviceCount: 19,
    totalRevenue: 32000,
    isActive: false,
    createdAt: '2024-02-15',
    updatedAt: '2024-05-20',
  },
];

type FormMode = 'create' | 'edit' | 'view';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [formMode, setFormMode] = useState<FormMode>('create');

  // Filter categories
  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'active' && category.isActive) ||
                         (selectedStatus === 'inactive' && !category.isActive);

    return matchesSearch && matchesStatus;
  });

  // CRUD Functions
  const handleCreateCategory = () => {
    setSelectedCategory(null);
    setFormMode('create');
    setShowCategoryForm(true);
  };

  const handleViewCategory = (category: Category) => {
    setSelectedCategory(category);
    setFormMode('view');
    setShowCategoryForm(true);
  };

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setFormMode('edit');
    setShowCategoryForm(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
    }
  };

  const handleToggleStatus = async (categoryId: string) => {
    setCategories(prev => prev.map(cat => 
      cat.id === categoryId ? { ...cat, isActive: !cat.isActive } : cat
    ));
  };

  if (isLoading) {
    return <LoadingState message="جاري تحميل الفئات..." />;
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            إدارة الفئات
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            إدارة فئات الخدمات في منصة فريلا سوريا مع إمكانية الإضافة والتعديل والحذف
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:mr-16 sm:flex-none">
          <button
            type="button"
            onClick={handleCreateCategory}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PlusIcon className="h-4 w-4 ml-2" />
            إضافة فئة جديدة
          </button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TagIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الفئات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {categories.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TagIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    الفئات النشطة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {categories.filter(cat => cat.isActive).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الخدمات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {categories.reduce((sum, cat) => sum + cat.serviceCount, 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الإيرادات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${categories.reduce((sum, cat) => sum + cat.totalRevenue, 0).toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث في الفئات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Status filter */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as 'all' | 'active' | 'inactive')}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشطة</option>
              <option value="inactive">غير نشطة</option>
            </select>
          </div>
        </div>
      </div>

      {/* Categories table */}
      {filteredCategories.length === 0 ? (
        <EmptyState
          icon={<TagIcon className="h-12 w-12" />}
          title="لا توجد فئات"
          description="لم يتم العثور على فئات تطابق معايير البحث المحددة"
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الفئة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الإحصائيات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredCategories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div
                            className="h-10 w-10 rounded-lg flex items-center justify-center text-white text-lg"
                            style={{ backgroundColor: category.color }}
                          >
                            {category.icon}
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {category.nameAr}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {category.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        <div>{category.serviceCount} خدمة</div>
                        <div className="text-gray-500 dark:text-gray-400">
                          ${category.totalRevenue.toLocaleString()} إيرادات
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleStatus(category.id)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          category.isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}
                      >
                        {category.isActive ? 'نشطة' : 'غير نشطة'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(category.createdAt).toLocaleDateString('ar-SY')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleViewCategory(category)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title="عرض التفاصيل"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditCategory(category)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          title="تعديل"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="حذف"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Category Form Modal */}
      {showCategoryForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {formMode === 'create' ? 'إضافة فئة جديدة' :
                 formMode === 'edit' ? 'تعديل الفئة' : 'تفاصيل الفئة'}
              </h3>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      الاسم بالعربية
                    </label>
                    <input
                      type="text"
                      defaultValue={selectedCategory?.nameAr || ''}
                      disabled={formMode === 'view'}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      الاسم بالإنجليزية
                    </label>
                    <input
                      type="text"
                      defaultValue={selectedCategory?.name || ''}
                      disabled={formMode === 'view'}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      الأيقونة
                    </label>
                    <input
                      type="text"
                      defaultValue={selectedCategory?.icon || ''}
                      disabled={formMode === 'view'}
                      placeholder="مثال: 💻"
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      اللون
                    </label>
                    <input
                      type="color"
                      defaultValue={selectedCategory?.color || '#3B82F6'}
                      disabled={formMode === 'view'}
                      className="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الوصف بالعربية
                  </label>
                  <textarea
                    rows={3}
                    defaultValue={selectedCategory?.descriptionAr || ''}
                    disabled={formMode === 'view'}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الوصف بالإنجليزية
                  </label>
                  <textarea
                    rows={3}
                    defaultValue={selectedCategory?.description || ''}
                    disabled={formMode === 'view'}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                {formMode !== 'create' && (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      defaultChecked={selectedCategory?.isActive}
                      disabled={formMode === 'view'}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label className="mr-2 block text-sm text-gray-900 dark:text-white">
                      فئة نشطة
                    </label>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse mt-6">
                <button
                  onClick={() => setShowCategoryForm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700"
                >
                  إلغاء
                </button>
                {formMode !== 'view' && (
                  <button
                    className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    {formMode === 'create' ? 'إضافة' : 'حفظ التغييرات'}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
