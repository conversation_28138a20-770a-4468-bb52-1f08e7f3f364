import PropTypes from 'prop-types';
import type { Requireable, Validator } from 'prop-types';
import type { Range, View } from './types.js';
export declare const isCalendarType: PropTypes.Requireable<"gregory" | "hebrew" | "islamic" | "iso8601" | "Arabic" | "Hebrew" | "ISO 8601" | "US">;
export declare const isClassName: PropTypes.Requireable<NonNullable<string | (string | null | undefined)[] | null | undefined>>;
export declare const isMinDate: Validator<Date | null | undefined>;
export declare const isMaxDate: Validator<Date | null | undefined>;
export declare const isRef: PropTypes.Requireable<NonNullable<((...args: any[]) => any) | Required<PropTypes.InferProps<{
    current: PropTypes.Requireable<any>;
}>> | null | undefined>>;
export declare const isValue: PropTypes.Requireable<NonNullable<Date | Range<Date> | null | undefined>>;
export declare const isViews: PropTypes.Requireable<(string | null | undefined)[]>;
export declare const isView: Requireable<View>;
export declare const rangeOf: <T>(type: PropTypes.Requireable<T>) => PropTypes.Requireable<Range<T>>;
export declare const tileGroupProps: {
    activeStartDate: PropTypes.Validator<Date>;
    hover: PropTypes.Requireable<Date>;
    locale: PropTypes.Requireable<string>;
    maxDate: PropTypes.Validator<Date | null | undefined>;
    minDate: PropTypes.Validator<Date | null | undefined>;
    onClick: PropTypes.Requireable<(...args: any[]) => any>;
    onMouseOver: PropTypes.Requireable<(...args: any[]) => any>;
    tileClassName: PropTypes.Requireable<NonNullable<NonNullable<string | (string | null | undefined)[] | null | undefined> | ((...args: any[]) => any) | null | undefined>>;
    tileContent: PropTypes.Requireable<NonNullable<((...args: any[]) => any) | PropTypes.ReactNodeLike>>;
    value: PropTypes.Requireable<NonNullable<Date | Range<Date> | null | undefined>>;
    valueType: PropTypes.Validator<NonNullable<"century" | "decade" | "year" | "month" | "day">>;
};
export declare const tileProps: {
    activeStartDate: PropTypes.Validator<Date>;
    classes: PropTypes.Validator<string[]>;
    date: PropTypes.Validator<Date>;
    locale: PropTypes.Requireable<string>;
    maxDate: PropTypes.Validator<Date | null | undefined>;
    minDate: PropTypes.Validator<Date | null | undefined>;
    onClick: PropTypes.Requireable<(...args: any[]) => any>;
    onMouseOver: PropTypes.Requireable<(...args: any[]) => any>;
    style: PropTypes.Requireable<{
        [x: string]: NonNullable<string | number | null | undefined> | null | undefined;
    }>;
    tileClassName: PropTypes.Requireable<NonNullable<NonNullable<string | (string | null | undefined)[] | null | undefined> | ((...args: any[]) => any) | null | undefined>>;
    tileContent: PropTypes.Requireable<NonNullable<((...args: any[]) => any) | PropTypes.ReactNodeLike>>;
    tileDisabled: PropTypes.Requireable<(...args: any[]) => any>;
};
