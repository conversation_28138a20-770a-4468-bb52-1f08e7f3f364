{"version": 3, "names": ["getInvertedMultiplier", "gestureDirection", "I18nManager", "getConstants", "isRTL"], "sourceRoot": "../../../src", "sources": ["utils/getInvertedMultiplier.tsx"], "mappings": ";;;;;;AAAA;AAIe,SAASA,qBAAqB,CAC3CC,gBAAkC,EAC1B;EACR,QAAQA,gBAAgB;IACtB,KAAK,UAAU;MACb,OAAO,CAAC;IACV,KAAK,mBAAmB;MACtB,OAAO,CAAC,CAAC;IACX,KAAK,YAAY;MACf,OAAOC,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IAClD,KAAK,qBAAqB;MACxB,OAAOF,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAAC;AAEvD"}