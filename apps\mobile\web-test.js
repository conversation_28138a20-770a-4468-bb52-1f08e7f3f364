const express = require('express');
const path = require('path');

const app = express();
const PORT = 3001;

// Serve static files
app.use(express.static(path.join(__dirname, 'web-build')));

// Basic API mock for testing
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Freela Syria Mobile API Mock' });
});

// Serve the app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'web-build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🌐 Freela Syria Mobile (Web) running at http://localhost:${PORT}`);
  console.log('📱 This is a web version for testing the mobile app UI');
});