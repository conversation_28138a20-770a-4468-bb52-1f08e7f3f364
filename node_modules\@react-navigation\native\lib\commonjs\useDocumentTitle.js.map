{"version": 3, "names": ["useDocumentTitle", "ref", "enabled", "formatter", "options", "route", "title", "name", "React", "useEffect", "navigation", "current", "getCurrentOptions", "getCurrentRoute", "document", "addListener", "e", "data"], "sourceRoot": "../../src", "sources": ["useDocumentTitle.tsx"], "mappings": ";;;;;;AAIA;AAA+B;AAAA;AAI/B;AACA;AACA;AACe,SAASA,gBAAgB,CACtCC,GAA2D,EAK3D;EAAA,IAJA;IACEC,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG,CAACC,OAAO,EAAEC,KAAK,KAAK,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,MAAID,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,IAAI;EACzC,CAAC,uEAAG,CAAC,CAAC;EAE5BC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACP,OAAO,EAAE;MACZ;IACF;IAEA,MAAMQ,UAAU,GAAGT,GAAG,CAACU,OAAO;IAE9B,IAAID,UAAU,EAAE;MACd,MAAMJ,KAAK,GAAGH,SAAS,CACrBO,UAAU,CAACE,iBAAiB,EAAE,EAC9BF,UAAU,CAACG,eAAe,EAAE,CAC7B;MAEDC,QAAQ,CAACR,KAAK,GAAGA,KAAK;IACxB;IAEA,OAAOI,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,CAAC,SAAS,EAAGC,CAAC,IAAK;MAC/C,MAAMV,KAAK,GAAGH,SAAS,CAACa,CAAC,CAACC,IAAI,CAACb,OAAO,EAAEM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,eAAe,EAAE,CAAC;MAEtEC,QAAQ,CAACR,KAAK,GAAGA,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}