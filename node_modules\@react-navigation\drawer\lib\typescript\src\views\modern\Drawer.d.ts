import type { DrawerProps } from '../../types';
export default function Drawer({ dimensions, drawerPosition, drawerStyle, drawerType, gestureHandlerProps, hideStatusBarOnOpen, keyboardDismissMode, onClose, onOpen, open, overlayStyle, renderDrawerContent, renderSceneContent, statusBarAnimation, swipeDistanceThreshold, swipeEdgeWidth, swipeEnabled, swipeVelocityThreshold, overlayAccessibilityLabel, }: DrawerProps): JSX.Element;
//# sourceMappingURL=Drawer.d.ts.map