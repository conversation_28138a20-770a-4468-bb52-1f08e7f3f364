load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "shell",
    srcs = glob(["**/*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/fresco/fresco-react-native:imagepipeline"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:appcompat"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react:react"),
        react_native_target("java/com/facebook/react/animated:animated"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/devsupport:devsupport"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/module/model:model"),
        react_native_target("java/com/facebook/react/modules/accessibilityinfo:accessibilityinfo"),
        react_native_target("java/com/facebook/react/modules/appearance:appearance"),
        react_native_target("java/com/facebook/react/modules/appstate:appstate"),
        react_native_target("java/com/facebook/react/modules/blob:blob"),
        react_native_target("java/com/facebook/react/modules/camera:camera"),
        react_native_target("java/com/facebook/react/modules/clipboard:clipboard"),
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/modules/debug:debug"),
        react_native_target("java/com/facebook/react/modules/devtoolssettings:devtoolssettings"),
        react_native_target("java/com/facebook/react/modules/dialog:dialog"),
        react_native_target("java/com/facebook/react/modules/fresco:fresco"),
        react_native_target("java/com/facebook/react/modules/i18nmanager:i18nmanager"),
        react_native_target("java/com/facebook/react/modules/image:image"),
        react_native_target("java/com/facebook/react/modules/intent:intent"),
        react_native_target("java/com/facebook/react/modules/network:network"),
        react_native_target("java/com/facebook/react/modules/permissions:permissions"),
        react_native_target("java/com/facebook/react/modules/share:share"),
        react_native_target("java/com/facebook/react/modules/sound:sound"),
        react_native_target("java/com/facebook/react/modules/statusbar:statusbar"),
        react_native_target("java/com/facebook/react/modules/toast:toast"),
        react_native_target("java/com/facebook/react/modules/vibration:vibration"),
        react_native_target("java/com/facebook/react/modules/websocket:websocket"),
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/views/drawer:drawer"),
        react_native_target("java/com/facebook/react/views/image:image"),
        react_native_target("java/com/facebook/react/views/modal:modal"),
        react_native_target("java/com/facebook/react/views/progressbar:progressbar"),
        react_native_target("java/com/facebook/react/views/scroll:scroll"),
        react_native_target("java/com/facebook/react/views/swiperefresh:swiperefresh"),
        react_native_target("java/com/facebook/react/views/switchview:switchview"),
        react_native_target("java/com/facebook/react/views/text:text"),
        react_native_target("java/com/facebook/react/views/text/frescosupport:frescosupport"),
        react_native_target("java/com/facebook/react/views/textinput:textinput"),
        react_native_target("java/com/facebook/react/views/unimplementedview:unimplementedview"),
        react_native_target("java/com/facebook/react/views/view:view"),
        react_native_target("res:shell"),
    ],
)
