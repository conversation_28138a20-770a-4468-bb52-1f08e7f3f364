{"version": 3, "names": ["tryInstallAppOnDevice", "args", "adbPath", "device", "androidProject", "selectedTask", "appName", "sourceDir", "defaultVariant", "mode", "toLowerCase", "variantFromSelectedTask", "replace", "split", "variantPath", "join", "variantAppName", "pathToApk", "binaryPath", "buildDirectory", "apkFile", "getInstallApkName", "installArgs", "user", "undefined", "push", "adbArgs", "logger", "info", "debug", "execa", "sync", "stdio", "error", "CLIError", "variant", "availableCPUs", "adb", "getAvailableCPUs", "availableCPU", "concat", "apkName", "fs", "existsSync", "Error"], "sources": ["../../../src/commands/runAndroid/tryInstallAppOnDevice.ts"], "sourcesContent": ["import execa from 'execa';\nimport fs from 'fs';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\n\nimport adb from './adb';\nimport type {AndroidProject, Flags} from './';\n\nfunction tryInstallAppOnDevice(\n  args: Flags,\n  adbPath: string,\n  device: string,\n  androidProject: AndroidProject,\n  selectedTask?: string,\n) {\n  try {\n    // \"app\" is usually the default value for Android apps with only 1 app\n    const {appName, sourceDir} = androidProject;\n\n    const defaultVariant = (args.mode || 'debug').toLowerCase();\n\n    // handle if selected task from interactive mode includes build flavour as well, eg. installProductionDebug should create ['production','debug'] array\n    const variantFromSelectedTask = selectedTask\n      ?.replace('install', '')\n      .split(/(?=[A-Z])/);\n\n    // create path to output file, eg. `production/debug`\n    const variantPath =\n      variantFromSelectedTask?.join('/')?.toLowerCase() ?? defaultVariant;\n    // create output file name, eg. `production-debug`\n    const variantAppName =\n      variantFromSelectedTask?.join('-')?.toLowerCase() ?? defaultVariant;\n\n    let pathToApk;\n    if (!args.binaryPath) {\n      const buildDirectory = `${sourceDir}/${appName}/build/outputs/apk/${variantPath}`;\n      const apkFile = getInstallApkName(\n        appName,\n        adbPath,\n        variantAppName,\n        device,\n        buildDirectory,\n      );\n      pathToApk = `${buildDirectory}/${apkFile}`;\n    } else {\n      pathToApk = args.binaryPath;\n    }\n\n    const installArgs = ['-s', device, 'install', '-r', '-d'];\n    if (args.user !== undefined) {\n      installArgs.push('--user', `${args.user}`);\n    }\n    const adbArgs = [...installArgs, pathToApk];\n    logger.info(`Installing the app on the device \"${device}\"...`);\n    logger.debug(`Running command \"cd android && adb ${adbArgs.join(' ')}\"`);\n    execa.sync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (error) {\n    throw new CLIError(\n      'Failed to install the app on the device.',\n      error as any,\n    );\n  }\n}\n\nfunction getInstallApkName(\n  appName: string,\n  adbPath: string,\n  variant: string,\n  device: string,\n  buildDirectory: string,\n) {\n  const availableCPUs = adb.getAvailableCPUs(adbPath, device);\n\n  // check if there is an apk file like app-armeabi-v7a-debug.apk\n  for (const availableCPU of availableCPUs.concat('universal')) {\n    const apkName = `${appName}-${availableCPU}-${variant}.apk`;\n    if (fs.existsSync(`${buildDirectory}/${apkName}`)) {\n      return apkName;\n    }\n  }\n\n  // check if there is a default file like app-debug.apk\n  const apkName = `${appName}-${variant}.apk`;\n  if (fs.existsSync(`${buildDirectory}/${apkName}`)) {\n    return apkName;\n  }\n\n  throw new Error('Could not find the correct install APK file.');\n}\n\nexport default tryInstallAppOnDevice;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAAwB;AAGxB,SAASA,qBAAqB,CAC5BC,IAAW,EACXC,OAAe,EACfC,MAAc,EACdC,cAA8B,EAC9BC,YAAqB,EACrB;EACA,IAAI;IAAA;IACF;IACA,MAAM;MAACC,OAAO;MAAEC;IAAS,CAAC,GAAGH,cAAc;IAE3C,MAAMI,cAAc,GAAG,CAACP,IAAI,CAACQ,IAAI,IAAI,OAAO,EAAEC,WAAW,EAAE;;IAE3D;IACA,MAAMC,uBAAuB,GAAGN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CACxCO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACvBC,KAAK,CAAC,WAAW,CAAC;;IAErB;IACA,MAAMC,WAAW,GACf,CAAAH,uBAAuB,aAAvBA,uBAAuB,gDAAvBA,uBAAuB,CAAEI,IAAI,CAAC,GAAG,CAAC,0DAAlC,sBAAoCL,WAAW,EAAE,KAAIF,cAAc;IACrE;IACA,MAAMQ,cAAc,GAClB,CAAAL,uBAAuB,aAAvBA,uBAAuB,iDAAvBA,uBAAuB,CAAEI,IAAI,CAAC,GAAG,CAAC,2DAAlC,uBAAoCL,WAAW,EAAE,KAAIF,cAAc;IAErE,IAAIS,SAAS;IACb,IAAI,CAAChB,IAAI,CAACiB,UAAU,EAAE;MACpB,MAAMC,cAAc,GAAI,GAAEZ,SAAU,IAAGD,OAAQ,sBAAqBQ,WAAY,EAAC;MACjF,MAAMM,OAAO,GAAGC,iBAAiB,CAC/Bf,OAAO,EACPJ,OAAO,EACPc,cAAc,EACdb,MAAM,EACNgB,cAAc,CACf;MACDF,SAAS,GAAI,GAAEE,cAAe,IAAGC,OAAQ,EAAC;IAC5C,CAAC,MAAM;MACLH,SAAS,GAAGhB,IAAI,CAACiB,UAAU;IAC7B;IAEA,MAAMI,WAAW,GAAG,CAAC,IAAI,EAAEnB,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IACzD,IAAIF,IAAI,CAACsB,IAAI,KAAKC,SAAS,EAAE;MAC3BF,WAAW,CAACG,IAAI,CAAC,QAAQ,EAAG,GAAExB,IAAI,CAACsB,IAAK,EAAC,CAAC;IAC5C;IACA,MAAMG,OAAO,GAAG,CAAC,GAAGJ,WAAW,EAAEL,SAAS,CAAC;IAC3CU,kBAAM,CAACC,IAAI,CAAE,qCAAoCzB,MAAO,MAAK,CAAC;IAC9DwB,kBAAM,CAACE,KAAK,CAAE,sCAAqCH,OAAO,CAACX,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;IACxEe,gBAAK,CAACC,IAAI,CAAC7B,OAAO,EAAEwB,OAAO,EAAE;MAACM,KAAK,EAAE;IAAS,CAAC,CAAC;EAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,0CAA0C,EAC1CD,KAAK,CACN;EACH;AACF;AAEA,SAASZ,iBAAiB,CACxBf,OAAe,EACfJ,OAAe,EACfiC,OAAe,EACfhC,MAAc,EACdgB,cAAsB,EACtB;EACA,MAAMiB,aAAa,GAAGC,YAAG,CAACC,gBAAgB,CAACpC,OAAO,EAAEC,MAAM,CAAC;;EAE3D;EACA,KAAK,MAAMoC,YAAY,IAAIH,aAAa,CAACI,MAAM,CAAC,WAAW,CAAC,EAAE;IAC5D,MAAMC,OAAO,GAAI,GAAEnC,OAAQ,IAAGiC,YAAa,IAAGJ,OAAQ,MAAK;IAC3D,IAAIO,aAAE,CAACC,UAAU,CAAE,GAAExB,cAAe,IAAGsB,OAAQ,EAAC,CAAC,EAAE;MACjD,OAAOA,OAAO;IAChB;EACF;;EAEA;EACA,MAAMA,OAAO,GAAI,GAAEnC,OAAQ,IAAG6B,OAAQ,MAAK;EAC3C,IAAIO,aAAE,CAACC,UAAU,CAAE,GAAExB,cAAe,IAAGsB,OAAQ,EAAC,CAAC,EAAE;IACjD,OAAOA,OAAO;EAChB;EAEA,MAAM,IAAIG,KAAK,CAAC,8CAA8C,CAAC;AACjE;AAAC,eAEc5C,qBAAqB;AAAA"}