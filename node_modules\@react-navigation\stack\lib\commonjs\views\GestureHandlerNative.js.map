{"version": 3, "names": ["PanGestureHandler", "props", "gestureRef", "React", "useRef"], "sourceRoot": "../../../src", "sources": ["views/GestureHandlerNative.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AAKA;AAAyE;AAAA;AAAA;AAAA;AAElE,SAASA,iBAAiB,CAACC,KAAkC,EAAE;EACpE,MAAMC,UAAU,GAAGC,KAAK,CAACC,MAAM,CAA0B,IAAI,CAAC;EAE9D,oBACE,oBAAC,iCAAwB,CAAC,QAAQ;IAAC,KAAK,EAAEF;EAAW,gBACnD,oBAAC,4CAAuB,eAAKD,KAAK;IAAE,GAAG,EAAEC;EAAW,GAAG,CACrB;AAExC"}