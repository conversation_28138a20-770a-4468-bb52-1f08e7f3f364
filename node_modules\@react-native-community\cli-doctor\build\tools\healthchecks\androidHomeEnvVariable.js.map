{"version": 3, "names": ["URLS", "darwin", "win32", "linux", "label", "description", "platform", "process", "message", "chalk", "dim", "getDiagnostics", "needsToBeFixed", "env", "ANDROID_HOME", "runAutomaticFix", "loader", "logManualInstallation", "succeed", "fail"], "sources": ["../../../src/tools/healthchecks/androidHomeEnvVariable.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport {HealthCheckInterface} from '../../types';\n\n// List of answers on how to set `ANDROID_HOME` for each platform\nconst URLS = {\n  darwin: 'https://stackoverflow.com/a/28296325/4252781',\n  win32: 'https://stackoverflow.com/a/54888107/4252781',\n  linux: 'https://stackoverflow.com/a/39228100/4252781',\n};\n\nconst label = 'ANDROID_HOME';\nconst description =\n  'Environment variable that points to your Android SDK installation';\n\n// Force the options for the platform to avoid providing a link\n// for `ANDROID_HOME` for every platform NodeJS supports\nconst platform = process.platform as 'darwin' | 'win32' | 'linux';\n\nconst message = `Read more about how to set the ${label} at ${chalk.dim(\n  URLS[platform],\n)}`;\n\nexport default {\n  label,\n  description,\n  getDiagnostics: async () => ({\n    needsToBeFixed: !process.env.ANDROID_HOME,\n  }),\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    // Variable could have been added if installing Android Studio so double checking\n    if (process.env.ANDROID_HOME) {\n      loader.succeed();\n\n      return;\n    }\n\n    loader.fail();\n\n    logManualInstallation({\n      message,\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAG1B;AACA,MAAMA,IAAI,GAAG;EACXC,MAAM,EAAE,8CAA8C;EACtDC,KAAK,EAAE,8CAA8C;EACrDC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,KAAK,GAAG,cAAc;AAC5B,MAAMC,WAAW,GACf,mEAAmE;;AAErE;AACA;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACD,QAAwC;AAEjE,MAAME,OAAO,GAAI,kCAAiCJ,KAAM,OAAMK,gBAAK,CAACC,GAAG,CACrEV,IAAI,CAACM,QAAQ,CAAC,CACd,EAAC;AAAC,eAEW;EACbF,KAAK;EACLC,WAAW;EACXM,cAAc,EAAE,aAAa;IAC3BC,cAAc,EAAE,CAACL,OAAO,CAACM,GAAG,CAACC;EAC/B,CAAC,CAAC;EACFC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1D;IACA,IAAIV,OAAO,CAACM,GAAG,CAACC,YAAY,EAAE;MAC5BE,MAAM,CAACE,OAAO,EAAE;MAEhB;IACF;IAEAF,MAAM,CAACG,IAAI,EAAE;IAEbF,qBAAqB,CAAC;MACpBT;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}