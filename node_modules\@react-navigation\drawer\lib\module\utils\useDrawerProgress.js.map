{"version": 3, "names": ["React", "DrawerProgressContext", "useDrawerProgress", "progress", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useDrawerProgress.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,qBAAqB,MAAM,yBAAyB;AAE3D,eAAe,SAASC,iBAAiB,GAEf;EACxB,MAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,qBAAqB,CAAC;EAExD,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,sEAAsE,CACvE;EACH;EAEA,OAAOH,QAAQ;AACjB"}