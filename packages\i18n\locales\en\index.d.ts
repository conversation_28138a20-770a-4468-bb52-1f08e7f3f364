declare const _default: {
    common: {
        navigation: {
            home: string;
            search: string;
            services: string;
            bookings: string;
            messages: string;
            profile: string;
            dashboard: string;
            settings: string;
            help: string;
            logout: string;
        };
        actions: {
            save: string;
            cancel: string;
            delete: string;
            edit: string;
            submit: string;
            back: string;
            next: string;
            previous: string;
            confirm: string;
            retry: string;
            close: string;
            open: string;
            view: string;
            download: string;
            upload: string;
            share: string;
            copy: string;
            search: string;
            filter: string;
            sort: string;
            refresh: string;
        };
        status: {
            loading: string;
            success: string;
            error: string;
            pending: string;
            completed: string;
            cancelled: string;
            active: string;
            inactive: string;
            approved: string;
            rejected: string;
            draft: string;
        };
        time: {
            now: string;
            today: string;
            yesterday: string;
            tomorrow: string;
            thisWeek: string;
            lastWeek: string;
            thisMonth: string;
            lastMonth: string;
            minute: string;
            minutes: string;
            hour: string;
            hours: string;
            day: string;
            days: string;
            week: string;
            weeks: string;
            month: string;
            months: string;
            year: string;
            years: string;
        };
        weekdays: {
            monday: string;
            tuesday: string;
            wednesday: string;
            thursday: string;
            friday: string;
            saturday: string;
            sunday: string;
        };
        months: {
            january: string;
            february: string;
            march: string;
            april: string;
            may: string;
            june: string;
            july: string;
            august: string;
            september: string;
            october: string;
            november: string;
            december: string;
        };
        currency: {
            usd: string;
            syp: string;
            symbol: {
                usd: string;
                syp: string;
            };
        };
        location: {
            governorates: {
                damascus: string;
                aleppo: string;
                homs: string;
                hama: string;
                latakia: string;
                tartus: string;
                idlib: string;
                daraa: string;
                sweida: string;
                quneitra: string;
                raqqa: string;
                deir_ez_zor: string;
                hasaka: string;
                damascus_countryside: string;
            };
        };
        validation: {
            required: string;
            email: string;
            phone: string;
            password: string;
            confirmPassword: string;
            minLength: string;
            maxLength: string;
            min: string;
            max: string;
            numeric: string;
            url: string;
        };
        errors: {
            general: string;
            network: string;
            unauthorized: string;
            forbidden: string;
            notFound: string;
            serverError: string;
            timeout: string;
            offline: string;
        };
        pagination: {
            previous: string;
            next: string;
            first: string;
            last: string;
            page: string;
            of: string;
            showing: string;
            to: string;
            results: string;
        };
        fileUpload: {
            dragDrop: string;
            selectFile: string;
            selectFiles: string;
            maxSize: string;
            allowedTypes: string;
            uploading: string;
            uploadSuccess: string;
            uploadError: string;
            fileTooLarge: string;
            invalidType: string;
        };
    };
    auth: {};
    expert: {};
    client: {};
};
export default _default;
//# sourceMappingURL=index.d.ts.map