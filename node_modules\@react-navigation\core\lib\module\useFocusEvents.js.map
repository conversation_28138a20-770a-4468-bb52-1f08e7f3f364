{"version": 3, "names": ["React", "NavigationContext", "useFocusEvents", "state", "emitter", "navigation", "useContext", "lastFocusedKeyRef", "useRef", "currentFocusedKey", "routes", "index", "key", "useEffect", "addListener", "current", "emit", "type", "target", "undefined", "lastFocused<PERSON>ey", "isFocused"], "sourceRoot": "../../src", "sources": ["useFocusEvents.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,iBAAiB,MAAM,qBAAqB;AASnD;AACA;AACA;AACA,eAAe,SAASC,cAAc,OAGnB;EAAA,IAHmD;IACpEC,KAAK;IACLC;EACc,CAAC;EACf,MAAMC,UAAU,GAAGL,KAAK,CAACM,UAAU,CAACL,iBAAiB,CAAC;EACtD,MAAMM,iBAAiB,GAAGP,KAAK,CAACQ,MAAM,EAAsB;EAE5D,MAAMC,iBAAiB,GAAGN,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAACC,GAAG;;EAEvD;EACA;EACAZ,KAAK,CAACa,SAAS,CACb,MACER,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAAC,OAAO,EAAE,MAAM;IACrCP,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;IAC7CL,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDL,KAAK,CAACa,SAAS,CACb,MACER,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAAC,MAAM,EAAE,MAAM;IACpCP,iBAAiB,CAACQ,OAAO,GAAGI,SAAS;IACrCf,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC3D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDL,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,MAAMO,cAAc,GAAGb,iBAAiB,CAACQ,OAAO;IAEhDR,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;;IAE7C;IACA;IACA,IAAIW,cAAc,KAAKD,SAAS,IAAI,CAACd,UAAU,EAAE;MAC/CD,OAAO,CAACY,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC5D;;IAEA;IACA;IACA,IACEW,cAAc,KAAKX,iBAAiB,IACpC,EAAEJ,UAAU,GAAGA,UAAU,CAACgB,SAAS,EAAE,GAAG,IAAI,CAAC,EAC7C;MACA;IACF;IAEA,IAAID,cAAc,KAAKD,SAAS,EAAE;MAChC;MACA;IACF;IAEAf,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAEE;IAAe,CAAC,CAAC;IACtDhB,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC9C"}