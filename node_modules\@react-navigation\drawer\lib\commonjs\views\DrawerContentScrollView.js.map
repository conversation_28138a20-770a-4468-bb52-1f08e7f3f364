{"version": 3, "names": ["DrawerContentScrollView", "ref", "contentContainerStyle", "style", "children", "rest", "drawerPosition", "React", "useContext", "DrawerPositionContext", "insets", "useSafeAreaInsets", "isRight", "I18nManager", "getConstants", "isRTL", "paddingTop", "top", "paddingStart", "left", "paddingEnd", "right", "styles", "container", "forwardRef", "StyleSheet", "create", "flex"], "sourceRoot": "../../../src", "sources": ["views/DrawerContentScrollView.tsx"], "mappings": ";;;;;;AAAA;AACA;AAMA;AAEA;AAAmE;AAAA;AAAA;AAAA;AAMnE,SAASA,uBAAuB,OAE9BC,GAA2B,EAC3B;EAAA,IAFA;IAAEC,qBAAqB;IAAEC,KAAK;IAAEC,QAAQ;IAAE,GAAGC;EAAY,CAAC;EAG1D,MAAMC,cAAc,GAAGC,KAAK,CAACC,UAAU,CAACC,8BAAqB,CAAC;EAC9D,MAAMC,MAAM,GAAG,IAAAC,6CAAiB,GAAE;EAElC,MAAMC,OAAO,GAAGC,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAC5CT,cAAc,KAAK,MAAM,GACzBA,cAAc,KAAK,OAAO;EAE9B,oBACE,oBAAC,uBAAU,eACLD,IAAI;IACR,GAAG,EAAEJ,GAAI;IACT,qBAAqB,EAAE,CACrB;MACEe,UAAU,EAAEN,MAAM,CAACO,GAAG,GAAG,CAAC;MAC1BC,YAAY,EAAE,CAACN,OAAO,GAAGF,MAAM,CAACS,IAAI,GAAG,CAAC;MACxCC,UAAU,EAAER,OAAO,GAAGF,MAAM,CAACW,KAAK,GAAG;IACvC,CAAC,EACDnB,qBAAqB,CACrB;IACF,KAAK,EAAE,CAACoB,MAAM,CAACC,SAAS,EAAEpB,KAAK;EAAE,IAEhCC,QAAQ,CACE;AAEjB;AAAC,4BAEcG,KAAK,CAACiB,UAAU,CAACxB,uBAAuB,CAAC;AAAA;AAExD,MAAMsB,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}