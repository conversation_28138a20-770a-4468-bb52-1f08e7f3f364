const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Freela Syria Mobile Development Server...');
console.log('📱 This will start the Metro bundler for React Native development');
console.log('');

try {
  // Start Metro bundler
  execSync('npx react-native start --reset-cache', { 
    stdio: 'inherit',
    cwd: __dirname
  });
} catch (error) {
  console.error('❌ Failed to start development server:', error.message);
  console.log('');
  console.log('💡 Troubleshooting tips:');
  console.log('   1. Make sure you have React Native CLI installed: npm install -g @react-native-community/cli');
  console.log('   2. Check if all dependencies are installed: npm install --legacy-peer-deps');
  console.log('   3. Clear Metro cache: npx react-native start --reset-cache');
  console.log('   4. For Android development, make sure Android Studio and SDK are installed');
  process.exit(1);
}