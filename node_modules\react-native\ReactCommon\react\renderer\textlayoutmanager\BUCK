load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "YOGA_CXX_TARGET",
    "fb_xplat_cxx_test",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_target",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

oncall("react_native")

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "textlayoutmanager",
    srcs = glob(
        [
            "*.cpp",
        ],
    ),
    headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "",
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/renderer/textlayoutmanager",
    ),
    compiler_flags_pedantic = True,
    cxx_exported_headers = subdir_glob(
        [
            ("platform/cxx", "*.h"),
        ],
        prefix = "react/renderer/textlayoutmanager",
    ),
    cxx_headers = subdir_glob(
        [
            ("platform/cxx", "**/*.h"),
        ],
        prefix = "",
    ),
    cxx_srcs = glob(
        [
            "platform/cxx/**/*.cpp",
        ],
    ),
    cxx_tests = [":tests"],
    fbandroid_deps = [
        react_native_target("jni/react/jni:jni"),
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
    ],
    fbandroid_exported_headers = subdir_glob(
        [
            ("platform/android/react/renderer/textlayoutmanager", "**/*.h"),
        ],
        prefix = "react/renderer/textlayoutmanager",
    ),
    fbandroid_headers = subdir_glob(
        [
            ("platform/android/react/renderer/textlayoutmanager", "**/*.h"),
        ],
        prefix = "",
    ),
    fbandroid_srcs = glob(
        [
            "platform/android/react/renderer/textlayoutmanager/**/*.cpp",
        ],
    ),
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    ios_deps = [
    ],
    ios_exported_headers = subdir_glob(
        [
            ("platform/ios/react/renderer/textlayoutmanager", "*.h"),
        ],
        prefix = "react/renderer/textlayoutmanager",
    ),
    ios_frameworks = [
        "$SDKROOT/System/Library/Frameworks/CoreGraphics.framework",
        "$SDKROOT/System/Library/Frameworks/Foundation.framework",
        "$SDKROOT/System/Library/Frameworks/UIKit.framework",
    ],
    ios_headers = subdir_glob(
        [
            ("platform/ios/react/renderer/textlayoutmanager", "**/*.h"),
        ],
        prefix = "",
    ),
    ios_srcs = glob(
        [
            "platform/ios/**/*.cpp",
            "platform/ios/**/*.mm",
        ],
    ),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        YOGA_CXX_TARGET,
        react_native_xplat_target("react/renderer/attributedstring:attributedstring"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/utils:utils"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/mounting:mounting"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
    ],
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["tests/**/*.cpp"]),
    headers = glob(["tests/**/*.h"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    contacts = ["<EMAIL>"],
    platforms = (
        # `Apple` and `Android` flavors are disabled because the module (built with those flavors) requires Emulator/Simulator (which is expensive and slow). At the same time, we don't really have tests here.
        # ANDROID,
        # APPLE,
        CXX,
    ),
    deps = [
        ":textlayoutmanager",
        "//xplat/third-party/gmock:gtest",
    ],
)
