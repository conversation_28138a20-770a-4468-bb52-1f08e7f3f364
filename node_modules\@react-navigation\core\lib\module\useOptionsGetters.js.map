{"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationStateContext", "useOptionsGetters", "key", "options", "navigation", "optionsRef", "useRef", "optionsGettersFromChildRef", "onOptionsChange", "useContext", "addOptionsGetter", "parentAddOptionsGetter", "optionsChangeListener", "useCallback", "isFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "current", "length", "useEffect", "addListener", "getOptionsFromListener", "hasOwnProperty", "result", "getCurrentOptions", "optionsFromListener", "getter"], "sourceRoot": "../../src", "sources": ["useOptionsGetters.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,sBAAsB,MAAM,0BAA0B;AAS7D,eAAe,SAASC,iBAAiB,OAI7B;EAAA,IAJ8B;IACxCC,GAAG;IACHC,OAAO;IACPC;EACO,CAAC;EACR,MAAMC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAAqBH,OAAO,CAAC;EAC5D,MAAMI,0BAA0B,GAAGT,KAAK,CAACQ,MAAM,CAE7C,CAAC,CAAC,CAAC;EAEL,MAAM;IAAEE;EAAgB,CAAC,GAAGV,KAAK,CAACW,UAAU,CAACV,wBAAwB,CAAC;EACtE,MAAM;IAAEW,gBAAgB,EAAEC;EAAuB,CAAC,GAAGb,KAAK,CAACW,UAAU,CACnET,sBAAsB,CACvB;EAED,MAAMY,qBAAqB,GAAGd,KAAK,CAACe,WAAW,CAAC,MAAM;IACpD,MAAMC,SAAS,GAAG,CAAAV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,SAAS,EAAE,KAAI,IAAI;IACjD,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACV,0BAA0B,CAACW,OAAO,CAAC,CAACC,MAAM;IAE1E,IAAIL,SAAS,IAAI,CAACC,WAAW,EAAE;MAC7BP,eAAe,CAACH,UAAU,CAACa,OAAO,IAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACd,UAAU,EAAEI,eAAe,CAAC,CAAC;EAEjCV,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpBf,UAAU,CAACa,OAAO,GAAGf,OAAO;IAC5BS,qBAAqB,EAAE;IAEvB,OAAOR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,WAAW,CAAC,OAAO,EAAET,qBAAqB,CAAC;EAChE,CAAC,EAAE,CAACR,UAAU,EAAED,OAAO,EAAES,qBAAqB,CAAC,CAAC;EAEhD,MAAMU,sBAAsB,GAAGxB,KAAK,CAACe,WAAW,CAAC,MAAM;IACrD,KAAK,IAAIX,GAAG,IAAIK,0BAA0B,CAACW,OAAO,EAAE;MAClD,IAAIX,0BAA0B,CAACW,OAAO,CAACK,cAAc,CAACrB,GAAG,CAAC,EAAE;QAAA;QAC1D,MAAMsB,MAAM,4BAAG,0BAAAjB,0BAA0B,CAACW,OAAO,EAAChB,GAAG,CAAC,0DAAvC,kDAA2C;;QAE1D;QACA,IAAIsB,MAAM,KAAK,IAAI,EAAE;UACnB,OAAOA,MAAM;QACf;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAG3B,KAAK,CAACe,WAAW,CAAC,MAAM;IAChD,MAAMC,SAAS,GAAG,CAAAV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,SAAS,EAAE,KAAI,IAAI;IAEjD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA,MAAMY,mBAAmB,GAAGJ,sBAAsB,EAAE;IAEpD,IAAII,mBAAmB,KAAK,IAAI,EAAE;MAChC,OAAOA,mBAAmB;IAC5B;IAEA,OAAOrB,UAAU,CAACa,OAAO;EAC3B,CAAC,EAAE,CAACd,UAAU,EAAEkB,sBAAsB,CAAC,CAAC;EAExCxB,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpB,OAAOT,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAGT,GAAG,EAAGuB,iBAAiB,CAAC;EAC1D,CAAC,EAAE,CAACA,iBAAiB,EAAEd,sBAAsB,EAAET,GAAG,CAAC,CAAC;EAEpD,MAAMQ,gBAAgB,GAAGZ,KAAK,CAACe,WAAW,CACxC,CAACX,GAAW,EAAEyB,MAAuC,KAAK;IACxDpB,0BAA0B,CAACW,OAAO,CAAChB,GAAG,CAAC,GAAGyB,MAAM;IAChDf,qBAAqB,EAAE;IAEvB,OAAO,MAAM;MACX;MACA,OAAOL,0BAA0B,CAACW,OAAO,CAAChB,GAAG,CAAC;MAC9CU,qBAAqB,EAAE;IACzB,CAAC;EACH,CAAC,EACD,CAACA,qBAAqB,CAAC,CACxB;EAED,OAAO;IACLF,gBAAgB;IAChBe;EACF,CAAC;AACH"}