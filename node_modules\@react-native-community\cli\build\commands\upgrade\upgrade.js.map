{"version": 3, "names": ["repos", "rawDiffUrl", "webDiffUrl", "dependencyName", "isConnected", "output", "includes", "checkForErrors", "CLIError", "logger", "warn", "getLatestRNVersion", "repoName", "info", "stdout", "stderr", "execa", "getRNPeerDeps", "version", "JSON", "parse", "getPatch", "currentVersion", "newVersion", "config", "patch", "data", "fetch", "error", "message", "chalk", "underline", "dim", "patchWithRenamedProjects", "Object", "keys", "project", "for<PERSON>ach", "platform", "xcodeProject", "ios", "replace", "RegExp", "name", "packageName", "split", "join", "getVersionToUpgradeTo", "argv", "projectDir", "argVersion", "semverCoercedVersion", "semver", "coerce", "valid", "gt", "eq", "dependencies", "require", "path", "parsedVersion", "length", "satisfies", "installDeps", "root", "peerDeps", "deps", "map", "module", "PackageManager", "install", "silent", "installCocoaPodsDeps", "process", "installPods", "directory", "pop", "debug", "applyPatch", "tmpPatchFile", "defaultExcludes", "filesThatDontExist", "filesThatFailedToApply", "relativePathFromRoot", "excludes", "e", "errorLines", "filter", "x", "Boolean", "file", "bold", "upgrade", "ctx", "rnName", "success", "patchSuccess", "fs", "writeFileSync", "Error", "unlinkSync", "stdio", "upgradeCommand", "description", "func"], "sources": ["../../../src/commands/upgrade/upgrade.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport chalk from 'chalk';\nimport semver from 'semver';\nimport execa from 'execa';\nimport {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError, fetch} from '@react-native-community/cli-tools';\nimport * as PackageManager from '../../tools/packageManager';\nimport {installPods} from '@react-native-community/cli-doctor';\n\ntype UpgradeError = {message: string; stderr: string};\n\n// https://react-native-community.github.io/upgrade-helper/?from=0.59.10&to=0.60.0-rc.3\n\ntype RepoNameType = 'react-native' | 'react-native-tvos';\n\nconst repos = {\n  'react-native': {\n    rawDiffUrl:\n      'https://raw.githubusercontent.com/react-native-community/rn-diff-purge/diffs/diffs',\n    webDiffUrl: 'https://react-native-community.github.io/upgrade-helper',\n    dependencyName: 'react-native',\n  },\n  'react-native-tvos': {\n    rawDiffUrl:\n      'https://raw.githubusercontent.com/react-native-tvos/rn-diff-purge-tv/diffs/diffs',\n    webDiffUrl: 'https://react-native-community.github.io/upgrade-helper',\n    dependencyName: 'react-native@npm:react-native-tvos',\n  },\n};\n\nconst isConnected = (output: string): boolean => {\n  // there is no reliable way of checking for internet connectivity, so we should just\n  // read the output from npm (to check for connectivity errors) which is faster and relatively more reliable.\n  return !output.includes('the host is inaccessible');\n};\n\nconst checkForErrors = (output: string): void => {\n  if (!output) {\n    return;\n  }\n  if (!isConnected(output)) {\n    throw new CLIError(\n      'Upgrade failed. You do not seem to have an internet connection.',\n    );\n  }\n\n  if (output.includes('npm ERR')) {\n    throw new CLIError(`Upgrade failed with the following errors:\\n${output}`);\n  }\n\n  if (output.includes('npm WARN')) {\n    logger.warn(output);\n  }\n};\n\nconst getLatestRNVersion = async (repoName: RepoNameType): Promise<string> => {\n  logger.info('No version passed. Fetching latest...');\n  const {stdout, stderr} = await execa('npm', ['info', repoName, 'version']);\n  checkForErrors(stderr);\n  return stdout;\n};\n\nconst getRNPeerDeps = async (\n  version: string,\n  repoName: RepoNameType,\n): Promise<{[key: string]: string}> => {\n  const {stdout, stderr} = await execa('npm', [\n    'info',\n    `${repoName}@${version}`,\n    'peerDependencies',\n    '--json',\n  ]);\n  checkForErrors(stderr);\n  return JSON.parse(stdout);\n};\n\nconst getPatch = async (\n  currentVersion: string,\n  newVersion: string,\n  config: Config,\n  repoName: RepoNameType,\n) => {\n  let patch;\n\n  logger.info(`Fetching diff between v${currentVersion} and v${newVersion}...`);\n\n  try {\n    const {data} = await fetch(\n      `${repos[repoName].rawDiffUrl}/${currentVersion}..${newVersion}.diff`,\n    );\n\n    patch = data;\n  } catch (error) {\n    logger.error((error as UpgradeError).message);\n    logger.error(\n      `Failed to fetch diff for react-native@${newVersion}. Maybe it's not released yet?`,\n    );\n    logger.info(\n      `For available releases to diff see: ${chalk.underline.dim(\n        'https://github.com/react-native-community/rn-diff-purge#diff-table-full-table-here',\n      )}`,\n    );\n    return null;\n  }\n\n  let patchWithRenamedProjects = patch;\n\n  Object.keys(config.project).forEach((platform) => {\n    if (!config.project[platform]) {\n      return;\n    }\n    if (platform === 'ios') {\n      const xcodeProject = config.project.ios!.xcodeProject;\n      if (xcodeProject) {\n        patchWithRenamedProjects = patchWithRenamedProjects.replace(\n          new RegExp('RnDiffApp', 'g'),\n          xcodeProject.name.replace('.xcodeproj', ''),\n        );\n      }\n    } else if (platform === 'android') {\n      patchWithRenamedProjects = patchWithRenamedProjects\n        .replace(\n          new RegExp('com\\\\.rndiffapp', 'g'),\n          config.project[platform]!.packageName,\n        )\n        .replace(\n          new RegExp('com\\\\.rndiffapp'.split('.').join('/'), 'g'),\n          config.project[platform]!.packageName.split('.').join('/'),\n        );\n    } else {\n      logger.warn(\n        `Unsupported platform: \"${platform}\". \\`upgrade\\` only supports iOS and Android.`,\n      );\n    }\n  });\n\n  return patchWithRenamedProjects;\n};\n\nconst getVersionToUpgradeTo = async (\n  argv: Array<string>,\n  currentVersion: string,\n  projectDir: string,\n  repoName: RepoNameType,\n) => {\n  const argVersion = argv[0];\n  const semverCoercedVersion = semver.coerce(argVersion);\n  const newVersion = argVersion\n    ? semver.valid(argVersion) ||\n      (semverCoercedVersion ? semverCoercedVersion.version : null)\n    : await getLatestRNVersion(repoName);\n\n  if (!newVersion) {\n    logger.error(\n      `Provided version \"${argv[0]}\" is not allowed. Please pass a valid semver version`,\n    );\n    return null;\n  }\n\n  if (semver.gt(currentVersion, newVersion)) {\n    logger.error(\n      `Trying to upgrade from newer version \"${currentVersion}\" to older \"${newVersion}\"`,\n    );\n    return null;\n  }\n  if (semver.eq(currentVersion, newVersion)) {\n    const {\n      dependencies: {'react-native': version},\n    } = require(path.join(projectDir, 'package.json'));\n\n    const parsedVersion = version.split('@')[version.split('@').length - 1];\n\n    if (semver.satisfies(newVersion, parsedVersion)) {\n      logger.warn(\n        `Specified version \"${newVersion}\" is already installed in node_modules and it satisfies \"${parsedVersion}\" semver range. No need to upgrade`,\n      );\n      return null;\n    }\n    logger.error(\n      `Dependency mismatch. Specified version \"${newVersion}\" is already installed in node_modules and it doesn't satisfy \"${parsedVersion}\" semver range of your \"react-native\" dependency. Please re-install your dependencies`,\n    );\n    return null;\n  }\n\n  return newVersion;\n};\n\nconst installDeps = async (\n  root: string,\n  newVersion: string,\n  repoName: RepoNameType,\n) => {\n  logger.info(\n    `Installing \"react-native@${newVersion}\" and its peer dependencies...`,\n  );\n  const peerDeps = await getRNPeerDeps(newVersion, repoName);\n  const deps = [\n    `${repos[repoName].dependencyName}@${newVersion}`,\n    ...Object.keys(peerDeps).map((module) => `${module}@${peerDeps[module]}`),\n  ];\n  await PackageManager.install(deps, {\n    silent: true,\n    root,\n  });\n  await execa('git', ['add', 'package.json']);\n  try {\n    await execa('git', ['add', 'yarn.lock']);\n  } catch (error) {\n    // ignore\n  }\n  try {\n    await execa('git', ['add', 'package-lock.json']);\n  } catch (error) {\n    // ignore\n  }\n};\n\nconst installCocoaPodsDeps = async (projectDir: string) => {\n  if (process.platform === 'darwin') {\n    try {\n      logger.info(\n        `Installing CocoaPods dependencies ${chalk.dim(\n          '(this may take a few minutes)',\n        )}`,\n      );\n      await installPods({\n        directory: projectDir.split('/').pop() || '',\n      });\n    } catch (error) {\n      if ((error as UpgradeError).stderr) {\n        logger.debug(\n          `\"pod install\" or \"pod repo update\" failed. Error output:\\n${\n            (error as UpgradeError).stderr\n          }`,\n        );\n      }\n      logger.error(\n        'Installation of CocoaPods dependencies failed. Try to install them manually by running \"pod install\" in \"ios\" directory after finishing upgrade',\n      );\n    }\n  }\n};\n\nconst applyPatch = async (\n  currentVersion: string,\n  newVersion: string,\n  tmpPatchFile: string,\n  repoName: RepoNameType,\n) => {\n  const defaultExcludes = ['package.json'];\n  let filesThatDontExist: Array<string> = [];\n  let filesThatFailedToApply: Array<string> = [];\n\n  const {stdout: relativePathFromRoot} = await execa('git', [\n    'rev-parse',\n    '--show-prefix',\n  ]);\n  try {\n    try {\n      const excludes = defaultExcludes.map(\n        (e) => `--exclude=${path.join(relativePathFromRoot, e)}`,\n      );\n      await execa('git', [\n        'apply',\n        // According to git documentation, `--binary` flag is turned on by\n        // default. However it's necessary when running `git apply --check` to\n        // actually accept binary files, maybe a bug in git?\n        '--binary',\n        '--check',\n        tmpPatchFile,\n        ...excludes,\n        '-p2',\n        '--3way',\n        `--directory=${relativePathFromRoot}`,\n      ]);\n      logger.info('Applying diff...');\n    } catch (error) {\n      const errorLines: Array<string> = (error as UpgradeError).stderr.split(\n        '\\n',\n      );\n      filesThatDontExist = [\n        ...errorLines\n          .filter((x) => x.includes('does not exist in index'))\n          .map((x) =>\n            x.replace(/^error: (.*): does not exist in index$/, '$1'),\n          ),\n      ].filter(Boolean);\n\n      filesThatFailedToApply = errorLines\n        .filter((x) => x.includes('patch does not apply'))\n        .map((x) => x.replace(/^error: (.*): patch does not apply$/, '$1'))\n        .filter(Boolean);\n\n      logger.info('Applying diff...');\n      logger.warn(\n        `Excluding files that exist in the template, but not in your project:\\n${filesThatDontExist\n          .map((file) => `  - ${chalk.bold(file)}`)\n          .join('\\n')}`,\n      );\n      if (filesThatFailedToApply.length) {\n        logger.error(\n          `Excluding files that failed to apply the diff:\\n${filesThatFailedToApply\n            .map((file) => `  - ${chalk.bold(file)}`)\n            .join(\n              '\\n',\n            )}\\nPlease make sure to check the actual changes after the upgrade command is finished.\\nYou can find them in our Upgrade Helper web app: ${chalk.underline.dim(\n            `${repos[repoName].webDiffUrl}/?from=${currentVersion}&to=${newVersion}`,\n          )}`,\n        );\n      }\n    } finally {\n      const excludes = [\n        ...defaultExcludes,\n        ...filesThatDontExist,\n        ...filesThatFailedToApply,\n      ].map((e) => `--exclude=${path.join(relativePathFromRoot, e)}`);\n      await execa('git', [\n        'apply',\n        tmpPatchFile,\n        ...excludes,\n        '-p2',\n        '--3way',\n        `--directory=${relativePathFromRoot}`,\n      ]);\n    }\n  } catch (error) {\n    if ((error as UpgradeError).stderr) {\n      logger.debug(\n        `\"git apply\" failed. Error output:\\n${(error as UpgradeError).stderr}`,\n      );\n    }\n    logger.error(\n      'Automatically applying diff failed. We did our best to automatically upgrade as many files as possible',\n    );\n    return false;\n  }\n  return true;\n};\n\n/**\n * Upgrade application to a new version of React Native.\n */\nasync function upgrade(argv: Array<string>, ctx: Config) {\n  const tmpPatchFile = 'tmp-upgrade-rn.patch';\n  const projectDir = ctx.root;\n  const {name: rnName, version: currentVersion} = require(path.join(\n    projectDir,\n    'node_modules/react-native/package.json',\n  ));\n\n  const repoName: RepoNameType =\n    rnName === 'react-native-tvos' ? 'react-native-tvos' : 'react-native';\n\n  const newVersion = await getVersionToUpgradeTo(\n    argv,\n    currentVersion,\n    projectDir,\n    repoName,\n  );\n\n  if (!newVersion) {\n    return;\n  }\n\n  const patch = await getPatch(currentVersion, newVersion, ctx, repoName);\n\n  if (patch === null) {\n    return;\n  }\n\n  if (patch === '') {\n    logger.info('Diff has no changes to apply, proceeding further');\n    await installDeps(projectDir, newVersion, repoName);\n    await installCocoaPodsDeps(projectDir);\n\n    logger.success(\n      `Upgraded React Native to v${newVersion} 🎉. Now you can review and commit the changes`,\n    );\n    return;\n  }\n  let patchSuccess;\n\n  try {\n    fs.writeFileSync(tmpPatchFile, patch);\n    patchSuccess = await applyPatch(\n      currentVersion,\n      newVersion,\n      tmpPatchFile,\n      repoName,\n    );\n  } catch (error) {\n    throw new Error((error as UpgradeError).stderr || (error as string));\n  } finally {\n    try {\n      fs.unlinkSync(tmpPatchFile);\n    } catch (e) {\n      // ignore\n    }\n    const {stdout} = await execa('git', ['status', '-s']);\n    if (!patchSuccess) {\n      if (stdout) {\n        logger.warn(\n          'Continuing after failure. Some of the files are upgraded but you will need to deal with conflicts manually',\n        );\n        await installDeps(projectDir, newVersion, repoName);\n        logger.info('Running \"git status\" to check what changed...');\n        await execa('git', ['status'], {stdio: 'inherit'});\n      } else {\n        logger.error(\n          'Patch failed to apply for unknown reason. Please fall back to manual way of upgrading',\n        );\n      }\n    } else {\n      await installDeps(projectDir, newVersion, repoName);\n      await installCocoaPodsDeps(projectDir);\n      logger.info('Running \"git status\" to check what changed...');\n      await execa('git', ['status'], {stdio: 'inherit'});\n    }\n    if (!patchSuccess) {\n      if (stdout) {\n        logger.warn(\n          'Please run \"git diff\" to review the conflicts and resolve them',\n        );\n      }\n      if (process.platform === 'darwin') {\n        logger.warn(\n          'After resolving conflicts don\\'t forget to run \"pod install\" inside \"ios\" directory',\n        );\n      }\n      logger.info(`You may find these resources helpful:\n• Release notes: ${chalk.underline.dim(\n        `https://github.com/facebook/react-native/releases/tag/v${newVersion}`,\n      )}\n• Manual Upgrade Helper: ${chalk.underline.dim(\n        `${repos[repoName].webDiffUrl}/?from=${currentVersion}&to=${newVersion}`,\n      )}\n• Git diff: ${chalk.underline.dim(\n        `${repos[repoName].rawDiffUrl}/${currentVersion}..${newVersion}.diff`,\n      )}`);\n\n      throw new CLIError(\n        'Upgrade failed. Please see the messages above for details',\n      );\n    }\n  }\n  logger.success(\n    `Upgraded React Native to v${newVersion} 🎉. Now you can review and commit the changes`,\n  );\n}\nconst upgradeCommand = {\n  name: 'upgrade [version]',\n  description:\n    \"Upgrade your app's template files to the specified or latest npm version using `rn-diff-purge` project. Only valid semver versions are allowed.\",\n  func: upgrade,\n};\nexport default upgradeCommand;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAAA;AAAA;AAQ/D,MAAMA,KAAK,GAAG;EACZ,cAAc,EAAE;IACdC,UAAU,EACR,oFAAoF;IACtFC,UAAU,EAAE,yDAAyD;IACrEC,cAAc,EAAE;EAClB,CAAC;EACD,mBAAmB,EAAE;IACnBF,UAAU,EACR,kFAAkF;IACpFC,UAAU,EAAE,yDAAyD;IACrEC,cAAc,EAAE;EAClB;AACF,CAAC;AAED,MAAMC,WAAW,GAAIC,MAAc,IAAc;EAC/C;EACA;EACA,OAAO,CAACA,MAAM,CAACC,QAAQ,CAAC,0BAA0B,CAAC;AACrD,CAAC;AAED,MAAMC,cAAc,GAAIF,MAAc,IAAW;EAC/C,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EACA,IAAI,CAACD,WAAW,CAACC,MAAM,CAAC,EAAE;IACxB,MAAM,KAAIG,oBAAQ,EAChB,iEAAiE,CAClE;EACH;EAEA,IAAIH,MAAM,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC9B,MAAM,KAAIE,oBAAQ,EAAE,8CAA6CH,MAAO,EAAC,CAAC;EAC5E;EAEA,IAAIA,MAAM,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC/BG,kBAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EACrB;AACF,CAAC;AAED,MAAMM,kBAAkB,GAAG,MAAOC,QAAsB,IAAsB;EAC5EH,kBAAM,CAACI,IAAI,CAAC,uCAAuC,CAAC;EACpD,MAAM;IAACC,MAAM;IAAEC;EAAM,CAAC,GAAG,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,EAAEJ,QAAQ,EAAE,SAAS,CAAC,CAAC;EAC1EL,cAAc,CAACQ,MAAM,CAAC;EACtB,OAAOD,MAAM;AACf,CAAC;AAED,MAAMG,aAAa,GAAG,OACpBC,OAAe,EACfN,QAAsB,KACe;EACrC,MAAM;IAACE,MAAM;IAAEC;EAAM,CAAC,GAAG,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAE,CAC1C,MAAM,EACL,GAAEJ,QAAS,IAAGM,OAAQ,EAAC,EACxB,kBAAkB,EAClB,QAAQ,CACT,CAAC;EACFX,cAAc,CAACQ,MAAM,CAAC;EACtB,OAAOI,IAAI,CAACC,KAAK,CAACN,MAAM,CAAC;AAC3B,CAAC;AAED,MAAMO,QAAQ,GAAG,OACfC,cAAsB,EACtBC,UAAkB,EAClBC,MAAc,EACdZ,QAAsB,KACnB;EACH,IAAIa,KAAK;EAEThB,kBAAM,CAACI,IAAI,CAAE,0BAAyBS,cAAe,SAAQC,UAAW,KAAI,CAAC;EAE7E,IAAI;IACF,MAAM;MAACG;IAAI,CAAC,GAAG,MAAM,IAAAC,iBAAK,EACvB,GAAE3B,KAAK,CAACY,QAAQ,CAAC,CAACX,UAAW,IAAGqB,cAAe,KAAIC,UAAW,OAAM,CACtE;IAEDE,KAAK,GAAGC,IAAI;EACd,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdnB,kBAAM,CAACmB,KAAK,CAAEA,KAAK,CAAkBC,OAAO,CAAC;IAC7CpB,kBAAM,CAACmB,KAAK,CACT,yCAAwCL,UAAW,gCAA+B,CACpF;IACDd,kBAAM,CAACI,IAAI,CACR,uCAAsCiB,gBAAK,CAACC,SAAS,CAACC,GAAG,CACxD,oFAAoF,CACpF,EAAC,CACJ;IACD,OAAO,IAAI;EACb;EAEA,IAAIC,wBAAwB,GAAGR,KAAK;EAEpCS,MAAM,CAACC,IAAI,CAACX,MAAM,CAACY,OAAO,CAAC,CAACC,OAAO,CAAEC,QAAQ,IAAK;IAChD,IAAI,CAACd,MAAM,CAACY,OAAO,CAACE,QAAQ,CAAC,EAAE;MAC7B;IACF;IACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,MAAMC,YAAY,GAAGf,MAAM,CAACY,OAAO,CAACI,GAAG,CAAED,YAAY;MACrD,IAAIA,YAAY,EAAE;QAChBN,wBAAwB,GAAGA,wBAAwB,CAACQ,OAAO,CACzD,IAAIC,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAC5BH,YAAY,CAACI,IAAI,CAACF,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAC5C;MACH;IACF,CAAC,MAAM,IAAIH,QAAQ,KAAK,SAAS,EAAE;MACjCL,wBAAwB,GAAGA,wBAAwB,CAChDQ,OAAO,CACN,IAAIC,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC,EAClClB,MAAM,CAACY,OAAO,CAACE,QAAQ,CAAC,CAAEM,WAAW,CACtC,CACAH,OAAO,CACN,IAAIC,MAAM,CAAC,iBAAiB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EACvDtB,MAAM,CAACY,OAAO,CAACE,QAAQ,CAAC,CAAEM,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAC3D;IACL,CAAC,MAAM;MACLrC,kBAAM,CAACC,IAAI,CACR,0BAAyB4B,QAAS,+CAA8C,CAClF;IACH;EACF,CAAC,CAAC;EAEF,OAAOL,wBAAwB;AACjC,CAAC;AAED,MAAMc,qBAAqB,GAAG,OAC5BC,IAAmB,EACnB1B,cAAsB,EACtB2B,UAAkB,EAClBrC,QAAsB,KACnB;EACH,MAAMsC,UAAU,GAAGF,IAAI,CAAC,CAAC,CAAC;EAC1B,MAAMG,oBAAoB,GAAGC,iBAAM,CAACC,MAAM,CAACH,UAAU,CAAC;EACtD,MAAM3B,UAAU,GAAG2B,UAAU,GACzBE,iBAAM,CAACE,KAAK,CAACJ,UAAU,CAAC,KACvBC,oBAAoB,GAAGA,oBAAoB,CAACjC,OAAO,GAAG,IAAI,CAAC,GAC5D,MAAMP,kBAAkB,CAACC,QAAQ,CAAC;EAEtC,IAAI,CAACW,UAAU,EAAE;IACfd,kBAAM,CAACmB,KAAK,CACT,qBAAoBoB,IAAI,CAAC,CAAC,CAAE,sDAAqD,CACnF;IACD,OAAO,IAAI;EACb;EAEA,IAAII,iBAAM,CAACG,EAAE,CAACjC,cAAc,EAAEC,UAAU,CAAC,EAAE;IACzCd,kBAAM,CAACmB,KAAK,CACT,yCAAwCN,cAAe,eAAcC,UAAW,GAAE,CACpF;IACD,OAAO,IAAI;EACb;EACA,IAAI6B,iBAAM,CAACI,EAAE,CAAClC,cAAc,EAAEC,UAAU,CAAC,EAAE;IACzC,MAAM;MACJkC,YAAY,EAAE;QAAC,cAAc,EAAEvC;MAAO;IACxC,CAAC,GAAGwC,OAAO,CAACC,eAAI,CAACb,IAAI,CAACG,UAAU,EAAE,cAAc,CAAC,CAAC;IAElD,MAAMW,aAAa,GAAG1C,OAAO,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAAC3B,OAAO,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAACgB,MAAM,GAAG,CAAC,CAAC;IAEvE,IAAIT,iBAAM,CAACU,SAAS,CAACvC,UAAU,EAAEqC,aAAa,CAAC,EAAE;MAC/CnD,kBAAM,CAACC,IAAI,CACR,sBAAqBa,UAAW,4DAA2DqC,aAAc,oCAAmC,CAC9I;MACD,OAAO,IAAI;IACb;IACAnD,kBAAM,CAACmB,KAAK,CACT,2CAA0CL,UAAW,kEAAiEqC,aAAc,uFAAsF,CAC5N;IACD,OAAO,IAAI;EACb;EAEA,OAAOrC,UAAU;AACnB,CAAC;AAED,MAAMwC,WAAW,GAAG,OAClBC,IAAY,EACZzC,UAAkB,EAClBX,QAAsB,KACnB;EACHH,kBAAM,CAACI,IAAI,CACR,4BAA2BU,UAAW,gCAA+B,CACvE;EACD,MAAM0C,QAAQ,GAAG,MAAMhD,aAAa,CAACM,UAAU,EAAEX,QAAQ,CAAC;EAC1D,MAAMsD,IAAI,GAAG,CACV,GAAElE,KAAK,CAACY,QAAQ,CAAC,CAACT,cAAe,IAAGoB,UAAW,EAAC,EACjD,GAAGW,MAAM,CAACC,IAAI,CAAC8B,QAAQ,CAAC,CAACE,GAAG,CAAEC,MAAM,IAAM,GAAEA,MAAO,IAAGH,QAAQ,CAACG,MAAM,CAAE,EAAC,CAAC,CAC1E;EACD,MAAMC,cAAc,CAACC,OAAO,CAACJ,IAAI,EAAE;IACjCK,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;EACF,MAAM,IAAAhD,gBAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;EAC3C,IAAI;IACF,MAAM,IAAAA,gBAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;EAC1C,CAAC,CAAC,OAAOY,KAAK,EAAE;IACd;EAAA;EAEF,IAAI;IACF,MAAM,IAAAZ,gBAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;EAClD,CAAC,CAAC,OAAOY,KAAK,EAAE;IACd;EAAA;AAEJ,CAAC;AAED,MAAM4C,oBAAoB,GAAG,MAAOvB,UAAkB,IAAK;EACzD,IAAIwB,OAAO,CAACnC,QAAQ,KAAK,QAAQ,EAAE;IACjC,IAAI;MACF7B,kBAAM,CAACI,IAAI,CACR,qCAAoCiB,gBAAK,CAACE,GAAG,CAC5C,+BAA+B,CAC/B,EAAC,CACJ;MACD,MAAM,IAAA0C,wBAAW,EAAC;QAChBC,SAAS,EAAE1B,UAAU,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC+B,GAAG,EAAE,IAAI;MAC5C,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACd,IAAKA,KAAK,CAAkBb,MAAM,EAAE;QAClCN,kBAAM,CAACoE,KAAK,CACT,6DACEjD,KAAK,CAAkBb,MACzB,EAAC,CACH;MACH;MACAN,kBAAM,CAACmB,KAAK,CACV,iJAAiJ,CAClJ;IACH;EACF;AACF,CAAC;AAED,MAAMkD,UAAU,GAAG,OACjBxD,cAAsB,EACtBC,UAAkB,EAClBwD,YAAoB,EACpBnE,QAAsB,KACnB;EACH,MAAMoE,eAAe,GAAG,CAAC,cAAc,CAAC;EACxC,IAAIC,kBAAiC,GAAG,EAAE;EAC1C,IAAIC,sBAAqC,GAAG,EAAE;EAE9C,MAAM;IAACpE,MAAM,EAAEqE;EAAoB,CAAC,GAAG,MAAM,IAAAnE,gBAAK,EAAC,KAAK,EAAE,CACxD,WAAW,EACX,eAAe,CAChB,CAAC;EACF,IAAI;IACF,IAAI;MACF,MAAMoE,QAAQ,GAAGJ,eAAe,CAACb,GAAG,CACjCkB,CAAC,IAAM,aAAY1B,eAAI,CAACb,IAAI,CAACqC,oBAAoB,EAAEE,CAAC,CAAE,EAAC,CACzD;MACD,MAAM,IAAArE,gBAAK,EAAC,KAAK,EAAE,CACjB,OAAO;MACP;MACA;MACA;MACA,UAAU,EACV,SAAS,EACT+D,YAAY,EACZ,GAAGK,QAAQ,EACX,KAAK,EACL,QAAQ,EACP,eAAcD,oBAAqB,EAAC,CACtC,CAAC;MACF1E,kBAAM,CAACI,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd,MAAM0D,UAAyB,GAAI1D,KAAK,CAAkBb,MAAM,CAAC8B,KAAK,CACpE,IAAI,CACL;MACDoC,kBAAkB,GAAG,CACnB,GAAGK,UAAU,CACVC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAClF,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CACpD6D,GAAG,CAAEqB,CAAC,IACLA,CAAC,CAAC/C,OAAO,CAAC,wCAAwC,EAAE,IAAI,CAAC,CAC1D,CACJ,CAAC8C,MAAM,CAACE,OAAO,CAAC;MAEjBP,sBAAsB,GAAGI,UAAU,CAChCC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAClF,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CACjD6D,GAAG,CAAEqB,CAAC,IAAKA,CAAC,CAAC/C,OAAO,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC,CAClE8C,MAAM,CAACE,OAAO,CAAC;MAElBhF,kBAAM,CAACI,IAAI,CAAC,kBAAkB,CAAC;MAC/BJ,kBAAM,CAACC,IAAI,CACR,yEAAwEuE,kBAAkB,CACxFd,GAAG,CAAEuB,IAAI,IAAM,OAAM5D,gBAAK,CAAC6D,IAAI,CAACD,IAAI,CAAE,EAAC,CAAC,CACxC5C,IAAI,CAAC,IAAI,CAAE,EAAC,CAChB;MACD,IAAIoC,sBAAsB,CAACrB,MAAM,EAAE;QACjCpD,kBAAM,CAACmB,KAAK,CACT,mDAAkDsD,sBAAsB,CACtEf,GAAG,CAAEuB,IAAI,IAAM,OAAM5D,gBAAK,CAAC6D,IAAI,CAACD,IAAI,CAAE,EAAC,CAAC,CACxC5C,IAAI,CACH,IAAI,CACJ,2IAA0IhB,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC9J,GAAEhC,KAAK,CAACY,QAAQ,CAAC,CAACV,UAAW,UAASoB,cAAe,OAAMC,UAAW,EAAC,CACxE,EAAC,CACJ;MACH;IACF,CAAC,SAAS;MACR,MAAM6D,QAAQ,GAAG,CACf,GAAGJ,eAAe,EAClB,GAAGC,kBAAkB,EACrB,GAAGC,sBAAsB,CAC1B,CAACf,GAAG,CAAEkB,CAAC,IAAM,aAAY1B,eAAI,CAACb,IAAI,CAACqC,oBAAoB,EAAEE,CAAC,CAAE,EAAC,CAAC;MAC/D,MAAM,IAAArE,gBAAK,EAAC,KAAK,EAAE,CACjB,OAAO,EACP+D,YAAY,EACZ,GAAGK,QAAQ,EACX,KAAK,EACL,QAAQ,EACP,eAAcD,oBAAqB,EAAC,CACtC,CAAC;IACJ;EACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;IACd,IAAKA,KAAK,CAAkBb,MAAM,EAAE;MAClCN,kBAAM,CAACoE,KAAK,CACT,sCAAsCjD,KAAK,CAAkBb,MAAO,EAAC,CACvE;IACH;IACAN,kBAAM,CAACmB,KAAK,CACV,wGAAwG,CACzG;IACD,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA,eAAegE,OAAO,CAAC5C,IAAmB,EAAE6C,GAAW,EAAE;EACvD,MAAMd,YAAY,GAAG,sBAAsB;EAC3C,MAAM9B,UAAU,GAAG4C,GAAG,CAAC7B,IAAI;EAC3B,MAAM;IAACrB,IAAI,EAAEmD,MAAM;IAAE5E,OAAO,EAAEI;EAAc,CAAC,GAAGoC,OAAO,CAACC,eAAI,CAACb,IAAI,CAC/DG,UAAU,EACV,wCAAwC,CACzC,CAAC;EAEF,MAAMrC,QAAsB,GAC1BkF,MAAM,KAAK,mBAAmB,GAAG,mBAAmB,GAAG,cAAc;EAEvE,MAAMvE,UAAU,GAAG,MAAMwB,qBAAqB,CAC5CC,IAAI,EACJ1B,cAAc,EACd2B,UAAU,EACVrC,QAAQ,CACT;EAED,IAAI,CAACW,UAAU,EAAE;IACf;EACF;EAEA,MAAME,KAAK,GAAG,MAAMJ,QAAQ,CAACC,cAAc,EAAEC,UAAU,EAAEsE,GAAG,EAAEjF,QAAQ,CAAC;EAEvE,IAAIa,KAAK,KAAK,IAAI,EAAE;IAClB;EACF;EAEA,IAAIA,KAAK,KAAK,EAAE,EAAE;IAChBhB,kBAAM,CAACI,IAAI,CAAC,kDAAkD,CAAC;IAC/D,MAAMkD,WAAW,CAACd,UAAU,EAAE1B,UAAU,EAAEX,QAAQ,CAAC;IACnD,MAAM4D,oBAAoB,CAACvB,UAAU,CAAC;IAEtCxC,kBAAM,CAACsF,OAAO,CACX,6BAA4BxE,UAAW,gDAA+C,CACxF;IACD;EACF;EACA,IAAIyE,YAAY;EAEhB,IAAI;IACFC,aAAE,CAACC,aAAa,CAACnB,YAAY,EAAEtD,KAAK,CAAC;IACrCuE,YAAY,GAAG,MAAMlB,UAAU,CAC7BxD,cAAc,EACdC,UAAU,EACVwD,YAAY,EACZnE,QAAQ,CACT;EACH,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACd,MAAM,IAAIuE,KAAK,CAAEvE,KAAK,CAAkBb,MAAM,IAAKa,KAAgB,CAAC;EACtE,CAAC,SAAS;IACR,IAAI;MACFqE,aAAE,CAACG,UAAU,CAACrB,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOM,CAAC,EAAE;MACV;IAAA;IAEF,MAAM;MAACvE;IAAM,CAAC,GAAG,MAAM,IAAAE,gBAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACrD,IAAI,CAACgF,YAAY,EAAE;MACjB,IAAIlF,MAAM,EAAE;QACVL,kBAAM,CAACC,IAAI,CACT,4GAA4G,CAC7G;QACD,MAAMqD,WAAW,CAACd,UAAU,EAAE1B,UAAU,EAAEX,QAAQ,CAAC;QACnDH,kBAAM,CAACI,IAAI,CAAC,+CAA+C,CAAC;QAC5D,MAAM,IAAAG,gBAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;UAACqF,KAAK,EAAE;QAAS,CAAC,CAAC;MACpD,CAAC,MAAM;QACL5F,kBAAM,CAACmB,KAAK,CACV,uFAAuF,CACxF;MACH;IACF,CAAC,MAAM;MACL,MAAMmC,WAAW,CAACd,UAAU,EAAE1B,UAAU,EAAEX,QAAQ,CAAC;MACnD,MAAM4D,oBAAoB,CAACvB,UAAU,CAAC;MACtCxC,kBAAM,CAACI,IAAI,CAAC,+CAA+C,CAAC;MAC5D,MAAM,IAAAG,gBAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;QAACqF,KAAK,EAAE;MAAS,CAAC,CAAC;IACpD;IACA,IAAI,CAACL,YAAY,EAAE;MACjB,IAAIlF,MAAM,EAAE;QACVL,kBAAM,CAACC,IAAI,CACT,gEAAgE,CACjE;MACH;MACA,IAAI+D,OAAO,CAACnC,QAAQ,KAAK,QAAQ,EAAE;QACjC7B,kBAAM,CAACC,IAAI,CACT,qFAAqF,CACtF;MACH;MACAD,kBAAM,CAACI,IAAI,CAAE;AACnB,mBAAmBiB,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC7B,0DAAyDT,UAAW,EAAC,CACtE;AACR,2BAA2BO,gBAAK,CAACC,SAAS,CAACC,GAAG,CACrC,GAAEhC,KAAK,CAACY,QAAQ,CAAC,CAACV,UAAW,UAASoB,cAAe,OAAMC,UAAW,EAAC,CACxE;AACR,cAAcO,gBAAK,CAACC,SAAS,CAACC,GAAG,CACxB,GAAEhC,KAAK,CAACY,QAAQ,CAAC,CAACX,UAAW,IAAGqB,cAAe,KAAIC,UAAW,OAAM,CACrE,EAAC,CAAC;MAEJ,MAAM,KAAIf,oBAAQ,EAChB,2DAA2D,CAC5D;IACH;EACF;EACAC,kBAAM,CAACsF,OAAO,CACX,6BAA4BxE,UAAW,gDAA+C,CACxF;AACH;AACA,MAAM+E,cAAc,GAAG;EACrB3D,IAAI,EAAE,mBAAmB;EACzB4D,WAAW,EACT,iJAAiJ;EACnJC,IAAI,EAAEZ;AACR,CAAC;AAAC,eACaU,cAAc;AAAA"}