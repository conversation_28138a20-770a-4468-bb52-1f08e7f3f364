import React from 'react';
import { View, Text } from 'react-native';
import Toast, { ToastConfig } from 'react-native-toast-message';

// Custom toast configuration
export const toastConfig: ToastConfig = {
  success: ({ text1, text2 }) => (
    <View style={{
      backgroundColor: '#10B981',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <Text style={{ color: 'white', fontWeight: 'bold' }}>{text1}</Text>
      {text2 && <Text style={{ color: 'white', fontSize: 14 }}>{text2}</Text>}
    </View>
  ),
  error: ({ text1, text2 }) => (
    <View style={{
      backgroundColor: '#EF4444',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <Text style={{ color: 'white', fontWeight: 'bold' }}>{text1}</Text>
      {text2 && <Text style={{ color: 'white', fontSize: 14 }}>{text2}</Text>}
    </View>
  ),
  info: ({ text1, text2 }) => (
    <View style={{
      backgroundColor: '#3B82F6',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <Text style={{ color: 'white', fontWeight: 'bold' }}>{text1}</Text>
      {text2 && <Text style={{ color: 'white', fontSize: 14 }}>{text2}</Text>}
    </View>
  ),
};

// Toast helper functions
export const showToast = {
  success: (message: string, description?: string) => {
    Toast.show({
      type: 'success',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 3000,
    });
  },
  
  error: (message: string, description?: string) => {
    Toast.show({
      type: 'error',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 4000,
    });
  },
  
  info: (message: string, description?: string) => {
    Toast.show({
      type: 'info',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 3000,
    });
  },
};
