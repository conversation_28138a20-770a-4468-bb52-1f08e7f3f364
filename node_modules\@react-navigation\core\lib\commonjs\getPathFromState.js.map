{"version": 3, "names": ["getActiveRoute", "state", "route", "index", "routes", "length", "getPathFromState", "options", "Error", "validatePathConfig", "configs", "screens", "createNormalizedConfigs", "path", "current", "allParams", "pattern", "focusedParams", "focusedRoute", "currentOptions", "nestedRouteNames", "hasNext", "name", "push", "params", "stringify", "currentParams", "fromEntries", "Object", "entries", "map", "key", "value", "String", "assign", "split", "filter", "p", "startsWith", "for<PERSON>ach", "getParamName", "undefined", "nextRoute", "nestedConfig", "join", "endsWith", "encodeURIComponent", "param", "query", "queryString", "sort", "replace", "joinPaths", "paths", "concat", "Boolean", "createConfigItem", "config", "parentPattern", "exact", "c", "result"], "sourceRoot": "../../src", "sources": ["getPathFromState.tsx"], "mappings": ";;;;;;AAKA;AAEA;AAEA;AAAsD;AAAA;AAAA;AAiBtD,MAAMA,cAAc,GAAIC,KAAY,IAAwC;EAC1E,MAAMC,KAAK,GACT,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,GAC3BF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACE,KAAK,CAAC,GACzBF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAE3C,IAAIH,KAAK,CAACD,KAAK,EAAE;IACf,OAAOD,cAAc,CAACE,KAAK,CAACD,KAAK,CAAC;EACpC;EAEA,OAAOC,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASI,gBAAgB,CACtCL,KAAY,EACZM,OAA4B,EACpB;EACR,IAAIN,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMO,KAAK,CACT,+EAA+E,CAChF;EACH;EAEA,IAAID,OAAO,EAAE;IACX,IAAAE,2BAAkB,EAACF,OAAO,CAAC;EAC7B;;EAEA;EACA,MAAMG,OAAmC,GAAGH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,OAAO,GACxDC,uBAAuB,CAACL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,CAAC,GACzC,CAAC,CAAC;EAEN,IAAIE,IAAI,GAAG,GAAG;EACd,IAAIC,OAA0B,GAAGb,KAAK;EAEtC,MAAMc,SAA8B,GAAG,CAAC,CAAC;EAEzC,OAAOD,OAAO,EAAE;IACd,IAAIX,KAAK,GAAG,OAAOW,OAAO,CAACX,KAAK,KAAK,QAAQ,GAAGW,OAAO,CAACX,KAAK,GAAG,CAAC;IACjE,IAAID,KAAK,GAAGY,OAAO,CAACV,MAAM,CAACD,KAAK,CAE/B;IAED,IAAIa,OAA2B;IAE/B,IAAIC,aAA8C;IAClD,IAAIC,YAAY,GAAGlB,cAAc,CAACC,KAAK,CAAC;IACxC,IAAIkB,cAAc,GAAGT,OAAO;;IAE5B;IACA,IAAIU,gBAAgB,GAAG,EAAE;IAEzB,IAAIC,OAAO,GAAG,IAAI;IAElB,OAAOnB,KAAK,CAACoB,IAAI,IAAIH,cAAc,IAAIE,OAAO,EAAE;MAC9CL,OAAO,GAAGG,cAAc,CAACjB,KAAK,CAACoB,IAAI,CAAC,CAACN,OAAO;MAE5CI,gBAAgB,CAACG,IAAI,CAACrB,KAAK,CAACoB,IAAI,CAAC;MAEjC,IAAIpB,KAAK,CAACsB,MAAM,EAAE;QAAA;QAChB,MAAMC,SAAS,4BAAGN,cAAc,CAACjB,KAAK,CAACoB,IAAI,CAAC,0DAA1B,sBAA4BG,SAAS;QAEvD,MAAMC,aAAa,GAAG,IAAAC,oBAAW,EAC/BC,MAAM,CAACC,OAAO,CAAC3B,KAAK,CAACsB,MAAM,CAAC,CAACM,GAAG,CAAC;UAAA,IAAC,CAACC,GAAG,EAAEC,KAAK,CAAC;UAAA,OAAK,CACjDD,GAAG,EACHN,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGM,GAAG,CAAC,GAAGN,SAAS,CAACM,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGC,MAAM,CAACD,KAAK,CAAC,CACzD;QAAA,EAAC,CACH;QAED,IAAIhB,OAAO,EAAE;UACXY,MAAM,CAACM,MAAM,CAACnB,SAAS,EAAEW,aAAa,CAAC;QACzC;QAEA,IAAIR,YAAY,KAAKhB,KAAK,EAAE;UAAA;UAC1B;UACA;UACAe,aAAa,GAAG;YAAE,GAAGS;UAAc,CAAC;UAEpC,YAAAV,OAAO,6CAAP,SACImB,KAAK,CAAC,GAAG,CAAC,CACXC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC;UAChC;UAAA,CACCC,OAAO,CAAEF,CAAC,IAAK;YACd,MAAMf,IAAI,GAAGkB,YAAY,CAACH,CAAC,CAAC;;YAE5B;YACA,IAAIpB,aAAa,EAAE;cACjB;cACA,OAAOA,aAAa,CAACK,IAAI,CAAC;YAC5B;UACF,CAAC,CAAC;QACN;MACF;;MAEA;MACA,IAAI,CAACH,cAAc,CAACjB,KAAK,CAACoB,IAAI,CAAC,CAACX,OAAO,IAAIT,KAAK,CAACD,KAAK,KAAKwC,SAAS,EAAE;QACpEpB,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLlB,KAAK,GACH,OAAOD,KAAK,CAACD,KAAK,CAACE,KAAK,KAAK,QAAQ,GACjCD,KAAK,CAACD,KAAK,CAACE,KAAK,GACjBD,KAAK,CAACD,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC;QAEnC,MAAMqC,SAAS,GAAGxC,KAAK,CAACD,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC;QAC3C,MAAMwC,YAAY,GAAGxB,cAAc,CAACjB,KAAK,CAACoB,IAAI,CAAC,CAACX,OAAO;;QAEvD;QACA,IAAIgC,YAAY,IAAID,SAAS,CAACpB,IAAI,IAAIqB,YAAY,EAAE;UAClDzC,KAAK,GAAGwC,SAA8C;UACtDvB,cAAc,GAAGwB,YAAY;QAC/B,CAAC,MAAM;UACL;UACAtB,OAAO,GAAG,KAAK;QACjB;MACF;IACF;IAEA,IAAIL,OAAO,KAAKyB,SAAS,EAAE;MACzBzB,OAAO,GAAGI,gBAAgB,CAACwB,IAAI,CAAC,GAAG,CAAC;IACtC;IAEA,IAAIzB,cAAc,CAACjB,KAAK,CAACoB,IAAI,CAAC,KAAKmB,SAAS,EAAE;MAC5C5B,IAAI,IAAIG,OAAO,CACZmB,KAAK,CAAC,GAAG,CAAC,CACVL,GAAG,CAAEO,CAAC,IAAK;QACV,MAAMf,IAAI,GAAGkB,YAAY,CAACH,CAAC,CAAC;;QAE5B;QACA;QACA;QACA,IAAIA,CAAC,KAAK,GAAG,EAAE;UACb,OAAOnC,KAAK,CAACoB,IAAI;QACnB;;QAEA;QACA,IAAIe,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UACrB,MAAMN,KAAK,GAAGjB,SAAS,CAACO,IAAI,CAAC;UAE7B,IAAIU,KAAK,KAAKS,SAAS,IAAIJ,CAAC,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC1C;YACA,OAAO,EAAE;UACX;UAEA,OAAOC,kBAAkB,CAACd,KAAK,CAAC;QAClC;QAEA,OAAOc,kBAAkB,CAACT,CAAC,CAAC;MAC9B,CAAC,CAAC,CACDO,IAAI,CAAC,GAAG,CAAC;IACd,CAAC,MAAM;MACL/B,IAAI,IAAIiC,kBAAkB,CAAC5C,KAAK,CAACoB,IAAI,CAAC;IACxC;IAEA,IAAI,CAACL,aAAa,EAAE;MAClBA,aAAa,GAAGC,YAAY,CAACM,MAAM;IACrC;IAEA,IAAItB,KAAK,CAACD,KAAK,EAAE;MACfY,IAAI,IAAI,GAAG;IACb,CAAC,MAAM,IAAII,aAAa,EAAE;MACxB,KAAK,IAAI8B,KAAK,IAAI9B,aAAa,EAAE;QAC/B,IAAIA,aAAa,CAAC8B,KAAK,CAAC,KAAK,WAAW,EAAE;UACxC;UACA,OAAO9B,aAAa,CAAC8B,KAAK,CAAC;QAC7B;MACF;MAEA,MAAMC,KAAK,GAAGC,WAAW,CAACxB,SAAS,CAACR,aAAa,EAAE;QAAEiC,IAAI,EAAE;MAAM,CAAC,CAAC;MAEnE,IAAIF,KAAK,EAAE;QACTnC,IAAI,IAAK,IAAGmC,KAAM,EAAC;MACrB;IACF;IAEAlC,OAAO,GAAGZ,KAAK,CAACD,KAAK;EACvB;;EAEA;EACAY,IAAI,GAAGA,IAAI,CAACsC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChCtC,IAAI,GAAGA,IAAI,CAACR,MAAM,GAAG,CAAC,GAAGQ,IAAI,CAACsC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGtC,IAAI;EAEvD,OAAOA,IAAI;AACb;AAEA,MAAM2B,YAAY,GAAIxB,OAAe,IACnCA,OAAO,CAACmC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAE9C,MAAMC,SAAS,GAAG;EAAA,kCAAIC,KAAK;IAALA,KAAK;EAAA;EAAA,OACxB,EAAE,CACAC,MAAM,CAAC,GAAGD,KAAK,CAACvB,GAAG,CAAEO,CAAC,IAAKA,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACzCC,MAAM,CAACmB,OAAO,CAAC,CACfX,IAAI,CAAC,GAAG,CAAC;AAAA;AAEd,MAAMY,gBAAgB,GAAG,CACvBC,MAAmC,EACnCC,aAAsB,KACP;EAAA;EACf,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAMzC,OAAO,GAAG0C,aAAa,GAAGN,SAAS,CAACM,aAAa,EAAED,MAAM,CAAC,GAAGA,MAAM;IAEzE,OAAO;MAAEzC;IAAQ,CAAC;EACpB;;EAEA;EACA;EACA,IAAIA,OAA2B;EAE/B,IAAIyC,MAAM,CAACE,KAAK,IAAIF,MAAM,CAAC5C,IAAI,KAAK4B,SAAS,EAAE;IAC7C,MAAM,IAAIjC,KAAK,CACb,sJAAsJ,CACvJ;EACH;EAEAQ,OAAO,GACLyC,MAAM,CAACE,KAAK,KAAK,IAAI,GACjBP,SAAS,CAACM,aAAa,IAAI,EAAE,EAAED,MAAM,CAAC5C,IAAI,IAAI,EAAE,CAAC,GACjD4C,MAAM,CAAC5C,IAAI,IAAI,EAAE;EAEvB,MAAMF,OAAO,GAAG8C,MAAM,CAAC9C,OAAO,GAC1BC,uBAAuB,CAAC6C,MAAM,CAAC9C,OAAO,EAAEK,OAAO,CAAC,GAChDyB,SAAS;EAEb,OAAO;IACL;IACAzB,OAAO,eAAEA,OAAO,8CAAP,UAASmB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACmB,OAAO,CAAC,CAACX,IAAI,CAAC,GAAG,CAAC;IACtDnB,SAAS,EAAEgC,MAAM,CAAChC,SAAS;IAC3Bd;EACF,CAAC;AACH,CAAC;AAED,MAAMC,uBAAuB,GAAG,CAC9BL,OAA8B,EAC9BS,OAAgB,KAEhB,IAAAW,oBAAW,EACTC,MAAM,CAACC,OAAO,CAACtB,OAAO,CAAC,CAACuB,GAAG,CAAC,SAAe;EAAA,IAAd,CAACR,IAAI,EAAEsC,CAAC,CAAC;EACpC,MAAMC,MAAM,GAAGL,gBAAgB,CAACI,CAAC,EAAE5C,OAAO,CAAC;EAE3C,OAAO,CAACM,IAAI,EAAEuC,MAAM,CAAC;AACvB,CAAC,CAAC,CACH"}