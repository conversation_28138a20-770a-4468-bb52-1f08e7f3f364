{"version": 3, "names": ["React", "View", "Dummy", "children", "PanGestureHandler", "TapGestureHandler", "GestureHandlerRootView", "GestureState"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;AAMnC,MAAMC,KAAU,GAAG;EAAA,IAAC;IAAEC;EAAwC,CAAC;EAAA,oBAC7D,0CAAGA,QAAQ,CAAI;AAAA,CAChB;AAED,OAAO,MAAMC,iBAAiB,GAC5BF,KAAyD;AAE3D,OAAO,MAAMG,iBAAiB,GAC5BH,KAAyD;AAE3D,OAAO,MAAMI,sBAAsB,GAAGL,IAAI;AAE1C,WAAkBM,YAAY;AAO7B,WAPiBA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;AAAA,GAAZA,YAAY,KAAZA,YAAY"}