load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "YOGA_CXX_TARGET",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "modal",
    srcs = glob(
        ["**/*.cpp"],
        exclude = glob([
            "tests/**/*.cpp",
            "platform/**/*.cpp",
        ]),
    ),
    headers = [],
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/renderer/components/modal",
    ),
    compiler_flags_pedantic = True,
    fbandroid_deps = [
        "//xplat/folly:dynamic",
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
    ],
    fbandroid_exported_headers = subdir_glob(
        [
            ("", "*.h"),
            ("platform/android", "*.h"),
        ],
        prefix = "react/renderer/components/modal",
    ),
    fbandroid_headers = glob(
        ["platform/android/*.h"],
    ),
    fbandroid_srcs = glob(
        ["platform/android/*.cpp"],
    ),
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    ios_exported_headers = subdir_glob(
        [
            ("", "*.h"),
            ("platform/ios", "*.h"),
        ],
        prefix = "react/renderer/components/modal",
    ),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        YOGA_CXX_TARGET,
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/components/image:image"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("react/renderer/imagemanager:imagemanager"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        "//xplat/js/react-native-github:generated_components-rncore",
    ],
)
