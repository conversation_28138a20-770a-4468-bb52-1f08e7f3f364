{"version": 3, "names": ["func", "init", "detached", "name", "description", "options"], "sources": ["../../../src/commands/init/index.ts"], "sourcesContent": ["import init from './init';\n\nexport default {\n  func: init,\n  detached: true,\n  name: 'init <projectName>',\n  description:\n    'Initialize a new React Native project named <projectName> in a directory of the same name.',\n  options: [\n    {\n      name: '--version <string>',\n      description: 'Shortcut for `--template react-native@version`',\n    },\n    {\n      name: '--template <string>',\n      description:\n        'Uses a custom template. Valid arguments are the ones supported by `yarn add [package]` or `npm install [package]`, if you are using `--npm` option',\n    },\n    {\n      name: '--npm',\n      description: 'Forces using npm for initialization',\n    },\n    {\n      name: '--directory <string>',\n      description: 'Uses a custom directory instead of `<projectName>`.',\n    },\n    {\n      name: '--title <string>',\n      description: 'Uses a custom app title name for application',\n    },\n    {\n      name: '--skip-install',\n      description: 'Skips dependencies installation step',\n    },\n    {\n      name: '--package-name <string>',\n      description:\n        'Inits a project with a custom package name (Android) and bundle ID (iOS), e.g. com.example.app',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAAA;AAA0B;AAAA,eAEX;EACbA,IAAI,EAAEC,aAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT,4FAA4F;EAC9FC,OAAO,EAAE,CACP;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,yBAAyB;IAC/BC,WAAW,EACT;EACJ,CAAC;AAEL,CAAC;AAAA"}