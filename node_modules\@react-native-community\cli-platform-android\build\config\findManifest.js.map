{"version": 3, "names": ["findManifest", "folder", "manifestPath", "glob", "sync", "path", "join", "cwd", "ignore"], "sources": ["../../src/config/findManifest.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport glob from 'glob';\nimport path from 'path';\n\nexport default function findManifest(folder: string) {\n  const manifestPath = glob.sync(path.join('**', 'AndroidManifest.xml'), {\n    cwd: folder,\n    ignore: [\n      'node_modules/**',\n      '**/build/**',\n      '**/debug/**',\n      'Examples/**',\n      'examples/**',\n      '**/Pods/**',\n      '**/sdks/hermes/android/**',\n    ],\n  })[0];\n\n  return manifestPath ? path.join(folder, manifestPath) : null;\n}\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AATxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKe,SAASA,YAAY,CAACC,MAAc,EAAE;EACnD,MAAMC,YAAY,GAAGC,eAAI,CAACC,IAAI,CAACC,eAAI,CAACC,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,EAAE;IACrEC,GAAG,EAAEN,MAAM;IACXO,MAAM,EAAE,CACN,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,YAAY,EACZ,2BAA2B;EAE/B,CAAC,CAAC,CAAC,CAAC,CAAC;EAEL,OAAON,YAAY,GAAGG,eAAI,CAACC,IAAI,CAACL,MAAM,EAAEC,YAAY,CAAC,GAAG,IAAI;AAC9D"}