{"version": 3, "file": "getStateFromPath.d.ts", "sourceRoot": "", "sources": ["../../../src/getStateFromPath.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAEV,eAAe,EACf,YAAY,EACb,MAAM,2BAA2B,CAAC;AAKnC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAG7C,KAAK,OAAO,CAAC,SAAS,SAAS,EAAE,IAAI;IACnC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,OAAO,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;CACnC,CAAC;AAkBF,KAAK,WAAW,GAAG,YAAY,CAAC,eAAe,CAAC,GAAG;IACjD,KAAK,CAAC,EAAE,WAAW,CAAC;CACrB,CAAC;AAQF;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,CAAC,OAAO,UAAU,gBAAgB,CAAC,SAAS,SAAS,EAAE,EAC3D,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,GAC3B,WAAW,GAAG,SAAS,CAwLzB"}