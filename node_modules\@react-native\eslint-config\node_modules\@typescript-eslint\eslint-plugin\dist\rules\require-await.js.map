{"version": 3, "file": "require-await.js", "sourceRoot": "", "sources": ["../../src/rules/require-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AAGnC,8CAAgC;AAchC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;YACxE,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,YAAY,EAAE,qCAAqC;SACpD;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,IAAI,SAAS,GAAqB,IAAI,CAAC;QAEvC;;WAEG;QACH,SAAS,aAAa,CAAC,IAAkB;YACvC,SAAS,GAAG;gBACV,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,KAAK,EAAE,IAAI,CAAC,SAAS,IAAI,KAAK;gBAC9B,YAAY,EAAE,KAAK;aACpB,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAkB;YACtC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE;gBACvC,8EAA8E;gBAC9E,OAAO;aACR;YAED,IACE,IAAI,CAAC,KAAK;gBACV,CAAC,SAAS,CAAC,QAAQ;gBACnB,CAAC,eAAe,CAAC,IAAI,CAAC;gBACtB,CAAC,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,YAAY,CAAC,EAC5C;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC;oBACzC,SAAS,EAAE,cAAc;oBACzB,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;qBAC9D;iBACF,CAAC,CAAC;aACJ;YAED,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,CAAC;QAED;;WAEG;QACH,SAAS,cAAc,CAAC,IAAa;YACnC,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QAED;;WAEG;QACH,SAAS,cAAc;YACrB,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO;aACR;YACD,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED;;;WAGG;QACH,SAAS,oBAAoB,CAAC,IAA8B;;YAC1D,IAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,KAAK,CAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACvC,OAAO;aACR;YAED,IAAI,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,0CAAE,IAAI,MAAK,sBAAc,CAAC,OAAO,EAAE;gBACnD,4EAA4E;gBAC5E,0CAA0C;gBAC1C,SAAS,CAAC,YAAY,KAAtB,SAAS,CAAC,YAAY,GAAK,KAAK,EAAC;aAClC;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,YAAY,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;YACzD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;gBAC/B,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,CAC5D,IAAI,EACJ,eAAe,EACf,OAAO,CACR,CAAC;gBACF,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC/B,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC9B,MAAM;iBACP;aACF;QACH,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YACtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,eAAe,EAAE,cAAc;YAC/B,8BAA8B,EAAE,cAAc;YAC9C,kCAAkC,EAAE,oBAAoB;YAExD,wCAAwC;YACxC,gEAAgE;YAChE,+EAA+E,CAC7E,IAGC;gBAED,MAAM,UAAU,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClE,IAAI,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;oBAC5C,cAAc,EAAE,CAAC;iBAClB;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,uDAAuD;gBACvD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;oBAC3D,OAAO;iBACR;gBAED,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACtE,IAAI,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;oBAC5C,cAAc,EAAE,CAAC;iBAClB;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,eAAe,CAAC,IAAkB;;IACzC,OAAO,CACL,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc;QACjD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED,wHAAwH;AACxH;;GAEG;AACH,SAAS,uBAAuB,CAC9B,IAAkB,EAClB,UAA+B;IAE/B,OAAO,IAAI,CAAC,UAAU,CACpB,IAAI,CAAC,EAAE;QACL,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC;QAC7D,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAC5D,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CACpD,CAAC;AACJ,CAAC;AAED,0HAA0H;AAC1H;;GAEG;AACH,SAAS,kBAAkB,CACzB,IAAkB,EAClB,UAA+B;IAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACrC,CAAC;IACF,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,GAAG,GAAG,IAAI,CAAC;IAEf,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAChC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,EACvD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CACrD,CAAC;QAEF,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC;QAC7B,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;KAC1B;SAAM,IACL,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;QACvC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC/C;QACA,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;QACzB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;KAC3D;SAAM;QACL,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACvB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;KAC3D;IAED,OAAO;QACL,KAAK;QACL,GAAG;KACJ,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAa;IAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;KAC1D;IACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC"}