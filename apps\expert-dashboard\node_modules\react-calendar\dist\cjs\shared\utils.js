"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapCalendarType = exports.getTileClasses = exports.doRangesOverlap = exports.isRangeWithinRange = exports.isValueWithinRange = exports.between = void 0;
var warning_1 = __importDefault(require("warning"));
var const_js_1 = require("./const.js");
var dates_js_1 = require("./dates.js");
/**
 * Returns a value no smaller than min and no larger than max.
 *
 * @param {Date} value Value to return.
 * @param {Date} min Minimum return value.
 * @param {Date} max Maximum return value.
 * @returns {Date} Value between min and max.
 */
function between(value, min, max) {
    if (min && min > value) {
        return min;
    }
    if (max && max < value) {
        return max;
    }
    return value;
}
exports.between = between;
function isValueWithinRange(value, range) {
    return range[0] <= value && range[1] >= value;
}
exports.isValueWithinRange = isValueWithinRange;
function isRangeWithinRange(greaterRange, smallerRange) {
    return greaterRange[0] <= smallerRange[0] && greaterRange[1] >= smallerRange[1];
}
exports.isRangeWithinRange = isRangeWithinRange;
function doRangesOverlap(range1, range2) {
    return isValueWithinRange(range1[0], range2) || isValueWithinRange(range1[1], range2);
}
exports.doRangesOverlap = doRangesOverlap;
function getRangeClassNames(valueRange, dateRange, baseClassName) {
    var isRange = doRangesOverlap(dateRange, valueRange);
    var classes = [];
    if (isRange) {
        classes.push(baseClassName);
        var isRangeStart = isValueWithinRange(valueRange[0], dateRange);
        var isRangeEnd = isValueWithinRange(valueRange[1], dateRange);
        if (isRangeStart) {
            classes.push("".concat(baseClassName, "Start"));
        }
        if (isRangeEnd) {
            classes.push("".concat(baseClassName, "End"));
        }
        if (isRangeStart && isRangeEnd) {
            classes.push("".concat(baseClassName, "BothEnds"));
        }
    }
    return classes;
}
function isCompleteValue(value) {
    if (Array.isArray(value)) {
        return value[0] !== null && value[1] !== null;
    }
    return value !== null;
}
function getTileClasses(args) {
    if (!args) {
        throw new Error('args is required');
    }
    var value = args.value, date = args.date, hover = args.hover;
    var className = 'react-calendar__tile';
    var classes = [className];
    if (!date) {
        return classes;
    }
    var now = new Date();
    var dateRange = (function () {
        if (Array.isArray(date)) {
            return date;
        }
        var dateType = args.dateType;
        if (!dateType) {
            throw new Error('dateType is required when date is not an array of two dates');
        }
        return (0, dates_js_1.getRange)(dateType, date);
    })();
    if (isValueWithinRange(now, dateRange)) {
        classes.push("".concat(className, "--now"));
    }
    if (!value || !isCompleteValue(value)) {
        return classes;
    }
    var valueRange = (function () {
        if (Array.isArray(value)) {
            return value;
        }
        var valueType = args.valueType;
        if (!valueType) {
            throw new Error('valueType is required when value is not an array of two dates');
        }
        return (0, dates_js_1.getRange)(valueType, value);
    })();
    if (isRangeWithinRange(valueRange, dateRange)) {
        classes.push("".concat(className, "--active"));
    }
    else if (doRangesOverlap(valueRange, dateRange)) {
        classes.push("".concat(className, "--hasActive"));
    }
    var valueRangeClassNames = getRangeClassNames(valueRange, dateRange, "".concat(className, "--range"));
    classes.push.apply(classes, valueRangeClassNames);
    var valueArray = Array.isArray(value) ? value : [value];
    if (hover && valueArray.length === 1) {
        var hoverRange = hover > valueRange[0] ? [valueRange[0], hover] : [hover, valueRange[0]];
        var hoverRangeClassNames = getRangeClassNames(hoverRange, dateRange, "".concat(className, "--hover"));
        classes.push.apply(classes, hoverRangeClassNames);
    }
    return classes;
}
exports.getTileClasses = getTileClasses;
var calendarTypeMap = (_a = {},
    _a[const_js_1.DEPRECATED_CALENDAR_TYPES.ARABIC] = const_js_1.CALENDAR_TYPES.ISLAMIC,
    _a[const_js_1.DEPRECATED_CALENDAR_TYPES.HEBREW] = const_js_1.CALENDAR_TYPES.HEBREW,
    _a[const_js_1.DEPRECATED_CALENDAR_TYPES.ISO_8601] = const_js_1.CALENDAR_TYPES.ISO_8601,
    _a[const_js_1.DEPRECATED_CALENDAR_TYPES.US] = const_js_1.CALENDAR_TYPES.GREGORY,
    _a);
function isDeprecatedCalendarType(calendarType) {
    return calendarType !== undefined && calendarType in const_js_1.DEPRECATED_CALENDAR_TYPES;
}
var warned = false;
function mapCalendarType(calendarTypeOrDeprecatedCalendarType) {
    if (isDeprecatedCalendarType(calendarTypeOrDeprecatedCalendarType)) {
        var calendarType = calendarTypeMap[calendarTypeOrDeprecatedCalendarType];
        (0, warning_1.default)(warned, "Specifying calendarType=\"".concat(calendarTypeOrDeprecatedCalendarType, "\" is deprecated. Use calendarType=\"").concat(calendarType, "\" instead."));
        warned = true;
        return calendarType;
    }
    return calendarTypeOrDeprecatedCalendarType;
}
exports.mapCalendarType = mapCalendarType;
