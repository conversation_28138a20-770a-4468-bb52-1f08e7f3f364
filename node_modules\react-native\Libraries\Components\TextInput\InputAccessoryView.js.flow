/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

import * as React from 'react';
import Platform from '../../Utilities/Platform';
import StyleSheet, {
  type ViewStyleProp,
  type ColorValue,
} from '../../StyleSheet/StyleSheet';

type Props = $ReadOnly<{|
  +children: React.Node,
  nativeID?: ?string,
  style?: ?ViewStyleProp,
  backgroundColor?: ?ColorValue,
|}>;

module.exports = class InputAccessoryView extends React.Component<Props> {};
