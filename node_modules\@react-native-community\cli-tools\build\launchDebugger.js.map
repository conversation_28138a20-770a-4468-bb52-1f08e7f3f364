{"version": 3, "names": ["launchDebugger", "url", "launchDefaultBrowser"], "sources": ["../src/launchDebugger.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport launchDefaultBrowser from './launchDefaultBrowser';\n\nasync function launchDebugger(url: string) {\n  return launchDefaultBrowser(url);\n}\n\nexport default launchDebugger;\n"], "mappings": ";;;;;;AASA;AAA0D;AAT1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA,eAAeA,cAAc,CAACC,GAAW,EAAE;EACzC,OAAO,IAAAC,6BAAoB,EAACD,GAAG,CAAC;AAClC;AAAC,eAEcD,cAAc;AAAA"}