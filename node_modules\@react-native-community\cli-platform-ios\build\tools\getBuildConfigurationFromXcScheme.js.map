{"version": 3, "names": ["xmlParser", "XMLParser", "ignoreAttributes", "getBuildConfigurationFromXcScheme", "scheme", "configuration", "sourceDir", "xcProject", "fs", "readdirSync", "find", "dir", "includes", "xmlScheme", "readFileSync", "path", "join", "encoding", "Scheme", "parse", "LaunchAction", "CLIError"], "sources": ["../../src/tools/getBuildConfigurationFromXcScheme.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport {XMLParser} from 'fast-xml-parser';\nimport fs from 'fs';\nimport path from 'path';\n\nconst xmlParser = new XMLParser({ignoreAttributes: false});\n\nexport function getBuildConfigurationFromXcScheme(\n  scheme: string,\n  configuration: string,\n  sourceDir: string,\n) {\n  try {\n    const xcProject = fs\n      .readdirSync(sourceDir)\n      .find((dir) => dir.includes('.xcodeproj'));\n\n    if (xcProject) {\n      const xmlScheme = fs.readFileSync(\n        path.join(\n          sourceDir,\n          xcProject,\n          'xcshareddata',\n          'xcschemes',\n          `${scheme}.xcscheme`,\n        ),\n        {\n          encoding: 'utf-8',\n        },\n      );\n\n      const {Scheme} = xmlParser.parse(xmlScheme);\n\n      return Scheme.LaunchAction['@_buildConfiguration'];\n    }\n  } catch {\n    throw new CLIError(\n      `Could not find scheme ${scheme}. Please make sure the schema you want to run exists.`,\n    );\n  }\n\n  return configuration;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAExB,MAAMA,SAAS,GAAG,KAAIC,0BAAS,EAAC;EAACC,gBAAgB,EAAE;AAAK,CAAC,CAAC;AAEnD,SAASC,iCAAiC,CAC/CC,MAAc,EACdC,aAAqB,EACrBC,SAAiB,EACjB;EACA,IAAI;IACF,MAAMC,SAAS,GAAGC,aAAE,CACjBC,WAAW,CAACH,SAAS,CAAC,CACtBI,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE5C,IAAIL,SAAS,EAAE;MACb,MAAMM,SAAS,GAAGL,aAAE,CAACM,YAAY,CAC/BC,eAAI,CAACC,IAAI,CACPV,SAAS,EACTC,SAAS,EACT,cAAc,EACd,WAAW,EACV,GAAEH,MAAO,WAAU,CACrB,EACD;QACEa,QAAQ,EAAE;MACZ,CAAC,CACF;MAED,MAAM;QAACC;MAAM,CAAC,GAAGlB,SAAS,CAACmB,KAAK,CAACN,SAAS,CAAC;MAE3C,OAAOK,MAAM,CAACE,YAAY,CAAC,sBAAsB,CAAC;IACpD;EACF,CAAC,CAAC,MAAM;IACN,MAAM,KAAIC,oBAAQ,EACf,yBAAwBjB,MAAO,uDAAsD,CACvF;EACH;EAEA,OAAOC,aAAa;AACtB"}