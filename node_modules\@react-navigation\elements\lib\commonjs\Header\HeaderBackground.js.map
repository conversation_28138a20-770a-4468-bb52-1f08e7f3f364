{"version": 3, "names": ["HeaderBackground", "style", "rest", "colors", "useTheme", "styles", "container", "backgroundColor", "card", "borderBottomColor", "border", "shadowColor", "StyleSheet", "create", "flex", "Platform", "select", "android", "elevation", "ios", "shadowOpacity", "shadowRadius", "shadowOffset", "width", "height", "hairlineWidth", "default", "borderBottomWidth"], "sourceRoot": "../../../src", "sources": ["Header/HeaderBackground.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAOsB;AAAA;AAAA;AAOP,SAASA,gBAAgB,OAA4B;EAAA,IAA3B;IAAEC,KAAK;IAAE,GAAGC;EAAY,CAAC;EAChE,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAE7B,oBACE,oBAAC,qBAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChB;MACEC,eAAe,EAAEJ,MAAM,CAACK,IAAI;MAC5BC,iBAAiB,EAAEN,MAAM,CAACO,MAAM;MAChCC,WAAW,EAAER,MAAM,CAACO;IACtB,CAAC,EACDT,KAAK;EACL,GACEC,IAAI,EACR;AAEN;AAEA,MAAMG,MAAM,GAAGO,uBAAU,CAACC,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACP,GAAGC,qBAAQ,CAACC,MAAM,CAAC;MACjBC,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACDC,GAAG,EAAE;QACHC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;UACZC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAEZ,uBAAU,CAACa;QACrB;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,iBAAiB,EAAEf,uBAAU,CAACa;MAChC;IACF,CAAC;EACH;AACF,CAAC,CAAC"}