{"version": 3, "names": ["useNavigationCache", "state", "getState", "navigation", "setOptions", "router", "emitter", "stackRef", "React", "useContext", "NavigationBuilderContext", "cache", "useMemo", "current", "actions", "actionCreators", "CommonActions", "routes", "reduce", "acc", "route", "previous", "key", "emit", "rest", "dispatch", "thunk", "action", "source", "withStack", "callback", "isStackSet", "process", "env", "NODE_ENV", "Error", "stack", "undefined", "helpers", "Object", "keys", "name", "args", "create", "getParent", "id", "getId", "options", "o", "isFocused", "index"], "sourceRoot": "../../src", "sources": ["useNavigationCache.tsx"], "mappings": ";;;;;;AAAA;AAOA;AAEA;AAAkE;AAAA;AAAA;AAmClE;AACA;AACA;AACA;AACA;AACe,SAASA,kBAAkB,OAWb;EAAA,IAP3B;IACAC,KAAK;IACLC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVC,MAAM;IACNC;EACwB,CAAC;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAwB,CAAC;;EAE/D;EACA;EACA;EACA,MAAMC,KAAK,GAAGH,KAAK,CAACI,OAAO,CACzB,OAAO;IAAEC,OAAO,EAAE,CAAC;EAAqD,CAAC,CAAC;EAC1E;EACA,CAACX,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAEC,OAAO,CAAC,CACpD;EAED,MAAMQ,OAAO,GAAG;IACd,GAAGT,MAAM,CAACU,cAAc;IACxB,GAAGC;EACL,CAAC;EAEDL,KAAK,CAACE,OAAO,GAAGZ,KAAK,CAACgB,MAAM,CAACC,MAAM,CAEjC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAChB,MAAMC,QAAQ,GAAGV,KAAK,CAACE,OAAO,CAACO,KAAK,CAACE,GAAG,CAAC;IAMzC,IAAID,QAAQ,EAAE;MACZ;MACAF,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAGD,QAAQ;IAC3B,CAAC,MAAM;MACL;MACA,MAAM;QAAEE,IAAI;QAAE,GAAGC;MAAK,CAAC,GAAGrB,UAAU;MAEpC,MAAMsB,QAAQ,GAAIC,KAAY,IAAK;QACjC,MAAMC,MAAM,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACxB,QAAQ,EAAE,CAAC,GAAGwB,KAAK;QAEtE,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClBxB,UAAU,CAACsB,QAAQ,CAAC;YAAEG,MAAM,EAAER,KAAK,CAACE,GAAG;YAAE,GAAGK;UAAO,CAAC,CAAC;QACvD;MACF,CAAC;MAED,MAAME,SAAS,GAAIC,QAAoB,IAAK;QAC1C,IAAIC,UAAU,GAAG,KAAK;QAEtB,IAAI;UACF,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrC3B,QAAQ,IACR,CAACA,QAAQ,CAACM,OAAO,EACjB;YACA;YACAN,QAAQ,CAACM,OAAO,GAAG,IAAIsB,KAAK,EAAE,CAACC,KAAK;YACpCL,UAAU,GAAG,IAAI;UACnB;UAEAD,QAAQ,EAAE;QACZ,CAAC,SAAS;UACR,IAAIC,UAAU,IAAIxB,QAAQ,EAAE;YAC1BA,QAAQ,CAACM,OAAO,GAAGwB,SAAS;UAC9B;QACF;MACF,CAAC;MAED,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC1B,OAAO,CAAC,CAACI,MAAM,CACzC,CAACC,GAAG,EAAEsB,IAAI,KAAK;QACbtB,GAAG,CAACsB,IAAI,CAAC,GAAG;UAAA,kCAAIC,IAAI;YAAJA,IAAI;UAAA;UAAA,OAClBb,SAAS,CAAC;UACR;UACAJ,QAAQ,CAACX,OAAO,CAAC2B,IAAI,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CACjC;QAAA;QAEH,OAAOvB,GAAG;MACZ,CAAC,EACD,CAAC,CAAC,CACH;MAEDA,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAG;QACf,GAAGE,IAAI;QACP,GAAGc,OAAO;QACV;QACA,GAAIhC,OAAO,CAACqC,MAAM,CAACvB,KAAK,CAACE,GAAG,CAAS;QACrCG,QAAQ,EAAGC,KAAY,IAAKG,SAAS,CAAC,MAAMJ,QAAQ,CAACC,KAAK,CAAC,CAAC;QAC5DkB,SAAS,EAAGC,EAAW,IAAK;UAC1B,IAAIA,EAAE,KAAKR,SAAS,IAAIQ,EAAE,KAAKrB,IAAI,CAACsB,KAAK,EAAE,EAAE;YAC3C;YACA;YACA,OAAO3B,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC;UACvB;UAEA,OAAOE,IAAI,CAACoB,SAAS,CAACC,EAAE,CAAC;QAC3B,CAAC;QACDzC,UAAU,EAAG2C,OAAe,IAC1B3C,UAAU,CAAE4C,CAAC,KAAM;UACjB,GAAGA,CAAC;UACJ,CAAC5B,KAAK,CAACE,GAAG,GAAG;YAAE,GAAG0B,CAAC,CAAC5B,KAAK,CAACE,GAAG,CAAC;YAAE,GAAGyB;UAAQ;QAC7C,CAAC,CAAC,CAAC;QACLE,SAAS,EAAE,MAAM;UACf,MAAMhD,KAAK,GAAGC,QAAQ,EAAE;UAExB,IAAID,KAAK,CAACgB,MAAM,CAAChB,KAAK,CAACiD,KAAK,CAAC,CAAC5B,GAAG,KAAKF,KAAK,CAACE,GAAG,EAAE;YAC/C,OAAO,KAAK;UACd;;UAEA;UACA;UACA,OAAOnB,UAAU,GAAGA,UAAU,CAAC8C,SAAS,EAAE,GAAG,IAAI;QACnD;MACF,CAAC;IACH;IAEA,OAAO9B,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAOR,KAAK,CAACE,OAAO;AACtB"}