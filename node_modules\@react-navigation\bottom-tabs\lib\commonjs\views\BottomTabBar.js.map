{"version": 3, "names": ["DEFAULT_TABBAR_HEIGHT", "COMPACT_TABBAR_HEIGHT", "DEFAULT_MAX_TAB_ITEM_WIDTH", "useNativeDriver", "Platform", "OS", "shouldUseHorizontalLabels", "state", "descriptors", "layout", "dimensions", "tabBarLabelPosition", "routes", "index", "key", "options", "width", "max<PERSON>ab<PERSON><PERSON><PERSON>", "reduce", "acc", "route", "tabBarItemStyle", "flattenedStyle", "StyleSheet", "flatten", "max<PERSON><PERSON><PERSON>", "height", "getPaddingBottom", "insets", "Math", "max", "bottom", "select", "ios", "default", "getTabBarHeight", "style", "rest", "customHeight", "isLandscape", "horizontalLabels", "paddingBottom", "isPad", "BottomTabBar", "navigation", "colors", "useTheme", "buildLink", "useLinkBuilder", "focusedRoute", "focusedDescriptor", "focusedOptions", "tabBarShowLabel", "tabBarHideOnKeyboard", "tabBarVisibilityAnimationConfig", "tabBarStyle", "tabBarBackground", "tabBarActiveTintColor", "tabBarInactiveTintColor", "tabBarActiveBackgroundColor", "tabBarInactiveBackgroundColor", "useSafeAreaFrame", "isKeyboardShown", "useIsKeyboardShown", "onHeightChange", "React", "useContext", "BottomTabBarHeightCallbackContext", "shouldShowTabBar", "visibilityAnimationConfigRef", "useRef", "useEffect", "current", "isTabBarHidden", "setIsTabBarHidden", "useState", "visible", "Animated", "Value", "visibilityAnimationConfig", "animation", "show", "spring", "timing", "toValue", "duration", "config", "start", "finished", "hide", "stopAnimation", "setLayout", "handleLayout", "e", "nativeEvent", "tabBarHeight", "hasHorizontalLabels", "tabBarBackgroundElement", "styles", "tabBar", "backgroundColor", "card", "borderTopColor", "border", "transform", "translateY", "interpolate", "inputRange", "outputRange", "hairlineWidth", "position", "paddingHorizontal", "left", "right", "absoluteFill", "content", "map", "focused", "onPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "CommonActions", "navigate", "name", "merge", "onLongPress", "label", "tabBarLabel", "undefined", "title", "accessibilityLabel", "tabBarAccessibilityLabel", "length", "params", "tabBarTestID", "tabBarAllowFontScaling", "tabBarButton", "tabBarIcon", "color", "size", "tabBarBadge", "tabBarBadgeStyle", "tabBarLabelStyle", "tabBarIconStyle", "create", "borderTopWidth", "elevation", "flex", "flexDirection"], "sourceRoot": "../../../src", "sources": ["views/BottomTabBar.tsx"], "mappings": ";;;;;;;AAAA;AACA;AASA;AACA;AASA;AAGA;AACA;AACA;AAA4C;AAM5C,MAAMA,qBAAqB,GAAG,EAAE;AAChC,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,0BAA0B,GAAG,GAAG;AAEtC,MAAMC,eAAe,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;AAS7C,MAAMC,yBAAyB,GAAG,QAKnB;EAAA,IALoB;IACjCC,KAAK;IACLC,WAAW;IACXC,MAAM;IACNC;EACO,CAAC;EACR,MAAM;IAAEC;EAAoB,CAAC,GAC3BH,WAAW,CAACD,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO;EAEpD,IAAIJ,mBAAmB,EAAE;IACvB,QAAQA,mBAAmB;MACzB,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;IAAC;EAEnB;EAEA,IAAIF,MAAM,CAACO,KAAK,IAAI,GAAG,EAAE;IACvB;IACA,MAAMC,WAAW,GAAGV,KAAK,CAACK,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MACtD,MAAM;QAAEC;MAAgB,CAAC,GAAGb,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAACC,OAAO;MAC1D,MAAMO,cAAc,GAAGC,uBAAU,CAACC,OAAO,CAACH,eAAe,CAAC;MAE1D,IAAIC,cAAc,EAAE;QAClB,IAAI,OAAOA,cAAc,CAACN,KAAK,KAAK,QAAQ,EAAE;UAC5C,OAAOG,GAAG,GAAGG,cAAc,CAACN,KAAK;QACnC,CAAC,MAAM,IAAI,OAAOM,cAAc,CAACG,QAAQ,KAAK,QAAQ,EAAE;UACtD,OAAON,GAAG,GAAGG,cAAc,CAACG,QAAQ;QACtC;MACF;MAEA,OAAON,GAAG,GAAGjB,0BAA0B;IACzC,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOe,WAAW,IAAIR,MAAM,CAACO,KAAK;EACpC,CAAC,MAAM;IACL,OAAON,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACgB,MAAM;EAC7C;AACF,CAAC;AAED,MAAMC,gBAAgB,GAAIC,MAAkB,IAC1CC,IAAI,CAACC,GAAG,CAACF,MAAM,CAACG,MAAM,GAAG3B,qBAAQ,CAAC4B,MAAM,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAE/D,MAAMC,eAAe,GAAG,SAUzB;EAAA;EAAA,IAV0B;IAC9B5B,KAAK;IACLC,WAAW;IACXE,UAAU;IACVkB,MAAM;IACNQ,KAAK;IACL,GAAGC;EAIL,CAAC;EACC;EACA,MAAMC,YAAY,0BAAGf,uBAAU,CAACC,OAAO,CAACY,KAAK,CAAC,wDAAzB,oBAA2BV,MAAM;EAEtD,IAAI,OAAOY,YAAY,KAAK,QAAQ,EAAE;IACpC,OAAOA,YAAY;EACrB;EAEA,MAAMC,WAAW,GAAG7B,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACgB,MAAM;EACxD,MAAMc,gBAAgB,GAAGlC,yBAAyB,CAAC;IACjDC,KAAK;IACLC,WAAW;IACXE,UAAU;IACV,GAAG2B;EACL,CAAC,CAAC;EACF,MAAMI,aAAa,GAAGd,gBAAgB,CAACC,MAAM,CAAC;EAE9C,IACExB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrB,CAACD,qBAAQ,CAACsC,KAAK,IACfH,WAAW,IACXC,gBAAgB,EAChB;IACA,OAAOvC,qBAAqB,GAAGwC,aAAa;EAC9C;EAEA,OAAOzC,qBAAqB,GAAGyC,aAAa;AAC9C,CAAC;AAAC;AAEa,SAASE,YAAY,QAM1B;EAAA,IAN2B;IACnCpC,KAAK;IACLqC,UAAU;IACVpC,WAAW;IACXoB,MAAM;IACNQ;EACK,CAAC;EACN,MAAM;IAAES;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAC7B,MAAMC,SAAS,GAAG,IAAAC,sBAAc,GAAE;EAElC,MAAMC,YAAY,GAAG1C,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC;EAC9C,MAAMqC,iBAAiB,GAAG1C,WAAW,CAACyC,YAAY,CAACnC,GAAG,CAAC;EACvD,MAAMqC,cAAc,GAAGD,iBAAiB,CAACnC,OAAO;EAEhD,MAAM;IACJqC,eAAe;IACfC,oBAAoB,GAAG,KAAK;IAC5BC,+BAA+B;IAC/BC,WAAW;IACXC,gBAAgB;IAChBC,qBAAqB;IACrBC,uBAAuB;IACvBC,2BAA2B;IAC3BC;EACF,CAAC,GAAGT,cAAc;EAElB,MAAMzC,UAAU,GAAG,IAAAmD,4CAAgB,GAAE;EACrC,MAAMC,eAAe,GAAG,IAAAC,2BAAkB,GAAE;EAE5C,MAAMC,cAAc,GAAGC,cAAK,CAACC,UAAU,CAACC,0CAAiC,CAAC;EAE1E,MAAMC,gBAAgB,GAAG,EAAEf,oBAAoB,IAAIS,eAAe,CAAC;EAEnE,MAAMO,4BAA4B,GAAGJ,cAAK,CAACK,MAAM,CAC/ChB,+BAA+B,CAChC;EAEDW,cAAK,CAACM,SAAS,CAAC,MAAM;IACpBF,4BAA4B,CAACG,OAAO,GAAGlB,+BAA+B;EACxE,CAAC,CAAC;EAEF,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGT,cAAK,CAACU,QAAQ,CAAC,CAACP,gBAAgB,CAAC;EAE7E,MAAM,CAACQ,OAAO,CAAC,GAAGX,cAAK,CAACU,QAAQ,CAC9B,MAAM,IAAIE,qBAAQ,CAACC,KAAK,CAACV,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CACnD;EAEDH,cAAK,CAACM,SAAS,CAAC,MAAM;IACpB,MAAMQ,yBAAyB,GAAGV,4BAA4B,CAACG,OAAO;IAEtE,IAAIJ,gBAAgB,EAAE;MAAA;MACpB,MAAMY,SAAS,GACb,CAAAD,yBAAyB,aAAzBA,yBAAyB,gDAAzBA,yBAAyB,CAAEE,IAAI,0DAA/B,sBAAiCD,SAAS,MAAK,QAAQ,GACnDH,qBAAQ,CAACK,MAAM,GACfL,qBAAQ,CAACM,MAAM;MAErBH,SAAS,CAACJ,OAAO,EAAE;QACjBQ,OAAO,EAAE,CAAC;QACVjF,eAAe;QACfkF,QAAQ,EAAE,GAAG;QACb,IAAGN,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEE,IAAI,2DAA/B,uBAAiCK,MAAM;MAC5C,CAAC,CAAC,CAACC,KAAK,CAAC,SAAkB;QAAA,IAAjB;UAAEC;QAAS,CAAC;QACpB,IAAIA,QAAQ,EAAE;UACZd,iBAAiB,CAAC,KAAK,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA;MACLA,iBAAiB,CAAC,IAAI,CAAC;MAEvB,MAAMM,SAAS,GACb,CAAAD,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEU,IAAI,2DAA/B,uBAAiCT,SAAS,MAAK,QAAQ,GACnDH,qBAAQ,CAACK,MAAM,GACfL,qBAAQ,CAACM,MAAM;MAErBH,SAAS,CAACJ,OAAO,EAAE;QACjBQ,OAAO,EAAE,CAAC;QACVjF,eAAe;QACfkF,QAAQ,EAAE,GAAG;QACb,IAAGN,yBAAyB,aAAzBA,yBAAyB,iDAAzBA,yBAAyB,CAAEU,IAAI,2DAA/B,uBAAiCH,MAAM;MAC5C,CAAC,CAAC,CAACC,KAAK,EAAE;IACZ;IAEA,OAAO,MAAMX,OAAO,CAACc,aAAa,EAAE;EACtC,CAAC,EAAE,CAACd,OAAO,EAAER,gBAAgB,CAAC,CAAC;EAE/B,MAAM,CAAC3D,MAAM,EAAEkF,SAAS,CAAC,GAAG1B,cAAK,CAACU,QAAQ,CAAC;IACzCjD,MAAM,EAAE,CAAC;IACTV,KAAK,EAAEN,UAAU,CAACM;EACpB,CAAC,CAAC;EAEF,MAAM4E,YAAY,GAAIC,CAAoB,IAAK;IAC7C,MAAM;MAAEnE,MAAM;MAAEV;IAAM,CAAC,GAAG6E,CAAC,CAACC,WAAW,CAACrF,MAAM;IAE9CuD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGtC,MAAM,CAAC;IAExBiE,SAAS,CAAElF,MAAM,IAAK;MACpB,IAAIiB,MAAM,KAAKjB,MAAM,CAACiB,MAAM,IAAIV,KAAK,KAAKP,MAAM,CAACO,KAAK,EAAE;QACtD,OAAOP,MAAM;MACf,CAAC,MAAM;QACL,OAAO;UACLiB,MAAM;UACNV;QACF,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM;IAAEJ;EAAO,CAAC,GAAGL,KAAK;EAExB,MAAMkC,aAAa,GAAGd,gBAAgB,CAACC,MAAM,CAAC;EAC9C,MAAMmE,YAAY,GAAG5D,eAAe,CAAC;IACnC5B,KAAK;IACLC,WAAW;IACXoB,MAAM;IACNlB,UAAU;IACVD,MAAM;IACN2B,KAAK,EAAE,CAACmB,WAAW,EAAEnB,KAAK;EAC5B,CAAC,CAAC;EAEF,MAAM4D,mBAAmB,GAAG1F,yBAAyB,CAAC;IACpDC,KAAK;IACLC,WAAW;IACXE,UAAU;IACVD;EACF,CAAC,CAAC;EAEF,MAAMwF,uBAAuB,GAAGzC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,EAAI;EAEpD,oBACE,6BAAC,qBAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACL0C,MAAM,CAACC,MAAM,EACb;MACEC,eAAe,EACbH,uBAAuB,IAAI,IAAI,GAAG,aAAa,GAAGpD,MAAM,CAACwD,IAAI;MAC/DC,cAAc,EAAEzD,MAAM,CAAC0D;IACzB,CAAC,EACD;MACEC,SAAS,EAAE,CACT;QACEC,UAAU,EAAE7B,OAAO,CAAC8B,WAAW,CAAC;UAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CACXnG,MAAM,CAACiB,MAAM,GAAGe,aAAa,GAAGlB,uBAAU,CAACsF,aAAa,EACxD,CAAC;QAEL,CAAC;MACH,CAAC,CACF;MACD;MACA;MACAC,QAAQ,EAAErC,cAAc,GAAG,UAAU,GAAI;IAC3C,CAAC,EACD;MACE/C,MAAM,EAAEqE,YAAY;MACpBtD,aAAa;MACbsE,iBAAiB,EAAElF,IAAI,CAACC,GAAG,CAACF,MAAM,CAACoF,IAAI,EAAEpF,MAAM,CAACqF,KAAK;IACvD,CAAC,EACD1D,WAAW,CACX;IACF,aAAa,EAAEkB,cAAc,GAAG,MAAM,GAAG,MAAO;IAChD,QAAQ,EAAEmB;EAAa,gBAEvB,6BAAC,iBAAI;IAAC,aAAa,EAAC,MAAM;IAAC,KAAK,EAAErE,uBAAU,CAAC2F;EAAa,GACvDjB,uBAAuB,CACnB,eACP,6BAAC,iBAAI;IAAC,iBAAiB,EAAC,SAAS;IAAC,KAAK,EAAEC,MAAM,CAACiB;EAAQ,GACrDvG,MAAM,CAACwG,GAAG,CAAC,CAAChG,KAAK,EAAEP,KAAK,KAAK;IAC5B,MAAMwG,OAAO,GAAGxG,KAAK,KAAKN,KAAK,CAACM,KAAK;IACrC,MAAM;MAAEE;IAAQ,CAAC,GAAGP,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC;IAE1C,MAAMwG,OAAO,GAAG,MAAM;MACpB,MAAMC,KAAK,GAAG3E,UAAU,CAAC4E,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAEtG,KAAK,CAACN,GAAG;QACjB6G,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAI,CAACN,OAAO,IAAI,CAACE,KAAK,CAACK,gBAAgB,EAAE;QACvChF,UAAU,CAACiF,QAAQ,CAAC;UAClB,GAAGC,qBAAa,CAACC,QAAQ,CAAC;YAAEC,IAAI,EAAE5G,KAAK,CAAC4G,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC;UAC5DP,MAAM,EAAEnH,KAAK,CAACO;QAChB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAMoH,WAAW,GAAG,MAAM;MACxBtF,UAAU,CAAC4E,IAAI,CAAC;QACdC,IAAI,EAAE,cAAc;QACpBC,MAAM,EAAEtG,KAAK,CAACN;MAChB,CAAC,CAAC;IACJ,CAAC;IAED,MAAMqH,KAAK,GACTpH,OAAO,CAACqH,WAAW,KAAKC,SAAS,GAC7BtH,OAAO,CAACqH,WAAW,GACnBrH,OAAO,CAACuH,KAAK,KAAKD,SAAS,GAC3BtH,OAAO,CAACuH,KAAK,GACblH,KAAK,CAAC4G,IAAI;IAEhB,MAAMO,kBAAkB,GACtBxH,OAAO,CAACyH,wBAAwB,KAAKH,SAAS,GAC1CtH,OAAO,CAACyH,wBAAwB,GAChC,OAAOL,KAAK,KAAK,QAAQ,IAAI/H,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjD,GAAE8H,KAAM,UAAStH,KAAK,GAAG,CAAE,OAAMD,MAAM,CAAC6H,MAAO,EAAC,GACjDJ,SAAS;IAEf,oBACE,6BAAC,yBAAiB,CAAC,QAAQ;MACzB,GAAG,EAAEjH,KAAK,CAACN,GAAI;MACf,KAAK,EAAEN,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAAC8B;IAAW,gBAEzC,6BAAC,8BAAsB,CAAC,QAAQ;MAAC,KAAK,EAAExB;IAAM,gBAC5C,6BAAC,sBAAa;MACZ,KAAK,EAAEA,KAAM;MACb,UAAU,EAAEZ,WAAW,CAACY,KAAK,CAACN,GAAG,CAAE;MACnC,OAAO,EAAEuG,OAAQ;MACjB,UAAU,EAAErB,mBAAoB;MAChC,OAAO,EAAEsB,OAAQ;MACjB,WAAW,EAAEY,WAAY;MACzB,kBAAkB,EAAEK,kBAAmB;MACvC,EAAE,EAAExF,SAAS,CAAC3B,KAAK,CAAC4G,IAAI,EAAE5G,KAAK,CAACsH,MAAM,CAAE;MACxC,MAAM,EAAE3H,OAAO,CAAC4H,YAAa;MAC7B,gBAAgB,EAAE5H,OAAO,CAAC6H,sBAAuB;MACjD,eAAe,EAAEnF,qBAAsB;MACvC,iBAAiB,EAAEC,uBAAwB;MAC3C,qBAAqB,EAAEC,2BAA4B;MACnD,uBAAuB,EAAEC,6BAA8B;MACvD,MAAM,EAAE7C,OAAO,CAAC8H,YAAa;MAC7B,IAAI,EACF9H,OAAO,CAAC+H,UAAU,KACjB;QAAA,IAAC;UAAEC,KAAK;UAAEC;QAAK,CAAC;QAAA,oBACf,6BAAC,qBAAW;UAAC,KAAK,EAAED,KAAM;UAAC,IAAI,EAAEC;QAAK,EAAG;MAAA,CAC1C,CACF;MACD,KAAK,EAAEjI,OAAO,CAACkI,WAAY;MAC3B,UAAU,EAAElI,OAAO,CAACmI,gBAAiB;MACrC,KAAK,EAAEf,KAAM;MACb,SAAS,EAAE/E,eAAgB;MAC3B,UAAU,EAAErC,OAAO,CAACoI,gBAAiB;MACrC,SAAS,EAAEpI,OAAO,CAACqI,eAAgB;MACnC,KAAK,EAAErI,OAAO,CAACM;IAAgB,EAC/B,CAC8B,CACP;EAEjC,CAAC,CAAC,CACG,CACO;AAEpB;AAEA,MAAM6E,MAAM,GAAG3E,uBAAU,CAAC8H,MAAM,CAAC;EAC/BlD,MAAM,EAAE;IACNa,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRlF,MAAM,EAAE,CAAC;IACTuH,cAAc,EAAE/H,uBAAU,CAACsF,aAAa;IACxC0C,SAAS,EAAE;EACb,CAAC;EACDpC,OAAO,EAAE;IACPqC,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC"}