load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_prebuilt_jar(
    name = "yoga-annotations",
    binary_jar = ":annotations-binary.jar",
    visibility = ["PUBLIC"],
)

fb_native.remote_file(
    name = "annotations-binary.jar",
    sha1 = "fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63",
    url = "mvn:com.facebook.yoga:proguard-annotations:jar:1.19.0",
)

rn_android_library(
    name = "annotations",
    srcs = glob(["*.java"]),
    autoglob = False,
    language = "JAVA",
    proguard_config = "proguard_annotations.pro",
    visibility = [
        "PUBLIC",
    ],
    exported_deps = [
        ":yoga-annotations",
    ],
)
