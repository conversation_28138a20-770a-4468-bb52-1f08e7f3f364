{"version": 3, "names": ["profileHermes", "dst<PERSON><PERSON>", "ctx", "options", "logger", "info", "filename", "downloadProfile", "sourcemapPath", "raw", "generateSourcemap", "port", "appId", "appIdSuffix", "host", "err", "name", "description", "func", "default", "process", "env", "RCT_METRO_PORT", "examples", "desc", "cmd"], "sources": ["../../src/profileHermes/index.ts"], "sourcesContent": ["import {logger, CLIError} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport {downloadProfile} from './downloadProfile';\n\ntype Options = {\n  filename?: string;\n  raw?: boolean;\n  sourcemapPath?: string;\n  generateSourcemap?: boolean;\n  port: string;\n  appId?: string;\n  appIdSuffix?: string;\n  host?: string;\n};\n\nasync function profileHermes(\n  [dstPath]: Array<string>,\n  ctx: Config,\n  options: Options,\n) {\n  try {\n    logger.info(\n      'Downloading a Hermes Sampling Profiler from your Android device...',\n    );\n    if (!options.filename) {\n      logger.info('No filename is provided, pulling latest file');\n    }\n    await downloadProfile(\n      ctx,\n      dstPath,\n      options.filename,\n      options.sourcemapPath,\n      options.raw,\n      options.generateSourcemap,\n      options.port,\n      options.appId,\n      options.appIdSuffix,\n      options.host,\n    );\n  } catch (err) {\n    throw err as CLIError;\n  }\n}\n\nexport default {\n  name: 'profile-hermes [destinationDir]',\n  description:\n    'Pull and convert a Hermes tracing profile to Chrome tracing profile, then store it in the directory <destinationDir> of the local machine',\n  func: profileHermes,\n  options: [\n    {\n      name: '--filename <string>',\n      description:\n        'File name of the profile to be downloaded, eg. sampling-profiler-trace8593107139682635366.cpuprofile',\n    },\n    {\n      name: '--raw',\n      description:\n        'Pulls the original Hermes tracing profile without any transformation',\n    },\n    {\n      name: '--sourcemap-path <string>',\n      description:\n        'The local path to your source map file, eg. /tmp/sourcemap.json',\n    },\n    {\n      name: '--generate-sourcemap',\n      description: 'Generates the JS bundle and source map',\n    },\n    {\n      name: '--port <number>',\n      default: `${process.env.RCT_METRO_PORT || 8081}`,\n    },\n    {\n      name: '--appId <string>',\n      description:\n        'Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.',\n    },\n    {\n      name: '--appIdSuffix <string>',\n      description: 'Specify an applicationIdSuffix to launch after build.',\n    },\n    {\n      name: '--host <string>',\n      description: 'The host of the packager.',\n      default: 'localhost',\n    },\n  ],\n  examples: [\n    {\n      desc:\n        'Download the Hermes Sampling Profiler to the directory <destinationDir> on the local machine',\n      cmd: 'profile-hermes /tmp',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAaA,eAAeA,aAAa,CAC1B,CAACC,OAAO,CAAgB,EACxBC,GAAW,EACXC,OAAgB,EAChB;EACA,IAAI;IACFC,kBAAM,CAACC,IAAI,CACT,oEAAoE,CACrE;IACD,IAAI,CAACF,OAAO,CAACG,QAAQ,EAAE;MACrBF,kBAAM,CAACC,IAAI,CAAC,8CAA8C,CAAC;IAC7D;IACA,MAAM,IAAAE,gCAAe,EACnBL,GAAG,EACHD,OAAO,EACPE,OAAO,CAACG,QAAQ,EAChBH,OAAO,CAACK,aAAa,EACrBL,OAAO,CAACM,GAAG,EACXN,OAAO,CAACO,iBAAiB,EACzBP,OAAO,CAACQ,IAAI,EACZR,OAAO,CAACS,KAAK,EACbT,OAAO,CAACU,WAAW,EACnBV,OAAO,CAACW,IAAI,CACb;EACH,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,MAAMA,GAAG;EACX;AACF;AAAC,eAEc;EACbC,IAAI,EAAE,iCAAiC;EACvCC,WAAW,EACT,2IAA2I;EAC7IC,IAAI,EAAElB,aAAa;EACnBG,OAAO,EAAE,CACP;IACEa,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,OAAO;IACbC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,2BAA2B;IACjCC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBG,OAAO,EAAG,GAAEC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAI,IAAK;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,2BAA2B;IACxCE,OAAO,EAAE;EACX,CAAC,CACF;EACDI,QAAQ,EAAE,CACR;IACEC,IAAI,EACF,8FAA8F;IAChGC,GAAG,EAAE;EACP,CAAC;AAEL,CAAC;AAAA"}