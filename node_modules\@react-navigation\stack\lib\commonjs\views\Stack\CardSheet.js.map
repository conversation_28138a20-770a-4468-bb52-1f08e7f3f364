{"version": 3, "names": ["React", "forwardRef", "CardSheet", "ref", "enabled", "layout", "style", "rest", "fill", "setFill", "useState", "pointerEvents", "setPointerEvents", "useImperativeHandle", "useEffect", "document", "body", "width", "clientWidth", "height", "clientHeight", "styles", "page", "card", "StyleSheet", "create", "minHeight", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/Stack/CardSheet.tsx"], "mappings": ";;;;;;AAAA;AACA;AAA2D;AAAA;AAAA;AAY3D;AACA;AACA;AACA;AAAA,4BACeA,KAAK,CAACC,UAAU,CAAsB,SAASC,SAAS,OAErEC,GAAG,EACH;EAAA,IAFA;IAAEC,OAAO;IAAEC,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAK,CAAC;EAGnC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EAC7C;EACA;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GACrCZ,KAAK,CAACU,QAAQ,CAA6B,MAAM,CAAC;EAEpDV,KAAK,CAACa,mBAAmB,CAACV,GAAG,EAAE,MAAM;IACnC,OAAO;MAAES;IAAiB,CAAC;EAC7B,CAAC,CAAC;EAEFZ,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MACrD;MACA;IACF;IAEA,MAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACE,WAAW;IACvC,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,IAAI,CAACI,YAAY;IAEzCX,OAAO,CAACQ,KAAK,KAAKZ,MAAM,CAACY,KAAK,IAAIE,MAAM,KAAKd,MAAM,CAACc,MAAM,CAAC;EAC7D,CAAC,EAAE,CAACd,MAAM,CAACc,MAAM,EAAEd,MAAM,CAACY,KAAK,CAAC,CAAC;EAEjC,oBACE,oBAAC,iBAAI,eACCV,IAAI;IACR,aAAa,EAAEI,aAAc;IAC7B,KAAK,EAAE,CAACP,OAAO,IAAII,IAAI,GAAGa,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAEjB,KAAK;EAAE,GAC5D;AAEN,CAAC,CAAC;AAAA;AAEF,MAAMe,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,IAAI,EAAE;IACJI,SAAS,EAAE;EACb,CAAC;EACDH,IAAI,EAAE;IACJI,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}