{"version": 3, "names": ["createNavigatorFactory", "DrawerRouter", "useNavigationBuilder", "React", "warnOnce", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Drawer<PERSON><PERSON><PERSON><PERSON>", "id", "initialRouteName", "defaultStatus", "customDefaultStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "screenListeners", "screenOptions", "restWithDeprecated", "openByDefault", "lazy", "drawerContentOptions", "rest", "defaultScreenOptions", "Object", "assign", "drawerPosition", "drawerType", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "edgeWidth", "drawerHideStatusBarOnOpen", "hideStatusBar", "keyboardDismissMode", "swipeMinDistance", "minSwipeDistance", "overlayColor", "drawerStatusBarAnimation", "statusBarAnimation", "gestureHandlerProps", "keys", "for<PERSON>ach", "key", "undefined", "JSON", "stringify", "state", "descriptors", "navigation", "NavigationContent"], "sourceRoot": "../../../src", "sources": ["navigators/createDrawerNavigator.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EAItBC,YAAY,EAIZC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAOhC,OAAOC,UAAU,MAAM,qBAAqB;AAW5C,SAASC,eAAe,OASd;EAAA,IATe;IACvBC,EAAE;IACFC,gBAAgB;IAChBC,aAAa,EAAEC,mBAAmB;IAClCC,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACb,GAAGC;EACE,CAAC;EACN,MAAM;IACJ;IACAC,aAAa;IACb;IACAC,IAAI;IACJ;IACAC,oBAAoB;IACpB,GAAGC;EACL,CAAC,GAAGJ,kBAAkB;EAEtB,IAAIK,oBAA6C,GAAG,CAAC,CAAC;EAEtD,IAAIF,oBAAoB,EAAE;IACxBG,MAAM,CAACC,MAAM,CAACF,oBAAoB,EAAE;MAClCG,cAAc,EAAEL,oBAAoB,CAACK,cAAc;MACnDC,UAAU,EAAEN,oBAAoB,CAACM,UAAU;MAC3CC,cAAc,EAAEP,oBAAoB,CAACQ,SAAS;MAC9CC,yBAAyB,EAAET,oBAAoB,CAACU,aAAa;MAC7DC,mBAAmB,EAAEX,oBAAoB,CAACW,mBAAmB;MAC7DC,gBAAgB,EAAEZ,oBAAoB,CAACa,gBAAgB;MACvDC,YAAY,EAAEd,oBAAoB,CAACc,YAAY;MAC/CC,wBAAwB,EAAEf,oBAAoB,CAACgB,kBAAkB;MACjEC,mBAAmB,EAAEjB,oBAAoB,CAACiB;IAC5C,CAAC,CAAC;IAGAd,MAAM,CAACe,IAAI,CAAChB,oBAAoB,CAAC,CACjCiB,OAAO,CAAEC,GAAG,IAAK;MACjB,IAAIlB,oBAAoB,CAACkB,GAAG,CAAC,KAAKC,SAAS,EAAE;QAC3C;QACA,OAAOnB,oBAAoB,CAACkB,GAAG,CAAC;MAClC;IACF,CAAC,CAAC;IAEFlC,QAAQ,CACNc,oBAAoB,EACnB,+LAA8LsB,IAAI,CAACC,SAAS,CAC3MrB,oBAAoB,EACpB,IAAI,EACJ,CAAC,CACD,qFAAoF,CACvF;EACH;EAEA,IAAI,OAAOH,IAAI,KAAK,SAAS,EAAE;IAC7BG,oBAAoB,CAACH,IAAI,GAAGA,IAAI;IAEhCb,QAAQ,CACN,IAAI,EACH,uKAAsK,CACxK;EACH;EAEA,IAAI,OAAOY,aAAa,KAAK,SAAS,EAAE;IACtCZ,QAAQ,CACN,IAAI,EACH,0MAAyM,CAC3M;EACH;EAEA,MAAMK,aAA2B,GAC/BC,mBAAmB,KAAK6B,SAAS,GAC7B7B,mBAAmB,GACnBM,aAAa,GACb,MAAM,GACN,QAAQ;EAEd,MAAM;IAAE0B,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzD3C,oBAAoB,CAMlBD,YAAY,EAAE;IACdM,EAAE;IACFC,gBAAgB;IAChBC,aAAa;IACbE,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACbM;EACF,CAAC,CAAC;EAEJ,oBACE,oBAAC,iBAAiB,qBAChB,oBAAC,UAAU,eACLD,IAAI;IACR,aAAa,EAAEV,aAAc;IAC7B,KAAK,EAAEiC,KAAM;IACb,WAAW,EAAEC,WAAY;IACzB,UAAU,EAAEC;EAAW,GACvB,CACgB;AAExB;AAEA,eAAe5C,sBAAsB,CAKnCM,eAAe,CAAC"}