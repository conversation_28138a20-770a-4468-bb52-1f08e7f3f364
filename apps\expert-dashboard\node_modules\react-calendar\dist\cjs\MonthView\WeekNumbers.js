"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var date_utils_1 = require("@wojtekmaj/date-utils");
var WeekNumber_js_1 = __importDefault(require("./WeekNumber.js"));
var Flex_js_1 = __importDefault(require("../Flex.js"));
var dates_js_1 = require("../shared/dates.js");
var utils_js_1 = require("../shared/utils.js");
function WeekNumbers(props) {
    var activeStartDate = props.activeStartDate, calendarTypeOrDeprecatedCalendarType = props.calendarType, onClickWeekNumber = props.onClickWeekNumber, onMouseLeave = props.onMouseLeave, showFixedNumberOfWeeks = props.showFixedNumberOfWeeks;
    var calendarType = (0, utils_js_1.mapCalendarType)(calendarTypeOrDeprecatedCalendarType);
    var numberOfWeeks = (function () {
        if (showFixedNumberOfWeeks) {
            return 6;
        }
        var numberOfDays = (0, date_utils_1.getDaysInMonth)(activeStartDate);
        var startWeekday = (0, dates_js_1.getDayOfWeek)(activeStartDate, calendarType);
        var days = numberOfDays - (7 - startWeekday);
        return 1 + Math.ceil(days / 7);
    })();
    var dates = (function () {
        var year = (0, date_utils_1.getYear)(activeStartDate);
        var monthIndex = (0, date_utils_1.getMonth)(activeStartDate);
        var day = (0, date_utils_1.getDate)(activeStartDate);
        var result = [];
        for (var index = 0; index < numberOfWeeks; index += 1) {
            result.push((0, dates_js_1.getBeginOfWeek)(new Date(year, monthIndex, day + index * 7), calendarType));
        }
        return result;
    })();
    var weekNumbers = dates.map(function (date) { return (0, dates_js_1.getWeekNumber)(date, calendarType); });
    return (react_1.default.createElement(Flex_js_1.default, { className: "react-calendar__month-view__weekNumbers", count: numberOfWeeks, direction: "column", onFocus: onMouseLeave, onMouseOver: onMouseLeave, style: { flexBasis: 'calc(100% * (1 / 8)', flexShrink: 0 } }, weekNumbers.map(function (weekNumber, weekIndex) {
        var date = dates[weekIndex];
        if (!date) {
            throw new Error('date is not defined');
        }
        return (react_1.default.createElement(WeekNumber_js_1.default, { key: weekNumber, date: date, onClickWeekNumber: onClickWeekNumber, weekNumber: weekNumber }));
    })));
}
exports.default = WeekNumbers;
