# Temp workaround to get the build working e2e, <PERSON><PERSON><PERSON> builds them for us
load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_prebuilt_aar", "rn_prebuilt_native_library")

rn_prebuilt_native_library(
    name = "reactnative-libs",
    native_libs = "lib",
    visibility = ["PUBLIC"],
)

rn_android_prebuilt_aar(
    name = "android-jsc",
    aar = ":android-jsc-aar",
    visibility = ["PUBLIC"],
)

fb_native.remote_file(
    name = "android-jsc-aar",
    sha1 = "880cedd93f43e0fc841f01f2fa185a63d9230f85",
    url = "mvn:org.webkit:android-jsc:aar:r174650",
)
