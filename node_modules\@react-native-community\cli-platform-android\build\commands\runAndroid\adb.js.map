{"version": 3, "names": ["parseDevicesResult", "result", "devices", "lines", "trim", "split", "i", "length", "words", "filter", "w", "push", "getDevices", "adbPath", "devicesResult", "execSync", "toString", "e", "getAvailableCPUs", "device", "baseArgs", "cpus", "execFileSync", "concat", "getCPU"], "sources": ["../../../src/commands/runAndroid/adb.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execSync, execFileSync} from 'child_process';\n\n/**\n * Parses the output of the 'adb devices' command\n */\nfunction parseDevicesResult(result: string): Array<string> {\n  if (!result) {\n    return [];\n  }\n\n  const devices = [];\n  const lines = result.trim().split(/\\r?\\n/);\n\n  for (let i = 0; i < lines.length; i++) {\n    const words = lines[i].split(/[ ,\\t]+/).filter((w) => w !== '');\n\n    if (words[1] === 'device') {\n      devices.push(words[0]);\n    }\n  }\n  return devices;\n}\n\n/**\n * Executes the commands needed to get a list of devices from ADB\n */\nfunction getDevices(adbPath: string): Array<string> {\n  try {\n    const devicesResult = execSync(`\"${adbPath}\" devices`);\n    return parseDevicesResult(devicesResult.toString());\n  } catch (e) {\n    return [];\n  }\n}\n\n/**\n * Gets available CPUs of devices from ADB\n */\nfunction getAvailableCPUs(adbPath: string, device: string): Array<string> {\n  try {\n    const baseArgs = ['-s', device, 'shell', 'getprop'];\n\n    let cpus = execFileSync(\n      adbPath,\n      baseArgs.concat(['ro.product.cpu.abilist']),\n    ).toString();\n\n    // pre-Lollipop\n    if (!cpus || cpus.trim().length === 0) {\n      cpus = execFileSync(\n        adbPath,\n        baseArgs.concat(['ro.product.cpu.abi']),\n      ).toString();\n    }\n\n    return (cpus || '').trim().split(',');\n  } catch (e) {\n    return [];\n  }\n}\n\n/**\n * Gets the CPU architecture of a device from ADB\n */\nfunction getCPU(adbPath: string, device: string): string | null {\n  try {\n    const cpus = execFileSync(adbPath, [\n      '-s',\n      device,\n      'shell',\n      'getprop',\n      'ro.product.cpu.abi',\n    ])\n      .toString()\n      .trim();\n\n    return cpus.length > 0 ? cpus : null;\n  } catch (e) {\n    return null;\n  }\n}\n\nexport default {\n  getDevices,\n  getAvailableCPUs,\n  getCPU,\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA,SAASA,kBAAkB,CAACC,MAAc,EAAiB;EACzD,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EAEA,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAI,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;EAE1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,KAAK,GAAGL,KAAK,CAACG,CAAC,CAAC,CAACD,KAAK,CAAC,SAAS,CAAC,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;IAE/D,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACzBN,OAAO,CAACS,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACxB;EACF;EACA,OAAON,OAAO;AAChB;;AAEA;AACA;AACA;AACA,SAASU,UAAU,CAACC,OAAe,EAAiB;EAClD,IAAI;IACF,MAAMC,aAAa,GAAG,IAAAC,yBAAQ,EAAE,IAAGF,OAAQ,WAAU,CAAC;IACtD,OAAOb,kBAAkB,CAACc,aAAa,CAACE,QAAQ,EAAE,CAAC;EACrD,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,EAAE;EACX;AACF;;AAEA;AACA;AACA;AACA,SAASC,gBAAgB,CAACL,OAAe,EAAEM,MAAc,EAAiB;EACxE,IAAI;IACF,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAED,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;IAEnD,IAAIE,IAAI,GAAG,IAAAC,6BAAY,EACrBT,OAAO,EACPO,QAAQ,CAACG,MAAM,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAC5C,CAACP,QAAQ,EAAE;;IAEZ;IACA,IAAI,CAACK,IAAI,IAAIA,IAAI,CAACjB,IAAI,EAAE,CAACG,MAAM,KAAK,CAAC,EAAE;MACrCc,IAAI,GAAG,IAAAC,6BAAY,EACjBT,OAAO,EACPO,QAAQ,CAACG,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACxC,CAACP,QAAQ,EAAE;IACd;IAEA,OAAO,CAACK,IAAI,IAAI,EAAE,EAAEjB,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EACvC,CAAC,CAAC,OAAOY,CAAC,EAAE;IACV,OAAO,EAAE;EACX;AACF;;AAEA;AACA;AACA;AACA,SAASO,MAAM,CAACX,OAAe,EAAEM,MAAc,EAAiB;EAC9D,IAAI;IACF,MAAME,IAAI,GAAG,IAAAC,6BAAY,EAACT,OAAO,EAAE,CACjC,IAAI,EACJM,MAAM,EACN,OAAO,EACP,SAAS,EACT,oBAAoB,CACrB,CAAC,CACCH,QAAQ,EAAE,CACVZ,IAAI,EAAE;IAET,OAAOiB,IAAI,CAACd,MAAM,GAAG,CAAC,GAAGc,IAAI,GAAG,IAAI;EACtC,CAAC,CAAC,OAAOJ,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF;AAAC,eAEc;EACbL,UAAU;EACVM,gBAAgB;EAChBM;AACF,CAAC;AAAA"}