load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_xplat_target", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "jni",
    srcs = glob(["**/*.cpp"]),
    headers = glob(["**/*.h"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("react/common/mapbuffer", "*.h"),
        ],
        prefix = "react/common/mapbuffer",
    ),
    fbandroid_allow_jni_merging = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    soname = "libmapbufferjni.$(ext)",
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
        react_native_xplat_target("react/debug:debug"),
        FBJNI_TARGET,
    ],
)
