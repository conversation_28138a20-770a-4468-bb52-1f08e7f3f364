"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModuleScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ModuleScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scopeManager, ScopeType_1.ScopeType.module, upperScope, block, false);
    }
}
exports.ModuleScope = ModuleScope;
//# sourceMappingURL=ModuleScope.js.map