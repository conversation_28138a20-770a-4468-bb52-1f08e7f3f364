{"version": 3, "names": ["RNCMaskedView", "require", "default", "e", "isMaskedViewAvailable", "UIManager", "getViewManagerConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "rest"], "sourceRoot": "../../src", "sources": ["MaskedViewNative.tsx"], "mappings": ";;;;;;AAGA;AACA;AAAyC;AAAA;AAJzC;AACA;AACA;;AAWA,IAAIA,aAAyC;AAE7C,IAAI;EACF;EACA;EACAA,aAAa,GAAGC,OAAO,CAAC,uCAAuC,CAAC,CAACC,OAAO;AAC1E,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGF,MAAMC,qBAAqB,GACzBC,sBAAS,CAACC,oBAAoB,CAAC,eAAe,CAAC,IAAI,IAAI;AAE1C,SAASC,UAAU,OAA+B;EAAA,IAA9B;IAAEC,QAAQ;IAAE,GAAGC;EAAY,CAAC;EAC7D,IAAIL,qBAAqB,IAAIJ,aAAa,EAAE;IAC1C,oBAAO,oBAAC,aAAa,EAAKS,IAAI,EAAGD,QAAQ,CAAiB;EAC5D;EAEA,OAAOA,QAAQ;AACjB"}