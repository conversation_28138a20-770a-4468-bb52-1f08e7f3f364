{"version": 3, "sources": ["types.ts"], "names": ["NetInfoStateType", "NetInfoCellularGeneration"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEYA,gB;;;WAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;AAAAA,EAAAA,gB;GAAAA,gB,gCAAAA,gB;;IAcAC,yB;;;WAAAA,yB;AAAAA,EAAAA,yB;AAAAA,EAAAA,yB;AAAAA,EAAAA,yB;AAAAA,EAAAA,yB;GAAAA,yB,yCAAAA,yB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nexport enum NetInfoStateType {\n  unknown = 'unknown',\n  none = 'none',\n  cellular = 'cellular',\n  wifi = 'wifi',\n  bluetooth = 'bluetooth',\n  ethernet = 'ethernet',\n  wimax = 'wimax',\n  vpn = 'vpn',\n  other = 'other',\n}\n\nexport type NetInfoMethodType = 'HEAD' | 'GET';\n\nexport enum NetInfoCellularGeneration {\n  '2g' = '2g',\n  '3g' = '3g',\n  '4g' = '4g',\n  '5g' = '5g',\n}\n\nexport interface NetInfoConnectedDetails {\n  isConnectionExpensive: boolean;\n}\n\ninterface NetInfoConnectedState<\n  T extends NetInfoStateType,\n  D extends Record<string, unknown> = Record<string, unknown>\n> {\n  type: T;\n  isConnected: boolean;\n  isInternetReachable: boolean | null;\n  details: D & NetInfoConnectedDetails;\n  isWifiEnabled?: boolean;\n}\n\ninterface NetInfoDisconnectedState<T extends NetInfoStateType> {\n  type: T;\n  isConnected: boolean;\n  isInternetReachable: boolean;\n  details: null;\n  isWifiEnabled?: boolean;\n}\n\nexport interface NetInfoUnknownState {\n  type: NetInfoStateType.unknown;\n  isConnected: boolean | null;\n  isInternetReachable: null;\n  details: null;\n  isWifiEnabled?: boolean;\n}\n\nexport type NetInfoNoConnectionState = NetInfoDisconnectedState<\n  NetInfoStateType.none\n>;\nexport type NetInfoDisconnectedStates =\n  | NetInfoUnknownState\n  | NetInfoNoConnectionState;\n\nexport type NetInfoCellularState = NetInfoConnectedState<\n  NetInfoStateType.cellular,\n  {\n    cellularGeneration: NetInfoCellularGeneration | null;\n    carrier: string | null;\n  }\n>;\nexport type NetInfoWifiState = NetInfoConnectedState<\n  NetInfoStateType.wifi,\n  {\n    ssid: string | null;\n    bssid: string | null;\n    strength: number | null;\n    ipAddress: string | null;\n    subnet: string | null;\n    frequency: number | null;\n    linkSpeed: number | null;\n    rxLinkSpeed: number | null;\n    txLinkSpeed: number | null;\n  }\n>;\nexport type NetInfoBluetoothState = NetInfoConnectedState<\n  NetInfoStateType.bluetooth\n>;\nexport type NetInfoEthernetState = NetInfoConnectedState<\n  NetInfoStateType.ethernet,\n  {\n    ipAddress: string | null;\n    subnet: string | null;\n  }\n>;\nexport type NetInfoWimaxState = NetInfoConnectedState<NetInfoStateType.wimax>;\nexport type NetInfoVpnState = NetInfoConnectedState<NetInfoStateType.vpn>;\nexport type NetInfoOtherState = NetInfoConnectedState<NetInfoStateType.other>;\nexport type NetInfoConnectedStates =\n  | NetInfoCellularState\n  | NetInfoWifiState\n  | NetInfoBluetoothState\n  | NetInfoEthernetState\n  | NetInfoWimaxState\n  | NetInfoVpnState\n  | NetInfoOtherState;\n\nexport type NetInfoState = NetInfoDisconnectedStates | NetInfoConnectedStates;\n\nexport type NetInfoChangeHandler = (state: NetInfoState) => void;\nexport type NetInfoSubscription = () => void;\n\nexport interface NetInfoConfiguration {\n  reachabilityUrl: string;\n  reachabilityMethod?: NetInfoMethodType;\n  reachabilityHeaders?: Record<string, string>;\n  reachabilityTest: (response: Response) => Promise<boolean>;\n  reachabilityLongTimeout: number;\n  reachabilityShortTimeout: number;\n  reachabilityRequestTimeout: number;\n  reachabilityShouldRun: () => boolean;\n  shouldFetchWiFiSSID: boolean;\n  useNativeReachability: boolean;\n}\n"]}