load("//tools/build_defs/oss:rn_defs.bzl", "rn_apple_library")

rn_apple_library(
    name = "FBLazyVector",
    exported_headers = [
        "FBLazyVector/FBLazyIterator.h",
        "FBLazyVector/FBLazyVector.h",
    ],
    autoglob = False,
    complete_nullability = True,
    contacts = ["<EMAIL>"],
    enable_exceptions = False,
    extension_api_only = True,
    frameworks = [],
    labels = [
        "fbios_link_group:xplat/default/public.react_native.infra",
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
        "talkios_link_group:xplat/default/public.react_native.infra",
    ],
    link_whole = False,
    visibility = ["PUBLIC"],
)
