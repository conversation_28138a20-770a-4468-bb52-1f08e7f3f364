{"version": 3, "names": ["name", "description", "default", "parse", "val", "workers", "Number", "path", "resolve"], "sources": ["../../../src/commands/bundle/bundleCommandLineArgs.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\n\nexport interface CommandLineArgs {\n  assetsDest?: string;\n  assetCatalogDest?: string;\n  entryFile: string;\n  resetCache: boolean;\n  resetGlobalCache: boolean;\n  transformer?: string;\n  minify?: boolean;\n  config?: string;\n  platform: string;\n  dev: boolean;\n  bundleOutput: string;\n  bundleEncoding?: string;\n  maxWorkers?: number;\n  sourcemapOutput?: string;\n  sourcemapSourcesRoot?: string;\n  sourcemapUseAbsolutePath: boolean;\n  verbose: boolean;\n  unstableTransformProfile: string;\n  generateStaticViewConfigs: boolean;\n}\n\nexport default [\n  {\n    name: '--entry-file <path>',\n    description:\n      'Path to the root JS file, either absolute or relative to JS root',\n  },\n  {\n    name: '--platform <string>',\n    description: 'Either \"ios\" or \"android\"',\n    default: 'ios',\n  },\n  {\n    name: '--transformer <string>',\n    description: 'Specify a custom transformer to be used',\n  },\n  {\n    name: '--dev [boolean]',\n    description: 'If false, warnings are disabled and the bundle is minified',\n    parse: (val: string) => val !== 'false',\n    default: true,\n  },\n  {\n    name: '--minify [boolean]',\n    description:\n      'Allows overriding whether bundle is minified. This defaults to ' +\n      'false if dev is true, and true if dev is false. Disabling minification ' +\n      'can be useful for speeding up production builds for testing purposes.',\n    parse: (val: string) => val !== 'false',\n  },\n  {\n    name: '--bundle-output <string>',\n    description:\n      'File name where to store the resulting bundle, ex. /tmp/groups.bundle',\n  },\n  {\n    name: '--bundle-encoding <string>',\n    description:\n      'Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer).',\n    default: 'utf8',\n  },\n  {\n    name: '--max-workers <number>',\n    description:\n      'Specifies the maximum number of workers the worker-pool ' +\n      'will spawn for transforming files. This defaults to the number of the ' +\n      'cores available on your machine.',\n    parse: (workers: string) => Number(workers),\n  },\n  {\n    name: '--sourcemap-output <string>',\n    description:\n      'File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map',\n  },\n  {\n    name: '--sourcemap-sources-root <string>',\n    description:\n      \"Path to make sourcemap's sources entries relative to, ex. /root/dir\",\n  },\n  {\n    name: '--sourcemap-use-absolute-path',\n    description: 'Report SourceMapURL using its full path',\n    default: false,\n  },\n  {\n    name: '--assets-dest <string>',\n    description:\n      'Directory name where to store assets referenced in the bundle',\n  },\n  {\n    name: '--unstable-transform-profile <string>',\n    description:\n      'Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default',\n    default: 'default',\n  },\n  {\n    name: '--asset-catalog-dest [string]',\n    description: 'Path where to create an iOS Asset Catalog for images',\n  },\n  {\n    name: '--reset-cache',\n    description: 'Removes cached files',\n    default: false,\n  },\n  {\n    name: '--read-global-cache',\n    description:\n      'Try to fetch transformed JS code from the global cache, if configured.',\n    default: false,\n  },\n  {\n    name: '--config <string>',\n    description: 'Path to the CLI configuration file',\n    parse: (val: string) => path.resolve(val),\n  },\n  {\n    name: '--generate-static-view-configs',\n    description:\n      'Generate static view configs for Fabric components. ' +\n      'If there are no Fabric components in the bundle or Fabric is disabled, this is just no-op.',\n    default: true,\n  },\n];\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AARxB;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAgCe,CACb;EACEA,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,2BAA2B;EACxCC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE;AACf,CAAC,EACD;EACED,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,4DAA4D;EACzEE,KAAK,EAAGC,GAAW,IAAKA,GAAG,KAAK,OAAO;EACvCF,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT,iEAAiE,GACjE,yEAAyE,GACzE,uEAAuE;EACzEE,KAAK,EAAGC,GAAW,IAAKA,GAAG,KAAK;AAClC,CAAC,EACD;EACEJ,IAAI,EAAE,0BAA0B;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,4BAA4B;EAClCC,WAAW,EACT,8FAA8F;EAChGC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EACT,0DAA0D,GAC1D,wEAAwE,GACxE,kCAAkC;EACpCE,KAAK,EAAGE,OAAe,IAAKC,MAAM,CAACD,OAAO;AAC5C,CAAC,EACD;EACEL,IAAI,EAAE,6BAA6B;EACnCC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,mCAAmC;EACzCC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,+BAA+B;EACrCC,WAAW,EAAE,yCAAyC;EACtDC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,uCAAuC;EAC7CC,WAAW,EACT,0GAA0G;EAC5GC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,+BAA+B;EACrCC,WAAW,EAAE;AACf,CAAC,EACD;EACED,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE,sBAAsB;EACnCC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EACT,wEAAwE;EAC1EC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,oCAAoC;EACjDE,KAAK,EAAGC,GAAW,IAAKG,eAAI,CAACC,OAAO,CAACJ,GAAG;AAC1C,CAAC,EACD;EACEJ,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EACT,sDAAsD,GACtD,4FAA4F;EAC9FC,OAAO,EAAE;AACX,CAAC,CACF;AAAA"}