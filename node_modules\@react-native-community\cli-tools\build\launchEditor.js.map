{"version": 3, "names": ["isTerminalEditor", "editor", "COMMON_EDITORS", "COMMON_WINDOWS_EDITORS", "WINDOWS_FILE_NAME_WHITELIST", "addWorkspaceToArgumentsIfExists", "args", "workspace", "unshift", "getArgumentsForLineNumber", "fileName", "lineNumber", "path", "basename", "String", "getArgumentsForFileName", "guessEditor", "process", "env", "REACT_EDITOR", "shellQuote", "parse", "platform", "output", "execSync", "toString", "processNames", "Object", "keys", "processName", "indexOf", "runningProcesses", "split", "map", "line", "replace", "includes", "error", "VISUAL", "EDITOR", "printInstructions", "title", "WINDOWS_FIXIT_INSTRUCTIONS", "FIXIT_INSTRUCTIONS", "logger", "info", "chalk", "bgBlue", "white", "bold", "join", "transformToAbsolutePathIfNeeded", "pathName", "isAbsolute", "resolve", "cwd", "findRootForFile", "projectRoots", "absoluteFileName", "find", "root", "absoluteRoot", "startsWith", "sep", "editorWindowsLaunchPath", "fs", "existsSync", "stdio", "editor<PERSON><PERSON><PERSON>", "values", "editorImageNames", "i", "length", "editor<PERSON><PERSON>", "toLowerCase", "results", "pid", "parseInt", "trim", "_childProcess", "launchEditor", "isNaN", "concat", "test", "underline", "kill", "spawn", "on", "errorCode", "message"], "sources": ["../src/launchEditor.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport {execSync, spawn, ChildProcess} from 'child_process';\nimport shellQuote from 'shell-quote';\nimport logger from './logger';\n\nfunction isTerminalEditor(editor: string) {\n  switch (editor) {\n    case 'vim':\n    case 'emacs':\n    case 'nano':\n      return true;\n    default:\n      return false;\n  }\n}\n\n// Map from full process name to binary that starts the process\n// We can't just re-use full process name, because it will spawn a new instance\n// of the app every time\nconst COMMON_EDITORS: Record<string, string> = {\n  '/Applications/Atom.app/Contents/MacOS/Atom': 'atom',\n  '/Applications/Atom Beta.app/Contents/MacOS/Atom Beta':\n    '/Applications/Atom Beta.app/Contents/MacOS/Atom Beta',\n  '/Applications/IntelliJ IDEA.app/Contents/MacOS/idea': 'idea',\n  '/Applications/Sublime Text.app/Contents/MacOS/Sublime Text':\n    '/Applications/Sublime Text.app/Contents/SharedSupport/bin/subl',\n  '/Applications/Sublime Text 2.app/Contents/MacOS/Sublime Text 2':\n    '/Applications/Sublime Text 2.app/Contents/SharedSupport/bin/subl',\n  '/Applications/Visual Studio Code.app/Contents/MacOS/Electron': 'code',\n  '/Applications/WebStorm.app/Contents/MacOS/webstorm': 'webstorm',\n};\n\n// Map of process image name used to identify already running instances of the editor\n// And an editor id which is used to determine which arguments to add to the commandline\nconst COMMON_WINDOWS_EDITORS: Record<string, string> = {\n  'Code.exe': 'code',\n  'sublime_text.exe': 'subl',\n  'devenv.exe': 'devenv',\n  'notepad.exe': 'notepad',\n};\n\n// Transpiled version of: /^([a-zA-Z]+:)?[\\p{L}0-9@/.\\-_\\\\]+$/u\n// Non-transpiled version requires support for Unicode property regex. Allows\n// alphanumeric characters, periods, dashes, at signs, slashes, and underscores, with an optional drive prefix\nconst WINDOWS_FILE_NAME_WHITELIST = /^([a-zA-Z]+:)?(?:[\\x2D-9@-Z\\\\_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEF\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7B9\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF1A]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDE9D\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFF1]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D])+$/;\n\nfunction addWorkspaceToArgumentsIfExists(args: string[], workspace: string) {\n  if (workspace) {\n    args.unshift(workspace);\n  }\n  return args;\n}\n\nfunction getArgumentsForLineNumber(\n  editor: string,\n  fileName: string,\n  lineNumber: number,\n  workspace: any,\n) {\n  switch (path.basename(editor)) {\n    case 'vim':\n    case 'mvim':\n      return [fileName, `+${lineNumber}`];\n    case 'atom':\n    case 'Atom':\n    case 'Atom Beta':\n    case 'subl':\n    case 'sublime':\n    case 'webstorm':\n    case 'wstorm':\n    case 'appcode':\n    case 'charm':\n    case 'idea':\n      return [`${fileName}:${lineNumber}`];\n    case 'joe':\n    case 'emacs':\n    case 'emacsclient':\n      return [`+${lineNumber}`, fileName];\n    case 'rmate':\n    case 'mate':\n    case 'mine':\n      return ['--line', String(lineNumber), fileName];\n    case 'code':\n      return addWorkspaceToArgumentsIfExists(\n        ['-g', `${fileName}:${lineNumber}`],\n        workspace,\n      );\n    case 'devenv':\n      return ['/EDIT', fileName];\n    // For all others, drop the lineNumber until we have\n    // a mapping above, since providing the lineNumber incorrectly\n    // can result in errors or confusing behavior.\n    default:\n      return [fileName];\n  }\n}\n\nfunction getArgumentsForFileName(\n  editor: string,\n  fileName: string,\n  workspace: any,\n) {\n  switch (path.basename(editor)) {\n    case 'code':\n      return addWorkspaceToArgumentsIfExists([fileName], workspace);\n    case 'devenv':\n      return ['/EDIT', fileName];\n    // Every other editor just takes the filename as an argument\n    default:\n      return [fileName];\n  }\n}\n\nfunction guessEditor() {\n  // Explicit config always wins\n  if (process.env.REACT_EDITOR) {\n    return shellQuote.parse(process.env.REACT_EDITOR) as string[];\n  }\n\n  try {\n    // Using `ps x` on OSX we can find out which editor is currently running.\n    // Potentially we could use similar technique for Linux\n    if (process.platform === 'darwin') {\n      const output = execSync('ps x').toString();\n      const processNames = Object.keys(COMMON_EDITORS);\n      for (const processName of processNames) {\n        if (output.indexOf(processName) !== -1) {\n          return [COMMON_EDITORS[processName]];\n        }\n      }\n    } else if (process.platform === 'win32') {\n      const output = execSync(\n        'tasklist /NH /FO CSV /FI \"SESSIONNAME ne Services\"',\n      ).toString();\n\n      const runningProcesses = output\n        .split('\\n')\n        .map((line) => line.replace(/^\"|\".*\\r$/gm, ''));\n      const processNames = Object.keys(COMMON_WINDOWS_EDITORS);\n      for (const processName of processNames) {\n        if (runningProcesses.includes(processName)) {\n          return [COMMON_WINDOWS_EDITORS[processName]];\n        }\n      }\n    }\n  } catch (error) {\n    // Ignore...\n  }\n\n  // Last resort, use old skool env vars\n  if (process.env.VISUAL) {\n    return [process.env.VISUAL];\n  }\n  if (process.env.EDITOR) {\n    return [process.env.EDITOR];\n  }\n\n  return [];\n}\n\nfunction printInstructions(title: string) {\n  const WINDOWS_FIXIT_INSTRUCTIONS = [\n    '  To set it up, you can run something like \"SETX REACT_EDITOR code\"',\n    '  which will set the environment variable in future shells,',\n    '  then \"SET REACTEDITOR=code\" to set it in the current shell',\n  ];\n\n  const FIXIT_INSTRUCTIONS = [\n    '  To set it up, you can add something like ',\n    '  export REACT_EDITOR=atom to your ~/.bashrc or ~/.zshrc depending on ',\n    '  which shell you use.',\n  ];\n\n  logger.info(\n    [\n      '',\n      chalk.bgBlue.white.bold(` ${title} `),\n      '  When you see Red Box with stack trace, you can click any ',\n      '  stack frame to jump to the source file. The packager will launch your ',\n      '  editor of choice. It will first look at REACT_EDITOR environment ',\n      '  variable, then at EDITOR.',\n      ...(process.platform === 'win32'\n        ? WINDOWS_FIXIT_INSTRUCTIONS\n        : FIXIT_INSTRUCTIONS),\n      '',\n    ].join('\\n'),\n  );\n}\n\nfunction transformToAbsolutePathIfNeeded(pathName: string) {\n  if (!path.isAbsolute(pathName)) {\n    return path.resolve(process.cwd(), pathName);\n  }\n  return pathName;\n}\n\nfunction findRootForFile(\n  projectRoots: ReadonlyArray<string>,\n  fileName: string,\n) {\n  const absoluteFileName = transformToAbsolutePathIfNeeded(fileName);\n  return projectRoots.find((root) => {\n    const absoluteRoot = transformToAbsolutePathIfNeeded(root);\n    return absoluteFileName.startsWith(absoluteRoot + path.sep);\n  });\n}\n\n// On windows, find the editor executable path even if its not in the users path\nfunction editorWindowsLaunchPath(editor: string) {\n  if (fs.existsSync(editor)) {\n    // Editor is a full path to an exe, we can just launch it\n    return editor;\n  }\n\n  try {\n    execSync(`where ${editor}`, {stdio: 'ignore'});\n    // Editor is on the path, we can just launch it\n    return editor;\n  } catch (error) {\n    // ignore\n  }\n\n  try {\n    const editorNames = Object.values(COMMON_WINDOWS_EDITORS);\n    const editorImageNames = Object.keys(COMMON_WINDOWS_EDITORS);\n    for (let i = 0; i < editorNames.length; i++) {\n      const editorName = editorNames[i];\n      if (editor.toLowerCase() === editorName.toLowerCase()) {\n        // An editor was guessed by guessEditor, but isn't part of the users path\n        // Attempt to get the executable location from the running process\n        const output = execSync(\n          `tasklist /FO CSV /NH /FI \"IMAGENAME eq ${editorImageNames[i]}\"`,\n        ).toString();\n\n        const results = output.split(',');\n        if (results[0] !== `\"${editorImageNames[i]}\"`) {\n          // Failed to find a running instance...\n          return editor;\n        }\n\n        const pid = parseInt(results[1].replace(/^\"|\"$/gm, ''), 10);\n        return execSync(\n          `powershell (Get-CimInstance Win32_Process -Filter \"ProcessId=${pid}\").ExecutablePath`,\n        )\n          .toString()\n          .trim();\n      }\n    }\n  } catch (error) {\n    // ignore\n  }\n\n  // Just use what the user specified, it will probably fail,\n  // but we will show some help text when it fails.\n  return editor;\n}\n\nlet _childProcess: ChildProcess | null = null;\nfunction launchEditor(\n  fileName: string,\n  lineNumber: number,\n  projectRoots: ReadonlyArray<string>,\n) {\n  if (!fs.existsSync(fileName)) {\n    return;\n  }\n\n  // Sanitize lineNumber to prevent malicious use on win32\n  // via: https://github.com/nodejs/node/blob/c3bb4b1aa5e907d489619fb43d233c3336bfc03d/lib/child_process.js#L333\n  if (lineNumber && isNaN(lineNumber)) {\n    return;\n  }\n\n  let [editor, ...args] = guessEditor();\n  if (!editor) {\n    printInstructions('PRO TIP');\n    return;\n  }\n\n  const workspace = findRootForFile(projectRoots, fileName);\n  if (lineNumber) {\n    args = args.concat(\n      getArgumentsForLineNumber(editor, fileName, lineNumber, workspace),\n    );\n  } else {\n    args = args.concat(getArgumentsForFileName(editor, fileName, workspace));\n  }\n\n  // cmd.exe on Windows is vulnerable to RCE attacks given a file name of the\n  // form \"C:\\Users\\<USER>\\Downloads\\& curl ************\". Use a whitelist\n  // to validate user-provided file names. This doesn't cover the entire range\n  // of valid file names but should cover almost all of them in practice.\n  if (\n    process.platform === 'win32' &&\n    !WINDOWS_FILE_NAME_WHITELIST.test(fileName.trim())\n  ) {\n    logger.error(`Could not open ${path.basename(fileName)} in the editor.`);\n    logger.info(\n      'When running on Windows, file names are checked against a whitelist ' +\n        'to protect against remote code execution attacks. File names may ' +\n        'consist only of alphanumeric characters (all languages), periods, ' +\n        'dashes, at signs, slashes, and underscores.',\n    );\n    return;\n  }\n\n  logger.info(\n    `Opening ${chalk.underline(fileName)} with ${chalk.bold(editor)}`,\n  );\n\n  if (_childProcess && isTerminalEditor(editor)) {\n    // There's an existing editor process already and it's attached\n    // to the terminal, so go kill it. Otherwise two separate editor\n    // instances attach to the stdin/stdout which gets confusing.\n    _childProcess.kill('SIGKILL');\n  }\n\n  if (process.platform === 'win32') {\n    // On Windows, launch the editor in a shell because spawn can only\n    // launch .exe files.\n    _childProcess = spawn(\n      'cmd.exe',\n      ['/C', editorWindowsLaunchPath(editor)].concat(args),\n      {\n        stdio: 'inherit',\n      },\n    );\n  } else {\n    _childProcess = spawn(editor, args, {stdio: 'inherit'});\n  }\n  _childProcess.on('exit', (errorCode) => {\n    _childProcess = null;\n\n    if (errorCode) {\n      logger.error('Your editor exited with an error!');\n      printInstructions('Keep these instructions in mind:');\n    }\n  });\n\n  _childProcess.on('error', (error) => {\n    logger.error(error.message);\n    printInstructions('How to fix:');\n  });\n}\n\nexport default launchEditor;\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA8B;AAd9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,SAASA,gBAAgB,CAACC,MAAc,EAAE;EACxC,QAAQA,MAAM;IACZ,KAAK,KAAK;IACV,KAAK,OAAO;IACZ,KAAK,MAAM;MACT,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAAC;AAEnB;;AAEA;AACA;AACA;AACA,MAAMC,cAAsC,GAAG;EAC7C,4CAA4C,EAAE,MAAM;EACpD,sDAAsD,EACpD,sDAAsD;EACxD,qDAAqD,EAAE,MAAM;EAC7D,4DAA4D,EAC1D,gEAAgE;EAClE,gEAAgE,EAC9D,kEAAkE;EACpE,8DAA8D,EAAE,MAAM;EACtE,oDAAoD,EAAE;AACxD,CAAC;;AAED;AACA;AACA,MAAMC,sBAA8C,GAAG;EACrD,UAAU,EAAE,MAAM;EAClB,kBAAkB,EAAE,MAAM;EAC1B,YAAY,EAAE,QAAQ;EACtB,aAAa,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,6kOAA6kO;AAEjnO,SAASC,+BAA+B,CAACC,IAAc,EAAEC,SAAiB,EAAE;EAC1E,IAAIA,SAAS,EAAE;IACbD,IAAI,CAACE,OAAO,CAACD,SAAS,CAAC;EACzB;EACA,OAAOD,IAAI;AACb;AAEA,SAASG,yBAAyB,CAChCR,MAAc,EACdS,QAAgB,EAChBC,UAAkB,EAClBJ,SAAc,EACd;EACA,QAAQK,eAAI,CAACC,QAAQ,CAACZ,MAAM,CAAC;IAC3B,KAAK,KAAK;IACV,KAAK,MAAM;MACT,OAAO,CAACS,QAAQ,EAAG,IAAGC,UAAW,EAAC,CAAC;IACrC,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,WAAW;IAChB,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,OAAO;IACZ,KAAK,MAAM;MACT,OAAO,CAAE,GAAED,QAAS,IAAGC,UAAW,EAAC,CAAC;IACtC,KAAK,KAAK;IACV,KAAK,OAAO;IACZ,KAAK,aAAa;MAChB,OAAO,CAAE,IAAGA,UAAW,EAAC,EAAED,QAAQ,CAAC;IACrC,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO,CAAC,QAAQ,EAAEI,MAAM,CAACH,UAAU,CAAC,EAAED,QAAQ,CAAC;IACjD,KAAK,MAAM;MACT,OAAOL,+BAA+B,CACpC,CAAC,IAAI,EAAG,GAAEK,QAAS,IAAGC,UAAW,EAAC,CAAC,EACnCJ,SAAS,CACV;IACH,KAAK,QAAQ;MACX,OAAO,CAAC,OAAO,EAAEG,QAAQ,CAAC;IAC5B;IACA;IACA;IACA;MACE,OAAO,CAACA,QAAQ,CAAC;EAAC;AAExB;AAEA,SAASK,uBAAuB,CAC9Bd,MAAc,EACdS,QAAgB,EAChBH,SAAc,EACd;EACA,QAAQK,eAAI,CAACC,QAAQ,CAACZ,MAAM,CAAC;IAC3B,KAAK,MAAM;MACT,OAAOI,+BAA+B,CAAC,CAACK,QAAQ,CAAC,EAAEH,SAAS,CAAC;IAC/D,KAAK,QAAQ;MACX,OAAO,CAAC,OAAO,EAAEG,QAAQ,CAAC;IAC5B;IACA;MACE,OAAO,CAACA,QAAQ,CAAC;EAAC;AAExB;AAEA,SAASM,WAAW,GAAG;EACrB;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE;IAC5B,OAAOC,qBAAU,CAACC,KAAK,CAACJ,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC;EACnD;EAEA,IAAI;IACF;IACA;IACA,IAAIF,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAAC,yBAAQ,EAAC,MAAM,CAAC,CAACC,QAAQ,EAAE;MAC1C,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC;MAChD,KAAK,MAAM2B,WAAW,IAAIH,YAAY,EAAE;QACtC,IAAIH,MAAM,CAACO,OAAO,CAACD,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;UACtC,OAAO,CAAC3B,cAAc,CAAC2B,WAAW,CAAC,CAAC;QACtC;MACF;IACF,CAAC,MAAM,IAAIZ,OAAO,CAACK,QAAQ,KAAK,OAAO,EAAE;MACvC,MAAMC,MAAM,GAAG,IAAAC,yBAAQ,EACrB,oDAAoD,CACrD,CAACC,QAAQ,EAAE;MAEZ,MAAMM,gBAAgB,GAAGR,MAAM,CAC5BS,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;MACjD,MAAMT,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACzB,sBAAsB,CAAC;MACxD,KAAK,MAAM0B,WAAW,IAAIH,YAAY,EAAE;QACtC,IAAIK,gBAAgB,CAACK,QAAQ,CAACP,WAAW,CAAC,EAAE;UAC1C,OAAO,CAAC1B,sBAAsB,CAAC0B,WAAW,CAAC,CAAC;QAC9C;MACF;IACF;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd;EAAA;;EAGF;EACA,IAAIpB,OAAO,CAACC,GAAG,CAACoB,MAAM,EAAE;IACtB,OAAO,CAACrB,OAAO,CAACC,GAAG,CAACoB,MAAM,CAAC;EAC7B;EACA,IAAIrB,OAAO,CAACC,GAAG,CAACqB,MAAM,EAAE;IACtB,OAAO,CAACtB,OAAO,CAACC,GAAG,CAACqB,MAAM,CAAC;EAC7B;EAEA,OAAO,EAAE;AACX;AAEA,SAASC,iBAAiB,CAACC,KAAa,EAAE;EACxC,MAAMC,0BAA0B,GAAG,CACjC,qEAAqE,EACrE,6DAA6D,EAC7D,8DAA8D,CAC/D;EAED,MAAMC,kBAAkB,GAAG,CACzB,6CAA6C,EAC7C,wEAAwE,EACxE,wBAAwB,CACzB;EAEDC,eAAM,CAACC,IAAI,CACT,CACE,EAAE,EACFC,gBAAK,CAACC,MAAM,CAACC,KAAK,CAACC,IAAI,CAAE,IAAGR,KAAM,GAAE,CAAC,EACrC,6DAA6D,EAC7D,0EAA0E,EAC1E,qEAAqE,EACrE,6BAA6B,EAC7B,IAAIxB,OAAO,CAACK,QAAQ,KAAK,OAAO,GAC5BoB,0BAA0B,GAC1BC,kBAAkB,CAAC,EACvB,EAAE,CACH,CAACO,IAAI,CAAC,IAAI,CAAC,CACb;AACH;AAEA,SAASC,+BAA+B,CAACC,QAAgB,EAAE;EACzD,IAAI,CAACxC,eAAI,CAACyC,UAAU,CAACD,QAAQ,CAAC,EAAE;IAC9B,OAAOxC,eAAI,CAAC0C,OAAO,CAACrC,OAAO,CAACsC,GAAG,EAAE,EAAEH,QAAQ,CAAC;EAC9C;EACA,OAAOA,QAAQ;AACjB;AAEA,SAASI,eAAe,CACtBC,YAAmC,EACnC/C,QAAgB,EAChB;EACA,MAAMgD,gBAAgB,GAAGP,+BAA+B,CAACzC,QAAQ,CAAC;EAClE,OAAO+C,YAAY,CAACE,IAAI,CAAEC,IAAI,IAAK;IACjC,MAAMC,YAAY,GAAGV,+BAA+B,CAACS,IAAI,CAAC;IAC1D,OAAOF,gBAAgB,CAACI,UAAU,CAACD,YAAY,GAAGjD,eAAI,CAACmD,GAAG,CAAC;EAC7D,CAAC,CAAC;AACJ;;AAEA;AACA,SAASC,uBAAuB,CAAC/D,MAAc,EAAE;EAC/C,IAAIgE,aAAE,CAACC,UAAU,CAACjE,MAAM,CAAC,EAAE;IACzB;IACA,OAAOA,MAAM;EACf;EAEA,IAAI;IACF,IAAAuB,yBAAQ,EAAE,SAAQvB,MAAO,EAAC,EAAE;MAACkE,KAAK,EAAE;IAAQ,CAAC,CAAC;IAC9C;IACA,OAAOlE,MAAM;EACf,CAAC,CAAC,OAAOoC,KAAK,EAAE;IACd;EAAA;EAGF,IAAI;IACF,MAAM+B,WAAW,GAAGzC,MAAM,CAAC0C,MAAM,CAAClE,sBAAsB,CAAC;IACzD,MAAMmE,gBAAgB,GAAG3C,MAAM,CAACC,IAAI,CAACzB,sBAAsB,CAAC;IAC5D,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAME,UAAU,GAAGL,WAAW,CAACG,CAAC,CAAC;MACjC,IAAItE,MAAM,CAACyE,WAAW,EAAE,KAAKD,UAAU,CAACC,WAAW,EAAE,EAAE;QACrD;QACA;QACA,MAAMnD,MAAM,GAAG,IAAAC,yBAAQ,EACpB,0CAAyC8C,gBAAgB,CAACC,CAAC,CAAE,GAAE,CACjE,CAAC9C,QAAQ,EAAE;QAEZ,MAAMkD,OAAO,GAAGpD,MAAM,CAACS,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI2C,OAAO,CAAC,CAAC,CAAC,KAAM,IAAGL,gBAAgB,CAACC,CAAC,CAAE,GAAE,EAAE;UAC7C;UACA,OAAOtE,MAAM;QACf;QAEA,MAAM2E,GAAG,GAAGC,QAAQ,CAACF,OAAO,CAAC,CAAC,CAAC,CAACxC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3D,OAAO,IAAAX,yBAAQ,EACZ,gEAA+DoD,GAAI,mBAAkB,CACvF,CACEnD,QAAQ,EAAE,CACVqD,IAAI,EAAE;MACX;IACF;EACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd;EAAA;;EAGF;EACA;EACA,OAAOpC,MAAM;AACf;AAEA,IAAI8E,aAAkC,GAAG,IAAI;AAC7C,SAASC,YAAY,CACnBtE,QAAgB,EAChBC,UAAkB,EAClB8C,YAAmC,EACnC;EACA,IAAI,CAACQ,aAAE,CAACC,UAAU,CAACxD,QAAQ,CAAC,EAAE;IAC5B;EACF;;EAEA;EACA;EACA,IAAIC,UAAU,IAAIsE,KAAK,CAACtE,UAAU,CAAC,EAAE;IACnC;EACF;EAEA,IAAI,CAACV,MAAM,EAAE,GAAGK,IAAI,CAAC,GAAGU,WAAW,EAAE;EACrC,IAAI,CAACf,MAAM,EAAE;IACXuC,iBAAiB,CAAC,SAAS,CAAC;IAC5B;EACF;EAEA,MAAMjC,SAAS,GAAGiD,eAAe,CAACC,YAAY,EAAE/C,QAAQ,CAAC;EACzD,IAAIC,UAAU,EAAE;IACdL,IAAI,GAAGA,IAAI,CAAC4E,MAAM,CAChBzE,yBAAyB,CAACR,MAAM,EAAES,QAAQ,EAAEC,UAAU,EAAEJ,SAAS,CAAC,CACnE;EACH,CAAC,MAAM;IACLD,IAAI,GAAGA,IAAI,CAAC4E,MAAM,CAACnE,uBAAuB,CAACd,MAAM,EAAES,QAAQ,EAAEH,SAAS,CAAC,CAAC;EAC1E;;EAEA;EACA;EACA;EACA;EACA,IACEU,OAAO,CAACK,QAAQ,KAAK,OAAO,IAC5B,CAAClB,2BAA2B,CAAC+E,IAAI,CAACzE,QAAQ,CAACoE,IAAI,EAAE,CAAC,EAClD;IACAlC,eAAM,CAACP,KAAK,CAAE,kBAAiBzB,eAAI,CAACC,QAAQ,CAACH,QAAQ,CAAE,iBAAgB,CAAC;IACxEkC,eAAM,CAACC,IAAI,CACT,sEAAsE,GACpE,mEAAmE,GACnE,oEAAoE,GACpE,6CAA6C,CAChD;IACD;EACF;EAEAD,eAAM,CAACC,IAAI,CACR,WAAUC,gBAAK,CAACsC,SAAS,CAAC1E,QAAQ,CAAE,SAAQoC,gBAAK,CAACG,IAAI,CAAChD,MAAM,CAAE,EAAC,CAClE;EAED,IAAI8E,aAAa,IAAI/E,gBAAgB,CAACC,MAAM,CAAC,EAAE;IAC7C;IACA;IACA;IACA8E,aAAa,CAACM,IAAI,CAAC,SAAS,CAAC;EAC/B;EAEA,IAAIpE,OAAO,CAACK,QAAQ,KAAK,OAAO,EAAE;IAChC;IACA;IACAyD,aAAa,GAAG,IAAAO,sBAAK,EACnB,SAAS,EACT,CAAC,IAAI,EAAEtB,uBAAuB,CAAC/D,MAAM,CAAC,CAAC,CAACiF,MAAM,CAAC5E,IAAI,CAAC,EACpD;MACE6D,KAAK,EAAE;IACT,CAAC,CACF;EACH,CAAC,MAAM;IACLY,aAAa,GAAG,IAAAO,sBAAK,EAACrF,MAAM,EAAEK,IAAI,EAAE;MAAC6D,KAAK,EAAE;IAAS,CAAC,CAAC;EACzD;EACAY,aAAa,CAACQ,EAAE,CAAC,MAAM,EAAGC,SAAS,IAAK;IACtCT,aAAa,GAAG,IAAI;IAEpB,IAAIS,SAAS,EAAE;MACb5C,eAAM,CAACP,KAAK,CAAC,mCAAmC,CAAC;MACjDG,iBAAiB,CAAC,kCAAkC,CAAC;IACvD;EACF,CAAC,CAAC;EAEFuC,aAAa,CAACQ,EAAE,CAAC,OAAO,EAAGlD,KAAK,IAAK;IACnCO,eAAM,CAACP,KAAK,CAACA,KAAK,CAACoD,OAAO,CAAC;IAC3BjD,iBAAiB,CAAC,aAAa,CAAC;EAClC,CAAC,CAAC;AACJ;AAAC,eAEcwC,YAAY;AAAA"}