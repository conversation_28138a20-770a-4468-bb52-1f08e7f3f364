import React from 'react';
import { View, Text, Image, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AvatarProps {
  name: string;
  imageUri?: string;
  size?: number;
  style?: ViewStyle;
}

export const Avatar: React.FC<AvatarProps> = ({
  name,
  imageUri,
  size = 40,
  style,
}) => {
  const { colors } = useTheme();

  const getInitials = (fullName: string): string => {
    const names = fullName.trim().split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return fullName[0]?.toUpperCase() || '?';
  };

  const avatarStyle: ViewStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  };

  const textStyle = {
    color: colors.white,
    fontSize: size * 0.4,
    fontFamily: 'Cairo-Bold',
  };

  if (imageUri) {
    return (
      <Image
        source={{ uri: imageUri }}
        style={[avatarStyle, style]}
        resizeMode="cover"
      />
    );
  }

  return (
    <View style={[avatarStyle, style]}>
      <Text style={textStyle}>
        {getInitials(name)}
      </Text>
    </View>
  );
};
