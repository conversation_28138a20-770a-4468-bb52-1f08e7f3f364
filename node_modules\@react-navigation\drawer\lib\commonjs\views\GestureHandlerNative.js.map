{"version": 3, "names": ["PanGestureHandler", "props", "gestureRef", "React", "useRef"], "sourceRoot": "../../../src", "sources": ["views/GestureHandlerNative.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAKA;AAAiE;AAAA;AAAA;AAE1D,SAASA,iBAAiB,CAACC,KAAkC,EAAE;EACpE,MAAMC,UAAU,GAAGC,KAAK,CAACC,MAAM,CAA0B,IAAI,CAAC;EAE9D,oBACE,oBAAC,6BAAoB,CAAC,QAAQ;IAAC,KAAK,EAAEF;EAAW,gBAC/C,oBAAC,4CAAuB,EAAKD,KAAK,CAAI,CACR;AAEpC"}