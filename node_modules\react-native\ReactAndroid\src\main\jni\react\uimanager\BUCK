load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_target", "react_native_xplat_target", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "jni",
    srcs = glob(["*.cpp"]),
    headers = glob(["*.h"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "**/*.h"),
        ],
        prefix = "react/uimanager/jni",
    ),
    fbandroid_allow_jni_merging = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    soname = "libuimanagerjni.$(ext)",
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("runtimeexecutor:runtimeexecutor"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        react_native_xplat_target("react/renderer/componentregistry/native:native"),
        react_native_target("jni/react/jni:jni"),
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/jsi:JSIDynamic",
        "//xplat/jsi:jsi",
        "//xplat/third-party/linker_lib:atomic",
        FBJNI_TARGET,
    ],
)
