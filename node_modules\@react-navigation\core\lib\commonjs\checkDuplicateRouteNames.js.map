{"version": 3, "names": ["checkDuplicateRouteNames", "state", "duplicates", "getRouteNames", "location", "routes", "for<PERSON>ach", "route", "currentLocation", "name", "routeNames", "routeName", "push"], "sourceRoot": "../../src", "sources": ["checkDuplicateRouteNames.tsx"], "mappings": ";;;;;;AAEe,SAASA,wBAAwB,CAACC,KAAsB,EAAE;EACvE,MAAMC,UAAsB,GAAG,EAAE;EAEjC,MAAMC,aAAa,GAAG,CACpBC,QAAgB,EAChBH,KAAsD,KACnD;IACHA,KAAK,CAACI,MAAM,CAACC,OAAO,CAAEC,KAA+B,IAAK;MAAA;MACxD,MAAMC,eAAe,GAAGJ,QAAQ,GAC3B,GAAEA,QAAS,MAAKG,KAAK,CAACE,IAAK,EAAC,GAC7BF,KAAK,CAACE,IAAI;MAEd,gBAAAF,KAAK,CAACN,KAAK,0EAAX,aAAaS,UAAU,0DAAvB,sBAAyBJ,OAAO,CAAEK,SAAS,IAAK;QAC9C,IAAIA,SAAS,KAAKJ,KAAK,CAACE,IAAI,EAAE;UAC5BP,UAAU,CAACU,IAAI,CAAC,CACdJ,eAAe,EACd,GAAEA,eAAgB,MAAKD,KAAK,CAACE,IAAK,EAAC,CACrC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAIF,KAAK,CAACN,KAAK,EAAE;QACfE,aAAa,CAACK,eAAe,EAAED,KAAK,CAACN,KAAK,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EAEDE,aAAa,CAAC,EAAE,EAAEF,KAAK,CAAC;EAExB,OAAOC,UAAU;AACnB"}