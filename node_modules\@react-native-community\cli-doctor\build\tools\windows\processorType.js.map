{"version": 3, "names": ["getProcessorType", "process", "env", "PROCESSOR_IDENTIFIER", "includes"], "sources": ["../../../src/tools/windows/processorType.ts"], "sourcesContent": ["/**\n * Returns if the processor is Intel or AMD\n */\nexport const getProcessorType = () => {\n  return process.env.PROCESSOR_IDENTIFIER!.includes('Intel') ? 'Intel' : 'AMD';\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACO,MAAMA,gBAAgB,GAAG,MAAM;EACpC,OAAOC,OAAO,CAACC,GAAG,CAACC,oBAAoB,CAAEC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,KAAK;AAC9E,CAAC;AAAC"}