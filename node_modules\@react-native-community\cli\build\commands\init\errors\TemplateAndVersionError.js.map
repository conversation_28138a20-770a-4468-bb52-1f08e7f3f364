{"version": 3, "names": ["TemplateAndVersionError", "CLIError", "constructor", "template"], "sources": ["../../../../src/commands/init/errors/TemplateAndVersionError.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\n\nexport default class TemplateAndVersionError extends CLIError {\n  constructor(template: string) {\n    super(\n      `Passing both \"version\" and \"template\" is not supported. The template you select determines the version of react-native used. Please use only one of these options, for example:\n      \n      --template ${template}@x.y.z\n      \n      where x.y.z is the release of the template that contains the desired \"react-native\" version. Check the version tab of https://www.npmjs.com/package/${template} for available versions`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEe,MAAMA,uBAAuB,SAASC,oBAAQ,CAAC;EAC5DC,WAAW,CAACC,QAAgB,EAAE;IAC5B,KAAK,CACF;AACP;AACA,mBAAmBA,QAAS;AAC5B;AACA,4JAA4JA,QAAS,yBAAwB,CACxL;EACH;AACF;AAAC"}