{"version": 3, "names": ["getEmulatorName", "deviceId", "adbPath", "getAdbPath", "buffer", "execSync", "toString", "split", "os", "EOL", "replace", "trim", "getPhoneName", "promptForDeviceSelection", "allDevices", "length", "CLIError", "device", "prompts", "type", "name", "message", "choices", "map", "d", "title", "chalk", "bold", "toPascalCase", "green", "readableName", "connected", "value", "min", "listAndroidDevices", "devices", "adb", "getDevices", "for<PERSON>ach", "includes", "emulatorData", "phoneData", "emulators", "getEmulators", "emulator<PERSON>ame", "some", "undefined", "selected<PERSON><PERSON><PERSON>"], "sources": ["../../../src/commands/runAndroid/listAndroidDevices.ts"], "sourcesContent": ["import {execSync} from 'child_process';\nimport adb from './adb';\nimport getAdbPath from './getAdbPath';\nimport {getEmulators} from './tryLaunchEmulator';\nimport {toPascalCase} from './toPascalCase';\nimport os from 'os';\nimport prompts from 'prompts';\nimport chalk from 'chalk';\nimport {CLIError} from '@react-native-community/cli-tools';\n\ntype DeviceData = {\n  deviceId: string | undefined;\n  readableName: string;\n  connected: boolean;\n  type: 'emulator' | 'phone';\n};\n\n/**\n *\n * @param deviceId string\n * @returns name of Android emulator\n */\nfunction getEmulatorName(deviceId: string) {\n  const adbPath = getAdbPath();\n  const buffer = execSync(`${adbPath} -s ${deviceId} emu avd name`);\n\n  // 1st line should get us emu name\n  return buffer\n    .toString()\n    .split(os.EOL)[0]\n    .replace(/(\\r\\n|\\n|\\r)/gm, '')\n    .trim();\n}\n\n/**\n *\n * @param deviceId string\n * @returns Android device name in readable format\n */\nfunction getPhoneName(deviceId: string) {\n  const adbPath = getAdbPath();\n  const buffer = execSync(\n    `${adbPath} -s ${deviceId} shell getprop | grep ro.product.model`,\n  );\n  return buffer\n    .toString()\n    .replace(/\\[ro\\.product\\.model\\]:\\s*\\[(.*)\\]/, '$1')\n    .trim();\n}\n\nasync function promptForDeviceSelection(\n  allDevices: Array<DeviceData>,\n): Promise<DeviceData | undefined> {\n  if (!allDevices.length) {\n    throw new CLIError(\n      'No devices and/or emulators connected. Please create emulator with Android Studio or connect Android device.',\n    );\n  }\n  const {device} = await prompts({\n    type: 'select',\n    name: 'device',\n    message: 'Select the device / emulator you want to use',\n    choices: allDevices.map((d) => ({\n      title: `${chalk.bold(`${toPascalCase(d.type)}`)} ${chalk.green(\n        `${d.readableName}`,\n      )} (${d.connected ? 'connected' : 'disconnected'})`,\n      value: d,\n    })),\n    min: 1,\n  });\n\n  return device;\n}\n\nasync function listAndroidDevices() {\n  const adbPath = getAdbPath();\n  const devices = adb.getDevices(adbPath);\n\n  let allDevices: Array<DeviceData> = [];\n\n  devices.forEach((deviceId) => {\n    if (deviceId.includes('emulator')) {\n      const emulatorData: DeviceData = {\n        deviceId,\n        readableName: getEmulatorName(deviceId),\n        connected: true,\n        type: 'emulator',\n      };\n      allDevices = [...allDevices, emulatorData];\n    } else {\n      const phoneData: DeviceData = {\n        deviceId,\n        readableName: getPhoneName(deviceId),\n        type: 'phone',\n        connected: true,\n      };\n      allDevices = [...allDevices, phoneData];\n    }\n  });\n\n  const emulators = getEmulators();\n\n  // Find not booted ones:\n  emulators.forEach((emulatorName) => {\n    // skip those already booted\n    if (allDevices.some((device) => device.readableName === emulatorName)) {\n      return;\n    }\n    const emulatorData: DeviceData = {\n      deviceId: undefined,\n      readableName: emulatorName,\n      type: 'emulator',\n      connected: false,\n    };\n    allDevices = [...allDevices, emulatorData];\n  });\n\n  const selectedDevice = await promptForDeviceSelection(allDevices);\n  return selectedDevice;\n}\n\nexport default listAndroidDevices;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA2D;AAS3D;AACA;AACA;AACA;AACA;AACA,SAASA,eAAe,CAACC,QAAgB,EAAE;EACzC,MAAMC,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAC5B,MAAMC,MAAM,GAAG,IAAAC,yBAAQ,EAAE,GAAEH,OAAQ,OAAMD,QAAS,eAAc,CAAC;;EAEjE;EACA,OAAOG,MAAM,CACVE,QAAQ,EAAE,CACVC,KAAK,CAACC,aAAE,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChBC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAC7BC,IAAI,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAY,CAACX,QAAgB,EAAE;EACtC,MAAMC,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAC5B,MAAMC,MAAM,GAAG,IAAAC,yBAAQ,EACpB,GAAEH,OAAQ,OAAMD,QAAS,wCAAuC,CAClE;EACD,OAAOG,MAAM,CACVE,QAAQ,EAAE,CACVI,OAAO,CAAC,oCAAoC,EAAE,IAAI,CAAC,CACnDC,IAAI,EAAE;AACX;AAEA,eAAeE,wBAAwB,CACrCC,UAA6B,EACI;EACjC,IAAI,CAACA,UAAU,CAACC,MAAM,EAAE;IACtB,MAAM,KAAIC,oBAAQ,EAChB,8GAA8G,CAC/G;EACH;EACA,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAC,kBAAO,EAAC;IAC7BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,8CAA8C;IACvDC,OAAO,EAAER,UAAU,CAACS,GAAG,CAAEC,CAAC,KAAM;MAC9BC,KAAK,EAAG,GAAEC,gBAAK,CAACC,IAAI,CAAE,GAAE,IAAAC,0BAAY,EAACJ,CAAC,CAACL,IAAI,CAAE,EAAC,CAAE,IAAGO,gBAAK,CAACG,KAAK,CAC3D,GAAEL,CAAC,CAACM,YAAa,EAAC,CACnB,KAAIN,CAAC,CAACO,SAAS,GAAG,WAAW,GAAG,cAAe,GAAE;MACnDC,KAAK,EAAER;IACT,CAAC,CAAC,CAAC;IACHS,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,OAAOhB,MAAM;AACf;AAEA,eAAeiB,kBAAkB,GAAG;EAClC,MAAMhC,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAC5B,MAAMgC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACnC,OAAO,CAAC;EAEvC,IAAIY,UAA6B,GAAG,EAAE;EAEtCqB,OAAO,CAACG,OAAO,CAAErC,QAAQ,IAAK;IAC5B,IAAIA,QAAQ,CAACsC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,MAAMC,YAAwB,GAAG;QAC/BvC,QAAQ;QACR6B,YAAY,EAAE9B,eAAe,CAACC,QAAQ,CAAC;QACvC8B,SAAS,EAAE,IAAI;QACfZ,IAAI,EAAE;MACR,CAAC;MACDL,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE0B,YAAY,CAAC;IAC5C,CAAC,MAAM;MACL,MAAMC,SAAqB,GAAG;QAC5BxC,QAAQ;QACR6B,YAAY,EAAElB,YAAY,CAACX,QAAQ,CAAC;QACpCkB,IAAI,EAAE,OAAO;QACbY,SAAS,EAAE;MACb,CAAC;MACDjB,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE2B,SAAS,CAAC;IACzC;EACF,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAG,IAAAC,+BAAY,GAAE;;EAEhC;EACAD,SAAS,CAACJ,OAAO,CAAEM,YAAY,IAAK;IAClC;IACA,IAAI9B,UAAU,CAAC+B,IAAI,CAAE5B,MAAM,IAAKA,MAAM,CAACa,YAAY,KAAKc,YAAY,CAAC,EAAE;MACrE;IACF;IACA,MAAMJ,YAAwB,GAAG;MAC/BvC,QAAQ,EAAE6C,SAAS;MACnBhB,YAAY,EAAEc,YAAY;MAC1BzB,IAAI,EAAE,UAAU;MAChBY,SAAS,EAAE;IACb,CAAC;IACDjB,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE0B,YAAY,CAAC;EAC5C,CAAC,CAAC;EAEF,MAAMO,cAAc,GAAG,MAAMlC,wBAAwB,CAACC,UAAU,CAAC;EACjE,OAAOiC,cAAc;AACvB;AAAC,eAEcb,kBAAkB;AAAA"}