load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_target", "react_native_xplat_shared_library_target", "react_native_xplat_target", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "jni",
    srcs = [
        "ReactCommon/CompositeTurboModuleManagerDelegate.cpp",
        "ReactCommon/OnLoad.cpp",
        "ReactCommon/TurboModuleManager.cpp",
    ],
    header_namespace = "",
    exported_headers = {
        "ReactCommon/CompositeTurboModuleManagerDelegate.h": "ReactCommon/CompositeTurboModuleManagerDelegate.h",
        "ReactCommon/TurboModuleManager.h": "ReactCommon/TurboModuleManager.h",
        "ReactCommon/TurboModuleManagerDelegate.h": "ReactCommon/TurboModuleManagerDelegate.h",
    },
    fbandroid_allow_jni_merging = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    soname = "libturbomodulejsijni.$(ext)",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_target("jni/react/jni:jni"),
        FBJNI_TARGET,
    ],
    exported_deps = [
        ":callinvokerholder",
        react_native_xplat_shared_library_target("jsi:jsi"),
        react_native_xplat_target("react/nativemodule/core:core"),
        react_native_xplat_target("runtimeexecutor:runtimeexecutor"),
        react_native_target("jni/react/reactperflogger:jni"),
    ],
)

rn_xplat_cxx_library(
    name = "callinvokerholder",
    srcs = [
        "ReactCommon/CallInvokerHolder.cpp",
    ],
    header_namespace = "",
    exported_headers = {
        "ReactCommon/CallInvokerHolder.h": "ReactCommon/CallInvokerHolder.h",
    },
    fbandroid_deps = [
        FBJNI_TARGET,
    ],
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    preferred_linkage = "static",
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    exported_deps = [
        react_native_xplat_target("callinvoker:callinvoker"),
    ],
)
