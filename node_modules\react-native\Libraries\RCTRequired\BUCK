load("//tools/build_defs/oss:rn_defs.bzl", "rn_apple_library")

rn_apple_library(
    name = "RCTRequired",
    exported_headers = [
        "RCTRequired/RCTRequired.h",
    ],
    autoglob = False,
    complete_nullability = True,
    contacts = ["<EMAIL>"],
    disable_infer_precompiled_header = True,
    extension_api_only = True,
    frameworks = ["Foundation"],
    labels = [
        "fbios_link_group:xplat/default/public.react_native.infra",
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
        "talkios_link_group:xplat/default/public.react_native.infra",
    ],
)
