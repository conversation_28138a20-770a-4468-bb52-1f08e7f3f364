{"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "ScheduleUpdateContext", "createContext", "scheduleUpdate", "Error", "flushUpdates", "useScheduleUpdate", "callback", "useContext", "useEffect"], "sourceRoot": "../../src", "sources": ["useScheduleUpdate.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,qBAAqB,GAAG,mCAAmC;AAEjE,OAAO,MAAMC,qBAAqB,gBAAGF,KAAK,CAACG,aAAa,CAGrD;EACDC,cAAc,GAAG;IACf,MAAM,IAAIC,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACDK,YAAY,GAAG;IACb,MAAM,IAAID,KAAK,CAACJ,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASM,iBAAiB,CAACC,QAAoB,EAAE;EAC9D,MAAM;IAAEJ,cAAc;IAAEE;EAAa,CAAC,GAAGN,KAAK,CAACS,UAAU,CACvDP,qBAAqB,CACtB;EAEDE,cAAc,CAACI,QAAQ,CAAC;EAExBR,KAAK,CAACU,SAAS,CAACJ,YAAY,CAAC;AAC/B"}