{"version": 3, "names": ["interpolate", "interpolateDeprecated", "interpolateNode", "cond", "greaterThan", "Animated", "PROGRESS_EPSILON", "Overlay", "React", "forwardRef", "ref", "progress", "onPress", "style", "accessibilityLabel", "props", "animatedStyle", "opacity", "inputRange", "Platform", "OS", "outputRange", "zIndex", "styles", "overlay", "overlayStyle", "pressable", "select", "web", "WebkitTapHighlightColor", "default", "StyleSheet", "create", "absoluteFillObject", "backgroundColor", "flex"], "sourceRoot": "../../../../src", "sources": ["views/legacy/Overlay.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAA+C;AAAA;AAAA;AAAA;AAE/C,MAAM;EACJ;EACAA,WAAW,EAAEC,qBAAqB;EAClCC,eAAe;EACfC,IAAI;EACJC;AACF,CAAC,GAAGC,8BAAQ;AAEZ,MAAML,WAAmC,GACvCE,eAAe,IAAID,qBAAqB;AAE1C,MAAMK,gBAAgB,GAAG,IAAI;AAQ7B,MAAMC,OAAO,gBAAGC,KAAK,CAACC,UAAU,CAAC,SAASF,OAAO,OAQ/CG,GAA6B,EAC7B;EAAA,IARA;IACEC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,kBAAkB,GAAG,cAAc;IACnC,GAAGC;EACE,CAAC;EAGR,MAAMC,aAAa,GAAG;IACpBC,OAAO,EAAEjB,WAAW,CAACW,QAAQ,EAAE;MAC7B;MACA;MACA;MACA;MACAO,UAAU,EACRC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAACC,EAAE,KAAK,OAAO,GAChD,CAAC,CAAC,EAAE,CAAC,CAAC,GACN,CAACd,gBAAgB,EAAE,CAAC,CAAC;MAC3Be,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;IACF;IACA;IACA;IACAC,MAAM,EAAEnB,IAAI,CAACC,WAAW,CAACO,QAAQ,EAAEL,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,oBACE,oBAAC,8BAAQ,CAAC,IAAI,eACRS,KAAK;IACT,GAAG,EAAEL,GAAI;IACT,KAAK,EAAE,CAACa,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAET,aAAa,EAAEH,KAAK;EAAE,iBAE5D,oBAAC,sBAAS;IACR,OAAO,EAAED,OAAQ;IACjB,KAAK,EAAEW,MAAM,CAACG,SAAU;IACxB,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAEZ;EAAmB,EACvC,CACY;AAEpB,CAAC,CAAC;AAEF,MAAMW,YAAY,GAAGN,qBAAQ,CAACQ,MAAM,CAAyB;EAC3DC,GAAG,EAAE;IACH;IACA;IACAC,uBAAuB,EAAE;EAC3B,CAAC;EACDC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAMP,MAAM,GAAGQ,uBAAU,CAACC,MAAM,CAAC;EAC/BR,OAAO,EAAE;IACP,GAAGO,uBAAU,CAACE,kBAAkB;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDR,SAAS,EAAE;IACTS,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAAC,eAEY5B,OAAO;AAAA"}