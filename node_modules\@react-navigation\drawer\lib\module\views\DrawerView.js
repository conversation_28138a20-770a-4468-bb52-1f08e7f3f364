function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import { getHeaderTitle, Header, SafeAreaProviderCompat, Screen } from '@react-navigation/elements';
import { DrawerActions, useTheme } from '@react-navigation/native';
import * as React from 'react';
import { I18nManager, Platform, StyleSheet, View } from 'react-native';
import * as Reanimated from 'react-native-reanimated';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { addCancelListener } from '../utils/addCancelListener';
import DrawerPositionContext from '../utils/DrawerPositionContext';
import DrawerStatusContext from '../utils/DrawerStatusContext';
import getDrawerStatusFromState from '../utils/getDrawerStatusFromState';
import DrawerContent from './DrawerContent';
import DrawerToggleButton from './DrawerToggleButton';
import { GestureHandlerRootView } from './GestureHandler';
import { MaybeScreen, MaybeScreenContainer } from './ScreenFallback';
const getDefaultDrawerWidth = _ref => {
  let {
    height,
    width
  } = _ref;
  /*
   * Default drawer width is screen width - header height
   * with a max width of 280 on mobile and 320 on tablet
   * https://material.io/components/navigation-drawer
   */
  const smallerAxisSize = Math.min(height, width);
  const isLandscape = width > height;
  const isTablet = smallerAxisSize >= 600;
  const appBarHeight = Platform.OS === 'ios' ? isLandscape ? 32 : 44 : 56;
  const maxWidth = isTablet ? 320 : 280;
  return Math.min(smallerAxisSize - appBarHeight, maxWidth);
};
const GestureHandlerWrapper = GestureHandlerRootView ?? View;
function DrawerViewBase(_ref2) {
  var _Reanimated$isConfigu;
  let {
    state,
    navigation,
    descriptors,
    defaultStatus,
    drawerContent = props => /*#__PURE__*/React.createElement(DrawerContent, props),
    detachInactiveScreens = Platform.OS === 'web' || Platform.OS === 'android' || Platform.OS === 'ios',
    // Reanimated 2 is not configured
    // @ts-expect-error: the type definitions are incomplete
    useLegacyImplementation = !((_Reanimated$isConfigu = Reanimated.isConfigured) !== null && _Reanimated$isConfigu !== void 0 && _Reanimated$isConfigu.call(Reanimated))
  } = _ref2;
  // Reanimated v3 dropped legacy v1 syntax
  const legacyImplemenationNotAvailable = require('react-native-reanimated').abs === undefined;
  if (useLegacyImplementation && legacyImplemenationNotAvailable) {
    throw new Error('The `useLegacyImplementation` prop is not available with Reanimated 3 as it no longer includes support for Reanimated 1 legacy API. Remove the `useLegacyImplementation` prop from `Drawer.Navigator` to be able to use it.');
  }
  const Drawer = useLegacyImplementation ? require('./legacy/Drawer').default : require('./modern/Drawer').default;
  const focusedRouteKey = state.routes[state.index].key;
  const {
    drawerHideStatusBarOnOpen = false,
    drawerPosition = I18nManager.getConstants().isRTL ? 'right' : 'left',
    drawerStatusBarAnimation = 'slide',
    drawerStyle,
    drawerType = Platform.select({
      ios: 'slide',
      default: 'front'
    }),
    gestureHandlerProps,
    keyboardDismissMode = 'on-drag',
    overlayColor = 'rgba(0, 0, 0, 0.5)',
    swipeEdgeWidth = 32,
    swipeEnabled = Platform.OS !== 'web' && Platform.OS !== 'windows' && Platform.OS !== 'macos',
    swipeMinDistance = 60,
    overlayAccessibilityLabel
  } = descriptors[focusedRouteKey].options;
  const [loaded, setLoaded] = React.useState([focusedRouteKey]);
  if (!loaded.includes(focusedRouteKey)) {
    setLoaded([...loaded, focusedRouteKey]);
  }
  const dimensions = useSafeAreaFrame();
  const {
    colors
  } = useTheme();
  const drawerStatus = getDrawerStatusFromState(state);
  const handleDrawerOpen = React.useCallback(() => {
    navigation.dispatch({
      ...DrawerActions.openDrawer(),
      target: state.key
    });
  }, [navigation, state.key]);
  const handleDrawerClose = React.useCallback(() => {
    navigation.dispatch({
      ...DrawerActions.closeDrawer(),
      target: state.key
    });
  }, [navigation, state.key]);
  React.useEffect(() => {
    if (drawerStatus === defaultStatus || drawerType === 'permanent') {
      return;
    }
    const handleHardwareBack = () => {
      // We shouldn't handle the back button if the parent screen isn't focused
      // This will avoid the drawer overriding event listeners from a focused screen
      if (!navigation.isFocused()) {
        return false;
      }
      if (defaultStatus === 'open') {
        handleDrawerOpen();
      } else {
        handleDrawerClose();
      }
      return true;
    };

    // We only add the listeners when drawer opens
    // This way we can make sure that the listener is added as late as possible
    // This will make sure that our handler will run first when back button is pressed
    return addCancelListener(handleHardwareBack);
  }, [defaultStatus, drawerStatus, drawerType, handleDrawerClose, handleDrawerOpen, navigation]);
  const renderDrawerContent = () => {
    return /*#__PURE__*/React.createElement(DrawerPositionContext.Provider, {
      value: drawerPosition
    }, drawerContent({
      state: state,
      navigation: navigation,
      descriptors: descriptors
    }));
  };
  const renderSceneContent = () => {
    return /*#__PURE__*/React.createElement(MaybeScreenContainer, {
      enabled: detachInactiveScreens,
      hasTwoStates: true,
      style: styles.content
    }, state.routes.map((route, index) => {
      const descriptor = descriptors[route.key];
      const {
        lazy = true,
        unmountOnBlur
      } = descriptor.options;
      const isFocused = state.index === index;
      if (unmountOnBlur && !isFocused) {
        return null;
      }
      if (lazy && !loaded.includes(route.key) && !isFocused) {
        // Don't render a lazy screen if we've never navigated to it
        return null;
      }
      const {
        freezeOnBlur,
        header = _ref3 => {
          let {
            layout,
            options
          } = _ref3;
          return /*#__PURE__*/React.createElement(Header, _extends({}, options, {
            layout: layout,
            title: getHeaderTitle(options, route.name),
            headerLeft: options.headerLeft ?? (props => /*#__PURE__*/React.createElement(DrawerToggleButton, props))
          }));
        },
        headerShown,
        headerStatusBarHeight,
        headerTransparent,
        sceneContainerStyle
      } = descriptor.options;
      return /*#__PURE__*/React.createElement(MaybeScreen, {
        key: route.key,
        style: [StyleSheet.absoluteFill, {
          zIndex: isFocused ? 0 : -1
        }],
        visible: isFocused,
        enabled: detachInactiveScreens,
        freezeOnBlur: freezeOnBlur
      }, /*#__PURE__*/React.createElement(Screen, {
        focused: isFocused,
        route: descriptor.route,
        navigation: descriptor.navigation,
        headerShown: headerShown,
        headerStatusBarHeight: headerStatusBarHeight,
        headerTransparent: headerTransparent,
        header: header({
          layout: dimensions,
          route: descriptor.route,
          navigation: descriptor.navigation,
          options: descriptor.options
        }),
        style: sceneContainerStyle
      }, descriptor.render()));
    }));
  };
  return /*#__PURE__*/React.createElement(DrawerStatusContext.Provider, {
    value: drawerStatus
  }, /*#__PURE__*/React.createElement(Drawer, {
    open: drawerStatus !== 'closed',
    onOpen: handleDrawerOpen,
    onClose: handleDrawerClose,
    gestureHandlerProps: gestureHandlerProps,
    swipeEnabled: swipeEnabled,
    swipeEdgeWidth: swipeEdgeWidth,
    swipeVelocityThreshold: 500,
    swipeDistanceThreshold: swipeMinDistance,
    hideStatusBarOnOpen: drawerHideStatusBarOnOpen,
    statusBarAnimation: drawerStatusBarAnimation,
    keyboardDismissMode: keyboardDismissMode,
    drawerType: drawerType,
    overlayAccessibilityLabel: overlayAccessibilityLabel,
    drawerPosition: drawerPosition,
    drawerStyle: [{
      width: getDefaultDrawerWidth(dimensions),
      backgroundColor: colors.card
    }, drawerType === 'permanent' && (drawerPosition === 'left' ? {
      borderRightColor: colors.border,
      borderRightWidth: StyleSheet.hairlineWidth
    } : {
      borderLeftColor: colors.border,
      borderLeftWidth: StyleSheet.hairlineWidth
    }), drawerStyle],
    overlayStyle: {
      backgroundColor: overlayColor
    },
    renderDrawerContent: renderDrawerContent,
    renderSceneContent: renderSceneContent,
    dimensions: dimensions
  }));
}
export default function DrawerView(_ref4) {
  let {
    navigation,
    ...rest
  } = _ref4;
  return /*#__PURE__*/React.createElement(SafeAreaProviderCompat, null, /*#__PURE__*/React.createElement(GestureHandlerWrapper, {
    style: styles.content
  }, /*#__PURE__*/React.createElement(DrawerViewBase, _extends({
    navigation: navigation
  }, rest))));
}
const styles = StyleSheet.create({
  content: {
    flex: 1
  }
});
//# sourceMappingURL=DrawerView.js.map