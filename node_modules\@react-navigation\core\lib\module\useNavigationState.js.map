{"version": 3, "names": ["React", "useNavigation", "useNavigationState", "selector", "navigation", "setResult", "useState", "getState", "selectorRef", "useRef", "useEffect", "current", "unsubscribe", "addListener", "e", "data", "state"], "sourceRoot": "../../src", "sources": ["useNavigationState.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,aAAa,MAAM,iBAAiB;AAM3C;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkB,CACxCC,QAAgC,EAC7B;EACH,MAAMC,UAAU,GAAGH,aAAa,EAA6B;;EAE7D;EACA;EACA,MAAM,GAAGI,SAAS,CAAC,GAAGL,KAAK,CAACM,QAAQ,CAAC,MAAMH,QAAQ,CAACC,UAAU,CAACG,QAAQ,EAAE,CAAC,CAAC;;EAE3E;EACA,MAAMC,WAAW,GAAGR,KAAK,CAACS,MAAM,CAACN,QAAQ,CAAC;EAE1CH,KAAK,CAACU,SAAS,CAAC,MAAM;IACpBF,WAAW,CAACG,OAAO,GAAGR,QAAQ;EAChC,CAAC,CAAC;EAEFH,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,MAAME,WAAW,GAAGR,UAAU,CAACS,WAAW,CAAC,OAAO,EAAGC,CAAC,IAAK;MACzDT,SAAS,CAACG,WAAW,CAACG,OAAO,CAACG,CAAC,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,OAAOJ,WAAW;EACpB,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC;EAEhB,OAAOD,QAAQ,CAACC,UAAU,CAACG,QAAQ,EAAE,CAAC;AACxC"}