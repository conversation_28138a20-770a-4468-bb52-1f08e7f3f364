{"version": 3, "names": ["bundleWithOutput", "_", "config", "args", "output", "buildBundle", "name", "description", "func", "options", "bundleCommandLineArgs", "withOutput"], "sources": ["../../../src/commands/bundle/bundle.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport type {Config} from '@react-native-community/cli-types';\nimport buildBundle from './buildBundle';\nimport bundleCommandLineArgs, {CommandLineArgs} from './bundleCommandLineArgs';\n\n/**\n * Builds the bundle starting to look for dependencies at the given entry path.\n */\nfunction bundleWithOutput(\n  _: Array<string>,\n  config: Config,\n  args: CommandLineArgs,\n  output: any, // untyped metro/src/shared/output/bundle or metro/src/shared/output/RamBundle\n) {\n  return buildBundle(args, config, output);\n}\n\nexport default {\n  name: 'bundle',\n  description: 'builds the javascript bundle for offline use',\n  func: bundleWithOutput,\n  options: bundleCommandLineArgs,\n  // Used by `ramBundle.js`\n  withOutput: bundleWithOutput,\n};\n\nconst withOutput = bundleWithOutput;\n\nexport {withOutput};\n"], "mappings": ";;;;;;AAQA;AACA;AAA+E;AAT/E;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA,SAASA,gBAAgB,CACvBC,CAAgB,EAChBC,MAAc,EACdC,IAAqB,EACrBC,MAAW,CAAE;AAAA,EACb;EACA,OAAO,IAAAC,oBAAW,EAACF,IAAI,EAAED,MAAM,EAAEE,MAAM,CAAC;AAC1C;AAAC,eAEc;EACbE,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,8CAA8C;EAC3DC,IAAI,EAAER,gBAAgB;EACtBS,OAAO,EAAEC,8BAAqB;EAC9B;EACAC,UAAU,EAAEX;AACd,CAAC;AAAA;AAED,MAAMW,UAAU,GAAGX,gBAAgB;AAAC"}