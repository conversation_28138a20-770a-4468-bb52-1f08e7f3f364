{"version": 3, "names": ["getTempFilePath", "filename", "path", "join", "os", "tmpdir", "writeJsonSync", "targetPath", "data", "json", "JSON", "stringify", "e", "CLIError", "fs", "writeFileSync", "getSourcemapFromServer", "port", "platform", "dev", "minify", "host", "logger", "debug", "requestURL", "fetch", "undefined", "generateSourcemap", "bundleOptions", "sourceMapPath", "sourceMapResult", "error", "findSourcemap", "ctx", "intermediateBuildPath", "root", "generatedBuildPath", "existsSync"], "sources": ["../../src/profileHermes/sourcemapUtils.ts"], "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError, fetch} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport {SourceMap} from 'hermes-profile-transformer';\nimport {MetroBundleOptions} from './metroBundleOptions';\n\nfunction getTempFilePath(filename: string) {\n  return path.join(os.tmpdir(), filename);\n}\n\nfunction writeJsonSync(targetPath: string, data: any) {\n  let json;\n  try {\n    json = JSON.stringify(data);\n  } catch (e) {\n    throw new CLIError(\n      `Failed to serialize data to json before writing to ${targetPath}`,\n      e as Error,\n    );\n  }\n\n  try {\n    fs.writeFileSync(targetPath, json, 'utf-8');\n  } catch (e) {\n    throw new CLIError(`Failed to write json to ${targetPath}`, e as Error);\n  }\n}\n\nasync function getSourcemapFromServer(\n  port: string,\n  {platform, dev, minify, host}: MetroBundleOptions,\n): Promise<SourceMap | undefined> {\n  logger.debug('Getting source maps from Metro packager server');\n\n  const requestURL = `http://${host}:${port}/index.map?platform=${platform}&dev=${dev}&minify=${minify}`;\n  logger.debug(`Downloading from ${requestURL}`);\n  try {\n    const {data} = await fetch(requestURL);\n    return data as SourceMap;\n  } catch (e) {\n    logger.debug(`Failed to fetch source map from \"${requestURL}\"`);\n    return undefined;\n  }\n}\n\n/**\n * Generate a sourcemap by fetching it from a running metro server\n */\nexport async function generateSourcemap(\n  port: string,\n  bundleOptions: MetroBundleOptions,\n): Promise<string | undefined> {\n  // Fetch the source map to a temp directory\n  const sourceMapPath = getTempFilePath('index.map');\n  const sourceMapResult = await getSourcemapFromServer(port, bundleOptions);\n\n  if (sourceMapResult) {\n    logger.debug('Using source maps from Metro packager server');\n    writeJsonSync(sourceMapPath, sourceMapResult);\n    logger.debug(\n      `Successfully obtained the source map and stored it in ${sourceMapPath}`,\n    );\n    return sourceMapPath;\n  } else {\n    logger.error('Cannot obtain source maps from Metro packager server');\n    return undefined;\n  }\n}\n\n/**\n *\n * @param ctx\n */\nexport async function findSourcemap(\n  ctx: Config,\n  port: string,\n  bundleOptions: MetroBundleOptions,\n): Promise<string | undefined> {\n  const intermediateBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'intermediates',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.packager.map',\n  );\n\n  const generatedBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'generated',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.map',\n  );\n\n  if (fs.existsSync(generatedBuildPath)) {\n    logger.debug(`Getting the source map from ${generateSourcemap}`);\n    return generatedBuildPath;\n  } else if (fs.existsSync(intermediateBuildPath)) {\n    logger.debug(`Getting the source map from ${intermediateBuildPath}`);\n    return intermediateBuildPath;\n  } else {\n    return generateSourcemap(port, bundleOptions);\n  }\n}\n"], "mappings": ";;;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAIpB,SAASA,eAAe,CAACC,QAAgB,EAAE;EACzC,OAAOC,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,MAAM,EAAE,EAAEJ,QAAQ,CAAC;AACzC;AAEA,SAASK,aAAa,CAACC,UAAkB,EAAEC,IAAS,EAAE;EACpD,IAAIC,IAAI;EACR,IAAI;IACFA,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,MAAM,KAAIC,oBAAQ,EACf,sDAAqDN,UAAW,EAAC,EAClEK,CAAC,CACF;EACH;EAEA,IAAI;IACFE,aAAE,CAACC,aAAa,CAACR,UAAU,EAAEE,IAAI,EAAE,OAAO,CAAC;EAC7C,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,MAAM,KAAIC,oBAAQ,EAAE,2BAA0BN,UAAW,EAAC,EAAEK,CAAC,CAAU;EACzE;AACF;AAEA,eAAeI,sBAAsB,CACnCC,IAAY,EACZ;EAACC,QAAQ;EAAEC,GAAG;EAAEC,MAAM;EAAEC;AAAwB,CAAC,EACjB;EAChCC,kBAAM,CAACC,KAAK,CAAC,gDAAgD,CAAC;EAE9D,MAAMC,UAAU,GAAI,UAASH,IAAK,IAAGJ,IAAK,uBAAsBC,QAAS,QAAOC,GAAI,WAAUC,MAAO,EAAC;EACtGE,kBAAM,CAACC,KAAK,CAAE,oBAAmBC,UAAW,EAAC,CAAC;EAC9C,IAAI;IACF,MAAM;MAAChB;IAAI,CAAC,GAAG,MAAM,IAAAiB,iBAAK,EAACD,UAAU,CAAC;IACtC,OAAOhB,IAAI;EACb,CAAC,CAAC,OAAOI,CAAC,EAAE;IACVU,kBAAM,CAACC,KAAK,CAAE,oCAAmCC,UAAW,GAAE,CAAC;IAC/D,OAAOE,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACO,eAAeC,iBAAiB,CACrCV,IAAY,EACZW,aAAiC,EACJ;EAC7B;EACA,MAAMC,aAAa,GAAG7B,eAAe,CAAC,WAAW,CAAC;EAClD,MAAM8B,eAAe,GAAG,MAAMd,sBAAsB,CAACC,IAAI,EAAEW,aAAa,CAAC;EAEzE,IAAIE,eAAe,EAAE;IACnBR,kBAAM,CAACC,KAAK,CAAC,8CAA8C,CAAC;IAC5DjB,aAAa,CAACuB,aAAa,EAAEC,eAAe,CAAC;IAC7CR,kBAAM,CAACC,KAAK,CACT,yDAAwDM,aAAc,EAAC,CACzE;IACD,OAAOA,aAAa;EACtB,CAAC,MAAM;IACLP,kBAAM,CAACS,KAAK,CAAC,sDAAsD,CAAC;IACpE,OAAOL,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACO,eAAeM,aAAa,CACjCC,GAAW,EACXhB,IAAY,EACZW,aAAiC,EACJ;EAC7B,MAAMM,qBAAqB,GAAGhC,eAAI,CAACC,IAAI,CACrC8B,GAAG,CAACE,IAAI,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,eAAe,EACf,YAAY,EACZ,OAAO,EACP,OAAO,EACP,mCAAmC,CACpC;EAED,MAAMC,kBAAkB,GAAGlC,eAAI,CAACC,IAAI,CAClC8B,GAAG,CAACE,IAAI,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,WAAW,EACX,YAAY,EACZ,OAAO,EACP,OAAO,EACP,0BAA0B,CAC3B;EAED,IAAIrB,aAAE,CAACuB,UAAU,CAACD,kBAAkB,CAAC,EAAE;IACrCd,kBAAM,CAACC,KAAK,CAAE,+BAA8BI,iBAAkB,EAAC,CAAC;IAChE,OAAOS,kBAAkB;EAC3B,CAAC,MAAM,IAAItB,aAAE,CAACuB,UAAU,CAACH,qBAAqB,CAAC,EAAE;IAC/CZ,kBAAM,CAACC,KAAK,CAAE,+BAA8BW,qBAAsB,EAAC,CAAC;IACpE,OAAOA,qBAAqB;EAC9B,CAAC,MAAM;IACL,OAAOP,iBAAiB,CAACV,IAAI,EAAEW,aAAa,CAAC;EAC/C;AACF"}