{"version": 3, "names": ["useOnAction", "router", "getState", "setState", "key", "actionListeners", "beforeRemoveListeners", "routerConfigOptions", "emitter", "onAction", "onActionParent", "onRouteFocus", "onRouteFocusParent", "addListener", "addListenerParent", "onDispatchAction", "React", "useContext", "NavigationBuilderContext", "routerConfigOptionsRef", "useRef", "useEffect", "current", "useCallback", "action", "visitedNavigators", "Set", "state", "has", "add", "target", "result", "getStateForAction", "isPrevented", "shouldPreventRemove", "routes", "undefined", "shouldFocus", "shouldActionChangeFocus", "i", "length", "listener", "useOnPreventRemove"], "sourceRoot": "../../src", "sources": ["useOnAction.tsx"], "mappings": ";;;;;;AAOA;AAEA;AAMA;AAA+E;AAAA;AAAA;AAa/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,WAAW,OASvB;EAAA,IATwB;IAClCC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,GAAG;IACHC,eAAe;IACfC,qBAAqB;IACrBC,mBAAmB;IACnBC;EACO,CAAC;EACR,MAAM;IACJC,QAAQ,EAAEC,cAAc;IACxBC,YAAY,EAAEC,kBAAkB;IAChCC,WAAW,EAAEC,iBAAiB;IAC9BC;EACF,CAAC,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAwB,CAAC;EAE9C,MAAMC,sBAAsB,GAC1BH,KAAK,CAACI,MAAM,CAAsBb,mBAAmB,CAAC;EAExDS,KAAK,CAACK,SAAS,CAAC,MAAM;IACpBF,sBAAsB,CAACG,OAAO,GAAGf,mBAAmB;EACtD,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAGO,KAAK,CAACO,WAAW,CAChC,UACEC,MAAwB,EAErB;IAAA,IADHC,iBAA8B,uEAAG,IAAIC,GAAG,EAAU;IAElD,MAAMC,KAAK,GAAGzB,QAAQ,EAAE;;IAExB;IACA;IACA,IAAIuB,iBAAiB,CAACG,GAAG,CAACD,KAAK,CAACvB,GAAG,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IAEAqB,iBAAiB,CAACI,GAAG,CAACF,KAAK,CAACvB,GAAG,CAAC;IAEhC,IAAI,OAAOoB,MAAM,CAACM,MAAM,KAAK,QAAQ,IAAIN,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACvB,GAAG,EAAE;MACpE,IAAI2B,MAAM,GAAG9B,MAAM,CAAC+B,iBAAiB,CACnCL,KAAK,EACLH,MAAM,EACNL,sBAAsB,CAACG,OAAO,CAC/B;;MAED;MACA;MACAS,MAAM,GACJA,MAAM,KAAK,IAAI,IAAIP,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACvB,GAAG,GAAGuB,KAAK,GAAGI,MAAM;MAEjE,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBhB,gBAAgB,CAACS,MAAM,EAAEG,KAAK,KAAKI,MAAM,CAAC;QAE1C,IAAIJ,KAAK,KAAKI,MAAM,EAAE;UACpB,MAAME,WAAW,GAAG,IAAAC,uCAAmB,EACrC1B,OAAO,EACPF,qBAAqB,EACrBqB,KAAK,CAACQ,MAAM,EACZJ,MAAM,CAACI,MAAM,EACbX,MAAM,CACP;UAED,IAAIS,WAAW,EAAE;YACf,OAAO,IAAI;UACb;UAEA9B,QAAQ,CAAC4B,MAAM,CAAC;QAClB;QAEA,IAAInB,kBAAkB,KAAKwB,SAAS,EAAE;UACpC;UACA;UACA,MAAMC,WAAW,GAAGpC,MAAM,CAACqC,uBAAuB,CAACd,MAAM,CAAC;UAE1D,IAAIa,WAAW,IAAIjC,GAAG,KAAKgC,SAAS,EAAE;YACpCxB,kBAAkB,CAACR,GAAG,CAAC;UACzB;QACF;QAEA,OAAO,IAAI;MACb;IACF;IAEA,IAAIM,cAAc,KAAK0B,SAAS,EAAE;MAChC;MACA,IAAI1B,cAAc,CAACc,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QAC7C,OAAO,IAAI;MACb;IACF;;IAEA;IACA,KAAK,IAAIc,CAAC,GAAGlC,eAAe,CAACmC,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,MAAME,QAAQ,GAAGpC,eAAe,CAACkC,CAAC,CAAC;MAEnC,IAAIE,QAAQ,CAACjB,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QACvC,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd,CAAC,EACD,CACEpB,eAAe,EACfC,qBAAqB,EACrBE,OAAO,EACPN,QAAQ,EACRE,GAAG,EACHM,cAAc,EACdK,gBAAgB,EAChBH,kBAAkB,EAClBX,MAAM,EACNE,QAAQ,CACT,CACF;EAED,IAAAuC,2BAAkB,EAAC;IACjBxC,QAAQ;IACRM,OAAO;IACPF;EACF,CAAC,CAAC;EAEFU,KAAK,CAACK,SAAS,CACb,MAAMP,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG,QAAQ,EAAEL,QAAQ,CAAC,EAC7C,CAACK,iBAAiB,EAAEL,QAAQ,CAAC,CAC9B;EAED,OAAOA,QAAQ;AACjB"}