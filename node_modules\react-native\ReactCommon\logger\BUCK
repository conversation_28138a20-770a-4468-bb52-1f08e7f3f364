load("@fbsource//tools/build_defs:glob_defs.bzl", "subdir_glob")
load("@fbsource//tools/build_defs:platform_defs.bzl", "ANDROID", "APPLE", "CXX", "FBCODE", "WINDOWS")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_xplat_cxx_library")

EXPORTED_HEADERS = [
    "react_native_log.h",
]

rn_xplat_cxx_library(
    name = "logger",
    srcs = glob(
        ["*.cpp"],
    ),
    headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        exclude = EXPORTED_HEADERS,
        prefix = "logger",
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", header)
            for header in EXPORTED_HEADERS
        ],
        prefix = "logger",
    ),
    compiler_flags_pedantic = True,
    fbandroid_preferred_linkage = "shared",
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX, FBCODE, WINDOWS),
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//third-party/glog:glog",
    ],
)
