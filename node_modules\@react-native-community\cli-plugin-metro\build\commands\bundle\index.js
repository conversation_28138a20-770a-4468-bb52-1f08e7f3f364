"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "buildBundleWithConfig", {
  enumerable: true,
  get: function () {
    return _buildBundle.buildBundleWithConfig;
  }
});
Object.defineProperty(exports, "bundleCommand", {
  enumerable: true,
  get: function () {
    return _bundle.default;
  }
});
Object.defineProperty(exports, "ramBundleCommand", {
  enumerable: true,
  get: function () {
    return _ramBundle.default;
  }
});
var _bundle = _interopRequireDefault(require("./bundle"));
var _buildBundle = require("./buildBundle");
var _ramBundle = _interopRequireDefault(require("./ramBundle"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

//# sourceMappingURL=index.ts.map