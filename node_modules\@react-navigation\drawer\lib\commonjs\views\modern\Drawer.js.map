{"version": 3, "names": ["SWIPE_DISTANCE_MINIMUM", "DEFAULT_DRAWER_WIDTH", "minmax", "value", "start", "end", "Math", "min", "max", "Drawer", "dimensions", "drawerPosition", "drawerStyle", "drawerType", "gestureHandlerProps", "hideStatusBarOnOpen", "keyboardDismissMode", "onClose", "onOpen", "open", "overlayStyle", "renderDrawerContent", "renderSceneContent", "statusBarAnimation", "swipeDistanceThreshold", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "swipeEnabled", "swipeVelocityThreshold", "overlayAccessibilityLabel", "getDrawer<PERSON>idth", "width", "StyleSheet", "flatten", "endsWith", "percentage", "Number", "replace", "isFinite", "drawerWidth", "isOpen", "isRight", "getDrawerTranslationX", "React", "useCallback", "hideStatusBar", "hide", "StatusBar", "setHidden", "useEffect", "interactionHandleRef", "useRef", "startInteraction", "current", "InteractionManager", "createInteractionHandle", "endInteraction", "clearInteractionHandle", "hideKeyboard", "Keyboard", "dismiss", "onGestureStart", "onGestureFinish", "hitSlop", "right", "undefined", "left", "touchStartX", "useSharedValue", "touchX", "translationX", "gestureState", "GestureState", "UNDETERMINED", "toggle<PERSON>rawer", "isUserInitiated", "velocity", "translateX", "with<PERSON><PERSON><PERSON>", "stiffness", "damping", "mass", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "reduceMotion", "runOnJS", "onGestureEvent", "useAnimatedGestureHandler", "onStart", "event", "ctx", "hasCalledOnStart", "startX", "state", "x", "onActive", "onEnd", "nextOpen", "abs", "velocityX", "onFinish", "useDerivedValue", "touchDistance", "ACTIVE", "isRTL", "I18nManager", "getConstants", "drawerAnimatedStyle", "useAnimatedStyle", "distanceFromEdge", "transform", "contentAnimatedStyle", "progress", "interpolate", "styles", "main", "flexDirection", "content", "Platform", "OS", "container", "position", "zIndex", "create", "top", "bottom", "max<PERSON><PERSON><PERSON>", "flex", "select", "web", "default", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/modern/Drawer.tsx"], "mappings": ";;;;;;AAAA;AACA;AASA;AAWA;AACA;AAKA;AAAgC;AAAA;AAAA;AAAA;AAEhC,MAAMA,sBAAsB,GAAG,CAAC;AAChC,MAAMC,oBAAoB,GAAG,KAAK;AAQlC,MAAMC,MAAM,GAAG,CAACC,KAAa,EAAEC,KAAa,EAAEC,GAAW,KAAK;EAC5D,SAAS;;EAET,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,EAAEC,KAAK,CAAC,EAAEC,GAAG,CAAC;AAC9C,CAAC;AAEc,SAASI,MAAM,OAoBd;EAAA,IApBe;IAC7BC,UAAU;IACVC,cAAc;IACdC,WAAW;IACXC,UAAU;IACVC,mBAAmB;IACnBC,mBAAmB;IACnBC,mBAAmB;IACnBC,OAAO;IACPC,MAAM;IACNC,IAAI;IACJC,YAAY;IACZC,mBAAmB;IACnBC,kBAAkB;IAClBC,kBAAkB;IAClBC,sBAAsB;IACtBC,cAAc;IACdC,YAAY;IACZC,sBAAsB;IACtBC;EACW,CAAC;EACZ,MAAMC,cAAc,GAAG,MAAc;IACnC,MAAM;MAAEC,KAAK,GAAG7B;IAAqB,CAAC,GACpC8B,uBAAU,CAACC,OAAO,CAACpB,WAAW,CAAC,IAAI,CAAC,CAAC;IAEvC,IAAI,OAAOkB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD;MACA,MAAMC,UAAU,GAAGC,MAAM,CAACL,KAAK,CAACM,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;MAElD,IAAID,MAAM,CAACE,QAAQ,CAACH,UAAU,CAAC,EAAE;QAC/B,OAAOxB,UAAU,CAACoB,KAAK,IAAII,UAAU,GAAG,GAAG,CAAC;MAC9C;IACF;IAEA,OAAO,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAAC;EAC9C,CAAC;EAED,MAAMQ,WAAW,GAAGT,cAAc,EAAE;EAEpC,MAAMU,MAAM,GAAG1B,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGM,IAAI;EACvD,MAAMqB,OAAO,GAAG7B,cAAc,KAAK,OAAO;EAE1C,MAAM8B,qBAAqB,GAAGC,KAAK,CAACC,WAAW,CAC5CxB,IAAa,IAAK;IACjB,SAAS;;IAET,IAAIR,cAAc,KAAK,MAAM,EAAE;MAC7B,OAAOQ,IAAI,GAAG,CAAC,GAAG,CAACmB,WAAW;IAChC;IAEA,OAAOnB,IAAI,GAAG,CAAC,GAAGmB,WAAW;EAC/B,CAAC,EACD,CAAC3B,cAAc,EAAE2B,WAAW,CAAC,CAC9B;EAED,MAAMM,aAAa,GAAGF,KAAK,CAACC,WAAW,CACpCE,IAAa,IAAK;IACjB,IAAI9B,mBAAmB,EAAE;MACvB+B,sBAAS,CAACC,SAAS,CAACF,IAAI,EAAEtB,kBAAkB,CAAC;IAC/C;EACF,CAAC,EACD,CAACR,mBAAmB,EAAEQ,kBAAkB,CAAC,CAC1C;EAEDmB,KAAK,CAACM,SAAS,CAAC,MAAM;IACpBJ,aAAa,CAACL,MAAM,CAAC;IAErB,OAAO,MAAMK,aAAa,CAAC,KAAK,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAExB,mBAAmB,EAAEQ,kBAAkB,EAAEqB,aAAa,CAAC,CAAC;EAEpE,MAAMK,oBAAoB,GAAGP,KAAK,CAACQ,MAAM,CAAgB,IAAI,CAAC;EAE9D,MAAMC,gBAAgB,GAAG,MAAM;IAC7BF,oBAAoB,CAACG,OAAO,GAAGC,+BAAkB,CAACC,uBAAuB,EAAE;EAC7E,CAAC;EAED,MAAMC,cAAc,GAAG,MAAM;IAC3B,IAAIN,oBAAoB,CAACG,OAAO,IAAI,IAAI,EAAE;MACxCC,+BAAkB,CAACG,sBAAsB,CAACP,oBAAoB,CAACG,OAAO,CAAC;MACvEH,oBAAoB,CAACG,OAAO,GAAG,IAAI;IACrC;EACF,CAAC;EAED,MAAMK,YAAY,GAAG,MAAM;IACzB,IAAIzC,mBAAmB,KAAK,SAAS,EAAE;MACrC0C,qBAAQ,CAACC,OAAO,EAAE;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAG,MAAM;IAC3BT,gBAAgB,EAAE;IAClBM,YAAY,EAAE;IACdb,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMiB,eAAe,GAAG,MAAM;IAC5BN,cAAc,EAAE;EAClB,CAAC;;EAED;EACA;EACA,MAAMO,OAAO,GAAGtB,OAAO;EACnB;EACA;EACA;IAAEuB,KAAK,EAAE,CAAC;IAAEjC,KAAK,EAAES,MAAM,GAAGyB,SAAS,GAAGvC;EAAe,CAAC,GACxD;IAAEwC,IAAI,EAAE,CAAC;IAAEnC,KAAK,EAAES,MAAM,GAAGyB,SAAS,GAAGvC;EAAe,CAAC;EAE3D,MAAMyC,WAAW,GAAG,IAAAC,qCAAc,EAAC,CAAC,CAAC;EACrC,MAAMC,MAAM,GAAG,IAAAD,qCAAc,EAAC,CAAC,CAAC;EAChC,MAAME,YAAY,GAAG,IAAAF,qCAAc,EAAC1B,qBAAqB,CAACtB,IAAI,CAAC,CAAC;EAChE,MAAMmD,YAAY,GAAG,IAAAH,qCAAc,EAAeI,4BAAY,CAACC,YAAY,CAAC;EAE5E,MAAMC,YAAY,GAAG/B,KAAK,CAACC,WAAW,CACpC,SAAwD;IACtD,SAAS;;IAAC,IADX;MAAExB,IAAI;MAAEuD,eAAe;MAAEC;IAAwB,CAAC;IAGjD,MAAMC,UAAU,GAAGnC,qBAAqB,CAACtB,IAAI,CAAC;IAE9C+C,WAAW,CAAC/D,KAAK,GAAG,CAAC;IACrBiE,MAAM,CAACjE,KAAK,GAAG,CAAC;IAChBkE,YAAY,CAAClE,KAAK,GAAG,IAAA0E,iCAAU,EAACD,UAAU,EAAE;MAC1CD,QAAQ;MACRG,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,CAAC;MACPC,iBAAiB,EAAE,IAAI;MACvBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,IAAI;MACxB;MACAC,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,IAAI,CAACV,eAAe,EAAE;MACpB;IACF;IAEA,IAAIvD,IAAI,EAAE;MACR,IAAAkE,8BAAO,EAACnE,MAAM,CAAC,EAAE;IACnB,CAAC,MAAM;MACL,IAAAmE,8BAAO,EAACpE,OAAO,CAAC,EAAE;IACpB;EACF,CAAC,EACD,CAACwB,qBAAqB,EAAExB,OAAO,EAAEC,MAAM,EAAEgD,WAAW,EAAEE,MAAM,EAAEC,YAAY,CAAC,CAC5E;EAED3B,KAAK,CAACM,SAAS,CACb,MAAMyB,YAAY,CAAC;IAAEtD,IAAI;IAAEuD,eAAe,EAAE;EAAM,CAAC,CAAC,EACpD,CAACvD,IAAI,EAAEsD,YAAY,CAAC,CACrB;EAED,MAAMa,cAAc,GAAG,IAAAC,gDAAyB,EAG9C;IACAC,OAAO,EAAE,CAACC,KAAK,EAAEC,GAAG,KAAK;MACvBA,GAAG,CAACC,gBAAgB,GAAG,KAAK;MAC5BD,GAAG,CAACE,MAAM,GAAGvB,YAAY,CAAClE,KAAK;MAC/BmE,YAAY,CAACnE,KAAK,GAAGsF,KAAK,CAACI,KAAK;MAChC3B,WAAW,CAAC/D,KAAK,GAAGsF,KAAK,CAACK,CAAC;IAC7B,CAAC;IACDC,QAAQ,EAAE,CAACN,KAAK,EAAEC,GAAG,KAAK;MACxBtB,MAAM,CAACjE,KAAK,GAAGsF,KAAK,CAACK,CAAC;MACtBzB,YAAY,CAAClE,KAAK,GAAGuF,GAAG,CAACE,MAAM,GAAGH,KAAK,CAACpB,YAAY;MACpDC,YAAY,CAACnE,KAAK,GAAGsF,KAAK,CAACI,KAAK;;MAEhC;MACA;MACA;MACA,IAAI,CAACH,GAAG,CAACC,gBAAgB,EAAE;QACzBD,GAAG,CAACC,gBAAgB,GAAG,IAAI;QAC3B,IAAAN,8BAAO,EAACzB,cAAc,CAAC,EAAE;MAC3B;IACF,CAAC;IACDoC,KAAK,EAAGP,KAAK,IAAK;MAChBnB,YAAY,CAACnE,KAAK,GAAGsF,KAAK,CAACI,KAAK;MAEhC,MAAMI,QAAQ,GACX3F,IAAI,CAAC4F,GAAG,CAACT,KAAK,CAACpB,YAAY,CAAC,GAAGrE,sBAAsB,IACpDM,IAAI,CAAC4F,GAAG,CAACT,KAAK,CAACpB,YAAY,CAAC,GAAG1C,sBAAsB,IACvDrB,IAAI,CAAC4F,GAAG,CAACT,KAAK,CAACpB,YAAY,CAAC,GAAG7C,sBAAsB,GACjDb,cAAc,KAAK,MAAM;MACvB;MACA,CAAC8E,KAAK,CAACU,SAAS,KAAK,CAAC,GAAGV,KAAK,CAACpB,YAAY,GAAGoB,KAAK,CAACU,SAAS,IAAI,CAAC;MAClE;MACA,CAACV,KAAK,CAACU,SAAS,KAAK,CAAC,GAAGV,KAAK,CAACpB,YAAY,GAAGoB,KAAK,CAACU,SAAS,IAAI,CAAC,GACpEhF,IAAI;MAEVsD,YAAY,CAAC;QACXtD,IAAI,EAAE8E,QAAQ;QACdvB,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAEc,KAAK,CAACU;MAClB,CAAC,CAAC;IACJ,CAAC;IACDC,QAAQ,EAAE,MAAM;MACd,IAAAf,8BAAO,EAACxB,eAAe,CAAC,EAAE;IAC5B;EACF,CAAC,CAAC;EAEF,MAAMe,UAAU,GAAG,IAAAyB,sCAAe,EAAC,MAAM;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,aAAa,GACjBzF,UAAU,KAAK,OAAO,IAAIyD,YAAY,CAACnE,KAAK,KAAKoE,4BAAY,CAACgC,MAAM,GAChErG,MAAM,CACJS,cAAc,KAAK,MAAM,GACrBuD,WAAW,CAAC/D,KAAK,GAAGmC,WAAW,GAC/B5B,UAAU,CAACoB,KAAK,GAAGQ,WAAW,GAAG4B,WAAW,CAAC/D,KAAK,EACtD,CAAC,EACDO,UAAU,CAACoB,KAAK,CACjB,GACD,CAAC;IAEP,MAAM8C,UAAU,GACdjE,cAAc,KAAK,MAAM,GACrBT,MAAM,CAACmE,YAAY,CAAClE,KAAK,GAAGmG,aAAa,EAAE,CAAChE,WAAW,EAAE,CAAC,CAAC,GAC3DpC,MAAM,CAACmE,YAAY,CAAClE,KAAK,GAAGmG,aAAa,EAAE,CAAC,EAAEhE,WAAW,CAAC;IAEhE,OAAOsC,UAAU;EACnB,CAAC,CAAC;EAEF,MAAM4B,KAAK,GAAGC,wBAAW,CAACC,YAAY,EAAE,CAACF,KAAK;EAC9C,MAAMG,mBAAmB,GAAG,IAAAC,uCAAgB,EAAC,MAAM;IACjD,MAAMC,gBAAgB,GAAGnG,UAAU,CAACoB,KAAK,GAAGQ,WAAW;IAEvD,OAAO;MACLwE,SAAS,EACPjG,UAAU,KAAK,WAAW;MACtB;MACA;MACA,EAAE,GACF,CACE;QACE+D,UAAU;QACR;QACA,CAAC/D,UAAU,KAAK,MAAM,GAAG,CAAC,GAAG+D,UAAU,CAACzE,KAAK,KAC5CQ,cAAc,KAAK,MAAM,GACtB6F,KAAK,GACH,CAACK,gBAAgB,GACjB,CAAC,GACHL,KAAK,GACL,CAAC,GACDK,gBAAgB;MACxB,CAAC;IAEX,CAAC;EACH,CAAC,CAAC;EAEF,MAAME,oBAAoB,GAAG,IAAAH,uCAAgB,EAAC,MAAM;IAClD,OAAO;MACLE,SAAS,EACPjG,UAAU,KAAK,WAAW;MACtB;MACA;MACA,EAAE,GACF,CACE;QACE+D,UAAU;QACR;QACA/D,UAAU,KAAK,OAAO,GAClB,CAAC,GACD+D,UAAU,CAACzE,KAAK,GAChBmC,WAAW,IAAI3B,cAAc,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACzD,CAAC;IAEX,CAAC;EACH,CAAC,CAAC;EAEF,MAAMqG,QAAQ,GAAG,IAAAX,sCAAe,EAAC,MAAM;IACrC,OAAOxF,UAAU,KAAK,WAAW,GAC7B,CAAC,GACD,IAAAoG,kCAAW,EACTrC,UAAU,CAACzE,KAAK,EAChB,CAACsC,qBAAqB,CAAC,KAAK,CAAC,EAAEA,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAC3D,CAAC,CAAC,EAAE,CAAC,CAAC,CACP;EACP,CAAC,CAAC;EAEF,oBACE,oBAAC,8BAAqB,CAAC,QAAQ;IAAC,KAAK,EAAEuE;EAAS,gBAC9C,oBAAC,iCAAiB;IAChB,aAAa,EAAE,CAAC,CAAChH,sBAAsB,EAAEA,sBAAsB,CAAE;IACjE,WAAW,EAAE,CAAC,CAACA,sBAAsB,EAAEA,sBAAsB,CAAE;IAC/D,OAAO,EAAE8D,OAAQ;IACjB,OAAO,EAAEjD,UAAU,KAAK,WAAW,IAAIa,YAAa;IACpD,cAAc,EAAE4D;EAAe,GAC3BxE,mBAAmB,gBAGvB,oBAAC,8BAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACLoG,MAAM,CAACC,IAAI,EACX;MACEC,aAAa,EACXvG,UAAU,KAAK,WAAW,IAAI,CAAC2B,OAAO,GAAG,aAAa,GAAG;IAC7D,CAAC;EACD,gBAEF,oBAAC,8BAAQ,CAAC,IAAI;IAAC,KAAK,EAAE,CAAC0E,MAAM,CAACG,OAAO,EAAEN,oBAAoB;EAAE,gBAC3D,oBAAC,iBAAI;IACH,2BAA2B,EAAExE,MAAM,IAAI1B,UAAU,KAAK,WAAY;IAClE,yBAAyB,EACvB0B,MAAM,IAAI1B,UAAU,KAAK,WAAW,GAChC,qBAAqB,GACrB,MACL;IACD,KAAK,EAAEqG,MAAM,CAACG;EAAQ,GAErB/F,kBAAkB,EAAE,CAChB,EACNT,UAAU,KAAK,WAAW,gBACzB,oBAAC,gBAAO;IACN,QAAQ,EAAEmG,QAAS;IACnB,OAAO,EAAE,MACPvC,YAAY,CAAC;MAAEtD,IAAI,EAAE,KAAK;MAAEuD,eAAe,EAAE;IAAK,CAAC,CACpD;IACD,KAAK,EAAEtD,YAAa;IACpB,kBAAkB,EAAEQ;EAA0B,EAC9C,GACA,IAAI,CACM,eAChB,oBAAC,8BAAQ,CAAC,IAAI;IACZ,qBAAqB,EAAE0F,qBAAQ,CAACC,EAAE,KAAK,KAAM;IAC7C,KAAK,EAAE,CACLL,MAAM,CAACM,SAAS,EAChB;MACEC,QAAQ,EAAE5G,UAAU,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;MAC9D6G,MAAM,EAAE7G,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG;IACvC,CAAC,EACD8F,mBAAmB,EACnB/F,WAAW;EACX,GAEDS,mBAAmB,EAAE,CACR,CACF,CACE,CACW;AAErC;AAEA,MAAM6F,MAAM,GAAGnF,uBAAU,CAAC4F,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,MAAM;IAChBhG,KAAK,EAAE7B;EACT,CAAC;EACDoH,OAAO,EAAE;IACPU,IAAI,EAAE;EACR,CAAC;EACDZ,IAAI,EAAE;IACJY,IAAI,EAAE,CAAC;IACP,GAAGT,qBAAQ,CAACU,MAAM,CAAC;MACjB;MACA;MACAC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAS;IAChC,CAAC;EACH;AACF,CAAC,CAAC"}