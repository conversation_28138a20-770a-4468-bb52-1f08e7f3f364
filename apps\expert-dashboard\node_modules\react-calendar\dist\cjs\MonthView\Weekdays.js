"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var clsx_1 = __importDefault(require("clsx"));
var date_utils_1 = require("@wojtekmaj/date-utils");
var Flex_js_1 = __importDefault(require("../Flex.js"));
var dates_js_1 = require("../shared/dates.js");
var dateFormatter_js_1 = require("../shared/dateFormatter.js");
var utils_js_1 = require("../shared/utils.js");
var className = 'react-calendar__month-view__weekdays';
var weekdayClassName = "".concat(className, "__weekday");
function Weekdays(props) {
    var calendarTypeOrDeprecatedCalendarType = props.calendarType, _a = props.formatShortWeekday, formatShortWeekday = _a === void 0 ? dateFormatter_js_1.formatShortWeekday : _a, _b = props.formatWeekday, formatWeekday = _b === void 0 ? dateFormatter_js_1.formatWeekday : _b, locale = props.locale, onMouseLeave = props.onMouseLeave;
    var calendarType = (0, utils_js_1.mapCalendarType)(calendarTypeOrDeprecatedCalendarType);
    var anyDate = new Date();
    var beginOfMonth = (0, date_utils_1.getMonthStart)(anyDate);
    var year = (0, date_utils_1.getYear)(beginOfMonth);
    var monthIndex = (0, date_utils_1.getMonth)(beginOfMonth);
    var weekdays = [];
    for (var weekday = 1; weekday <= 7; weekday += 1) {
        var weekdayDate = new Date(year, monthIndex, weekday - (0, dates_js_1.getDayOfWeek)(beginOfMonth, calendarType));
        var abbr = formatWeekday(locale, weekdayDate);
        weekdays.push(react_1.default.createElement("div", { key: weekday, className: (0, clsx_1.default)(weekdayClassName, (0, dates_js_1.isCurrentDayOfWeek)(weekdayDate) && "".concat(weekdayClassName, "--current"), (0, dates_js_1.isWeekend)(weekdayDate, calendarType) && "".concat(weekdayClassName, "--weekend")) },
            react_1.default.createElement("abbr", { "aria-label": abbr, title: abbr }, formatShortWeekday(locale, weekdayDate).replace('.', ''))));
    }
    return (react_1.default.createElement(Flex_js_1.default, { className: className, count: 7, onFocus: onMouseLeave, onMouseOver: onMouseLeave }, weekdays));
}
exports.default = Weekdays;
