{"version": 3, "names": ["useTheme", "React", "Animated", "Platform", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tintColor", "style", "rest", "colors", "styles", "title", "color", "undefined", "text", "create", "select", "ios", "fontSize", "fontWeight", "android", "fontFamily", "default"], "sourceRoot": "../../../src", "sources": ["Header/HeaderTitle.tsx"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAOrB,eAAe,SAASC,WAAW,OAAuC;EAAA,IAAtC;IAAEC,SAAS;IAAEC,KAAK;IAAE,GAAGC;EAAY,CAAC;EACtE,MAAM;IAAEC;EAAO,CAAC,GAAGT,QAAQ,EAAE;EAE7B,oBACE,oBAAC,QAAQ,CAAC,IAAI;IACZ,iBAAiB,EAAC,QAAQ;IAC1B,cAAW,GAAG;IACd,aAAa,EAAE;EAAE,GACbQ,IAAI;IACR,KAAK,EAAE,CACLE,MAAM,CAACC,KAAK,EACZ;MAAEC,KAAK,EAAEN,SAAS,KAAKO,SAAS,GAAGJ,MAAM,CAACK,IAAI,GAAGR;IAAU,CAAC,EAC5DC,KAAK;EACL,GACF;AAEN;AAEA,MAAMG,MAAM,GAAGN,UAAU,CAACW,MAAM,CAAC;EAC/BJ,KAAK,EAAER,QAAQ,CAACa,MAAM,CAAC;IACrBC,GAAG,EAAE;MACHC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPF,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,mBAAmB;MAC/BF,UAAU,EAAE;IACd,CAAC;IACDG,OAAO,EAAE;MACPJ,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC"}