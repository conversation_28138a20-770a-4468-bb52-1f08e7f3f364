"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var date_utils_1 = require("@wojtekmaj/date-utils");
var Tile_js_1 = __importDefault(require("../Tile.js"));
var dates_js_1 = require("../shared/dates.js");
var dateFormatter_js_1 = require("../shared/dateFormatter.js");
var utils_js_1 = require("../shared/utils.js");
var className = 'react-calendar__month-view__days__day';
function Day(_a) {
    var calendarTypeOrDeprecatedCalendarType = _a.calendarType, _b = _a.classes, classes = _b === void 0 ? [] : _b, currentMonthIndex = _a.currentMonthIndex, _c = _a.formatDay, formatDay = _c === void 0 ? dateFormatter_js_1.formatDay : _c, _d = _a.formatLongDate, formatLongDate = _d === void 0 ? dateFormatter_js_1.formatLongDate : _d, otherProps = __rest(_a, ["calendarType", "classes", "currentMonthIndex", "formatDay", "formatLongDate"]);
    var calendarType = (0, utils_js_1.mapCalendarType)(calendarTypeOrDeprecatedCalendarType);
    var date = otherProps.date, locale = otherProps.locale;
    var classesProps = [];
    if (classes) {
        classesProps.push.apply(classesProps, classes);
    }
    if (className) {
        classesProps.push(className);
    }
    if ((0, dates_js_1.isWeekend)(date, calendarType)) {
        classesProps.push("".concat(className, "--weekend"));
    }
    if (date.getMonth() !== currentMonthIndex) {
        classesProps.push("".concat(className, "--neighboringMonth"));
    }
    return (react_1.default.createElement(Tile_js_1.default, __assign({}, otherProps, { classes: classesProps, formatAbbr: formatLongDate, maxDateTransform: date_utils_1.getDayEnd, minDateTransform: date_utils_1.getDayStart, view: "month" }), formatDay(locale, date)));
}
exports.default = Day;
