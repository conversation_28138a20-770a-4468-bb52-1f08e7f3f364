{"version": 3, "names": ["getAndroidAssetSuffix", "scale", "drawableFileTypes", "Set", "getAndroidResourceFolderName", "asset", "has", "type", "suffix", "Error", "JSON", "stringify", "androidFolder", "getResourceIdentifier", "folderPath", "get<PERSON><PERSON><PERSON><PERSON>", "name", "toLowerCase", "replace", "basePath", "httpServerLocation", "substr"], "sources": ["../../../src/commands/bundle/assetPathUtils.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport interface PackagerAsset {\n  httpServerLocation: string;\n  name: string;\n  type: string;\n}\n\n/**\n * FIXME: using number to represent discrete scale numbers is fragile in essence because of\n * floating point numbers imprecision.\n */\nfunction getAndroidAssetSuffix(scale: number): string {\n  switch (scale) {\n    case 0.75:\n      return 'ldpi';\n    case 1:\n      return 'mdpi';\n    case 1.5:\n      return 'hdpi';\n    case 2:\n      return 'xhdpi';\n    case 3:\n      return 'xxhdpi';\n    case 4:\n      return 'xxxhdpi';\n    default:\n      return '';\n  }\n}\n\n// See https://developer.android.com/guide/topics/resources/drawable-resource.html\nconst drawableFileTypes = new Set<string>([\n  'gif',\n  'jpeg',\n  'jpg',\n  'png',\n  'webp',\n  'xml',\n]);\n\nfunction getAndroidResourceFolderName(\n  asset: PackagerAsset,\n  scale: number,\n): string {\n  if (!drawableFileTypes.has(asset.type)) {\n    return 'raw';\n  }\n  const suffix = getAndroidAssetSuffix(scale);\n  if (!suffix) {\n    throw new Error(\n      `Don't know which android drawable suffix to use for asset: ${JSON.stringify(\n        asset,\n      )}`,\n    );\n  }\n  const androidFolder = `drawable-${suffix}`;\n  return androidFolder;\n}\n\nfunction getResourceIdentifier(asset: PackagerAsset): string {\n  const folderPath = getBasePath(asset);\n  return `${folderPath}/${asset.name}`\n    .toLowerCase()\n    .replace(/\\//g, '_') // Encode folder structure in file name\n    .replace(/([^a-z0-9_])/g, '') // Remove illegal chars\n    .replace(/^assets_/, ''); // Remove \"assets_\" prefix\n}\n\nfunction getBasePath(asset: PackagerAsset): string {\n  let basePath = asset.httpServerLocation;\n  if (basePath[0] === '/') {\n    basePath = basePath.substr(1);\n  }\n  return basePath;\n}\n\nexport default {\n  getAndroidAssetSuffix,\n  getAndroidResourceFolderName,\n  getResourceIdentifier,\n  getBasePath,\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,CAACC,KAAa,EAAU;EACpD,QAAQA,KAAK;IACX,KAAK,IAAI;MACP,OAAO,MAAM;IACf,KAAK,CAAC;MACJ,OAAO,MAAM;IACf,KAAK,GAAG;MACN,OAAO,MAAM;IACf,KAAK,CAAC;MACJ,OAAO,OAAO;IAChB,KAAK,CAAC;MACJ,OAAO,QAAQ;IACjB,KAAK,CAAC;MACJ,OAAO,SAAS;IAClB;MACE,OAAO,EAAE;EAAC;AAEhB;;AAEA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAS,CACxC,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,CACN,CAAC;AAEF,SAASC,4BAA4B,CACnCC,KAAoB,EACpBJ,KAAa,EACL;EACR,IAAI,CAACC,iBAAiB,CAACI,GAAG,CAACD,KAAK,CAACE,IAAI,CAAC,EAAE;IACtC,OAAO,KAAK;EACd;EACA,MAAMC,MAAM,GAAGR,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAI,CAACO,MAAM,EAAE;IACX,MAAM,IAAIC,KAAK,CACZ,8DAA6DC,IAAI,CAACC,SAAS,CAC1EN,KAAK,CACL,EAAC,CACJ;EACH;EACA,MAAMO,aAAa,GAAI,YAAWJ,MAAO,EAAC;EAC1C,OAAOI,aAAa;AACtB;AAEA,SAASC,qBAAqB,CAACR,KAAoB,EAAU;EAC3D,MAAMS,UAAU,GAAGC,WAAW,CAACV,KAAK,CAAC;EACrC,OAAQ,GAAES,UAAW,IAAGT,KAAK,CAACW,IAAK,EAAC,CACjCC,WAAW,EAAE,CACbC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;EAAA,CACpBA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;EAAA,CAC7BA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9B;;AAEA,SAASH,WAAW,CAACV,KAAoB,EAAU;EACjD,IAAIc,QAAQ,GAAGd,KAAK,CAACe,kBAAkB;EACvC,IAAID,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvBA,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC;EAC/B;EACA,OAAOF,QAAQ;AACjB;AAAC,eAEc;EACbnB,qBAAqB;EACrBI,4BAA4B;EAC5BS,qBAAqB;EACrBE;AACF,CAAC;AAAA"}