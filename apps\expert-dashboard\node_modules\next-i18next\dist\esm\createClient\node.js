import _defineProperty from "@babel/runtime/helpers/defineProperty";
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import i18n from 'i18next';
import i18nextFSBackend from 'i18next-fs-backend';
var globalInstance;
export default (function (config) {
  if (config.ns === undefined) config.ns = [];
  var instance;
  if (!globalInstance) {
    globalInstance = i18n.createInstance(config);
    instance = globalInstance;
  } else {
    instance = globalInstance.cloneInstance(_objectSpread(_objectSpread({}, config), {}, {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      initAsync: false,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      initImmediate: false // i18next < 24
    }));
  }

  var initPromise;
  if (!instance.isInitialized) {
    var _config$use, _config$use2;
    var hasCustomBackend = config === null || config === void 0 || (_config$use = config.use) === null || _config$use === void 0 ? void 0 : _config$use.some(function (b) {
      return b.type === 'backend';
    });
    if (!hasCustomBackend) {
      instance.use(i18nextFSBackend);
    }
    config === null || config === void 0 || (_config$use2 = config.use) === null || _config$use2 === void 0 || _config$use2.forEach(function (x) {
      return instance.use(x);
    });
    if (typeof config.onPreInitI18next === 'function') {
      config.onPreInitI18next(instance);
    }
    initPromise = instance.init(config);
  } else {
    initPromise = Promise.resolve(i18n.t);
  }
  return {
    i18n: instance,
    initPromise: initPromise
  };
});