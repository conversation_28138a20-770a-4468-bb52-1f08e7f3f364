"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const common_json_1 = __importDefault(require("./common.json"));
const auth_json_1 = __importDefault(require("./auth.json"));
const expert_json_1 = __importDefault(require("./expert.json"));
const client_json_1 = __importDefault(require("./client.json"));
exports.default = {
    common: common_json_1.default,
    auth: auth_json_1.default,
    expert: expert_json_1.default,
    client: client_json_1.default,
};
//# sourceMappingURL=index.js.map