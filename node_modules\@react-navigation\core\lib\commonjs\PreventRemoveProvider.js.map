{"version": 3, "names": ["transformPreventedRoutes", "preventedRoutesMap", "preventedRoutesToTransform", "values", "preventedRoutes", "reduce", "acc", "routeKey", "preventRemove", "PreventRemoveProvider", "children", "parentId", "React", "useState", "nanoid", "setPreventedRoutesMap", "Map", "navigation", "useContext", "NavigationHelpersContext", "route", "NavigationRouteContext", "preventRemoveContextValue", "PreventRemoveContext", "setParentPrevented", "setPreventRemove", "useLatestCallback", "id", "getState", "routes", "every", "key", "Error", "prevPrevented", "get", "nextPrevented", "set", "delete", "isPrevented", "some", "useEffect", "undefined", "value", "useMemo"], "sourceRoot": "../../src", "sources": ["PreventRemoveProvider.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AAA+E;AAAA;AAAA;AAc/E;AACA;AACA;AACA,MAAMA,wBAAwB,GAC5BC,kBAAsC,IAClB;EACpB,MAAMC,0BAA0B,GAAG,CAAC,GAAGD,kBAAkB,CAACE,MAAM,EAAE,CAAC;EAEnE,MAAMC,eAAe,GAAGF,0BAA0B,CAACG,MAAM,CACvD,CAACC,GAAG,WAAkC;IAAA;IAAA,IAAhC;MAAEC,QAAQ;MAAEC;IAAc,CAAC;IAC/BF,GAAG,CAACC,QAAQ,CAAC,GAAG;MACdC,aAAa,EAAE,kBAAAF,GAAG,CAACC,QAAQ,CAAC,kDAAb,cAAeC,aAAa,KAAIA;IACjD,CAAC;IACD,OAAOF,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,CACH;EAED,OAAOF,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACe,SAASK,qBAAqB,QAAsB;EAAA,IAArB;IAAEC;EAAgB,CAAC;EAC/D,MAAM,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,MAAM,IAAAC,iBAAM,GAAE,CAAC;EACjD,MAAM,CAACb,kBAAkB,EAAEc,qBAAqB,CAAC,GAC/CH,KAAK,CAACC,QAAQ,CAAqB,IAAIG,GAAG,EAAE,CAAC;EAE/C,MAAMC,UAAU,GAAGL,KAAK,CAACM,UAAU,CAACC,iCAAwB,CAAC;EAC7D,MAAMC,KAAK,GAAGR,KAAK,CAACM,UAAU,CAACG,+BAAsB,CAAC;EAEtD,MAAMC,yBAAyB,GAAGV,KAAK,CAACM,UAAU,CAACK,6BAAoB,CAAC;EACxE;EACA,MAAMC,kBAAkB,GAAGF,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEG,gBAAgB;EAEtE,MAAMA,gBAAgB,GAAG,IAAAC,0BAAiB,EACxC,CAACC,EAAU,EAAEpB,QAAgB,EAAEC,aAAsB,KAAW;IAC9D,IACEA,aAAa,KACZS,UAAU,IAAI,IAAI,IACjBA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CACNW,QAAQ,EAAE,CACXC,MAAM,CAACC,KAAK,CAAEV,KAAK,IAAKA,KAAK,CAACW,GAAG,KAAKxB,QAAQ,CAAC,CAAC,EACrD;MACA,MAAM,IAAIyB,KAAK,CACZ,sCAAqCzB,QAAS,+CAA8C,CAC9F;IACH;IAEAQ,qBAAqB,CAAEkB,aAAa,IAAK;MAAA;MACvC;MACA,IACE1B,QAAQ,4BAAK0B,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,uDAArB,mBAAuBpB,QAAQ,KAC5CC,aAAa,6BAAKyB,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,wDAArB,oBAAuBnB,aAAa,GACtD;QACA,OAAOyB,aAAa;MACtB;MAEA,MAAME,aAAa,GAAG,IAAInB,GAAG,CAACiB,aAAa,CAAC;MAE5C,IAAIzB,aAAa,EAAE;QACjB2B,aAAa,CAACC,GAAG,CAACT,EAAE,EAAE;UACpBpB,QAAQ;UACRC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL2B,aAAa,CAACE,MAAM,CAACV,EAAE,CAAC;MAC1B;MAEA,OAAOQ,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC,CACF;EAED,MAAMG,WAAW,GAAG,CAAC,GAAGrC,kBAAkB,CAACE,MAAM,EAAE,CAAC,CAACoC,IAAI,CACvD;IAAA,IAAC;MAAE/B;IAAc,CAAC;IAAA,OAAKA,aAAa;EAAA,EACrC;EAEDI,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAI,CAAApB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,GAAG,MAAKU,SAAS,IAAIjB,kBAAkB,KAAKiB,SAAS,EAAE;MAChE;MACA;MACAjB,kBAAkB,CAACb,QAAQ,EAAES,KAAK,CAACW,GAAG,EAAEO,WAAW,CAAC;MACpD,OAAO,MAAM;QACXd,kBAAkB,CAACb,QAAQ,EAAES,KAAK,CAACW,GAAG,EAAE,KAAK,CAAC;MAChD,CAAC;IACH;IAEA;EACF,CAAC,EAAE,CAACpB,QAAQ,EAAE2B,WAAW,EAAElB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,GAAG,EAAEP,kBAAkB,CAAC,CAAC;EAE3D,MAAMkB,KAAK,GAAG9B,KAAK,CAAC+B,OAAO,CACzB,OAAO;IACLlB,gBAAgB;IAChBrB,eAAe,EAAEJ,wBAAwB,CAACC,kBAAkB;EAC9D,CAAC,CAAC,EACF,CAACwB,gBAAgB,EAAExB,kBAAkB,CAAC,CACvC;EAED,oBACE,oBAAC,6BAAoB,CAAC,QAAQ;IAAC,KAAK,EAAEyC;EAAM,GACzChC,QAAQ,CACqB;AAEpC"}