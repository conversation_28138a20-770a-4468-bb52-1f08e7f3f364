{"version": 3, "names": ["label", "description", "getDiagnostics", "SDKs", "androidSdk", "version", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "versionRange", "versionRanges", "ANDROID_NDK", "runAutomaticFix", "loader", "logManualInstallation", "environmentInfo", "isNDKInstalled", "fail", "message", "chalk", "dim", "healthcheck", "url"], "sources": ["../../../src/tools/healthchecks/androidNDK.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport versionRanges from '../versionRanges';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {EnvironmentInfo, HealthCheckInterface} from '../../types';\n\nexport default {\n  label: 'Android NDK',\n  description: 'Required for building React Native from the source',\n  getDiagnostics: async ({SDKs}: EnvironmentInfo) => {\n    const androidSdk = SDKs['Android SDK'];\n    const version =\n      androidSdk === 'Not Found' ? androidSdk : androidSdk['Android NDK'];\n\n    return {\n      needsToBeFixed: doesSoftwareNeedToBeFixed({\n        version,\n        versionRange: versionRanges.ANDROID_NDK,\n      }),\n      version,\n      versionRange: versionRanges.ANDROID_NDK,\n    };\n  },\n  runAutomaticFix: async ({loader, logManualInstallation, environmentInfo}) => {\n    const androidSdk = environmentInfo.SDKs['Android SDK'];\n    const isNDKInstalled =\n      androidSdk !== 'Not Found' && androidSdk['Android NDK'] !== 'Not Found';\n\n    loader.fail();\n\n    if (isNDKInstalled) {\n      return logManualInstallation({\n        message: `Read more about how to update Android NDK at ${chalk.dim(\n          'https://developer.android.com/ndk/downloads',\n        )}`,\n      });\n    }\n\n    return logManualInstallation({\n      healthcheck: 'Android NDK',\n      url: 'https://developer.android.com/ndk/downloads',\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA+D;AAAA,eAGhD;EACbA,KAAK,EAAE,aAAa;EACpBC,WAAW,EAAE,oDAAoD;EACjEC,cAAc,EAAE,OAAO;IAACC;EAAqB,CAAC,KAAK;IACjD,MAAMC,UAAU,GAAGD,IAAI,CAAC,aAAa,CAAC;IACtC,MAAME,OAAO,GACXD,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGA,UAAU,CAAC,aAAa,CAAC;IAErE,OAAO;MACLE,cAAc,EAAE,IAAAC,4CAAyB,EAAC;QACxCF,OAAO;QACPG,YAAY,EAAEC,sBAAa,CAACC;MAC9B,CAAC,CAAC;MACFL,OAAO;MACPG,YAAY,EAAEC,sBAAa,CAACC;IAC9B,CAAC;EACH,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC,qBAAqB;IAAEC;EAAe,CAAC,KAAK;IAC3E,MAAMV,UAAU,GAAGU,eAAe,CAACX,IAAI,CAAC,aAAa,CAAC;IACtD,MAAMY,cAAc,GAClBX,UAAU,KAAK,WAAW,IAAIA,UAAU,CAAC,aAAa,CAAC,KAAK,WAAW;IAEzEQ,MAAM,CAACI,IAAI,EAAE;IAEb,IAAID,cAAc,EAAE;MAClB,OAAOF,qBAAqB,CAAC;QAC3BI,OAAO,EAAG,gDAA+CC,gBAAK,CAACC,GAAG,CAChE,6CAA6C,CAC7C;MACJ,CAAC,CAAC;IACJ;IAEA,OAAON,qBAAqB,CAAC;MAC3BO,WAAW,EAAE,aAAa;MAC1BC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}