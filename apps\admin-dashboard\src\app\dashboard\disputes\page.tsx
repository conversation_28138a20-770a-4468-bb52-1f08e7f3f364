'use client';

import { useState } from 'react';
import {
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  UserIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingState';
import { EmptyState } from '@/components/ui/EmptyState';

interface Dispute {
  id: string;
  bookingId: string;
  clientId: string;
  clientName: string;
  expertId: string;
  expertName: string;
  serviceTitle: string;
  reason: string;
  description: string;
  amount: number;
  status: 'open' | 'investigating' | 'resolved' | 'escalated' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  assignedTo?: string;
  resolution?: string;
  evidence: string[];
}

// Mock data for disputes
const mockDisputes: Dispute[] = [
  {
    id: '1',
    bookingId: 'BK-2024-001',
    clientId: 'client-1',
    clientName: 'أحمد محمد',
    expertId: 'expert-1',
    expertName: 'سارة أحمد',
    serviceTitle: 'تطوير موقع إلكتروني',
    reason: 'quality_issues',
    description: 'العمل المسلم لا يطابق المواصفات المتفق عليها',
    amount: 500,
    status: 'open',
    priority: 'high',
    createdAt: '2024-06-08',
    updatedAt: '2024-06-08',
    evidence: ['screenshot1.png', 'contract.pdf'],
  },
  {
    id: '2',
    bookingId: 'BK-2024-002',
    clientId: 'client-2',
    clientName: 'فاطمة علي',
    expertId: 'expert-2',
    expertName: 'محمد حسن',
    serviceTitle: 'تصميم شعار',
    reason: 'delivery_delay',
    description: 'تأخير في التسليم لأكثر من أسبوعين',
    amount: 150,
    status: 'investigating',
    priority: 'medium',
    createdAt: '2024-06-05',
    updatedAt: '2024-06-09',
    assignedTo: 'admin-1',
    evidence: ['timeline.pdf'],
  },
  {
    id: '3',
    bookingId: 'BK-2024-003',
    clientId: 'client-3',
    clientName: 'خالد يوسف',
    expertId: 'expert-3',
    expertName: 'ليلى محمود',
    serviceTitle: 'كتابة محتوى',
    reason: 'payment_dispute',
    description: 'خلاف حول المبلغ المستحق',
    amount: 300,
    status: 'resolved',
    priority: 'low',
    createdAt: '2024-06-01',
    updatedAt: '2024-06-07',
    assignedTo: 'admin-2',
    resolution: 'تم الاتفاق على تسوية بمبلغ 250 دولار',
    evidence: ['payment_proof.png'],
  },
  {
    id: '4',
    bookingId: 'BK-2024-004',
    clientId: 'client-4',
    clientName: 'نور الدين',
    expertId: 'expert-4',
    expertName: 'عمر سالم',
    serviceTitle: 'تطوير تطبيق موبايل',
    reason: 'communication_issues',
    description: 'عدم استجابة الخبير للرسائل',
    amount: 800,
    status: 'escalated',
    priority: 'urgent',
    createdAt: '2024-06-03',
    updatedAt: '2024-06-10',
    assignedTo: 'admin-1',
    evidence: ['chat_history.pdf', 'emails.pdf'],
  },
];

const statusLabels = {
  open: 'مفتوح',
  investigating: 'قيد التحقيق',
  resolved: 'محلول',
  escalated: 'مصعد',
  closed: 'مغلق',
};

const statusColors = {
  open: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  investigating: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  resolved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  escalated: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
};

const priorityLabels = {
  low: 'منخفضة',
  medium: 'متوسطة',
  high: 'عالية',
  urgent: 'عاجلة',
};

const priorityColors = {
  low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  urgent: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
};

const reasonLabels = {
  quality_issues: 'مشاكل في الجودة',
  delivery_delay: 'تأخير في التسليم',
  payment_dispute: 'نزاع مالي',
  communication_issues: 'مشاكل في التواصل',
  scope_change: 'تغيير في النطاق',
  other: 'أخرى',
};

type FormMode = 'view' | 'edit';

export default function DisputesPage() {
  const [disputes, setDisputes] = useState<Dispute[]>(mockDisputes);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'open' | 'investigating' | 'resolved' | 'escalated' | 'closed'>('all');
  const [selectedPriority, setSelectedPriority] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all');
  const [showDisputeModal, setShowDisputeModal] = useState(false);
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null);
  const [formMode, setFormMode] = useState<FormMode>('view');

  // Filter disputes
  const filteredDisputes = disputes.filter(dispute => {
    const matchesStatus = selectedStatus === 'all' || dispute.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || dispute.priority === selectedPriority;
    return matchesStatus && matchesPriority;
  });

  // CRUD Functions
  const handleViewDispute = (dispute: Dispute) => {
    setSelectedDispute(dispute);
    setFormMode('view');
    setShowDisputeModal(true);
  };

  const handleUpdateStatus = async (disputeId: string, newStatus: Dispute['status']) => {
    setDisputes(prev => prev.map(dispute => 
      dispute.id === disputeId 
        ? { ...dispute, status: newStatus, updatedAt: new Date().toISOString().split('T')[0] }
        : dispute
    ));
  };

  const handleAssignDispute = async (disputeId: string, adminId: string) => {
    setDisputes(prev => prev.map(dispute => 
      dispute.id === disputeId 
        ? { ...dispute, assignedTo: adminId, updatedAt: new Date().toISOString().split('T')[0] }
        : dispute
    ));
  };

  if (isLoading) {
    return <LoadingState message="جاري تحميل النزاعات..." />;
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          إدارة النزاعات
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          إدارة ومتابعة النزاعات بين العملاء والخبراء في منصة فريلا سوريا
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي النزاعات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {disputes.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    مفتوحة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {disputes.filter(d => d.status === 'open').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    قيد التحقيق
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {disputes.filter(d => d.status === 'investigating').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    محلولة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {disputes.filter(d => d.status === 'resolved').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    القيمة الإجمالية
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${disputes.reduce((sum, d) => sum + d.amount, 0).toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Status filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              حالة النزاع
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الحالات</option>
              <option value="open">مفتوح</option>
              <option value="investigating">قيد التحقيق</option>
              <option value="resolved">محلول</option>
              <option value="escalated">مصعد</option>
              <option value="closed">مغلق</option>
            </select>
          </div>

          {/* Priority filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الأولوية
            </label>
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value as any)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الأولويات</option>
              <option value="low">منخفضة</option>
              <option value="medium">متوسطة</option>
              <option value="high">عالية</option>
              <option value="urgent">عاجلة</option>
            </select>
          </div>
        </div>
      </div>

      {/* Disputes table */}
      {filteredDisputes.length === 0 ? (
        <EmptyState
          icon={<ExclamationTriangleIcon className="h-12 w-12" />}
          title="لا توجد نزاعات"
          description="لم يتم العثور على نزاعات تطابق معايير البحث المحددة"
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    النزاع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الأطراف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الأولوية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredDisputes.map((dispute) => (
                  <tr key={dispute.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {dispute.bookingId}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {reasonLabels[dispute.reason as keyof typeof reasonLabels]}
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                          {dispute.serviceTitle}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">
                        <div className="text-gray-900 dark:text-white">
                          <UserIcon className="h-4 w-4 inline ml-1" />
                          {dispute.clientName}
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          vs {dispute.expertName}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        ${dispute.amount.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={dispute.status}
                        onChange={(e) => handleUpdateStatus(dispute.id, e.target.value as Dispute['status'])}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border-0 ${statusColors[dispute.status]}`}
                      >
                        <option value="open">مفتوح</option>
                        <option value="investigating">قيد التحقيق</option>
                        <option value="resolved">محلول</option>
                        <option value="escalated">مصعد</option>
                        <option value="closed">مغلق</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityColors[dispute.priority]}`}>
                        {priorityLabels[dispute.priority]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 ml-1" />
                        {new Date(dispute.createdAt).toLocaleDateString('ar-SY')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        type="button"
                        onClick={() => handleViewDispute(dispute)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        title="عرض التفاصيل"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
