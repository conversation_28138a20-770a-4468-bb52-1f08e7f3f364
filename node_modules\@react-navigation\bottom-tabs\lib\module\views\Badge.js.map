{"version": 3, "names": ["useTheme", "color", "React", "Animated", "StyleSheet", "Badge", "children", "style", "visible", "size", "rest", "opacity", "useState", "Value", "rendered", "setRendered", "theme", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "finished", "stopAnimation", "backgroundColor", "colors", "notification", "restStyle", "flatten", "textColor", "isLight", "borderRadius", "fontSize", "Math", "floor", "transform", "scale", "interpolate", "inputRange", "outputRange", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "create", "alignSelf", "textAlign", "paddingHorizontal", "overflow"], "sourceRoot": "../../../src", "sources": ["views/Badge.tsx"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAaC,UAAU,QAAmB,cAAc;AAqBzE,eAAe,SAASC,KAAK,OAMnB;EAAA,IANoB;IAC5BC,QAAQ;IACRC,KAAK;IACLC,OAAO,GAAG,IAAI;IACdC,IAAI,GAAG,EAAE;IACT,GAAGC;EACE,CAAC;EACN,MAAM,CAACC,OAAO,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,MAAM,IAAIT,QAAQ,CAACU,KAAK,CAACL,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGb,KAAK,CAACU,QAAQ,CAACJ,OAAO,CAAC;EAEvD,MAAMQ,KAAK,GAAGhB,QAAQ,EAAE;EAExBE,KAAK,CAACe,SAAS,CAAC,MAAM;IACpB,IAAI,CAACH,QAAQ,EAAE;MACb;IACF;IAEAX,QAAQ,CAACe,MAAM,CAACP,OAAO,EAAE;MACvBQ,OAAO,EAAEX,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBY,QAAQ,EAAE,GAAG;MACbC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,SAAkB;MAAA,IAAjB;QAAEC;MAAS,CAAC;MACpB,IAAIA,QAAQ,IAAI,CAACf,OAAO,EAAE;QACxBO,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMJ,OAAO,CAACa,aAAa,EAAE;EACtC,CAAC,EAAE,CAACb,OAAO,EAAEG,QAAQ,EAAEN,OAAO,CAAC,CAAC;EAEhC,IAAI,CAACM,QAAQ,EAAE;IACb,IAAIN,OAAO,EAAE;MACXO,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAM;IAAEU,eAAe,GAAGT,KAAK,CAACU,MAAM,CAACC,YAAY;IAAE,GAAGC;EAAU,CAAC,GACjExB,UAAU,CAACyB,OAAO,CAACtB,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMuB,SAAS,GAAG7B,KAAK,CAACwB,eAAe,CAAC,CAACM,OAAO,EAAE,GAAG,OAAO,GAAG,OAAO;EAEtE,MAAMC,YAAY,GAAGvB,IAAI,GAAG,CAAC;EAC7B,MAAMwB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAE1B,IAAI,GAAG,CAAC,GAAI,CAAC,CAAC;EAE3C,oBACE,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAE,CAAE;IACjB,KAAK,EAAE,CACL;MACE2B,SAAS,EAAE,CACT;QACEC,KAAK,EAAE1B,OAAO,CAAC2B,WAAW,CAAC;UACzBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;MACH,CAAC,CACF;MACDvC,KAAK,EAAE6B,SAAS;MAChBW,UAAU,EAAEhC,IAAI,GAAG,CAAC;MACpBiC,MAAM,EAAEjC,IAAI;MACZkC,QAAQ,EAAElC,IAAI;MACdE,OAAO;MACPc,eAAe;MACfQ,QAAQ;MACRD;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBjB,SAAS;EACT,GACElB,IAAI,GAEPJ,QAAQ,CACK;AAEpB;AAEA,MAAMsC,MAAM,GAAGxC,UAAU,CAAC0C,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}