#!/usr/bin/env node

/**
 * React Native Setup Script for Freela Syria Mobile App
 * This script helps set up the React Native development environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Freela Syria React Native App...\n');

// Check if we're in the right directory
const currentDir = process.cwd();
const expectedPath = path.join('apps', 'mobile');

if (!currentDir.includes(expectedPath)) {
  console.error('❌ Please run this script from the apps/mobile directory');
  process.exit(1);
}

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Make sure you\'re in the mobile app directory.');
  process.exit(1);
}

console.log('✅ Found package.json');

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install --legacy-peer-deps', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies already installed');
}

// Check if metro.config.js exists
if (!fs.existsSync('metro.config.js')) {
  console.error('❌ metro.config.js not found');
  process.exit(1);
}

console.log('✅ Metro config found');

// Check if Android/iOS folders exist (for full React Native setup)
const hasAndroid = fs.existsSync('android');
const hasIOS = fs.existsSync('ios');

if (!hasAndroid && !hasIOS) {
  console.log('⚠️  Native platform folders (android/ios) not found');
  console.log('   This is expected for a development setup using Expo or web-only testing');
  console.log('   For full native development, you would need to run:');
  console.log('   npx react-native init YourApp --template react-native-template-typescript');
}

// Create a simple development server script
const devServerScript = `
const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Freela Syria Mobile Development Server...');
console.log('📱 This will start the Metro bundler for React Native development');
console.log('');

try {
  // Start Metro bundler
  execSync('npx react-native start --reset-cache', { 
    stdio: 'inherit',
    cwd: __dirname
  });
} catch (error) {
  console.error('❌ Failed to start development server:', error.message);
  console.log('');
  console.log('💡 Troubleshooting tips:');
  console.log('   1. Make sure you have React Native CLI installed: npm install -g @react-native-community/cli');
  console.log('   2. Check if all dependencies are installed: npm install --legacy-peer-deps');
  console.log('   3. Clear Metro cache: npx react-native start --reset-cache');
  console.log('   4. For Android development, make sure Android Studio and SDK are installed');
  process.exit(1);
}
`;

fs.writeFileSync('dev-server.js', devServerScript.trim());
console.log('✅ Created development server script (dev-server.js)');

// Create a testing script for web-based development
const webTestScript = `
const express = require('express');
const path = require('path');

const app = express();
const PORT = 3001;

// Serve static files
app.use(express.static(path.join(__dirname, 'web-build')));

// Basic API mock for testing
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Freela Syria Mobile API Mock' });
});

// Serve the app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'web-build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(\`🌐 Freela Syria Mobile (Web) running at http://localhost:\${PORT}\`);
  console.log('📱 This is a web version for testing the mobile app UI');
});
`;

fs.writeFileSync('web-test.js', webTestScript.trim());
console.log('✅ Created web testing script (web-test.js)');

// Create README for development
const devReadme = `# Freela Syria Mobile App - Development Setup

## 🚀 Quick Start

### Option 1: Metro Bundler (Recommended for React Native development)
\`\`\`bash
node dev-server.js
\`\`\`

### Option 2: Web Testing (For UI testing without native setup)
\`\`\`bash
node web-test.js
\`\`\`

## 📱 Development Commands

- \`npm start\` - Start Metro bundler
- \`npm run android\` - Run on Android (requires Android setup)
- \`npm run ios\` - Run on iOS (requires iOS setup)
- \`npm test\` - Run tests
- \`npm run lint\` - Run linting

## 🛠️ Prerequisites

### For Full Native Development:
1. **Node.js** (v16 or higher)
2. **React Native CLI**: \`npm install -g @react-native-community/cli\`
3. **Android Studio** (for Android development)
4. **Xcode** (for iOS development, macOS only)

### For Web Testing Only:
1. **Node.js** (v16 or higher)
2. **Express** (installed via npm)

## 🏗️ Project Structure

\`\`\`
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── navigation/     # Navigation setup
├── store/          # State management (Zustand)
├── services/       # API services
├── contexts/       # React contexts
├── types/          # TypeScript types
└── utils/          # Utility functions
\`\`\`

## 🎨 Features Implemented

✅ **Authentication Flow**
- Login/Register screens with validation
- Role selection (Client/Expert)
- Forgot password functionality

✅ **Main App Screens**
- Home screen with featured services
- Search and filtering
- Bookings management
- Service details
- User profiles

✅ **UI Components**
- Button, Input, Card, Avatar
- Loading states and error handling
- Arabic RTL support
- Dark theme support

✅ **Navigation**
- Tab navigation for main screens
- Stack navigation for details
- Authentication flow handling

## 🌐 Arabic RTL Support

The app is built with Arabic-first design:
- RTL layout support
- Arabic fonts (Cairo family)
- Proper text alignment
- Cultural considerations for Syrian market

## 🔧 Troubleshooting

### Metro Bundler Issues:
\`\`\`bash
npx react-native start --reset-cache
\`\`\`

### Dependency Issues:
\`\`\`bash
npm install --legacy-peer-deps
\`\`\`

### Android Development:
1. Install Android Studio
2. Set up Android SDK
3. Create virtual device (AVD)
4. Run: \`npm run android\`

### iOS Development (macOS only):
1. Install Xcode
2. Install iOS Simulator
3. Run: \`npm run ios\`

## 📚 Next Steps

1. **Complete Native Setup**: Set up Android Studio and/or Xcode
2. **API Integration**: Connect to backend API endpoints
3. **Testing**: Add unit and integration tests
4. **Performance**: Optimize for production
5. **Deployment**: Build and deploy to app stores
`;

fs.writeFileSync('DEV-README.md', devReadme.trim());
console.log('✅ Created development README (DEV-README.md)');

console.log('\n🎉 Setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Read DEV-README.md for detailed instructions');
console.log('2. Run: node dev-server.js (for Metro bundler)');
console.log('3. Or run: node web-test.js (for web testing)');
console.log('\n💡 For full native development, you\'ll need Android Studio and/or Xcode');
