{"version": 3, "names": ["systraceProfileMiddleware", "req", "res", "logger", "info", "dumpName", "Date", "now", "fs", "writeFileSync", "rawBody", "response", "end"], "sources": ["../src/systraceProfileMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\nimport fs from 'fs';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function systraceProfileMiddleware(\n  req: http.IncomingMessage & {rawBody: string},\n  res: http.ServerResponse,\n) {\n  logger.info('Dumping profile information...');\n  const dumpName = `/tmp/dump_${Date.now()}.json`;\n  fs.writeFileSync(dumpName, req.rawBody);\n  const response =\n    `Your profile was saved at:\\n${dumpName}\\n\\n` +\n    'On Google Chrome navigate to chrome://tracing and then click on \"load\" ' +\n    'to load and visualise your profile.\\n\\n' +\n    'This message is also printed to your console by the packager so you can copy it :)';\n  logger.info(response);\n  res.end(response);\n}\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AARzD;AACA;AACA;AACA;AACA;AACA;;AAKe,SAASA,yBAAyB,CAC/CC,GAA6C,EAC7CC,GAAwB,EACxB;EACAC,kBAAM,CAACC,IAAI,CAAC,gCAAgC,CAAC;EAC7C,MAAMC,QAAQ,GAAI,aAAYC,IAAI,CAACC,GAAG,EAAG,OAAM;EAC/CC,aAAE,CAACC,aAAa,CAACJ,QAAQ,EAAEJ,GAAG,CAACS,OAAO,CAAC;EACvC,MAAMC,QAAQ,GACX,+BAA8BN,QAAS,MAAK,GAC7C,yEAAyE,GACzE,yCAAyC,GACzC,oFAAoF;EACtFF,kBAAM,CAACC,IAAI,CAACO,QAAQ,CAAC;EACrBT,GAAG,CAACU,GAAG,CAACD,QAAQ,CAAC;AACnB"}