import * as React from 'react';
import Animated from 'react-native-reanimated';
declare const Overlay: React.ForwardRefExoticComponent<{
    children?: React.ReactNode | Animated.Node<React.ReactNode>;
    removeClippedSubviews?: boolean | Animated.Node<boolean | undefined> | undefined;
    hitSlop?: import("react-native").Insets | Animated.Node<import("react-native").Insets | undefined> | undefined;
    id?: string | Animated.Node<string | undefined> | undefined;
    onLayout?: ((event: import("react-native").LayoutChangeEvent) => void) | Animated.Node<((event: import("react-native").LayoutChangeEvent) => void) | undefined> | undefined;
    pointerEvents?: "none" | "auto" | "box-none" | "box-only" | Animated.Node<"none" | "auto" | "box-none" | "box-only" | undefined> | undefined;
    testID?: string | Animated.Node<string | undefined> | undefined;
    nativeID?: string | Animated.Node<string | undefined> | undefined;
    collapsable?: boolean | Animated.Node<boolean | undefined> | undefined;
    needsOffscreenAlphaCompositing?: boolean | Animated.Node<boolean | undefined> | undefined;
    renderToHardwareTextureAndroid?: boolean | Animated.Node<boolean | undefined> | undefined;
    focusable?: boolean | Animated.Node<boolean | undefined> | undefined;
    shouldRasterizeIOS?: boolean | Animated.Node<boolean | undefined> | undefined;
    isTVSelectable?: boolean | Animated.Node<boolean | undefined> | undefined;
    hasTVPreferredFocus?: boolean | Animated.Node<boolean | undefined> | undefined;
    tvParallaxProperties?: import("react-native").TVParallaxProperties | Animated.Node<import("react-native").TVParallaxProperties | undefined> | undefined;
    tvParallaxShiftDistanceX?: number | Animated.Node<number | undefined> | undefined;
    tvParallaxShiftDistanceY?: number | Animated.Node<number | undefined> | undefined;
    tvParallaxTiltAngle?: number | Animated.Node<number | undefined> | undefined;
    tvParallaxMagnification?: number | Animated.Node<number | undefined> | undefined;
    onStartShouldSetResponder?: ((event: import("react-native").GestureResponderEvent) => boolean) | Animated.Node<((event: import("react-native").GestureResponderEvent) => boolean) | undefined> | undefined;
    onMoveShouldSetResponder?: ((event: import("react-native").GestureResponderEvent) => boolean) | Animated.Node<((event: import("react-native").GestureResponderEvent) => boolean) | undefined> | undefined;
    onResponderEnd?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderGrant?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderReject?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderMove?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderRelease?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderStart?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onResponderTerminationRequest?: ((event: import("react-native").GestureResponderEvent) => boolean) | Animated.Node<((event: import("react-native").GestureResponderEvent) => boolean) | undefined> | undefined;
    onResponderTerminate?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onStartShouldSetResponderCapture?: ((event: import("react-native").GestureResponderEvent) => boolean) | Animated.Node<((event: import("react-native").GestureResponderEvent) => boolean) | undefined> | undefined;
    onMoveShouldSetResponderCapture?: ((event: import("react-native").GestureResponderEvent) => boolean) | Animated.Node<((event: import("react-native").GestureResponderEvent) => boolean) | undefined> | undefined;
    onTouchStart?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onTouchMove?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onTouchEnd?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onTouchCancel?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onTouchEndCapture?: ((event: import("react-native").GestureResponderEvent) => void) | Animated.Node<((event: import("react-native").GestureResponderEvent) => void) | undefined> | undefined;
    onPointerEnter?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerEnterCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerLeave?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerLeaveCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerMove?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerMoveCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerCancel?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerCancelCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerDown?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerDownCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerUp?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    onPointerUpCapture?: ((event: import("react-native").PointerEvent) => void) | Animated.Node<((event: import("react-native").PointerEvent) => void) | undefined> | undefined;
    accessible?: boolean | Animated.Node<boolean | undefined> | undefined;
    accessibilityActions?: readonly Readonly<{
        name: string;
        label?: string | undefined;
    }>[] | Animated.Node<readonly Readonly<{
        name: string;
        label?: string | undefined;
    }>[] | undefined> | undefined;
    accessibilityLabel?: string | Animated.Node<string | undefined> | undefined;
    'aria-label'?: string | Animated.Node<string | undefined> | undefined;
    accessibilityRole?: import("react-native").AccessibilityRole | Animated.Node<import("react-native").AccessibilityRole | undefined> | undefined;
    accessibilityState?: import("react-native").AccessibilityState | Animated.Node<import("react-native").AccessibilityState | undefined> | undefined;
    'aria-busy'?: boolean | Animated.Node<boolean | undefined> | undefined;
    'aria-checked'?: boolean | "mixed" | Animated.Node<boolean | "mixed" | undefined> | undefined;
    'aria-disabled'?: boolean | Animated.Node<boolean | undefined> | undefined;
    'aria-expanded'?: boolean | Animated.Node<boolean | undefined> | undefined;
    'aria-selected'?: boolean | Animated.Node<boolean | undefined> | undefined;
    'aria-labelledby'?: string | Animated.Node<string | undefined> | undefined;
    accessibilityHint?: string | Animated.Node<string | undefined> | undefined;
    accessibilityValue?: import("react-native").AccessibilityValue | Animated.Node<import("react-native").AccessibilityValue | undefined> | undefined;
    'aria-valuemax'?: number | Animated.Node<number | undefined> | undefined;
    'aria-valuemin'?: number | Animated.Node<number | undefined> | undefined;
    'aria-valuenow'?: number | Animated.Node<number | undefined> | undefined;
    'aria-valuetext'?: string | Animated.Node<string | undefined> | undefined;
    onAccessibilityAction?: ((event: import("react-native").AccessibilityActionEvent) => void) | Animated.Node<((event: import("react-native").AccessibilityActionEvent) => void) | undefined> | undefined;
    importantForAccessibility?: "auto" | "yes" | "no" | "no-hide-descendants" | Animated.Node<"auto" | "yes" | "no" | "no-hide-descendants" | undefined> | undefined;
    'aria-hidden'?: boolean | Animated.Node<boolean | undefined> | undefined;
    'aria-live'?: "polite" | "assertive" | "off" | Animated.Node<"polite" | "assertive" | "off" | undefined> | undefined;
    'aria-modal'?: boolean | Animated.Node<boolean | undefined> | undefined;
    role?: import("react-native").Role | Animated.Node<import("react-native").Role | undefined> | undefined;
    accessibilityLiveRegion?: "none" | "polite" | "assertive" | Animated.Node<"none" | "polite" | "assertive" | undefined> | undefined;
    accessibilityLabelledBy?: string | string[] | Animated.Node<string | string[] | undefined> | undefined;
    accessibilityElementsHidden?: boolean | Animated.Node<boolean | undefined> | undefined;
    accessibilityViewIsModal?: boolean | Animated.Node<boolean | undefined> | undefined;
    onAccessibilityEscape?: (() => void) | Animated.Node<(() => void) | undefined> | undefined;
    onAccessibilityTap?: (() => void) | Animated.Node<(() => void) | undefined> | undefined;
    onMagicTap?: (() => void) | Animated.Node<(() => void) | undefined> | undefined;
    accessibilityIgnoresInvertColors?: boolean | Animated.Node<boolean | undefined> | undefined;
    accessibilityLanguage?: string | Animated.Node<string | undefined> | undefined;
} & {
    style?: import("react-native").StyleProp<Animated.AnimateStyle<import("react-native").StyleProp<import("react-native").ViewStyle>>>;
} & {
    animatedProps?: Partial<Animated.AnimateProps<import("react-native").ViewProps>> | undefined;
    layout?: import("react-native-reanimated").BaseAnimationBuilder | import("react-native-reanimated").LayoutAnimationFunction | typeof import("react-native-reanimated").BaseAnimationBuilder | undefined;
    entering?: import("react-native-reanimated").BaseAnimationBuilder | typeof import("react-native-reanimated").BaseAnimationBuilder | import("react-native-reanimated").EntryExitAnimationFunction | import("react-native-reanimated").Keyframe | undefined;
    exiting?: import("react-native-reanimated").BaseAnimationBuilder | typeof import("react-native-reanimated").BaseAnimationBuilder | import("react-native-reanimated").EntryExitAnimationFunction | import("react-native-reanimated").Keyframe | undefined;
} & {
    progress: Animated.SharedValue<number>;
    onPress: () => void;
    accessibilityLabel?: string | undefined;
} & React.RefAttributes<Animated.View>>;
export default Overlay;
//# sourceMappingURL=Overlay.d.ts.map