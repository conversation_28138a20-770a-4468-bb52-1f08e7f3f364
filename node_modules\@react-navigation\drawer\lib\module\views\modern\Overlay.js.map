{"version": 3, "names": ["React", "Platform", "Pressable", "StyleSheet", "Animated", "useAnimatedProps", "useAnimatedStyle", "PROGRESS_EPSILON", "Overlay", "forwardRef", "ref", "progress", "onPress", "style", "accessibilityLabel", "props", "animatedStyle", "opacity", "value", "zIndex", "animatedProps", "active", "pointerEvents", "accessibilityElementsHidden", "importantForAccessibility", "styles", "overlay", "overlayStyle", "pressable", "select", "web", "WebkitTapHighlightColor", "default", "create", "absoluteFillObject", "backgroundColor", "flex"], "sourceRoot": "../../../../src", "sources": ["views/modern/Overlay.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,cAAc;AAC9D,OAAOC,QAAQ,IACbC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAEhC,MAAMC,gBAAgB,GAAG,IAAI;AAQ7B,MAAMC,OAAO,gBAAGR,KAAK,CAACS,UAAU,CAAC,SAASD,OAAO,OAQ/CE,GAA6B,EAC7B;EAAA,IARA;IACEC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,kBAAkB,GAAG,cAAc;IACnC,GAAGC;EACE,CAAC;EAGR,MAAMC,aAAa,GAAGV,gBAAgB,CAAC,MAAM;IAC3C,OAAO;MACLW,OAAO,EAAEN,QAAQ,CAACO,KAAK;MACvB;MACA;MACAC,MAAM,EAAER,QAAQ,CAACO,KAAK,GAAGX,gBAAgB,GAAG,CAAC,GAAG,CAAC;IACnD,CAAC;EACH,CAAC,CAAC;EAEF,MAAMa,aAAa,GAAGf,gBAAgB,CAAC,MAAM;IAC3C,MAAMgB,MAAM,GAAGV,QAAQ,CAACO,KAAK,GAAGX,gBAAgB;IAEhD,OAAO;MACLe,aAAa,EAAED,MAAM,GAAG,MAAM,GAAG,MAAM;MACvCE,2BAA2B,EAAE,CAACF,MAAM;MACpCG,yBAAyB,EAAEH,MAAM,GAAG,MAAM,GAAG;IAC/C,CAAC;EACH,CAAC,CAAC;EAEF,oBACE,oBAAC,QAAQ,CAAC,IAAI,eACRN,KAAK;IACT,GAAG,EAAEL,GAAI;IACT,KAAK,EAAE,CAACe,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAEX,aAAa,EAAEH,KAAK,CAAE;IAC5D,aAAa,EAAEO;EAAc,iBAE7B,oBAAC,SAAS;IACR,OAAO,EAAER,OAAQ;IACjB,KAAK,EAAEa,MAAM,CAACG,SAAU;IACxB,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAEd;EAAmB,EACvC,CACY;AAEpB,CAAC,CAAC;AAEF,MAAMa,YAAY,GAAG1B,QAAQ,CAAC4B,MAAM,CAAyB;EAC3DC,GAAG,EAAE;IACH;IACA;IACAC,uBAAuB,EAAE;EAC3B,CAAC;EACDC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAMP,MAAM,GAAGtB,UAAU,CAAC8B,MAAM,CAAC;EAC/BP,OAAO,EAAE;IACP,GAAGvB,UAAU,CAAC+B,kBAAkB;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACPd,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEF,eAAed,OAAO"}