/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.fabric.events;

import android.annotation.SuppressLint;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.facebook.jni.HybridData;
import com.facebook.proguard.annotations.DoNotStrip;
import com.facebook.react.bridge.NativeMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.fabric.FabricSoLoader;
import com.facebook.react.uimanager.events.EventCategoryDef;

/**
 * This class holds reference to the C++ EventEmitter object. Instances of this class are created on
 * the Bindings.cpp, where the pointer to the C++ event emitter is set.
 */
@SuppressLint("MissingNativeLoadLibrary")
public class EventEmitterWrapper {

  static {
    FabricSoLoader.staticInit();
  }

  @DoNotStrip private final HybridData mHybridData;

  private static native HybridData initHybrid();

  private EventEmitterWrapper() {
    mHybridData = initHybrid();
  }

  private native void invokeEvent(
      @NonNull String eventName, @NonNull NativeMap params, @EventCategoryDef int category);

  private native void invokeUniqueEvent(
      @NonNull String eventName, @NonNull NativeMap params, int customCoalesceKey);

  /**
   * Invokes the execution of the C++ EventEmitter.
   *
   * @param eventName {@link String} name of the event to execute.
   * @param params {@link WritableMap} payload of the event
   */
  public synchronized void invoke(
      @NonNull String eventName,
      @Nullable WritableMap params,
      @EventCategoryDef int eventCategory) {
    if (!isValid()) {
      return;
    }
    invokeEvent(eventName, (NativeMap) params, eventCategory);
  }

  /**
   * Invokes the execution of the C++ EventEmitter. C++ will coalesce events sent to the same
   * target.
   *
   * @param eventName {@link String} name of the event to execute.
   * @param params {@link WritableMap} payload of the event
   */
  public synchronized void invokeUnique(
      @NonNull String eventName, @Nullable WritableMap params, int customCoalesceKey) {
    if (!isValid()) {
      return;
    }
    invokeUniqueEvent(eventName, (NativeMap) params, customCoalesceKey);
  }

  public synchronized void destroy() {
    if (mHybridData != null) {
      mHybridData.resetNative();
    }
  }

  private boolean isValid() {
    if (mHybridData != null) {
      return mHybridData.isValid();
    }
    return false;
  }
}
