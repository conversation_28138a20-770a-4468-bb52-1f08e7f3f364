{"version": 3, "names": ["rawBodyMiddleware", "req", "_res", "next", "rawBody", "setEncoding", "on", "chunk"], "sources": ["../src/rawBodyMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\n\nexport default function rawBodyMiddleware(\n  req: http.IncomingMessage,\n  _res: http.ServerResponse,\n  next: (err?: any) => void,\n) {\n  (req as http.IncomingMessage & {rawBody: string}).rawBody = '';\n  req.setEncoding('utf8');\n\n  req.on('data', (chunk: string) => {\n    (req as http.IncomingMessage & {rawBody: string}).rawBody += chunk;\n  });\n\n  req.on('end', () => {\n    next();\n  });\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAGe,SAASA,iBAAiB,CACvCC,GAAyB,EACzBC,IAAyB,EACzBC,IAAyB,EACzB;EACCF,GAAG,CAA8CG,OAAO,GAAG,EAAE;EAC9DH,GAAG,CAACI,WAAW,CAAC,MAAM,CAAC;EAEvBJ,GAAG,CAACK,EAAE,CAAC,MAAM,EAAGC,KAAa,IAAK;IAC/BN,GAAG,CAA8CG,OAAO,IAAIG,KAAK;EACpE,CAAC,CAAC;EAEFN,GAAG,CAACK,EAAE,CAAC,KAAK,EAAE,MAAM;IAClBH,IAAI,EAAE;EACR,CAAC,CAAC;AACJ"}