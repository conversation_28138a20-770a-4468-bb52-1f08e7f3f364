{"version": 3, "names": ["useNavigationState", "selector", "navigation", "useNavigation", "setResult", "React", "useState", "getState", "selectorRef", "useRef", "useEffect", "current", "unsubscribe", "addListener", "e", "data", "state"], "sourceRoot": "../../src", "sources": ["useNavigationState.tsx"], "mappings": ";;;;;;AACA;AAGA;AAA4C;AAAA;AAAA;AAM5C;AACA;AACA;AACA;AACA;AACe,SAASA,kBAAkB,CACxCC,QAAgC,EAC7B;EACH,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAA6B;;EAE7D;EACA;EACA,MAAM,GAAGC,SAAS,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,MAAML,QAAQ,CAACC,UAAU,CAACK,QAAQ,EAAE,CAAC,CAAC;;EAE3E;EACA,MAAMC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAACR,QAAQ,CAAC;EAE1CI,KAAK,CAACK,SAAS,CAAC,MAAM;IACpBF,WAAW,CAACG,OAAO,GAAGV,QAAQ;EAChC,CAAC,CAAC;EAEFI,KAAK,CAACK,SAAS,CAAC,MAAM;IACpB,MAAME,WAAW,GAAGV,UAAU,CAACW,WAAW,CAAC,OAAO,EAAGC,CAAC,IAAK;MACzDV,SAAS,CAACI,WAAW,CAACG,OAAO,CAACG,CAAC,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,OAAOJ,WAAW;EACpB,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;EAEhB,OAAOD,QAAQ,CAACC,UAAU,CAACK,QAAQ,EAAE,CAAC;AACxC"}