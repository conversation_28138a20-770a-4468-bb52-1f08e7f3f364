{"version": 3, "names": ["React", "useChildListeners", "current", "listeners", "useRef", "action", "focus", "addListener", "useCallback", "type", "listener", "push", "removed", "index", "indexOf", "splice"], "sourceRoot": "../../src", "sources": ["useChildListeners.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B;AACA;AACA;AACA,eAAe,SAASC,iBAAiB,GAAG;EAC1C,MAAM;IAAEC,OAAO,EAAEC;EAAU,CAAC,GAAGH,KAAK,CAACI,MAAM,CAExC;IACDC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGP,KAAK,CAACQ,WAAW,CACnC,CAA8BC,IAAO,EAAEC,QAAwB,KAAK;IAClEP,SAAS,CAACM,IAAI,CAAC,CAACE,IAAI,CAACD,QAAQ,CAAC;IAE9B,IAAIE,OAAO,GAAG,KAAK;IACnB,OAAO,MAAM;MACX,MAAMC,KAAK,GAAGV,SAAS,CAACM,IAAI,CAAC,CAACK,OAAO,CAACJ,QAAQ,CAAC;MAE/C,IAAI,CAACE,OAAO,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC1BD,OAAO,GAAG,IAAI;QACdT,SAAS,CAACM,IAAI,CAAC,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EACD,CAACV,SAAS,CAAC,CACZ;EAED,OAAO;IACLA,SAAS;IACTI;EACF,CAAC;AACH"}