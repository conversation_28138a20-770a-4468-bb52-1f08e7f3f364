{"version": 3, "names": ["useDrawerStatus", "drawerStatus", "React", "useContext", "DrawerStatusContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useDrawerStatus.tsx"], "mappings": ";;;;;;AACA;AAEA;AAAwD;AAAA;AAAA;AAExD;AACA;AACA;AACA;AACe,SAASA,eAAe,GAAiB;EACtD,MAAMC,YAAY,GAAGC,KAAK,CAACC,UAAU,CAACC,4BAAmB,CAAC;EAE1D,IAAIH,YAAY,KAAKI,SAAS,EAAE;IAC9B,MAAM,IAAIC,KAAK,CACb,sEAAsE,CACvE;EACH;EAEA,OAAOL,YAAY;AACrB"}