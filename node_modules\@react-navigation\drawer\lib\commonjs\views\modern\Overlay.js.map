{"version": 3, "names": ["PROGRESS_EPSILON", "Overlay", "React", "forwardRef", "ref", "progress", "onPress", "style", "accessibilityLabel", "props", "animatedStyle", "useAnimatedStyle", "opacity", "value", "zIndex", "animatedProps", "useAnimatedProps", "active", "pointerEvents", "accessibilityElementsHidden", "importantForAccessibility", "styles", "overlay", "overlayStyle", "pressable", "Platform", "select", "web", "WebkitTapHighlightColor", "default", "StyleSheet", "create", "absoluteFillObject", "backgroundColor", "flex"], "sourceRoot": "../../../../src", "sources": ["views/modern/Overlay.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAGiC;AAAA;AAAA;AAEjC,MAAMA,gBAAgB,GAAG,IAAI;AAQ7B,MAAMC,OAAO,gBAAGC,KAAK,CAACC,UAAU,CAAC,SAASF,OAAO,OAQ/CG,GAA6B,EAC7B;EAAA,IARA;IACEC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,kBAAkB,GAAG,cAAc;IACnC,GAAGC;EACE,CAAC;EAGR,MAAMC,aAAa,GAAG,IAAAC,uCAAgB,EAAC,MAAM;IAC3C,OAAO;MACLC,OAAO,EAAEP,QAAQ,CAACQ,KAAK;MACvB;MACA;MACAC,MAAM,EAAET,QAAQ,CAACQ,KAAK,GAAGb,gBAAgB,GAAG,CAAC,GAAG,CAAC;IACnD,CAAC;EACH,CAAC,CAAC;EAEF,MAAMe,aAAa,GAAG,IAAAC,uCAAgB,EAAC,MAAM;IAC3C,MAAMC,MAAM,GAAGZ,QAAQ,CAACQ,KAAK,GAAGb,gBAAgB;IAEhD,OAAO;MACLkB,aAAa,EAAED,MAAM,GAAG,MAAM,GAAG,MAAM;MACvCE,2BAA2B,EAAE,CAACF,MAAM;MACpCG,yBAAyB,EAAEH,MAAM,GAAG,MAAM,GAAG;IAC/C,CAAC;EACH,CAAC,CAAC;EAEF,oBACE,oBAAC,8BAAQ,CAAC,IAAI,eACRR,KAAK;IACT,GAAG,EAAEL,GAAI;IACT,KAAK,EAAE,CAACiB,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAEb,aAAa,EAAEH,KAAK,CAAE;IAC5D,aAAa,EAAEQ;EAAc,iBAE7B,oBAAC,sBAAS;IACR,OAAO,EAAET,OAAQ;IACjB,KAAK,EAAEe,MAAM,CAACG,SAAU;IACxB,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAEhB;EAAmB,EACvC,CACY;AAEpB,CAAC,CAAC;AAEF,MAAMe,YAAY,GAAGE,qBAAQ,CAACC,MAAM,CAAyB;EAC3DC,GAAG,EAAE;IACH;IACA;IACAC,uBAAuB,EAAE;EAC3B,CAAC;EACDC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAMR,MAAM,GAAGS,uBAAU,CAACC,MAAM,CAAC;EAC/BT,OAAO,EAAE;IACP,GAAGQ,uBAAU,CAACE,kBAAkB;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDT,SAAS,EAAE;IACTU,IAAI,EAAE,CAAC;IACPhB,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAAC,eAEYjB,OAAO;AAAA"}