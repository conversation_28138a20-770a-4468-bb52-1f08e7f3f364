{"version": 3, "names": ["React", "MULTIPLE_NAVIGATOR_ERROR", "SingleNavigatorContext", "createContext", "undefined", "EnsureSingleNavigator", "children", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "value", "useMemo", "register", "key", "current<PERSON><PERSON>", "current", "Error", "unregister"], "sourceRoot": "../../src", "sources": ["EnsureSingleNavigator.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAM9B,MAAMC,wBAAwB,GAAI,oSAAmS;AAErU,OAAO,MAAMC,sBAAsB,gBAAGF,KAAK,CAACG,aAAa,CAMvDC,SAAS,CAAC;;AAEZ;AACA;AACA;AACA,eAAe,SAASC,qBAAqB,OAAsB;EAAA,IAArB;IAAEC;EAAgB,CAAC;EAC/D,MAAMC,eAAe,GAAGP,KAAK,CAACQ,MAAM,EAAsB;EAE1D,MAAMC,KAAK,GAAGT,KAAK,CAACU,OAAO,CACzB,OAAO;IACLC,QAAQ,CAACC,GAAW,EAAE;MACpB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAID,UAAU,KAAKT,SAAS,IAAIQ,GAAG,KAAKC,UAAU,EAAE;QAClD,MAAM,IAAIE,KAAK,CAACd,wBAAwB,CAAC;MAC3C;MAEAM,eAAe,CAACO,OAAO,GAAGF,GAAG;IAC/B,CAAC;IACDI,UAAU,CAACJ,GAAW,EAAE;MACtB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAIF,GAAG,KAAKC,UAAU,EAAE;QACtB;MACF;MAEAN,eAAe,CAACO,OAAO,GAAGV,SAAS;IACrC;EACF,CAAC,CAAC,EACF,EAAE,CACH;EAED,oBACE,oBAAC,sBAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEK;EAAM,GAC3CH,QAAQ,CACuB;AAEtC"}