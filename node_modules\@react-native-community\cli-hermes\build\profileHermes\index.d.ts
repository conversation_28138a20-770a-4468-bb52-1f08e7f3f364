import { Config } from '@react-native-community/cli-types';
type Options = {
    filename?: string;
    raw?: boolean;
    sourcemapPath?: string;
    generateSourcemap?: boolean;
    port: string;
    appId?: string;
    appIdSuffix?: string;
    host?: string;
};
declare function profileHermes([dstPath]: Array<string>, ctx: Config, options: Options): Promise<void>;
declare const _default: {
    name: string;
    description: string;
    func: typeof profileHermes;
    options: ({
        name: string;
        description: string;
        default?: undefined;
    } | {
        name: string;
        default: string;
        description?: undefined;
    } | {
        name: string;
        description: string;
        default: string;
    })[];
    examples: {
        desc: string;
        cmd: string;
    }[];
};
export default _default;
//# sourceMappingURL=index.d.ts.map