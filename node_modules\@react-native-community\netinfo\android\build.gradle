buildscript {
  // The Android Gradle plugin is only required when opening the android folder stand-alone.
  // This avoids unnecessary downloads and potential conflicts when the library is included as a
  // module dependency in an application project.
  if (project == rootProject) {
    repositories {
      google()
      mavenCentral()
    }

    dependencies {
      classpath("com.android.tools.build:gradle:7.4.2")
    }
  }
}

def getExtOrInitialValue(name, initialValue) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : initialValue
}

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeNetInfo_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeNetInfo_' + name]).toInteger()
}

apply plugin: 'com.android.library'

android {
  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  // Check AGP version for backward compatibility reasons
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace = "com.reactnativecommunity.netinfo"
  }

  compileOptions {
      sourceCompatibility JavaVersion.VERSION_1_8
      targetCompatibility JavaVersion.VERSION_1_8
  }

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault('minSdkVersion')
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
  }
  lintOptions{
    abortOnError false
  }
}

repositories {
  maven {
    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm

    // Use node resolver to locate react-native package
    def reactNativePackage = file(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim())
    if (reactNativePackage.exists()) {
      url "$reactNativePackage.parentFile/android"
    }
    // Fallback to react-native package colocated in node_modules
    else {
      url "$rootDir/../node_modules/react-native/android"
    }
  }
  google()
  mavenLocal()
  mavenCentral()
}

dependencies {
  //noinspection GradleDynamicVersion
  implementation 'com.facebook.react:react-native:+'

}
