load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_root_target", "react_native_target", "rn_android_library")

rn_android_library(
    name = "drawer",
    srcs = glob(["**/*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:drawerlayout"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_root_target(":generated_components_java-FBReactNativeComponentSpec"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/uimanager/annotations:annotations"),
        react_native_target("java/com/facebook/react/views/scroll:scroll"),
        react_native_target("res:uimanager"),
    ],
)
