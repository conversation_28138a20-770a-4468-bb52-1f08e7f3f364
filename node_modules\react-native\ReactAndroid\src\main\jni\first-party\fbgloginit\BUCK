load("//tools/build_defs/oss:rn_defs.bzl", "GLOG_DEP", "cxx_library")

cxx_library(
    name = "fbgloginit",
    srcs = [
        "glog_init.cpp",
    ],
    header_namespace = "",
    exported_headers = ["fb/glog_init.h"],
    compiler_flags = [
        "-fexceptions",
        "-fno-omit-frame-pointer",
    ],
    linker_flags = [
        "-llog",
    ],
    visibility = ["PUBLIC"],
    deps = [
        GLOG_DEP,
    ],
)
