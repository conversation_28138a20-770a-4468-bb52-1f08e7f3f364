# Freela Syria Mobile App - Setup & Testing Guide

## 🚀 **<PERSON><PERSON><PERSON>K START GUIDE**

### **Prerequisites**
- Node.js (v16 or higher)
- npm or yarn package manager
- Git for version control

### **Installation Steps**

1. **Navigate to Mobile App Directory**
   ```bash
   cd apps/mobile
   ```

2. **Install Dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Verify Installation**
   ```bash
   npm run type-check
   npm run lint
   ```

---

## 📱 **TESTING OPTIONS**

### **Option 1: Metro Bundler (Recommended)**

**Requirements:**
- Android Studio (for Android testing)
- Xcode (for iOS testing, macOS only)

**Setup Android Development:**
1. Download and install [Android Studio](https://developer.android.com/studio)
2. Install Android SDK (API level 30 or higher)
3. Create Android Virtual Device (AVD):
   - Open Android Studio
   - Go to Tools > AVD Manager
   - Create Virtual Device
   - Choose a device (e.g., Pixel 4)
   - Download and select Android 11+ system image
   - Finish setup

4. **Start Development Server:**
   ```bash
   npx react-native start --reset-cache
   ```

5. **Run on Android:**
   ```bash
   npx react-native run-android
   ```

**Setup iOS Development (macOS only):**
1. Install Xcode from Mac App Store
2. Install iOS Simulator
3. **Run on iOS:**
   ```bash
   npx react-native run-ios
   ```

### **Option 2: Web Testing (UI/UX Validation)**

For testing the UI components and screens without native setup:

1. **Install Additional Dependencies:**
   ```bash
   npm install express cors
   ```

2. **Create Web Test Server:**
   ```bash
   node -e "
   const express = require('express');
   const path = require('path');
   const app = express();
   
   app.use(express.static('.'));
   app.get('/', (req, res) => {
     res.send(\`
     <h1>Freela Syria Mobile App - Web Testing</h1>
     <p>This is a placeholder for web-based component testing.</p>
     <p>For full functionality, please use React Native with Android/iOS setup.</p>
     <h2>Implemented Features:</h2>
     <ul>
       <li>✅ Authentication Screens (Login, Register, Role Selection)</li>
       <li>✅ Home Screen with Featured Services</li>
       <li>✅ Search and Filtering</li>
       <li>✅ Bookings Management</li>
       <li>✅ Service Details</li>
       <li>✅ Expert Dashboard</li>
       <li>✅ Arabic RTL Support</li>
       <li>✅ Dark Theme</li>
     </ul>
     \`);
   });
   
   app.listen(3001, () => console.log('🌐 Web test server running at http://localhost:3001'));
   "
   ```

### **Option 3: Component Testing**

Test individual components:

```bash
npm test
```

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **1. Metro Bundler Not Starting**
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules
npm install --legacy-peer-deps
```

#### **2. Android Build Issues**
```bash
# Clean Android build
cd android
./gradlew clean
cd ..

# Rebuild
npx react-native run-android
```

#### **3. Dependency Conflicts**
```bash
# Use legacy peer deps
npm install --legacy-peer-deps

# Or force resolution
npm install --force
```

#### **4. TypeScript Errors**
```bash
# Check types
npm run type-check

# Fix common issues
npm run lint:fix
```

---

## 📋 **TESTING CHECKLIST**

### **Authentication Flow**
- [ ] Login screen loads correctly
- [ ] Form validation works (email, password)
- [ ] Registration form accepts user input
- [ ] Role selection shows both options
- [ ] Forgot password flow navigates correctly
- [ ] Arabic text displays properly (RTL)

### **Main App Screens**
- [ ] Home screen shows featured services
- [ ] Search functionality filters services
- [ ] Service details display correctly
- [ ] Booking screen shows user bookings
- [ ] Navigation between screens works
- [ ] Tab navigation functions properly

### **Expert Features**
- [ ] Expert dashboard loads
- [ ] Services management screen accessible
- [ ] Earnings screen displays data
- [ ] Expert-specific navigation works

### **UI Components**
- [ ] Buttons respond to touch
- [ ] Input fields accept text
- [ ] Cards display content properly
- [ ] Avatars show initials/images
- [ ] Loading states appear correctly

### **Theme & Localization**
- [ ] Dark theme toggles properly
- [ ] Arabic fonts load correctly
- [ ] RTL layout works as expected
- [ ] Colors and spacing consistent

---

## 🎯 **PERFORMANCE TESTING**

### **Metrics to Check**
- App startup time (< 3 seconds)
- Screen transition smoothness (60fps)
- Memory usage (< 100MB for basic usage)
- Bundle size optimization

### **Testing Commands**
```bash
# Bundle analysis
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle

# Performance profiling
npx react-native run-android --variant=release
```

---

## 📱 **DEVICE TESTING**

### **Android Physical Device**
1. Enable Developer Options on Android device
2. Enable USB Debugging
3. Connect device via USB
4. Run: `npx react-native run-android`

### **iOS Physical Device (macOS only)**
1. Connect iPhone/iPad via USB
2. Trust computer on device
3. Open Xcode and select device
4. Run: `npx react-native run-ios --device`

---

## 🔍 **CODE QUALITY CHECKS**

### **Linting**
```bash
npm run lint
npm run lint:fix
```

### **Type Checking**
```bash
npm run type-check
```

### **Testing**
```bash
npm test
npm run test:coverage
```

---

## 📊 **MONITORING & DEBUGGING**

### **React Native Debugger**
1. Install React Native Debugger
2. Start Metro bundler
3. Enable debugging in app
4. Open debugger for inspection

### **Flipper Integration**
1. Install Flipper desktop app
2. Run app in debug mode
3. Connect to Flipper for advanced debugging

---

## 🚀 **DEPLOYMENT PREPARATION**

### **Android Release Build**
```bash
cd android
./gradlew assembleRelease
```

### **iOS Release Build**
```bash
npx react-native run-ios --configuration Release
```

### **Bundle Generation**
```bash
# Android
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle

# iOS
npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle
```

---

## 📞 **SUPPORT & NEXT STEPS**

### **If You Encounter Issues:**
1. Check this troubleshooting guide
2. Review React Native documentation
3. Check GitHub issues for similar problems
4. Ensure all prerequisites are installed

### **Ready for Production:**
1. Complete native platform setup
2. Integrate with backend APIs
3. Add comprehensive testing
4. Prepare for app store submission

---

**🎉 The Freela Syria mobile app is ready for development and testing!**

All screens, components, and core functionality have been implemented. Follow this guide to set up your development environment and start testing the application.
