{"version": 3, "names": ["createDebuggerProxyEndpoint", "WebSocketServer", "ws", "Server", "wss", "noServer", "debuggerSocket", "clientSocket", "send", "dest", "message", "e", "logger", "warn", "debuggerSocketCloseHandler", "close", "clientSocketCloseHandler", "JSON", "stringify", "method", "on", "socket", "request", "url", "indexOf", "onerror", "onclose", "onmessage", "data", "server", "isDebuggerConnected"], "sources": ["../../src/websocket/createDebuggerProxyEndpoint.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport ws from 'ws';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function createDebuggerProxyEndpoint(): {\n  server: ws.Server;\n  isDebuggerConnected: () => boolean;\n} {\n  const WebSocketServer = ws.Server;\n  const wss = new WebSocketServer({\n    noServer: true,\n  });\n\n  let debuggerSocket: ws | null;\n  let clientSocket: ws | null;\n\n  function send(dest: ws | null, message: ws.Data) {\n    if (!dest) {\n      return;\n    }\n\n    try {\n      dest.send(message);\n    } catch (e) {\n      logger.warn(e as any);\n      // Sometimes this call throws 'not opened'\n    }\n  }\n\n  const debuggerSocketCloseHandler = () => {\n    debuggerSocket = null;\n    if (clientSocket) {\n      clientSocket.close(1011, 'Debugger was disconnected');\n    }\n  };\n\n  const clientSocketCloseHandler = () => {\n    clientSocket = null;\n    send(debuggerSocket, JSON.stringify({method: '$disconnected'}));\n  };\n\n  wss.on('connection', (socket, request) => {\n    const {url} = request;\n\n    if (url && url.indexOf('role=debugger') > -1) {\n      if (debuggerSocket) {\n        socket.close(1011, 'Another debugger is already connected');\n        return;\n      }\n      debuggerSocket = socket;\n      if (debuggerSocket) {\n        debuggerSocket.onerror = debuggerSocketCloseHandler;\n        debuggerSocket.onclose = debuggerSocketCloseHandler;\n        debuggerSocket.onmessage = ({data}) => send(clientSocket, data);\n      }\n    } else if (url && url.indexOf('role=client') > -1) {\n      if (clientSocket) {\n        clientSocket.onerror = () => {};\n        clientSocket.onclose = () => {};\n        clientSocket.onmessage = () => {};\n        clientSocket.close(1011, 'Another client connected');\n      }\n      clientSocket = socket;\n      clientSocket.onerror = clientSocketCloseHandler;\n      clientSocket.onclose = clientSocketCloseHandler;\n      clientSocket.onmessage = ({data}) => send(debuggerSocket, data);\n    } else {\n      socket.close(1011, 'Missing role param');\n    }\n  });\n\n  return {\n    server: wss,\n    isDebuggerConnected() {\n      return !!debuggerSocket;\n    },\n  };\n}\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAVzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKe,SAASA,2BAA2B,GAGjD;EACA,MAAMC,eAAe,GAAGC,aAAE,CAACC,MAAM;EACjC,MAAMC,GAAG,GAAG,IAAIH,eAAe,CAAC;IAC9BI,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,IAAIC,cAAyB;EAC7B,IAAIC,YAAuB;EAE3B,SAASC,IAAI,CAACC,IAAe,EAAEC,OAAgB,EAAE;IAC/C,IAAI,CAACD,IAAI,EAAE;MACT;IACF;IAEA,IAAI;MACFA,IAAI,CAACD,IAAI,CAACE,OAAO,CAAC;IACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVC,kBAAM,CAACC,IAAI,CAACF,CAAC,CAAQ;MACrB;IACF;EACF;;EAEA,MAAMG,0BAA0B,GAAG,MAAM;IACvCR,cAAc,GAAG,IAAI;IACrB,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACQ,KAAK,CAAC,IAAI,EAAE,2BAA2B,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAG,MAAM;IACrCT,YAAY,GAAG,IAAI;IACnBC,IAAI,CAACF,cAAc,EAAEW,IAAI,CAACC,SAAS,CAAC;MAACC,MAAM,EAAE;IAAe,CAAC,CAAC,CAAC;EACjE,CAAC;EAEDf,GAAG,CAACgB,EAAE,CAAC,YAAY,EAAE,CAACC,MAAM,EAAEC,OAAO,KAAK;IACxC,MAAM;MAACC;IAAG,CAAC,GAAGD,OAAO;IAErB,IAAIC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5C,IAAIlB,cAAc,EAAE;QAClBe,MAAM,CAACN,KAAK,CAAC,IAAI,EAAE,uCAAuC,CAAC;QAC3D;MACF;MACAT,cAAc,GAAGe,MAAM;MACvB,IAAIf,cAAc,EAAE;QAClBA,cAAc,CAACmB,OAAO,GAAGX,0BAA0B;QACnDR,cAAc,CAACoB,OAAO,GAAGZ,0BAA0B;QACnDR,cAAc,CAACqB,SAAS,GAAG,CAAC;UAACC;QAAI,CAAC,KAAKpB,IAAI,CAACD,YAAY,EAAEqB,IAAI,CAAC;MACjE;IACF,CAAC,MAAM,IAAIL,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACjD,IAAIjB,YAAY,EAAE;QAChBA,YAAY,CAACkB,OAAO,GAAG,MAAM,CAAC,CAAC;QAC/BlB,YAAY,CAACmB,OAAO,GAAG,MAAM,CAAC,CAAC;QAC/BnB,YAAY,CAACoB,SAAS,GAAG,MAAM,CAAC,CAAC;QACjCpB,YAAY,CAACQ,KAAK,CAAC,IAAI,EAAE,0BAA0B,CAAC;MACtD;MACAR,YAAY,GAAGc,MAAM;MACrBd,YAAY,CAACkB,OAAO,GAAGT,wBAAwB;MAC/CT,YAAY,CAACmB,OAAO,GAAGV,wBAAwB;MAC/CT,YAAY,CAACoB,SAAS,GAAG,CAAC;QAACC;MAAI,CAAC,KAAKpB,IAAI,CAACF,cAAc,EAAEsB,IAAI,CAAC;IACjE,CAAC,MAAM;MACLP,MAAM,CAACN,KAAK,CAAC,IAAI,EAAE,oBAAoB,CAAC;IAC1C;EACF,CAAC,CAAC;EAEF,OAAO;IACLc,MAAM,EAAEzB,GAAG;IACX0B,mBAAmB,GAAG;MACpB,OAAO,CAAC,CAACxB,cAAc;IACzB;EACF,CAAC;AACH"}