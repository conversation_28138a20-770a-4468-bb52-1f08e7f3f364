{"version": 3, "names": ["INTERNAL_CALLSITES_REGEX", "RegExp", "join", "getDefaultMetroConfig", "ctx", "resolver", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unstable_conditionNames", "serializer", "getPolyfills", "require", "path", "reactNativePath", "server", "port", "Number", "process", "env", "RCT_METRO_PORT", "symbolicator", "customizeFrame", "frame", "collapse", "Boolean", "file", "test", "transformer", "allowOptionalDependencies", "assetRegistryPath", "asyncRequireModulePath", "resolve", "babelTransformerPath", "watchFolders"], "sources": ["../../src/tools/getDefaultMetroConfig.ts"], "sourcesContent": ["import type {InputConfigT} from 'metro-config';\nimport path from 'path';\nimport type {ConfigLoadingContext} from './loadMetroConfig';\n\n/**\n * This module reproduces defaults from the @react-native/metro-config package,\n * used in ./loadMetroConfig.js to provide a soft upgrade process when upgrading\n * to React Native 0.72.\n *\n * These values will be used when:\n * - RN CLI 11.x or greater is present in a project (from React Native 0.72).\n * - The project has not yet followed the upgrade instructions to update\n *   metro.config.js to extend '@react-native/metro-config'.\n *\n * Until we remove this file in a future release, updates should be made both\n * here and in '@react-native/metro-config'. (Note: Updates to these values are\n * generally rare.)\n *\n * TODO(@huntie): Remove this file in a future React Native release.\n */\n\n/**\n * @deprecated (React Native 0.72.0) Defaults should be updated here and in\n *   https://github.com/facebook/react-native/tree/main/package/metro-config/index.js\n */\nconst INTERNAL_CALLSITES_REGEX = new RegExp(\n  [\n    '/Libraries/Renderer/implementations/.+\\\\.js$',\n    '/Libraries/BatchedBridge/MessageQueue\\\\.js$',\n    '/Libraries/YellowBox/.+\\\\.js$',\n    '/Libraries/LogBox/.+\\\\.js$',\n    '/Libraries/Core/Timers/.+\\\\.js$',\n    '/Libraries/WebSocket/.+\\\\.js$',\n    '/Libraries/vendor/.+\\\\.js$',\n    '/node_modules/react-devtools-core/.+\\\\.js$',\n    '/node_modules/react-refresh/.+\\\\.js$',\n    '/node_modules/scheduler/.+\\\\.js$',\n    '/node_modules/event-target-shim/.+\\\\.js$',\n    '/node_modules/invariant/.+\\\\.js$',\n    '/node_modules/react-native/index.js$',\n    '/metro-runtime/.+\\\\.js$',\n    '^\\\\[native code\\\\]$',\n  ].join('|'),\n);\n\n/**\n * Get the static Metro config defaults for a React Native project.\n *\n * @deprecated (React Native 0.72.0) Defaults should be updated here and in\n *   https://github.com/facebook/react-native/tree/main/package/metro-config/index.js\n */\nexport default function getDefaultMetroConfig(\n  ctx: ConfigLoadingContext,\n): InputConfigT {\n  return {\n    resolver: {\n      resolverMainFields: ['react-native', 'browser', 'main'],\n      unstable_conditionNames: ['require', 'react-native'],\n    },\n    serializer: {\n      getPolyfills: () =>\n        require(path.join(ctx.reactNativePath, 'rn-get-polyfills'))(),\n    },\n    server: {\n      port: Number(process.env.RCT_METRO_PORT) || 8081,\n    },\n    symbolicator: {\n      customizeFrame: (frame: {file?: string}) => {\n        const collapse = Boolean(\n          frame.file && INTERNAL_CALLSITES_REGEX.test(frame.file),\n        );\n        return {collapse};\n      },\n    },\n    transformer: {\n      allowOptionalDependencies: true,\n      assetRegistryPath: 'react-native/Libraries/Image/AssetRegistry',\n      asyncRequireModulePath: require.resolve(\n        'metro-runtime/src/modules/asyncRequire',\n      ),\n      babelTransformerPath: require.resolve(\n        'metro-react-native-babel-transformer',\n      ),\n    },\n    watchFolders: [],\n  };\n}\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAGxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAMA,wBAAwB,GAAG,IAAIC,MAAM,CACzC,CACE,8CAA8C,EAC9C,6CAA6C,EAC7C,+BAA+B,EAC/B,4BAA4B,EAC5B,iCAAiC,EACjC,+BAA+B,EAC/B,4BAA4B,EAC5B,4CAA4C,EAC5C,sCAAsC,EACtC,kCAAkC,EAClC,0CAA0C,EAC1C,kCAAkC,EAClC,sCAAsC,EACtC,yBAAyB,EACzB,qBAAqB,CACtB,CAACC,IAAI,CAAC,GAAG,CAAC,CACZ;;AAED;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,qBAAqB,CAC3CC,GAAyB,EACX;EACd,OAAO;IACLC,QAAQ,EAAE;MACRC,kBAAkB,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC;MACvDC,uBAAuB,EAAE,CAAC,SAAS,EAAE,cAAc;IACrD,CAAC;IACDC,UAAU,EAAE;MACVC,YAAY,EAAE,MACZC,OAAO,CAACC,eAAI,CAACT,IAAI,CAACE,GAAG,CAACQ,eAAe,EAAE,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IACDC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,IAAI;IAC9C,CAAC;IACDC,YAAY,EAAE;MACZC,cAAc,EAAGC,KAAsB,IAAK;QAC1C,MAAMC,QAAQ,GAAGC,OAAO,CACtBF,KAAK,CAACG,IAAI,IAAIxB,wBAAwB,CAACyB,IAAI,CAACJ,KAAK,CAACG,IAAI,CAAC,CACxD;QACD,OAAO;UAACF;QAAQ,CAAC;MACnB;IACF,CAAC;IACDI,WAAW,EAAE;MACXC,yBAAyB,EAAE,IAAI;MAC/BC,iBAAiB,EAAE,4CAA4C;MAC/DC,sBAAsB,EAAEnB,OAAO,CAACoB,OAAO,CACrC,wCAAwC,CACzC;MACDC,oBAAoB,EAAErB,OAAO,CAACoB,OAAO,CACnC,sCAAsC;IAE1C,CAAC;IACDE,YAAY,EAAE;EAChB,CAAC;AACH"}