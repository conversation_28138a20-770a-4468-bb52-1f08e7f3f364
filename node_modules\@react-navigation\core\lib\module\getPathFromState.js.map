{"version": 3, "names": ["queryString", "fromEntries", "validatePathConfig", "getActiveRoute", "state", "route", "index", "routes", "length", "getPathFromState", "options", "Error", "configs", "screens", "createNormalizedConfigs", "path", "current", "allParams", "pattern", "focusedParams", "focusedRoute", "currentOptions", "nestedRouteNames", "hasNext", "name", "push", "params", "stringify", "currentParams", "Object", "entries", "map", "key", "value", "String", "assign", "split", "filter", "p", "startsWith", "for<PERSON>ach", "getParamName", "undefined", "nextRoute", "nestedConfig", "join", "endsWith", "encodeURIComponent", "param", "query", "sort", "replace", "joinPaths", "paths", "concat", "Boolean", "createConfigItem", "config", "parentPattern", "exact", "c", "result"], "sourceRoot": "../../src", "sources": ["getPathFromState.tsx"], "mappings": "AAKA,OAAO,KAAKA,WAAW,MAAM,cAAc;AAE3C,OAAOC,WAAW,MAAM,eAAe;AAEvC,OAAOC,kBAAkB,MAAM,sBAAsB;AAiBrD,MAAMC,cAAc,GAAIC,KAAY,IAAwC;EAC1E,MAAMC,KAAK,GACT,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,GAC3BF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACE,KAAK,CAAC,GACzBF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAE3C,IAAIH,KAAK,CAACD,KAAK,EAAE;IACf,OAAOD,cAAc,CAACE,KAAK,CAACD,KAAK,CAAC;EACpC;EAEA,OAAOC,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASI,gBAAgB,CACtCL,KAAY,EACZM,OAA4B,EACpB;EACR,IAAIN,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMO,KAAK,CACT,+EAA+E,CAChF;EACH;EAEA,IAAID,OAAO,EAAE;IACXR,kBAAkB,CAACQ,OAAO,CAAC;EAC7B;;EAEA;EACA,MAAME,OAAmC,GAAGF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,OAAO,GACxDC,uBAAuB,CAACJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,CAAC,GACzC,CAAC,CAAC;EAEN,IAAIE,IAAI,GAAG,GAAG;EACd,IAAIC,OAA0B,GAAGZ,KAAK;EAEtC,MAAMa,SAA8B,GAAG,CAAC,CAAC;EAEzC,OAAOD,OAAO,EAAE;IACd,IAAIV,KAAK,GAAG,OAAOU,OAAO,CAACV,KAAK,KAAK,QAAQ,GAAGU,OAAO,CAACV,KAAK,GAAG,CAAC;IACjE,IAAID,KAAK,GAAGW,OAAO,CAACT,MAAM,CAACD,KAAK,CAE/B;IAED,IAAIY,OAA2B;IAE/B,IAAIC,aAA8C;IAClD,IAAIC,YAAY,GAAGjB,cAAc,CAACC,KAAK,CAAC;IACxC,IAAIiB,cAAc,GAAGT,OAAO;;IAE5B;IACA,IAAIU,gBAAgB,GAAG,EAAE;IAEzB,IAAIC,OAAO,GAAG,IAAI;IAElB,OAAOlB,KAAK,CAACmB,IAAI,IAAIH,cAAc,IAAIE,OAAO,EAAE;MAC9CL,OAAO,GAAGG,cAAc,CAAChB,KAAK,CAACmB,IAAI,CAAC,CAACN,OAAO;MAE5CI,gBAAgB,CAACG,IAAI,CAACpB,KAAK,CAACmB,IAAI,CAAC;MAEjC,IAAInB,KAAK,CAACqB,MAAM,EAAE;QAAA;QAChB,MAAMC,SAAS,4BAAGN,cAAc,CAAChB,KAAK,CAACmB,IAAI,CAAC,0DAA1B,sBAA4BG,SAAS;QAEvD,MAAMC,aAAa,GAAG3B,WAAW,CAC/B4B,MAAM,CAACC,OAAO,CAACzB,KAAK,CAACqB,MAAM,CAAC,CAACK,GAAG,CAAC;UAAA,IAAC,CAACC,GAAG,EAAEC,KAAK,CAAC;UAAA,OAAK,CACjDD,GAAG,EACHL,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGK,GAAG,CAAC,GAAGL,SAAS,CAACK,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGC,MAAM,CAACD,KAAK,CAAC,CACzD;QAAA,EAAC,CACH;QAED,IAAIf,OAAO,EAAE;UACXW,MAAM,CAACM,MAAM,CAAClB,SAAS,EAAEW,aAAa,CAAC;QACzC;QAEA,IAAIR,YAAY,KAAKf,KAAK,EAAE;UAAA;UAC1B;UACA;UACAc,aAAa,GAAG;YAAE,GAAGS;UAAc,CAAC;UAEpC,YAAAV,OAAO,6CAAP,SACIkB,KAAK,CAAC,GAAG,CAAC,CACXC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC;UAChC;UAAA,CACCC,OAAO,CAAEF,CAAC,IAAK;YACd,MAAMd,IAAI,GAAGiB,YAAY,CAACH,CAAC,CAAC;;YAE5B;YACA,IAAInB,aAAa,EAAE;cACjB;cACA,OAAOA,aAAa,CAACK,IAAI,CAAC;YAC5B;UACF,CAAC,CAAC;QACN;MACF;;MAEA;MACA,IAAI,CAACH,cAAc,CAAChB,KAAK,CAACmB,IAAI,CAAC,CAACX,OAAO,IAAIR,KAAK,CAACD,KAAK,KAAKsC,SAAS,EAAE;QACpEnB,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLjB,KAAK,GACH,OAAOD,KAAK,CAACD,KAAK,CAACE,KAAK,KAAK,QAAQ,GACjCD,KAAK,CAACD,KAAK,CAACE,KAAK,GACjBD,KAAK,CAACD,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC;QAEnC,MAAMmC,SAAS,GAAGtC,KAAK,CAACD,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC;QAC3C,MAAMsC,YAAY,GAAGvB,cAAc,CAAChB,KAAK,CAACmB,IAAI,CAAC,CAACX,OAAO;;QAEvD;QACA,IAAI+B,YAAY,IAAID,SAAS,CAACnB,IAAI,IAAIoB,YAAY,EAAE;UAClDvC,KAAK,GAAGsC,SAA8C;UACtDtB,cAAc,GAAGuB,YAAY;QAC/B,CAAC,MAAM;UACL;UACArB,OAAO,GAAG,KAAK;QACjB;MACF;IACF;IAEA,IAAIL,OAAO,KAAKwB,SAAS,EAAE;MACzBxB,OAAO,GAAGI,gBAAgB,CAACuB,IAAI,CAAC,GAAG,CAAC;IACtC;IAEA,IAAIxB,cAAc,CAAChB,KAAK,CAACmB,IAAI,CAAC,KAAKkB,SAAS,EAAE;MAC5C3B,IAAI,IAAIG,OAAO,CACZkB,KAAK,CAAC,GAAG,CAAC,CACVL,GAAG,CAAEO,CAAC,IAAK;QACV,MAAMd,IAAI,GAAGiB,YAAY,CAACH,CAAC,CAAC;;QAE5B;QACA;QACA;QACA,IAAIA,CAAC,KAAK,GAAG,EAAE;UACb,OAAOjC,KAAK,CAACmB,IAAI;QACnB;;QAEA;QACA,IAAIc,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UACrB,MAAMN,KAAK,GAAGhB,SAAS,CAACO,IAAI,CAAC;UAE7B,IAAIS,KAAK,KAAKS,SAAS,IAAIJ,CAAC,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC1C;YACA,OAAO,EAAE;UACX;UAEA,OAAOC,kBAAkB,CAACd,KAAK,CAAC;QAClC;QAEA,OAAOc,kBAAkB,CAACT,CAAC,CAAC;MAC9B,CAAC,CAAC,CACDO,IAAI,CAAC,GAAG,CAAC;IACd,CAAC,MAAM;MACL9B,IAAI,IAAIgC,kBAAkB,CAAC1C,KAAK,CAACmB,IAAI,CAAC;IACxC;IAEA,IAAI,CAACL,aAAa,EAAE;MAClBA,aAAa,GAAGC,YAAY,CAACM,MAAM;IACrC;IAEA,IAAIrB,KAAK,CAACD,KAAK,EAAE;MACfW,IAAI,IAAI,GAAG;IACb,CAAC,MAAM,IAAII,aAAa,EAAE;MACxB,KAAK,IAAI6B,KAAK,IAAI7B,aAAa,EAAE;QAC/B,IAAIA,aAAa,CAAC6B,KAAK,CAAC,KAAK,WAAW,EAAE;UACxC;UACA,OAAO7B,aAAa,CAAC6B,KAAK,CAAC;QAC7B;MACF;MAEA,MAAMC,KAAK,GAAGjD,WAAW,CAAC2B,SAAS,CAACR,aAAa,EAAE;QAAE+B,IAAI,EAAE;MAAM,CAAC,CAAC;MAEnE,IAAID,KAAK,EAAE;QACTlC,IAAI,IAAK,IAAGkC,KAAM,EAAC;MACrB;IACF;IAEAjC,OAAO,GAAGX,KAAK,CAACD,KAAK;EACvB;;EAEA;EACAW,IAAI,GAAGA,IAAI,CAACoC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChCpC,IAAI,GAAGA,IAAI,CAACP,MAAM,GAAG,CAAC,GAAGO,IAAI,CAACoC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGpC,IAAI;EAEvD,OAAOA,IAAI;AACb;AAEA,MAAM0B,YAAY,GAAIvB,OAAe,IACnCA,OAAO,CAACiC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAE9C,MAAMC,SAAS,GAAG;EAAA,kCAAIC,KAAK;IAALA,KAAK;EAAA;EAAA,OACxB,EAAE,CACAC,MAAM,CAAC,GAAGD,KAAK,CAACtB,GAAG,CAAEO,CAAC,IAAKA,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACzCC,MAAM,CAACkB,OAAO,CAAC,CACfV,IAAI,CAAC,GAAG,CAAC;AAAA;AAEd,MAAMW,gBAAgB,GAAG,CACvBC,MAAmC,EACnCC,aAAsB,KACP;EAAA;EACf,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAMvC,OAAO,GAAGwC,aAAa,GAAGN,SAAS,CAACM,aAAa,EAAED,MAAM,CAAC,GAAGA,MAAM;IAEzE,OAAO;MAAEvC;IAAQ,CAAC;EACpB;;EAEA;EACA;EACA,IAAIA,OAA2B;EAE/B,IAAIuC,MAAM,CAACE,KAAK,IAAIF,MAAM,CAAC1C,IAAI,KAAK2B,SAAS,EAAE;IAC7C,MAAM,IAAI/B,KAAK,CACb,sJAAsJ,CACvJ;EACH;EAEAO,OAAO,GACLuC,MAAM,CAACE,KAAK,KAAK,IAAI,GACjBP,SAAS,CAACM,aAAa,IAAI,EAAE,EAAED,MAAM,CAAC1C,IAAI,IAAI,EAAE,CAAC,GACjD0C,MAAM,CAAC1C,IAAI,IAAI,EAAE;EAEvB,MAAMF,OAAO,GAAG4C,MAAM,CAAC5C,OAAO,GAC1BC,uBAAuB,CAAC2C,MAAM,CAAC5C,OAAO,EAAEK,OAAO,CAAC,GAChDwB,SAAS;EAEb,OAAO;IACL;IACAxB,OAAO,eAAEA,OAAO,8CAAP,UAASkB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACkB,OAAO,CAAC,CAACV,IAAI,CAAC,GAAG,CAAC;IACtDlB,SAAS,EAAE8B,MAAM,CAAC9B,SAAS;IAC3Bd;EACF,CAAC;AACH,CAAC;AAED,MAAMC,uBAAuB,GAAG,CAC9BJ,OAA8B,EAC9BQ,OAAgB,KAEhBjB,WAAW,CACT4B,MAAM,CAACC,OAAO,CAACpB,OAAO,CAAC,CAACqB,GAAG,CAAC,SAAe;EAAA,IAAd,CAACP,IAAI,EAAEoC,CAAC,CAAC;EACpC,MAAMC,MAAM,GAAGL,gBAAgB,CAACI,CAAC,EAAE1C,OAAO,CAAC;EAE3C,OAAO,CAACM,IAAI,EAAEqC,MAAM,CAAC;AACvB,CAAC,CAAC,CACH"}