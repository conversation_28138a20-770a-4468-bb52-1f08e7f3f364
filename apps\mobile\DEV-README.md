# Freela Syria Mobile App - Development Setup

## 🚀 Quick Start

### Option 1: Metro Bundler (Recommended for React Native development)
```bash
node dev-server.js
```

### Option 2: Web Testing (For UI testing without native setup)
```bash
node web-test.js
```

## 📱 Development Commands

- `npm start` - Start Metro bundler
- `npm run android` - Run on Android (requires Android setup)
- `npm run ios` - Run on iOS (requires iOS setup)
- `npm test` - Run tests
- `npm run lint` - Run linting

## 🛠️ Prerequisites

### For Full Native Development:
1. **Node.js** (v16 or higher)
2. **React Native CLI**: `npm install -g @react-native-community/cli`
3. **Android Studio** (for Android development)
4. **Xcode** (for iOS development, macOS only)

### For Web Testing Only:
1. **Node.js** (v16 or higher)
2. **Express** (installed via npm)

## 🏗️ Project Structure

```
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── navigation/     # Navigation setup
├── store/          # State management (Zustand)
├── services/       # API services
├── contexts/       # React contexts
├── types/          # TypeScript types
└── utils/          # Utility functions
```

## 🎨 Features Implemented

✅ **Authentication Flow**
- Login/Register screens with validation
- Role selection (Client/Expert)
- Forgot password functionality

✅ **Main App Screens**
- Home screen with featured services
- Search and filtering
- Bookings management
- Service details
- User profiles

✅ **UI Components**
- Button, Input, Card, Avatar
- Loading states and error handling
- Arabic RTL support
- Dark theme support

✅ **Navigation**
- Tab navigation for main screens
- Stack navigation for details
- Authentication flow handling

## 🌐 Arabic RTL Support

The app is built with Arabic-first design:
- RTL layout support
- Arabic fonts (Cairo family)
- Proper text alignment
- Cultural considerations for Syrian market

## 🔧 Troubleshooting

### Metro Bundler Issues:
```bash
npx react-native start --reset-cache
```

### Dependency Issues:
```bash
npm install --legacy-peer-deps
```

### Android Development:
1. Install Android Studio
2. Set up Android SDK
3. Create virtual device (AVD)
4. Run: `npm run android`

### iOS Development (macOS only):
1. Install Xcode
2. Install iOS Simulator
3. Run: `npm run ios`

## 📚 Next Steps

1. **Complete Native Setup**: Set up Android Studio and/or Xcode
2. **API Integration**: Connect to backend API endpoints
3. **Testing**: Add unit and integration tests
4. **Performance**: Optimize for production
5. **Deployment**: Build and deploy to app stores