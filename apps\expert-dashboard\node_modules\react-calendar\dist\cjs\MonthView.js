"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var prop_types_1 = __importDefault(require("prop-types"));
var clsx_1 = __importDefault(require("clsx"));
var Days_js_1 = __importDefault(require("./MonthView/Days.js"));
var Weekdays_js_1 = __importDefault(require("./MonthView/Weekdays.js"));
var WeekNumbers_js_1 = __importDefault(require("./MonthView/WeekNumbers.js"));
var const_js_1 = require("./shared/const.js");
var propTypes_js_1 = require("./shared/propTypes.js");
function getCalendarTypeFromLocale(locale) {
    if (locale) {
        for (var _i = 0, _a = Object.entries(const_js_1.CALENDAR_TYPE_LOCALES); _i < _a.length; _i++) {
            var _b = _a[_i], calendarType = _b[0], locales = _b[1];
            if (locales.includes(locale)) {
                return calendarType;
            }
        }
    }
    return const_js_1.CALENDAR_TYPES.ISO_8601;
}
/**
 * Displays a given month.
 */
var MonthView = function MonthView(props) {
    var activeStartDate = props.activeStartDate, locale = props.locale, onMouseLeave = props.onMouseLeave, showFixedNumberOfWeeks = props.showFixedNumberOfWeeks;
    var _a = props.calendarType, calendarType = _a === void 0 ? getCalendarTypeFromLocale(locale) : _a, formatShortWeekday = props.formatShortWeekday, formatWeekday = props.formatWeekday, onClickWeekNumber = props.onClickWeekNumber, showWeekNumbers = props.showWeekNumbers, childProps = __rest(props, ["calendarType", "formatShortWeekday", "formatWeekday", "onClickWeekNumber", "showWeekNumbers"]);
    function renderWeekdays() {
        return (react_1.default.createElement(Weekdays_js_1.default, { calendarType: calendarType, formatShortWeekday: formatShortWeekday, formatWeekday: formatWeekday, locale: locale, onMouseLeave: onMouseLeave }));
    }
    function renderWeekNumbers() {
        if (!showWeekNumbers) {
            return null;
        }
        return (react_1.default.createElement(WeekNumbers_js_1.default, { activeStartDate: activeStartDate, calendarType: calendarType, onClickWeekNumber: onClickWeekNumber, onMouseLeave: onMouseLeave, showFixedNumberOfWeeks: showFixedNumberOfWeeks }));
    }
    function renderDays() {
        return react_1.default.createElement(Days_js_1.default, __assign({ calendarType: calendarType }, childProps));
    }
    var className = 'react-calendar__month-view';
    return (react_1.default.createElement("div", { className: (0, clsx_1.default)(className, showWeekNumbers ? "".concat(className, "--weekNumbers") : '') },
        react_1.default.createElement("div", { style: {
                display: 'flex',
                alignItems: 'flex-end',
            } },
            renderWeekNumbers(),
            react_1.default.createElement("div", { style: {
                    flexGrow: 1,
                    width: '100%',
                } },
                renderWeekdays(),
                renderDays()))));
};
MonthView.propTypes = __assign(__assign({}, propTypes_js_1.tileGroupProps), { calendarType: propTypes_js_1.isCalendarType, formatDay: prop_types_1.default.func, formatLongDate: prop_types_1.default.func, formatShortWeekday: prop_types_1.default.func, formatWeekday: prop_types_1.default.func, onClickWeekNumber: prop_types_1.default.func, onMouseLeave: prop_types_1.default.func, showFixedNumberOfWeeks: prop_types_1.default.bool, showNeighboringMonth: prop_types_1.default.bool, showWeekNumbers: prop_types_1.default.bool });
exports.default = MonthView;
