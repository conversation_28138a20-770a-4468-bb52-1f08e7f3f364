"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = DrawerView;
var _elements = require("@react-navigation/elements");
var _native = require("@react-navigation/native");
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var Reanimated = _interopRequireWildcard(require("react-native-reanimated"));
var _reactNativeSafeAreaContext = require("react-native-safe-area-context");
var _addCancelListener = require("../utils/addCancelListener");
var _DrawerPositionContext = _interopRequireDefault(require("../utils/DrawerPositionContext"));
var _DrawerStatusContext = _interopRequireDefault(require("../utils/DrawerStatusContext"));
var _getDrawerStatusFromState = _interopRequireDefault(require("../utils/getDrawerStatusFromState"));
var _DrawerContent = _interopRequireDefault(require("./DrawerContent"));
var _DrawerToggleButton = _interopRequireDefault(require("./DrawerToggleButton"));
var _GestureHandler = require("./GestureHandler");
var _ScreenFallback = require("./ScreenFallback");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
const getDefaultDrawerWidth = _ref => {
  let {
    height,
    width
  } = _ref;
  /*
   * Default drawer width is screen width - header height
   * with a max width of 280 on mobile and 320 on tablet
   * https://material.io/components/navigation-drawer
   */
  const smallerAxisSize = Math.min(height, width);
  const isLandscape = width > height;
  const isTablet = smallerAxisSize >= 600;
  const appBarHeight = _reactNative.Platform.OS === 'ios' ? isLandscape ? 32 : 44 : 56;
  const maxWidth = isTablet ? 320 : 280;
  return Math.min(smallerAxisSize - appBarHeight, maxWidth);
};
const GestureHandlerWrapper = _GestureHandler.GestureHandlerRootView ?? _reactNative.View;
function DrawerViewBase(_ref2) {
  var _Reanimated$isConfigu;
  let {
    state,
    navigation,
    descriptors,
    defaultStatus,
    drawerContent = props => /*#__PURE__*/React.createElement(_DrawerContent.default, props),
    detachInactiveScreens = _reactNative.Platform.OS === 'web' || _reactNative.Platform.OS === 'android' || _reactNative.Platform.OS === 'ios',
    // Reanimated 2 is not configured
    // @ts-expect-error: the type definitions are incomplete
    useLegacyImplementation = !((_Reanimated$isConfigu = Reanimated.isConfigured) !== null && _Reanimated$isConfigu !== void 0 && _Reanimated$isConfigu.call(Reanimated))
  } = _ref2;
  // Reanimated v3 dropped legacy v1 syntax
  const legacyImplemenationNotAvailable = require('react-native-reanimated').abs === undefined;
  if (useLegacyImplementation && legacyImplemenationNotAvailable) {
    throw new Error('The `useLegacyImplementation` prop is not available with Reanimated 3 as it no longer includes support for Reanimated 1 legacy API. Remove the `useLegacyImplementation` prop from `Drawer.Navigator` to be able to use it.');
  }
  const Drawer = useLegacyImplementation ? require('./legacy/Drawer').default : require('./modern/Drawer').default;
  const focusedRouteKey = state.routes[state.index].key;
  const {
    drawerHideStatusBarOnOpen = false,
    drawerPosition = _reactNative.I18nManager.getConstants().isRTL ? 'right' : 'left',
    drawerStatusBarAnimation = 'slide',
    drawerStyle,
    drawerType = _reactNative.Platform.select({
      ios: 'slide',
      default: 'front'
    }),
    gestureHandlerProps,
    keyboardDismissMode = 'on-drag',
    overlayColor = 'rgba(0, 0, 0, 0.5)',
    swipeEdgeWidth = 32,
    swipeEnabled = _reactNative.Platform.OS !== 'web' && _reactNative.Platform.OS !== 'windows' && _reactNative.Platform.OS !== 'macos',
    swipeMinDistance = 60,
    overlayAccessibilityLabel
  } = descriptors[focusedRouteKey].options;
  const [loaded, setLoaded] = React.useState([focusedRouteKey]);
  if (!loaded.includes(focusedRouteKey)) {
    setLoaded([...loaded, focusedRouteKey]);
  }
  const dimensions = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();
  const {
    colors
  } = (0, _native.useTheme)();
  const drawerStatus = (0, _getDrawerStatusFromState.default)(state);
  const handleDrawerOpen = React.useCallback(() => {
    navigation.dispatch({
      ..._native.DrawerActions.openDrawer(),
      target: state.key
    });
  }, [navigation, state.key]);
  const handleDrawerClose = React.useCallback(() => {
    navigation.dispatch({
      ..._native.DrawerActions.closeDrawer(),
      target: state.key
    });
  }, [navigation, state.key]);
  React.useEffect(() => {
    if (drawerStatus === defaultStatus || drawerType === 'permanent') {
      return;
    }
    const handleHardwareBack = () => {
      // We shouldn't handle the back button if the parent screen isn't focused
      // This will avoid the drawer overriding event listeners from a focused screen
      if (!navigation.isFocused()) {
        return false;
      }
      if (defaultStatus === 'open') {
        handleDrawerOpen();
      } else {
        handleDrawerClose();
      }
      return true;
    };

    // We only add the listeners when drawer opens
    // This way we can make sure that the listener is added as late as possible
    // This will make sure that our handler will run first when back button is pressed
    return (0, _addCancelListener.addCancelListener)(handleHardwareBack);
  }, [defaultStatus, drawerStatus, drawerType, handleDrawerClose, handleDrawerOpen, navigation]);
  const renderDrawerContent = () => {
    return /*#__PURE__*/React.createElement(_DrawerPositionContext.default.Provider, {
      value: drawerPosition
    }, drawerContent({
      state: state,
      navigation: navigation,
      descriptors: descriptors
    }));
  };
  const renderSceneContent = () => {
    return /*#__PURE__*/React.createElement(_ScreenFallback.MaybeScreenContainer, {
      enabled: detachInactiveScreens,
      hasTwoStates: true,
      style: styles.content
    }, state.routes.map((route, index) => {
      const descriptor = descriptors[route.key];
      const {
        lazy = true,
        unmountOnBlur
      } = descriptor.options;
      const isFocused = state.index === index;
      if (unmountOnBlur && !isFocused) {
        return null;
      }
      if (lazy && !loaded.includes(route.key) && !isFocused) {
        // Don't render a lazy screen if we've never navigated to it
        return null;
      }
      const {
        freezeOnBlur,
        header = _ref3 => {
          let {
            layout,
            options
          } = _ref3;
          return /*#__PURE__*/React.createElement(_elements.Header, _extends({}, options, {
            layout: layout,
            title: (0, _elements.getHeaderTitle)(options, route.name),
            headerLeft: options.headerLeft ?? (props => /*#__PURE__*/React.createElement(_DrawerToggleButton.default, props))
          }));
        },
        headerShown,
        headerStatusBarHeight,
        headerTransparent,
        sceneContainerStyle
      } = descriptor.options;
      return /*#__PURE__*/React.createElement(_ScreenFallback.MaybeScreen, {
        key: route.key,
        style: [_reactNative.StyleSheet.absoluteFill, {
          zIndex: isFocused ? 0 : -1
        }],
        visible: isFocused,
        enabled: detachInactiveScreens,
        freezeOnBlur: freezeOnBlur
      }, /*#__PURE__*/React.createElement(_elements.Screen, {
        focused: isFocused,
        route: descriptor.route,
        navigation: descriptor.navigation,
        headerShown: headerShown,
        headerStatusBarHeight: headerStatusBarHeight,
        headerTransparent: headerTransparent,
        header: header({
          layout: dimensions,
          route: descriptor.route,
          navigation: descriptor.navigation,
          options: descriptor.options
        }),
        style: sceneContainerStyle
      }, descriptor.render()));
    }));
  };
  return /*#__PURE__*/React.createElement(_DrawerStatusContext.default.Provider, {
    value: drawerStatus
  }, /*#__PURE__*/React.createElement(Drawer, {
    open: drawerStatus !== 'closed',
    onOpen: handleDrawerOpen,
    onClose: handleDrawerClose,
    gestureHandlerProps: gestureHandlerProps,
    swipeEnabled: swipeEnabled,
    swipeEdgeWidth: swipeEdgeWidth,
    swipeVelocityThreshold: 500,
    swipeDistanceThreshold: swipeMinDistance,
    hideStatusBarOnOpen: drawerHideStatusBarOnOpen,
    statusBarAnimation: drawerStatusBarAnimation,
    keyboardDismissMode: keyboardDismissMode,
    drawerType: drawerType,
    overlayAccessibilityLabel: overlayAccessibilityLabel,
    drawerPosition: drawerPosition,
    drawerStyle: [{
      width: getDefaultDrawerWidth(dimensions),
      backgroundColor: colors.card
    }, drawerType === 'permanent' && (drawerPosition === 'left' ? {
      borderRightColor: colors.border,
      borderRightWidth: _reactNative.StyleSheet.hairlineWidth
    } : {
      borderLeftColor: colors.border,
      borderLeftWidth: _reactNative.StyleSheet.hairlineWidth
    }), drawerStyle],
    overlayStyle: {
      backgroundColor: overlayColor
    },
    renderDrawerContent: renderDrawerContent,
    renderSceneContent: renderSceneContent,
    dimensions: dimensions
  }));
}
function DrawerView(_ref4) {
  let {
    navigation,
    ...rest
  } = _ref4;
  return /*#__PURE__*/React.createElement(_elements.SafeAreaProviderCompat, null, /*#__PURE__*/React.createElement(GestureHandlerWrapper, {
    style: styles.content
  }, /*#__PURE__*/React.createElement(DrawerViewBase, _extends({
    navigation: navigation
  }, rest))));
}
const styles = _reactNative.StyleSheet.create({
  content: {
    flex: 1
  }
});
//# sourceMappingURL=DrawerView.js.map