{"version": 3, "names": ["createDevServerMiddleware", "options", "debuggerProxyEndpoint", "createDebuggerProxyEndpoint", "isDebuggerConnected", "messageSocketEndpoint", "createMessageSocketEndpoint", "broadcast", "eventsSocketEndpoint", "createEventsSocketEndpoint", "middleware", "connect", "use", "securityHeadersMiddleware", "compression", "nocache", "debuggerUIMiddleware", "devToolsMiddleware", "openStackFrameInEditorMiddleware", "openURLMiddleware", "statusPageMiddleware", "rawBodyMiddleware", "systraceProfileMiddleware", "_req", "res", "end", "errorhandler", "watchFolders", "for<PERSON>ach", "folder", "serveStatic", "websocketEndpoints", "server"], "sources": ["../src/index.ts"], "sourcesContent": ["import http from 'http';\n\nimport compression from 'compression';\nimport connect from 'connect';\nimport errorhandler from 'errorhandler';\nimport nocache from 'nocache';\nimport serveStatic from 'serve-static';\nimport {debuggerUIMiddleware} from '@react-native-community/cli-debugger-ui';\n\nimport devToolsMiddleware from './devToolsMiddleware';\nimport indexPageMiddleware from './indexPageMiddleware';\nimport openStackFrameInEditorMiddleware from './openStackFrameInEditorMiddleware';\nimport openURLMiddleware from './openURLMiddleware';\nimport rawBodyMiddleware from './rawBodyMiddleware';\nimport securityHeadersMiddleware from './securityHeadersMiddleware';\nimport statusPageMiddleware from './statusPageMiddleware';\nimport systraceProfileMiddleware from './systraceProfileMiddleware';\n\nimport createDebuggerProxyEndpoint from './websocket/createDebuggerProxyEndpoint';\nimport createMessageSocketEndpoint from './websocket/createMessageSocketEndpoint';\nimport createEventsSocketEndpoint from './websocket/createEventsSocketEndpoint';\n\nexport {devToolsMiddleware};\nexport {indexPageMiddleware};\nexport {openStackFrameInEditorMiddleware};\nexport {openURLMiddleware};\nexport {rawBodyMiddleware};\nexport {securityHeadersMiddleware};\nexport {statusPageMiddleware};\nexport {systraceProfileMiddleware};\n\ntype MiddlewareOptions = {\n  host?: string;\n  watchFolders: ReadonlyArray<string>;\n  port: number;\n};\n\nexport function createDevServerMiddleware(options: MiddlewareOptions) {\n  const debuggerProxyEndpoint = createDebuggerProxyEndpoint();\n  const isDebuggerConnected = debuggerProxyEndpoint.isDebuggerConnected;\n\n  const messageSocketEndpoint = createMessageSocketEndpoint();\n  const broadcast = messageSocketEndpoint.broadcast;\n\n  const eventsSocketEndpoint = createEventsSocketEndpoint(broadcast);\n\n  const middleware = connect()\n    .use(securityHeadersMiddleware)\n    // @ts-ignore compression and connect types mismatch\n    .use(compression())\n    .use(nocache())\n    .use('/debugger-ui', debuggerUIMiddleware())\n    .use(\n      '/launch-js-devtools',\n      devToolsMiddleware(options, isDebuggerConnected),\n    )\n    .use('/open-stack-frame', openStackFrameInEditorMiddleware(options))\n    .use('/open-url', openURLMiddleware)\n    .use('/status', statusPageMiddleware)\n    .use('/symbolicate', rawBodyMiddleware)\n    // @ts-ignore mismatch\n    .use('/systrace', systraceProfileMiddleware)\n    .use('/reload', (_req: http.IncomingMessage, res: http.ServerResponse) => {\n      broadcast('reload');\n      res.end('OK');\n    })\n    // @ts-ignore mismatch\n    .use(errorhandler());\n\n  options.watchFolders.forEach((folder) => {\n    // @ts-ignore mismatch between express and connect middleware types\n    middleware.use(serveStatic(folder));\n  });\n\n  return {\n    websocketEndpoints: {\n      '/debugger-proxy': debuggerProxyEndpoint.server,\n      '/message': messageSocketEndpoint.server,\n      '/events': eventsSocketEndpoint.server,\n    },\n    debuggerProxyEndpoint,\n    messageSocketEndpoint,\n    eventsSocketEndpoint,\n    middleware,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAgF;AAiBzE,SAASA,yBAAyB,CAACC,OAA0B,EAAE;EACpE,MAAMC,qBAAqB,GAAG,IAAAC,oCAA2B,GAAE;EAC3D,MAAMC,mBAAmB,GAAGF,qBAAqB,CAACE,mBAAmB;EAErE,MAAMC,qBAAqB,GAAG,IAAAC,oCAA2B,GAAE;EAC3D,MAAMC,SAAS,GAAGF,qBAAqB,CAACE,SAAS;EAEjD,MAAMC,oBAAoB,GAAG,IAAAC,mCAA0B,EAACF,SAAS,CAAC;EAElE,MAAMG,UAAU,GAAG,IAAAC,kBAAO,GAAE,CACzBC,GAAG,CAACC,kCAAyB;EAC9B;EAAA,CACCD,GAAG,CAAC,IAAAE,sBAAW,GAAE,CAAC,CAClBF,GAAG,CAAC,IAAAG,kBAAO,GAAE,CAAC,CACdH,GAAG,CAAC,cAAc,EAAE,IAAAI,qCAAoB,GAAE,CAAC,CAC3CJ,GAAG,CACF,qBAAqB,EACrB,IAAAK,2BAAkB,EAAChB,OAAO,EAAEG,mBAAmB,CAAC,CACjD,CACAQ,GAAG,CAAC,mBAAmB,EAAE,IAAAM,yCAAgC,EAACjB,OAAO,CAAC,CAAC,CACnEW,GAAG,CAAC,WAAW,EAAEO,0BAAiB,CAAC,CACnCP,GAAG,CAAC,SAAS,EAAEQ,6BAAoB,CAAC,CACpCR,GAAG,CAAC,cAAc,EAAES,0BAAiB;EACtC;EAAA,CACCT,GAAG,CAAC,WAAW,EAAEU,kCAAyB,CAAC,CAC3CV,GAAG,CAAC,SAAS,EAAE,CAACW,IAA0B,EAAEC,GAAwB,KAAK;IACxEjB,SAAS,CAAC,QAAQ,CAAC;IACnBiB,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC;EACf,CAAC;EACD;EAAA,CACCb,GAAG,CAAC,IAAAc,uBAAY,GAAE,CAAC;EAEtBzB,OAAO,CAAC0B,YAAY,CAACC,OAAO,CAAEC,MAAM,IAAK;IACvC;IACAnB,UAAU,CAACE,GAAG,CAAC,IAAAkB,sBAAW,EAACD,MAAM,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,OAAO;IACLE,kBAAkB,EAAE;MAClB,iBAAiB,EAAE7B,qBAAqB,CAAC8B,MAAM;MAC/C,UAAU,EAAE3B,qBAAqB,CAAC2B,MAAM;MACxC,SAAS,EAAExB,oBAAoB,CAACwB;IAClC,CAAC;IACD9B,qBAAqB;IACrBG,qBAAqB;IACrBG,oBAAoB;IACpBE;EACF,CAAC;AACH"}