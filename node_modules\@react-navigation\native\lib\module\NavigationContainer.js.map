{"version": 3, "names": ["BaseNavigationContainer", "getActionFromState", "getPathFromState", "getStateFromPath", "validatePathConfig", "React", "LinkingContext", "DefaultTheme", "ThemeProvider", "useBackButton", "useDocumentTitle", "useLinking", "useThenable", "global", "REACT_NAVIGATION_DEVTOOLS", "WeakMap", "NavigationContainerInner", "ref", "theme", "linking", "fallback", "documentTitle", "onReady", "rest", "isLinkingEnabled", "enabled", "config", "ref<PERSON><PERSON><PERSON>", "useRef", "getInitialState", "independent", "prefixes", "useEffect", "current", "set", "isResolved", "initialState", "useImperativeHandle", "linkingContext", "useMemo", "options", "isReady", "onReadyRef", "NavigationContainer", "forwardRef"], "sourceRoot": "../../src", "sources": ["NavigationContainer.tsx"], "mappings": ";AAAA,SACEA,uBAAuB,EACvBC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAIhBC,kBAAkB,QACb,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,aAAa,MAAM,yBAAyB;AAEnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AASvCC,MAAM,CAACC,yBAAyB,GAAG,IAAIC,OAAO,EAAE;AAUhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwB,OAS/BC,GAA6D,EAC7D;EAAA,IATA;IACEC,KAAK,GAAGX,YAAY;IACpBY,OAAO;IACPC,QAAQ,GAAG,IAAI;IACfC,aAAa;IACbC,OAAO;IACP,GAAGC;EACiB,CAAC;EAGvB,MAAMC,gBAAgB,GAAGL,OAAO,GAAGA,OAAO,CAACM,OAAO,KAAK,KAAK,GAAG,KAAK;EAEpE,IAAIN,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEO,MAAM,EAAE;IACnBtB,kBAAkB,CAACe,OAAO,CAACO,MAAM,CAAC;EACpC;EAEA,MAAMC,YAAY,GAChBtB,KAAK,CAACuB,MAAM,CAAwC,IAAI,CAAC;EAE3DnB,aAAa,CAACkB,YAAY,CAAC;EAC3BjB,gBAAgB,CAACiB,YAAY,EAAEN,aAAa,CAAC;EAE7C,MAAM;IAAEQ;EAAgB,CAAC,GAAGlB,UAAU,CAACgB,YAAY,EAAE;IACnDG,WAAW,EAAEP,IAAI,CAACO,WAAW;IAC7BL,OAAO,EAAED,gBAAgB;IACzBO,QAAQ,EAAE,EAAE;IACZ,GAAGZ;EACL,CAAC,CAAC;;EAEF;EACA;EACAd,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAIL,YAAY,CAACM,OAAO,EAAE;MACxBnB,yBAAyB,CAACoB,GAAG,CAACP,YAAY,CAACM,OAAO,EAAE;QAClD,IAAId,OAAO,GAAG;UACZ,OAAO;YACL,GAAGA,OAAO;YACVM,OAAO,EAAED,gBAAgB;YACzBO,QAAQ,EAAE,CAAAZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY,QAAQ,KAAI,EAAE;YACjC5B,gBAAgB,EAAE,CAAAgB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,gBAAgB,KAAIA,gBAAgB;YAC/DD,gBAAgB,EAAE,CAAAiB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjB,gBAAgB,KAAIA,gBAAgB;YAC/DD,kBAAkB,EAChB,CAAAkB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAElB,kBAAkB,KAAIA;UACnC,CAAC;QACH;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAM,CAACkC,UAAU,EAAEC,YAAY,CAAC,GAAGxB,WAAW,CAACiB,eAAe,CAAC;EAE/DxB,KAAK,CAACgC,mBAAmB,CAACpB,GAAG,EAAE,MAAMU,YAAY,CAACM,OAAO,CAAC;EAE1D,MAAMK,cAAc,GAAGjC,KAAK,CAACkC,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAErB;EAAQ,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAE7E,MAAMsB,OAAO,GAAGlB,IAAI,CAACa,YAAY,IAAI,IAAI,IAAI,CAACZ,gBAAgB,IAAIW,UAAU;EAE5E,MAAMO,UAAU,GAAGrC,KAAK,CAACuB,MAAM,CAACN,OAAO,CAAC;EAExCjB,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpBU,UAAU,CAACT,OAAO,GAAGX,OAAO;EAC9B,CAAC,CAAC;EAEFjB,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAIS,OAAO,EAAE;MAAA;MACX,uBAAAC,UAAU,CAACT,OAAO,wDAAlB,yBAAAS,UAAU,CAAY;IACxB;EACF,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EAEb,IAAI,CAACA,OAAO,EAAE;IACZ;IACA;IACA,OAAOrB,QAAQ;EACjB;EAEA,oBACE,oBAAC,cAAc,CAAC,QAAQ;IAAC,KAAK,EAAEkB;EAAe,gBAC7C,oBAAC,aAAa;IAAC,KAAK,EAAEpB;EAAM,gBAC1B,oBAAC,uBAAuB,eAClBK,IAAI;IACR,YAAY,EACVA,IAAI,CAACa,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGb,IAAI,CAACa,YACjD;IACD,GAAG,EAAET;EAAa,GAClB,CACY,CACQ;AAE9B;AAEA,MAAMgB,mBAAmB,gBAAGtC,KAAK,CAACuC,UAAU,CAAC5B,wBAAwB,CAM9C;AAEvB,eAAe2B,mBAAmB"}