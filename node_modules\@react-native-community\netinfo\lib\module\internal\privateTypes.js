/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */
export const DEVICE_CONNECTIVITY_EVENT = 'netInfo.networkStatusDidChange'; // Certain properties are optional when sent by the native module and are handled by the JS code
//# sourceMappingURL=privateTypes.js.map