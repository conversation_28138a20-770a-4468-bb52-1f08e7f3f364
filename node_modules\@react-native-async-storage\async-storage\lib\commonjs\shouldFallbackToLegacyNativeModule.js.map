{"version": 3, "names": ["_reactNative", "require", "shouldFallbackToLegacyNativeModule", "_NativeModules$Native", "expoConstants", "NativeModules", "modulesConstants", "ExponentConstants", "isLegacySdkVersion", "appOwnership", "executionEnvironment", "includes"], "sources": ["shouldFallbackToLegacyNativeModule.ts"], "sourcesContent": ["import { NativeModules } from \"react-native\";\n\nexport function shouldFallbackToLegacyNativeModule(): boolean {\n  const expoConstants =\n    NativeModules[\"NativeUnimoduleProxy\"]?.modulesConstants?.ExponentConstants;\n\n  if (expoConstants) {\n    /**\n     * In SDK <= 39, appOwnership is defined in managed apps but executionEnvironment is not.\n     * In bare React Native apps using expo-constants, appOwnership is never defined, so\n     * isLegacySdkVersion will be false in that context.\n     */\n    const isLegacySdkVersion =\n      expoConstants.appOwnership && !expoConstants.executionEnvironment;\n\n    /**\n     * Expo managed apps don't include the @react-native-async-storage/async-storage\n     * native modules yet, but the API interface is the same, so we can use the version\n     * exported from React Native still.\n     *\n     * If in future releases (eg: @react-native-async-storage/async-storage >= 2.0.0) this\n     * will likely not be valid anymore, and the package will need to be included in the Expo SDK\n     * to continue to work.\n     */\n    if (\n      isLegacySdkVersion ||\n      [\"storeClient\", \"standalone\"].includes(expoConstants.executionEnvironment)\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,SAASC,kCAAkCA,CAAA,EAAY;EAAA,IAAAC,qBAAA;EAC5D,MAAMC,aAAa,IAAAD,qBAAA,GACjBE,0BAAa,CAAC,sBAAsB,CAAC,cAAAF,qBAAA,gBAAAA,qBAAA,GAArCA,qBAAA,CAAuCG,gBAAgB,cAAAH,qBAAA,uBAAvDA,qBAAA,CAAyDI,iBAAiB;EAE5E,IAAIH,aAAa,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,MAAMI,kBAAkB,GACtBJ,aAAa,CAACK,YAAY,IAAI,CAACL,aAAa,CAACM,oBAAoB;;IAEnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IACEF,kBAAkB,IAClB,CAAC,aAAa,EAAE,YAAY,CAAC,CAACG,QAAQ,CAACP,aAAa,CAACM,oBAAoB,CAAC,EAC1E;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd"}