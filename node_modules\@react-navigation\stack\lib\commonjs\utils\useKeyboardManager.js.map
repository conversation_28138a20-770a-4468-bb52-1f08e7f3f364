{"version": 3, "names": ["useKeyboardManager", "isEnabled", "previouslyFocusedTextInputRef", "React", "useRef", "undefined", "startTimestampRef", "keyboardTimeoutRef", "clearKeyboardTimeout", "useCallback", "current", "clearTimeout", "onPageChangeStart", "input", "TextInput", "State", "currentlyFocusedInput", "blur", "Date", "now", "onPageChangeConfirm", "force", "Keyboard", "dismiss", "onPageChangeCancel", "setTimeout", "focus", "useEffect"], "sourceRoot": "../../../src", "sources": ["utils/useKeyboardManager.tsx"], "mappings": ";;;;;;AAAA;AACA;AAAkE;AAAA;AAInD,SAASA,kBAAkB,CAACC,SAAwB,EAAE;EACnE;EACA;EACA,MAAMC,6BAA6B,GAAGC,KAAK,CAACC,MAAM,CAAWC,SAAS,CAAC;EACvE,MAAMC,iBAAiB,GAAGH,KAAK,CAACC,MAAM,CAAS,CAAC,CAAC;EACjD,MAAMG,kBAAkB,GAAGJ,KAAK,CAACC,MAAM,EAAO;EAE9C,MAAMI,oBAAoB,GAAGL,KAAK,CAACM,WAAW,CAAC,MAAM;IACnD,IAAIF,kBAAkB,CAACG,OAAO,KAAKL,SAAS,EAAE;MAC5CM,YAAY,CAACJ,kBAAkB,CAACG,OAAO,CAAC;MACxCH,kBAAkB,CAACG,OAAO,GAAGL,SAAS;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAGT,KAAK,CAACM,WAAW,CAAC,MAAM;IAChD,IAAI,CAACR,SAAS,EAAE,EAAE;MAChB;IACF;IAEAO,oBAAoB,EAAE;IAEtB,MAAMK,KAAe,GAAGC,sBAAS,CAACC,KAAK,CAACC,qBAAqB,EAAE;;IAE/D;IACAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,IAAI,EAAE;;IAEb;IACAf,6BAA6B,CAACQ,OAAO,GAAGG,KAAK;;IAE7C;IACAP,iBAAiB,CAACI,OAAO,GAAGQ,IAAI,CAACC,GAAG,EAAE;EACxC,CAAC,EAAE,CAACX,oBAAoB,EAAEP,SAAS,CAAC,CAAC;EAErC,MAAMmB,mBAAmB,GAAGjB,KAAK,CAACM,WAAW,CAC1CY,KAAc,IAAK;IAClB,IAAI,CAACpB,SAAS,EAAE,EAAE;MAChB;IACF;IAEAO,oBAAoB,EAAE;IAEtB,IAAIa,KAAK,EAAE;MACT;MACA;MACA;MACAC,qBAAQ,CAACC,OAAO,EAAE;IACpB,CAAC,MAAM;MACL,MAAMV,KAAK,GAAGX,6BAA6B,CAACQ,OAAO;;MAEnD;MACA;MACAG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,IAAI,EAAE;IACf;;IAEA;IACAf,6BAA6B,CAACQ,OAAO,GAAGL,SAAS;EACnD,CAAC,EACD,CAACG,oBAAoB,EAAEP,SAAS,CAAC,CAClC;EAED,MAAMuB,kBAAkB,GAAGrB,KAAK,CAACM,WAAW,CAAC,MAAM;IACjD,IAAI,CAACR,SAAS,EAAE,EAAE;MAChB;IACF;IAEAO,oBAAoB,EAAE;;IAEtB;IACA,MAAMK,KAAK,GAAGX,6BAA6B,CAACQ,OAAO;IAEnD,IAAIG,KAAK,EAAE;MACT;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAIK,IAAI,CAACC,GAAG,EAAE,GAAGb,iBAAiB,CAACI,OAAO,GAAG,GAAG,EAAE;QAChDH,kBAAkB,CAACG,OAAO,GAAGe,UAAU,CAAC,MAAM;UAC5CZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,KAAK,EAAE;UACdxB,6BAA6B,CAACQ,OAAO,GAAGL,SAAS;QACnD,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLQ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,KAAK,EAAE;QACdxB,6BAA6B,CAACQ,OAAO,GAAGL,SAAS;MACnD;IACF;EACF,CAAC,EAAE,CAACG,oBAAoB,EAAEP,SAAS,CAAC,CAAC;EAErCE,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMnB,oBAAoB,EAAE;EACrC,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,OAAO;IACLI,iBAAiB;IACjBQ,mBAAmB;IACnBI;EACF,CAAC;AACH"}