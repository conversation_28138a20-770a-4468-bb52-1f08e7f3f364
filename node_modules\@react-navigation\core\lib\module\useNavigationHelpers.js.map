{"version": 3, "names": ["CommonActions", "React", "NavigationContext", "PrivateValueStore", "UnhandledActionContext", "useNavigationHelpers", "id", "navigatorId", "onAction", "getState", "emitter", "router", "onUnhandledAction", "useContext", "parentNavigationHelpers", "useMemo", "dispatch", "op", "action", "handled", "actions", "actionCreators", "helpers", "Object", "keys", "reduce", "acc", "name", "navigationHelpers", "emit", "isFocused", "canGoBack", "state", "getStateForAction", "goBack", "routeNames", "routeParamList", "routeGetIdList", "getId", "getParent", "undefined", "current"], "sourceRoot": "../../src", "sources": ["useNavigationHelpers.tsx"], "mappings": "AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAA4BC,iBAAiB,QAAQ,SAAS;AAC9D,OAAOC,sBAAsB,MAAM,0BAA0B;AAG7D;AACA;AACAD,iBAAiB;AAUjB;AACA;AACA;AACA;AACA,eAAe,SAASE,oBAAoB,OAWjB;EAAA,IANzB;IACAC,EAAE,EAAEC,WAAW;IACfC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC;EACsB,CAAC;EACvB,MAAMC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACT,sBAAsB,CAAC;EAClE,MAAMU,uBAAuB,GAAGb,KAAK,CAACY,UAAU,CAACX,iBAAiB,CAAC;EAEnE,OAAOD,KAAK,CAACc,OAAO,CAAC,MAAM;IACzB,MAAMC,QAAQ,GAAIC,EAAuC,IAAK;MAC5D,MAAMC,MAAM,GAAG,OAAOD,EAAE,KAAK,UAAU,GAAGA,EAAE,CAACR,QAAQ,EAAE,CAAC,GAAGQ,EAAE;MAE7D,MAAME,OAAO,GAAGX,QAAQ,CAACU,MAAM,CAAC;MAEhC,IAAI,CAACC,OAAO,EAAE;QACZP,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGM,MAAM,CAAC;MAC7B;IACF,CAAC;IAED,MAAME,OAAO,GAAG;MACd,GAAGT,MAAM,CAACU,cAAc;MACxB,GAAGrB;IACL,CAAC;IAED,MAAMsB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACzD;MACAD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA,OAAkBX,QAAQ,CAACI,OAAO,CAACO,IAAI,CAAC,CAAC,YAAO,CAAC,CAAC;MAAA;MAC9D,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAkB;IAEvB,MAAME,iBAAiB,GAAG;MACxB,GAAGd,uBAAuB;MAC1B,GAAGQ,OAAO;MACVN,QAAQ;MACRa,IAAI,EAAEnB,OAAO,CAACmB,IAAI;MAClBC,SAAS,EAAEhB,uBAAuB,GAC9BA,uBAAuB,CAACgB,SAAS,GACjC,MAAM,IAAI;MACdC,SAAS,EAAE,MAAM;QACf,MAAMC,KAAK,GAAGvB,QAAQ,EAAE;QAExB,OACEE,MAAM,CAACsB,iBAAiB,CAACD,KAAK,EAAEhC,aAAa,CAACkC,MAAM,EAAE,EAAY;UAChEC,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BC,cAAc,EAAE,CAAC,CAAC;UAClBC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,KAAK,IAAI,KACXvB,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEiB,SAAS,EAAE,KACpC,KAAK;MAET,CAAC;MACDO,KAAK,EAAE,MAAM/B,WAAW;MACxBgC,SAAS,EAAGjC,EAAW,IAAK;QAC1B,IAAIA,EAAE,KAAKkC,SAAS,EAAE;UACpB,IAAIC,OAAO,GAAGb,iBAAiB;UAE/B,OAAOa,OAAO,IAAInC,EAAE,KAAKmC,OAAO,CAACH,KAAK,EAAE,EAAE;YACxCG,OAAO,GAAGA,OAAO,CAACF,SAAS,EAAE;UAC/B;UAEA,OAAOE,OAAO;QAChB;QAEA,OAAO3B,uBAAuB;MAChC,CAAC;MACDL;IACF,CAA+D;IAE/D,OAAOmB,iBAAiB;EAC1B,CAAC,EAAE,CACDrB,WAAW,EACXG,OAAO,CAACmB,IAAI,EACZpB,QAAQ,EACRD,QAAQ,EACRI,iBAAiB,EACjBE,uBAAuB,EACvBH,MAAM,CACP,CAAC;AACJ"}