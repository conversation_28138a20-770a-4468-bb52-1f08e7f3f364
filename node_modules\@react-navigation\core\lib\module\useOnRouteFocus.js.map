{"version": 3, "names": ["React", "NavigationBuilderContext", "useOnRouteFocus", "router", "getState", "key", "sourceRouteKey", "setState", "onRouteFocus", "onRouteFocusParent", "useContext", "useCallback", "state", "result", "getStateForRouteFocus", "undefined"], "sourceRoot": "../../src", "sources": ["useOnRouteFocus.tsx"], "mappings": "AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAAM,4BAA4B;AASjE;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAe,OAKnB;EAAA,IALqD;IACvEC,MAAM;IACNC,QAAQ;IACRC,GAAG,EAAEC,cAAc;IACnBC;EACe,CAAC;EAChB,MAAM;IAAEC,YAAY,EAAEC;EAAmB,CAAC,GAAGT,KAAK,CAACU,UAAU,CAC3DT,wBAAwB,CACzB;EAED,OAAOD,KAAK,CAACW,WAAW,CACrBN,GAAW,IAAK;IACf,MAAMO,KAAK,GAAGR,QAAQ,EAAE;IACxB,MAAMS,MAAM,GAAGV,MAAM,CAACW,qBAAqB,CAACF,KAAK,EAAEP,GAAG,CAAC;IAEvD,IAAIQ,MAAM,KAAKD,KAAK,EAAE;MACpBL,QAAQ,CAACM,MAAM,CAAC;IAClB;IAEA,IAAIJ,kBAAkB,KAAKM,SAAS,IAAIT,cAAc,KAAKS,SAAS,EAAE;MACpEN,kBAAkB,CAACH,cAAc,CAAC;IACpC;EACF,CAAC,EACD,CAACF,QAAQ,EAAEK,kBAAkB,EAAEN,MAAM,EAAEI,QAAQ,EAAED,cAAc,CAAC,CACjE;AACH"}