{"version": 3, "sources": ["index.css"], "names": [], "mappings": "AAAA,UAEE,wCAA2C,CAC3C,eAAgB,CAChB,eAAgB,CAChB,WAAY,CACZ,QAAS,CACT,SACF,CACA,UACE,iBAAkB,CAClB,UAAW,CACX,qBAAsB,CACtB,4BAAgC,CAChC,gBAAiB,CACjB,kBAAmB,CACnB,WACF,CACA,SACE,YACF,CACA,YACE,gBACF,CACA,UACE,wBAAyB,CACzB,aACF,CACA,gBACE,aACF,CACA,QACE,aACF,CACA,qBACE,qBACF", "file": "ui.e31bb0bc.css", "sourceRoot": "../../src/ui", "sourcesContent": ["html,\nbody {\n  font-family: Helvetica, Verdana, sans-serif;\n  font-size: large;\n  font-weight: 200;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n}\n.shortcut {\n  border-radius: 4px;\n  color: #eee;\n  background-color: #333;\n  font-family: 'Monaco', monospace;\n  font-size: medium;\n  letter-spacing: 3px;\n  padding: 4px;\n}\n.content {\n  padding: 10px;\n}\n.reload-btn {\n  padding: 5px 10px;\n}\nbody.dark {\n  background-color: #242424;\n  color: #afafaf;\n}\n.dark .shortcut {\n  color: #c1c1c1;\n}\n.dark a {\n  color: #3b99fc;\n}\ninput[type='checkbox'] {\n  vertical-align: middle;\n}\n"]}