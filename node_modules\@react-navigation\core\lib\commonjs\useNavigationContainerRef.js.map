{"version": 3, "names": ["useNavigationContainerRef", "navigation", "React", "useRef", "current", "createNavigationContainerRef"], "sourceRoot": "../../src", "sources": ["useNavigationContainerRef.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAA0E;AAAA;AAAA;AAG3D,SAASA,yBAAyB,GAEC;EAChD,MAAMC,UAAU,GACdC,KAAK,CAACC,MAAM,CAAsD,IAAI,CAAC;EAEzE,IAAIF,UAAU,CAACG,OAAO,IAAI,IAAI,EAAE;IAC9BH,UAAU,CAACG,OAAO,GAAG,IAAAC,qCAA4B,GAAa;EAChE;EAEA,OAAOJ,UAAU,CAACG,OAAO;AAC3B"}