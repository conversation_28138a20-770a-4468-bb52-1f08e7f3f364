load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "YOGA_CXX_TARGET",
    "fb_xplat_cxx_test",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "androidtextinput",
    srcs = glob(
        ["androidtextinput/**/*.cpp"],
        exclude = glob(["tests/**/*.cpp"]),
    ),
    headers = glob(
        ["androidtextinput/**/*.h"],
        exclude = glob(["tests/**/*.h"]),
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
            ("androidtextinput/react/renderer/components/androidtextinput", "*.h"),
        ],
        prefix = "react/renderer/components/androidtextinput",
    ),
    compiler_flags_pedantic = True,
    cxx_tests = [":tests"],
    fbandroid_deps = [
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
    ],
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/folly:dynamic",
        YOGA_CXX_TARGET,
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/renderer/attributedstring:attributedstring"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("react/renderer/textlayoutmanager:textlayoutmanager"),
        react_native_xplat_target("react/renderer/components/text:text"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("react/renderer/components/image:image"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        react_native_xplat_target("react/renderer/imagemanager:imagemanager"),
        react_native_xplat_target("react/utils:utils"),
    ],
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["tests/**/*.cpp"]),
    headers = glob(["tests/**/*.h"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    contacts = ["<EMAIL>"],
    platforms = (
        # `Apple` and `Android` flavors are disabled because the module depends on `textlayoutmanager` which requires real an Emulator/Simulator to run.
        #  At the same time, the code of tests does not rely on the simulator capabilities and it would be wasteful to add `fbandroid_use_instrumentation_test = True`.
        # (Beware of this option though.)
        # ANDROID,
        # APPLE,
        CXX
    ),
    deps = [
        ":androidtextinput",
        "//xplat/third-party/gmock:gtest",
    ],
)
