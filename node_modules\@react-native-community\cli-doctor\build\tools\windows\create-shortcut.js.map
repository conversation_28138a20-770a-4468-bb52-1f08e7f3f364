{"version": 3, "names": ["createShortcut", "path", "name", "ico", "script", "script<PERSON>ath", "join", "tmpdir", "Math", "random", "writeFileSync", "executeCommand"], "sources": ["../../../src/tools/windows/create-shortcut.ts"], "sourcesContent": ["import {writeFileSync} from 'fs';\nimport {join} from 'path';\nimport {tmpdir} from 'os';\nimport {executeCommand} from './executeWinCommand';\n\ntype LnkOptions = {\n  path: string;\n  name: string;\n  ico: string;\n};\n\n/**\n * Creates a script in the user's Startup menu\n */\nexport const createShortcut = async ({path, name, ico}: LnkOptions) => {\n  // prettier-ignore\n  const script =\n`option explicit\nsub createLnk()\n    dim objShell, strStartMenuPath, objLink\n    set objShell = CreateObject(\"WScript.Shell\")\n    strStartMenuPath = objShell.SpecialFolders(\"StartMenu\")\n    set objLink = objShell.CreateShortcut(strStartMenuPath + \"\\\\\" + \"${name}.lnk\")\n    objLink.TargetPath = \"${path}\"\n    objLink.IconLocation = \"${ico}\"\n    objLink.Save\nend sub\n\ncall createLnk()`;\n\n  const scriptPath = join(tmpdir(), `shortcut-${Math.random()}.vbs`);\n  writeFileSync(scriptPath, script, 'utf-8');\n\n  await executeCommand(scriptPath);\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAQA;AACA;AACA;AACO,MAAMA,cAAc,GAAG,OAAO;EAACC,IAAI;EAAEC,IAAI;EAAEC;AAAe,CAAC,KAAK;EACrE;EACA,MAAMC,MAAM,GACb;AACD;AACA;AACA;AACA;AACA,uEAAuEF,IAAK;AAC5E,4BAA4BD,IAAK;AACjC,8BAA8BE,GAAI;AAClC;AACA;AACA;AACA,iBAAiB;EAEf,MAAME,UAAU,GAAG,IAAAC,YAAI,EAAC,IAAAC,YAAM,GAAE,EAAG,YAAWC,IAAI,CAACC,MAAM,EAAG,MAAK,CAAC;EAClE,IAAAC,mBAAa,EAACL,UAAU,EAAED,MAAM,EAAE,OAAO,CAAC;EAE1C,MAAM,IAAAO,iCAAc,EAACN,UAAU,CAAC;AAClC,CAAC;AAAC"}