{"version": 3, "sources": ["state.ts"], "names": ["NativeInterface", "InternetReachability", "PrivateTypes", "State", "constructor", "configuration", "Set", "state", "_internetReachability", "update", "convertedState", "_convertState", "_latestState", "_subscriptions", "for<PERSON>ach", "handler", "isInternetReachable", "nextState", "requestedInterface", "getCurrentState", "input", "currentState", "_fetchCurrentState", "Promise", "resolve", "add", "latest", "then", "delete", "tearDown", "_nativeEventSubscription", "remove", "clear", "_handleInternetReachabilityUpdate", "eventEmitter", "addListener", "DEVICE_CONNECTIVITY_EVENT", "_handleNativeStateUpdate"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,OAAOA,eAAP,MAA4B,mBAA5B;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AAEA,OAAO,KAAKC,YAAZ,MAA8B,gBAA9B;AAEA,eAAe,MAAMC,KAAN,CAAY;AAMzBC,EAAAA,WAAW,CAACC,aAAD,EAA4C;AAAA,sDALY,IAKZ;;AAAA,4CAJ9B,IAAIC,GAAJ,EAI8B;;AAAA,0CAHL,IAGK;;AAAA;;AAAA,sDAkBrDC,KADiC,IAExB;AACT;AACA,WAAKC,qBAAL,CAA2BC,MAA3B,CAAkCF,KAAlC,EAFS,CAIT;;;AACA,YAAMG,cAAc,GAAG,KAAKC,aAAL,CAAmBJ,KAAnB,CAAvB,CALS,CAOT;;;AACA,WAAKK,YAAL,GAAoBF,cAApB;;AACA,WAAKG,cAAL,CAAoBC,OAApB,CAA6BC,OAAD,IAAmBA,OAAO,CAACL,cAAD,CAAtD;AACD,KA7BsD;;AAAA,+DAgCrDM,mBAD0C,IAEjC;AACT,UAAI,CAAC,KAAKJ,YAAV,EAAwB;AACtB;AACD;;AAED,YAAMK,SAAS,GAAG,EAChB,GAAG,KAAKL,YADQ;AAEhBI,QAAAA;AAFgB,OAAlB;AAIA,WAAKJ,YAAL,GAAoBK,SAApB;;AACA,WAAKJ,cAAL,CAAoBC,OAApB,CAA6BC,OAAD,IAAmBA,OAAO,CAACE,SAAD,CAAtD;AACD,KA5CsD;;AAAA,gDA8C3B,MAC1BC,kBAD0B,IAEM;AAChC,YAAMX,KAAK,GAAG,MAAMP,eAAe,CAACmB,eAAhB,CAAgCD,kBAAhC,CAApB,CADgC,CAGhC;;AACA,WAAKV,qBAAL,CAA2BC,MAA3B,CAAkCF,KAAlC,EAJgC,CAKhC;;;AACA,YAAMG,cAAc,GAAG,KAAKC,aAAL,CAAmBJ,KAAnB,CAAvB;;AACA,UAAI,CAACW,kBAAL,EAAyB;AACvB,aAAKN,YAAL,GAAoBF,cAApB;;AACA,aAAKG,cAAL,CAAoBC,OAApB,CAA6BC,OAAD,IAAmBA,OAAO,CAACL,cAAD,CAAtD;AACD;;AAED,aAAOA,cAAP;AACD,KA7DsD;;AAAA,2CAgErDU,KADsB,IAEC;AACvB,UAAI,OAAOA,KAAK,CAACJ,mBAAb,KAAqC,SAAzC,EAAoD;AAClD,eAAOI,KAAP;AACD,OAFD,MAEO;AACL,eAAO,EACL,GAAGA,KADE;AAELJ,UAAAA,mBAAmB,EAAE,KAAKR,qBAAL,CAA2Ba,YAA3B;AAFhB,SAAP;AAID;AACF,KA1EsD;;AAAA,oCA6ErDH,kBADc,IAEkB;AAChC,UAAIA,kBAAJ,EAAwB;AACtB,eAAO,KAAKI,kBAAL,CAAwBJ,kBAAxB,CAAP;AACD,OAFD,MAEO,IAAI,KAAKN,YAAT,EAAuB;AAC5B,eAAOW,OAAO,CAACC,OAAR,CAAgB,KAAKZ,YAArB,CAAP;AACD,OAFM,MAEA;AACL,eAAO,KAAKU,kBAAL,EAAP;AACD;AACF,KAtFsD;;AAAA,iCAwFzCP,OAAD,IAA+C;AAC1D;AACA,WAAKF,cAAL,CAAoBY,GAApB,CAAwBV,OAAxB,EAF0D,CAI1D;;;AACA,UAAI,KAAKH,YAAT,EAAuB;AACrBG,QAAAA,OAAO,CAAC,KAAKH,YAAN,CAAP;AACD,OAFD,MAEO;AACL,aAAKc,MAAL,GAAcC,IAAd,CAAmBZ,OAAnB;AACD;AACF,KAlGsD;;AAAA,oCAoGtCA,OAAD,IAA+C;AAC7D,WAAKF,cAAL,CAAoBe,MAApB,CAA2Bb,OAA3B;AACD,KAtGsD;;AAAA,sCAwGrC,MAAY;AAC5B,UAAI,KAAKP,qBAAT,EAAgC;AAC9B,aAAKA,qBAAL,CAA2BqB,QAA3B;AACD;;AAED,UAAI,KAAKC,wBAAT,EAAmC;AACjC,aAAKA,wBAAL,CAA8BC,MAA9B;AACD;;AAED,WAAKlB,cAAL,CAAoBmB,KAApB;AACD,KAlHsD;;AACrD;AACA,SAAKxB,qBAAL,GAA6B,IAAIP,oBAAJ,CAC3BI,aAD2B,EAE3B,KAAK4B,iCAFsB,CAA7B,CAFqD,CAOrD;;AACA,SAAKH,wBAAL,GAAgC9B,eAAe,CAACkC,YAAhB,CAA6BC,WAA7B,CAC9BjC,YAAY,CAACkC,yBADiB,EAE9B,KAAKC,wBAFyB,CAAhC,CARqD,CAarD;;AACA,SAAKf,kBAAL;AACD;;AArBwB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NativeEventSubscription} from 'react-native';\nimport NativeInterface from './nativeInterface';\nimport InternetReachability from './internetReachability';\nimport * as Types from './types';\nimport * as PrivateTypes from './privateTypes';\n\nexport default class State {\n  private _nativeEventSubscription: NativeEventSubscription | null = null;\n  private _subscriptions = new Set<Types.NetInfoChangeHandler>();\n  private _latestState: Types.NetInfoState | null = null;\n  private _internetReachability: InternetReachability;\n\n  constructor(configuration: Types.NetInfoConfiguration) {\n    // Add the listener to the internet connectivity events\n    this._internetReachability = new InternetReachability(\n      configuration,\n      this._handleInternetReachabilityUpdate,\n    );\n\n    // Add the subscription to the native events\n    this._nativeEventSubscription = NativeInterface.eventEmitter.addListener(\n      PrivateTypes.DEVICE_CONNECTIVITY_EVENT,\n      this._handleNativeStateUpdate,\n    );\n\n    // Fetch the current state from the native module\n    this._fetchCurrentState();\n  }\n\n  private _handleNativeStateUpdate = (\n    state: PrivateTypes.NetInfoNativeModuleState,\n  ): void => {\n    // Update the internet reachability module\n    this._internetReachability.update(state);\n\n    // Convert the state from native to JS shape\n    const convertedState = this._convertState(state);\n\n    // Update the listeners\n    this._latestState = convertedState;\n    this._subscriptions.forEach((handler): void => handler(convertedState));\n  };\n\n  private _handleInternetReachabilityUpdate = (\n    isInternetReachable: boolean | null | undefined,\n  ): void => {\n    if (!this._latestState) {\n      return;\n    }\n\n    const nextState = {\n      ...this._latestState,\n      isInternetReachable,\n    } as Types.NetInfoState;\n    this._latestState = nextState;\n    this._subscriptions.forEach((handler): void => handler(nextState));\n  };\n\n  public _fetchCurrentState = async (\n    requestedInterface?: string,\n  ): Promise<Types.NetInfoState> => {\n    const state = await NativeInterface.getCurrentState(requestedInterface);\n\n    // Update the internet reachability module\n    this._internetReachability.update(state);\n    // Convert and store the new state\n    const convertedState = this._convertState(state);\n    if (!requestedInterface) {\n      this._latestState = convertedState;\n      this._subscriptions.forEach((handler): void => handler(convertedState));\n    }\n\n    return convertedState;\n  };\n\n  private _convertState = (\n    input: PrivateTypes.NetInfoNativeModuleState,\n  ): Types.NetInfoState => {\n    if (typeof input.isInternetReachable === 'boolean') {\n      return input as Types.NetInfoState;\n    } else {\n      return {\n        ...input,\n        isInternetReachable: this._internetReachability.currentState(),\n      } as Types.NetInfoState;\n    }\n  };\n\n  public latest = (\n    requestedInterface?: string,\n  ): Promise<Types.NetInfoState> => {\n    if (requestedInterface) {\n      return this._fetchCurrentState(requestedInterface);\n    } else if (this._latestState) {\n      return Promise.resolve(this._latestState);\n    } else {\n      return this._fetchCurrentState();\n    }\n  };\n\n  public add = (handler: Types.NetInfoChangeHandler): void => {\n    // Add the subscription handler to our set\n    this._subscriptions.add(handler);\n\n    // Send it the latest data we have\n    if (this._latestState) {\n      handler(this._latestState);\n    } else {\n      this.latest().then(handler);\n    }\n  };\n\n  public remove = (handler: Types.NetInfoChangeHandler): void => {\n    this._subscriptions.delete(handler);\n  };\n\n  public tearDown = (): void => {\n    if (this._internetReachability) {\n      this._internetReachability.tearDown();\n    }\n\n    if (this._nativeEventSubscription) {\n      this._nativeEventSubscription.remove();\n    }\n\n    this._subscriptions.clear();\n  };\n}\n"]}