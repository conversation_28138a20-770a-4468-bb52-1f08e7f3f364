{"version": 3, "names": ["runPodInstall", "loader", "directory", "shouldHandleRepoUpdate", "start", "chalk", "dim", "execa", "error", "stderr", "stdout", "includes", "runPodUpdate", "fail", "logger", "CLIError", "link", "docs", "log", "underline", "runSudo", "command", "Promise", "resolve", "reject", "sudo", "exec", "name", "installCocoaPodsWithGem", "options", "_error", "join", "installCocoaPods", "stop", "succeed", "installPods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fs", "existsSync", "process", "chdir", "hasPods", "runBundleInstall", "e", "info"], "sources": ["../../src/tools/installPods.ts"], "sourcesContent": ["import fs from 'fs';\nimport execa from 'execa';\nimport chalk from 'chalk';\nimport {\n  logger,\n  NoopLoader,\n  link,\n  CLIError,\n} from '@react-native-community/cli-tools';\nimport sudo from 'sudo-prompt';\nimport runBundleInstall from './runBundleInstall';\nimport {Loader} from '../types';\n\nasync function runPodInstall(\n  loader: Loader,\n  directory: string,\n  shouldHandleRepoUpdate: boolean = true,\n) {\n  try {\n    loader.start(\n      `Installing CocoaPods dependencies ${chalk.dim(\n        '(this may take a few minutes)',\n      )}`,\n    );\n\n    await execa('bundle', ['exec', 'pod', 'install']);\n  } catch (error) {\n    // \"pod\" command outputs errors to stdout (at least some of them)\n    const stderr = (error as any).stderr || (error as any).stdout;\n\n    /**\n     * If CocoaPods failed due to repo being out of date, it will\n     * include the update command in the error message.\n     *\n     * `shouldHandleRepoUpdate` will be set to `false` to\n     * prevent infinite loop (unlikely scenario)\n     */\n    if (stderr.includes('pod repo update') && shouldHandleRepoUpdate) {\n      await runPodUpdate(loader);\n      await runPodInstall(loader, directory, false);\n    } else {\n      loader.fail();\n      logger.error(stderr);\n\n      throw new CLIError(\n        `Looks like your iOS environment is not properly set. Please go to ${link.docs(\n          'environment-setup',\n        )} and follow the React Native CLI QuickStart guide for macOS and iOS.`,\n      );\n    }\n  }\n}\n\nasync function runPodUpdate(loader: Loader) {\n  try {\n    loader.start(\n      `Updating CocoaPods repositories ${chalk.dim(\n        '(this may take a few minutes)',\n      )}`,\n    );\n    await execa('pod', ['repo', 'update']);\n  } catch (error) {\n    // \"pod\" command outputs errors to stdout (at least some of them)\n    logger.log((error as any).stderr || (error as any).stdout);\n    loader.fail();\n\n    throw new CLIError(\n      `Failed to update CocoaPods repositories for iOS project.\\nPlease try again manually: \"pod repo update\".\\nCocoaPods documentation: ${chalk.dim.underline(\n        'https://cocoapods.org/',\n      )}`,\n    );\n  }\n}\n\nfunction runSudo(command: string): Promise<void> {\n  return new Promise((resolve, reject) => {\n    sudo.exec(command, {name: 'React Native CLI'}, (error) => {\n      if (error) {\n        reject(error);\n      }\n\n      resolve();\n    });\n  });\n}\n\nasync function installCocoaPodsWithGem() {\n  const options = ['install', 'cocoapods', '--no-document'];\n\n  try {\n    // First attempt to install `cocoapods`\n    await execa('gem', options);\n  } catch (_error) {\n    // If that doesn't work then try with sudo\n    await runSudo(`gem ${options.join(' ')}`);\n  }\n}\n\nasync function installCocoaPods(loader: Loader) {\n  loader.stop();\n\n  loader.start('Installing CocoaPods');\n\n  try {\n    await installCocoaPodsWithGem();\n\n    return loader.succeed();\n  } catch (error) {\n    loader.fail();\n    logger.error((error as any).stderr);\n\n    throw new CLIError(\n      `An error occured while trying to install CocoaPods, which is required by this template.\\nPlease try again manually: sudo gem install cocoapods.\\nCocoaPods documentation: ${chalk.dim.underline(\n        'https://cocoapods.org/',\n      )}`,\n    );\n  }\n}\n\nasync function installPods({\n  directory,\n  loader,\n}: {\n  directory: string;\n  loader?: Loader;\n}) {\n  loader = loader || new NoopLoader();\n  try {\n    if (!fs.existsSync('ios')) {\n      return;\n    }\n\n    process.chdir('ios');\n\n    const hasPods = fs.existsSync('Podfile');\n\n    if (!hasPods) {\n      return;\n    }\n\n    if (fs.existsSync('../Gemfile')) {\n      await runBundleInstall(loader);\n    }\n\n    try {\n      // Check if \"pod\" is available and usable. It happens that there are\n      // multiple versions of \"pod\" command and even though it's there, it exits\n      // with a failure\n      await execa('pod', ['--version']);\n    } catch (e) {\n      loader.info();\n      await installCocoaPods(loader);\n    }\n\n    await runPodInstall(loader, directory);\n  } finally {\n    process.chdir('..');\n  }\n}\n\nexport {runSudo, installCocoaPods};\n\nexport default installPods;\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAkD;AAGlD,eAAeA,aAAa,CAC1BC,MAAc,EACdC,SAAiB,EACjBC,sBAA+B,GAAG,IAAI,EACtC;EACA,IAAI;IACFF,MAAM,CAACG,KAAK,CACT,qCAAoCC,gBAAK,CAACC,GAAG,CAC5C,+BAA+B,CAC/B,EAAC,CACJ;IAED,MAAM,IAAAC,gBAAK,EAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd;IACA,MAAMC,MAAM,GAAID,KAAK,CAASC,MAAM,IAAKD,KAAK,CAASE,MAAM;;IAE7D;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAID,MAAM,CAACE,QAAQ,CAAC,iBAAiB,CAAC,IAAIR,sBAAsB,EAAE;MAChE,MAAMS,YAAY,CAACX,MAAM,CAAC;MAC1B,MAAMD,aAAa,CAACC,MAAM,EAAEC,SAAS,EAAE,KAAK,CAAC;IAC/C,CAAC,MAAM;MACLD,MAAM,CAACY,IAAI,EAAE;MACbC,kBAAM,CAACN,KAAK,CAACC,MAAM,CAAC;MAEpB,MAAM,KAAIM,oBAAQ,EACf,qEAAoEC,gBAAI,CAACC,IAAI,CAC5E,mBAAmB,CACnB,sEAAqE,CACxE;IACH;EACF;AACF;AAEA,eAAeL,YAAY,CAACX,MAAc,EAAE;EAC1C,IAAI;IACFA,MAAM,CAACG,KAAK,CACT,mCAAkCC,gBAAK,CAACC,GAAG,CAC1C,+BAA+B,CAC/B,EAAC,CACJ;IACD,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;EACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd;IACAM,kBAAM,CAACI,GAAG,CAAEV,KAAK,CAASC,MAAM,IAAKD,KAAK,CAASE,MAAM,CAAC;IAC1DT,MAAM,CAACY,IAAI,EAAE;IAEb,MAAM,KAAIE,oBAAQ,EACf,qIAAoIV,gBAAK,CAACC,GAAG,CAACa,SAAS,CACtJ,wBAAwB,CACxB,EAAC,CACJ;EACH;AACF;AAEA,SAASC,OAAO,CAACC,OAAe,EAAiB;EAC/C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtCC,qBAAI,CAACC,IAAI,CAACL,OAAO,EAAE;MAACM,IAAI,EAAE;IAAkB,CAAC,EAAGnB,KAAK,IAAK;MACxD,IAAIA,KAAK,EAAE;QACTgB,MAAM,CAAChB,KAAK,CAAC;MACf;MAEAe,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,eAAeK,uBAAuB,GAAG;EACvC,MAAMC,OAAO,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC;EAEzD,IAAI;IACF;IACA,MAAM,IAAAtB,gBAAK,EAAC,KAAK,EAAEsB,OAAO,CAAC;EAC7B,CAAC,CAAC,OAAOC,MAAM,EAAE;IACf;IACA,MAAMV,OAAO,CAAE,OAAMS,OAAO,CAACE,IAAI,CAAC,GAAG,CAAE,EAAC,CAAC;EAC3C;AACF;AAEA,eAAeC,gBAAgB,CAAC/B,MAAc,EAAE;EAC9CA,MAAM,CAACgC,IAAI,EAAE;EAEbhC,MAAM,CAACG,KAAK,CAAC,sBAAsB,CAAC;EAEpC,IAAI;IACF,MAAMwB,uBAAuB,EAAE;IAE/B,OAAO3B,MAAM,CAACiC,OAAO,EAAE;EACzB,CAAC,CAAC,OAAO1B,KAAK,EAAE;IACdP,MAAM,CAACY,IAAI,EAAE;IACbC,kBAAM,CAACN,KAAK,CAAEA,KAAK,CAASC,MAAM,CAAC;IAEnC,MAAM,KAAIM,oBAAQ,EACf,6KAA4KV,gBAAK,CAACC,GAAG,CAACa,SAAS,CAC9L,wBAAwB,CACxB,EAAC,CACJ;EACH;AACF;AAEA,eAAegB,WAAW,CAAC;EACzBjC,SAAS;EACTD;AAIF,CAAC,EAAE;EACDA,MAAM,GAAGA,MAAM,IAAI,KAAImC,sBAAU,GAAE;EACnC,IAAI;IACF,IAAI,CAACC,aAAE,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;MACzB;IACF;IAEAC,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC;IAEpB,MAAMC,OAAO,GAAGJ,aAAE,CAACC,UAAU,CAAC,SAAS,CAAC;IAExC,IAAI,CAACG,OAAO,EAAE;MACZ;IACF;IAEA,IAAIJ,aAAE,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;MAC/B,MAAM,IAAAI,yBAAgB,EAACzC,MAAM,CAAC;IAChC;IAEA,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAAM,gBAAK,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOoC,CAAC,EAAE;MACV1C,MAAM,CAAC2C,IAAI,EAAE;MACb,MAAMZ,gBAAgB,CAAC/B,MAAM,CAAC;IAChC;IAEA,MAAMD,aAAa,CAACC,MAAM,EAAEC,SAAS,CAAC;EACxC,CAAC,SAAS;IACRqC,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC;EACrB;AACF;AAAC,eAIcL,WAAW;AAAA"}