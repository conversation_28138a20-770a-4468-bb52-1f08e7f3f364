{"version": 3, "names": ["downloadAndUnzip", "loader", "downloadUrl", "component", "installPath", "start", "installer", "fetchToTemp", "text", "unzip", "deleteFile"], "sources": ["../../src/tools/downloadAndUnzip.ts"], "sourcesContent": ["import {fetchToTemp} from '@react-native-community/cli-tools';\nimport {Loader} from '../types';\nimport {unzip} from './unzip';\nimport {deleteFile} from './deleteFile';\n\n/**\n * Downloads `downloadUrl` and unzips the contents to `installPath` while\n * updating the message of `loader` at each step.\n */\nexport const downloadAndUnzip = async ({\n  loader,\n  downloadUrl,\n  component,\n  installPath,\n}: {\n  loader: Loader;\n  component: string;\n  downloadUrl: string;\n  installPath: string;\n}) => {\n  loader.start(\n    `Downloading ${component} from \"${downloadUrl}\" (this may take a few minutes)`,\n  );\n\n  const installer = await fetchToTemp(downloadUrl);\n\n  loader.text = `Installing ${component} in \"${installPath}\"`;\n  try {\n    await unzip(installer, installPath);\n  } finally {\n    await deleteFile(installer);\n  }\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACO,MAAMA,gBAAgB,GAAG,OAAO;EACrCC,MAAM;EACNC,WAAW;EACXC,SAAS;EACTC;AAMF,CAAC,KAAK;EACJH,MAAM,CAACI,KAAK,CACT,eAAcF,SAAU,UAASD,WAAY,iCAAgC,CAC/E;EAED,MAAMI,SAAS,GAAG,MAAM,IAAAC,uBAAW,EAACL,WAAW,CAAC;EAEhDD,MAAM,CAACO,IAAI,GAAI,cAAaL,SAAU,QAAOC,WAAY,GAAE;EAC3D,IAAI;IACF,MAAM,IAAAK,YAAK,EAACH,SAAS,EAAEF,WAAW,CAAC;EACrC,CAAC,SAAS;IACR,MAAM,IAAAM,sBAAU,EAACJ,SAAS,CAAC;EAC7B;AACF,CAAC;AAAC"}