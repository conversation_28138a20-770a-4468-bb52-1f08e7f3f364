"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var Months_js_1 = __importDefault(require("./YearView/Months.js"));
var propTypes_js_1 = require("./shared/propTypes.js");
/**
 * Displays a given year.
 */
var YearView = function YearView(props) {
    function renderMonths() {
        return react_1.default.createElement(Months_js_1.default, __assign({}, props));
    }
    return react_1.default.createElement("div", { className: "react-calendar__year-view" }, renderMonths());
};
YearView.propTypes = __assign({}, propTypes_js_1.tileGroupProps);
exports.default = YearView;
