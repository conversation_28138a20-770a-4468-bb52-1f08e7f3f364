"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2022_error = void 0;
const base_config_1 = require("./base-config");
exports.es2022_error = {
    ErrorOptions: base_config_1.TYPE,
    Error: base_config_1.TYPE,
    ErrorConstructor: base_config_1.TYPE,
    EvalErrorConstructor: base_config_1.TYPE,
    RangeErrorConstructor: base_config_1.TYPE,
    ReferenceErrorConstructor: base_config_1.TYPE,
    SyntaxErrorConstructor: base_config_1.TYPE,
    TypeErrorConstructor: base_config_1.TYPE,
    URIErrorConstructor: base_config_1.TYPE,
    AggregateErrorConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2022.error.js.map