{"version": 3, "names": ["usePreventRemove", "preventRemove", "callback", "id", "React", "useState", "nanoid", "navigation", "useNavigation", "key", "routeKey", "useRoute", "setPreventRemove", "usePreventRemoveContext", "useEffect", "beforeRemoveListener", "useLatestCallback", "e", "preventDefault", "data", "addListener"], "sourceRoot": "../../src", "sources": ["usePreventRemove.tsx"], "mappings": ";;;;;;AACA;AACA;AACA;AAGA;AACA;AACA;AAAkC;AAAA;AAAA;AAElC;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,gBAAgB,CACtCC,aAAsB,EACtBC,QAAmE,EACnE;EACA,MAAM,CAACC,EAAE,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,MAAM,IAAAC,iBAAM,GAAE,CAAC;EAE3C,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAAE;EAClC,MAAM;IAAEC,GAAG,EAAEC;EAAS,CAAC,GAAG,IAAAC,iBAAQ,GAAE;EAEpC,MAAM;IAAEC;EAAiB,CAAC,GAAG,IAAAC,gCAAuB,GAAE;EAEtDT,KAAK,CAACU,SAAS,CAAC,MAAM;IACpBF,gBAAgB,CAACT,EAAE,EAAEO,QAAQ,EAAET,aAAa,CAAC;IAC7C,OAAO,MAAM;MACXW,gBAAgB,CAACT,EAAE,EAAEO,QAAQ,EAAE,KAAK,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACE,gBAAgB,EAAET,EAAE,EAAEO,QAAQ,EAAET,aAAa,CAAC,CAAC;EAEnD,MAAMc,oBAAoB,GAAG,IAAAC,0BAAiB,EAE3CC,CAAC,IAAK;IACP,IAAI,CAAChB,aAAa,EAAE;MAClB;IACF;IAEAgB,CAAC,CAACC,cAAc,EAAE;IAElBhB,QAAQ,CAAC;MAAEiB,IAAI,EAAEF,CAAC,CAACE;IAAK,CAAC,CAAC;EAC5B,CAAC,CAAC;EAEFf,KAAK,CAACU,SAAS,CACb,MAAMP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,WAAW,CAAC,cAAc,EAAEL,oBAAoB,CAAC,EACnE,CAACR,UAAU,EAAEQ,oBAAoB,CAAC,CACnC;AACH"}