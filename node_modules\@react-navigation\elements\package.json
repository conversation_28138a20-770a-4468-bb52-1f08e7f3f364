{"name": "@react-navigation/elements", "description": "UI Components for React Navigation", "version": "1.3.31", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/elements"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "lib/commonjs/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "module": "lib/module/index.js", "types": "lib/typescript/src/index.d.ts", "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "devDependencies": {"@react-native-masked-view/masked-view": "0.2.9", "@react-navigation/native": "^6.1.18", "@testing-library/react-native": "^11.5.0", "@types/react": "~18.0.27", "@types/react-native": "~0.71.3", "del-cli": "^5.0.0", "react": "18.2.0", "react-native": "0.71.8", "react-native-builder-bob": "^0.20.4", "typescript": "^4.9.4"}, "peerDependencies": {"@react-navigation/native": "^6.0.0", "react": "*", "react-native": "*", "react-native-safe-area-context": ">= 3.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "a2269aa24bc0c2ceec227dbdcc4fa4c38902396a"}