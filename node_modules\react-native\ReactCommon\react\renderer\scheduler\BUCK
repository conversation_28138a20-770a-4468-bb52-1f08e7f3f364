load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "scheduler",
    srcs = glob(
        ["**/*.cpp"],
        exclude = glob(["tests/**/*.cpp"]),
    ),
    headers = glob(
        ["**/*.h"],
        exclude = glob(["tests/**/*.h"]),
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/renderer/scheduler",
    ),
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/jsi:jsi",
        react_native_xplat_target("react/config:config"),
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/mounting:mounting"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/runtimescheduler:runtimescheduler"),
        react_native_xplat_target("react/renderer/templateprocessor:templateprocessor"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/components/root:root"),
        react_native_xplat_target("react/utils:utils"),
    ],
)
