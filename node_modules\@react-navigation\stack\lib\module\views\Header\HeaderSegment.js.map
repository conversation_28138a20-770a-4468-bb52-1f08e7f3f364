{"version": 3, "names": ["getDefaultHeaderHeight", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "Platform", "StyleSheet", "memoize", "HeaderSegment", "props", "leftLabelLayout", "setLeftLabelLayout", "useState", "undefined", "titleLayout", "setTitleLayout", "handleTitleLayout", "e", "height", "width", "nativeEvent", "layout", "handleLeftLabelLayout", "getInterpolatedStyle", "styleInterpolator", "current", "next", "headerHeight", "progress", "layouts", "header", "screen", "title", "leftLabel", "modal", "onGoBack", "headerTitle", "headerLeft", "left", "headerRight", "right", "headerBackImage", "headerBackTitle", "headerBackTitleVisible", "OS", "headerTruncatedBackTitle", "headerBackAccessibilityLabel", "headerBackTestID", "headerBackAllowFontScaling", "headerBackTitleStyle", "headerTitleContainerStyle", "headerLeftContainerStyle", "headerRightContainerStyle", "headerBackgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerStatusBarHeight", "rest", "defaultHeight", "flatten", "titleStyle", "leftButtonStyle", "leftLabelStyle", "rightButtonStyle", "backgroundStyle", "backImage", "accessibilityLabel", "testID", "allowFontScaling", "onPress", "label", "truncatedLabel", "labelStyle", "onLabelLayout", "screenLayout", "canGoBack", "Boolean", "onLayout"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderSegment.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EACtBC,MAAM,EACNC,gBAAgB,EAEhBC,WAAW,QACN,4BAA4B;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAGEC,QAAQ,EACRC,UAAU,QAEL,cAAc;AAQrB,OAAOC,OAAO,MAAM,qBAAqB;AAYzC,eAAe,SAASC,aAAa,CAACC,KAAY,EAAE;EAClD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAE1DC,SAAS,CAAC;EAEZ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,KAAK,CAACQ,QAAQ,CAClDC,SAAS,CACV;EAED,MAAMG,iBAAiB,GAAIC,CAAoB,IAAK;IAClD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9CN,cAAc,CAAED,WAAW,IAAK;MAC9B,IACEA,WAAW,IACXI,MAAM,KAAKJ,WAAW,CAACI,MAAM,IAC7BC,KAAK,KAAKL,WAAW,CAACK,KAAK,EAC3B;QACA,OAAOL,WAAW;MACpB;MAEA,OAAO;QAAEI,MAAM;QAAEC;MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,qBAAqB,GAAIL,CAAoB,IAAK;IACtD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9C,IACEX,eAAe,IACfQ,MAAM,KAAKR,eAAe,CAACQ,MAAM,IACjCC,KAAK,KAAKT,eAAe,CAACS,KAAK,EAC/B;MACA;IACF;IAEAR,kBAAkB,CAAC;MAAEO,MAAM;MAAEC;IAAM,CAAC,CAAC;EACvC,CAAC;EAED,MAAMI,oBAAoB,GAAGhB,OAAO,CAClC,CACEiB,iBAA+C,EAC/CH,MAAc,EACdI,OAA+C,EAC/CC,IAAwD,EACxDZ,WAA+B,EAC/BJ,eAAmC,EACnCiB,YAAoB,KAEpBH,iBAAiB,CAAC;IAChBC,OAAO,EAAE;MAAEG,QAAQ,EAAEH;IAAQ,CAAC;IAC9BC,IAAI,EAAEA,IAAI,IAAI;MAAEE,QAAQ,EAAEF;IAAK,CAAC;IAChCG,OAAO,EAAE;MACPC,MAAM,EAAE;QACNZ,MAAM,EAAES,YAAY;QACpBR,KAAK,EAAEE,MAAM,CAACF;MAChB,CAAC;MACDY,MAAM,EAAEV,MAAM;MACdW,KAAK,EAAElB,WAAW;MAClBmB,SAAS,EAAEvB;IACb;EACF,CAAC,CAAC,CACL;EAED,MAAM;IACJkB,QAAQ;IACRP,MAAM;IACNa,KAAK;IACLC,QAAQ;IACRC,WAAW,EAAEJ,KAAK;IAClBK,UAAU,EAAEC,IAAI,GAAGH,QAAQ,GACtB1B,KAA4B,iBAAK,oBAAC,gBAAgB,EAAKA,KAAK,CAAI,GACjEI,SAAS;IACb0B,WAAW,EAAEC,KAAK;IAClBC,eAAe;IACfC,eAAe;IACfC,sBAAsB,GAAGtC,QAAQ,CAACuC,EAAE,KAAK,KAAK;IAC9CC,wBAAwB;IACxBC,4BAA4B;IAC5BC,gBAAgB;IAChBC,0BAA0B;IAC1BC,oBAAoB;IACpBC,yBAAyB;IACzBC,wBAAwB;IACxBC,yBAAyB;IACzBC,8BAA8B;IAC9BC,WAAW,EAAEC,iBAAiB;IAC9BC,qBAAqB;IACrBhC,iBAAiB;IACjB,GAAGiC;EACL,CAAC,GAAGhD,KAAK;EAET,MAAMiD,aAAa,GAAG1D,sBAAsB,CAC1CqB,MAAM,EACNa,KAAK,EACLsB,qBAAqB,CACtB;EAED,MAAM;IAAEtC,MAAM,GAAGwC;EAAc,CAAC,GAAGpD,UAAU,CAACqD,OAAO,CACnDJ,iBAAiB,IAAI,CAAC,CAAC,CACX;EAEd,MAAM;IACJK,UAAU;IACVC,eAAe;IACfC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC,GAAGzC,oBAAoB,CACtBC,iBAAiB,EACjBH,MAAM,EACNO,QAAQ,CAACH,OAAO,EAChBG,QAAQ,CAACF,IAAI,EACbZ,WAAW,EACX4B,eAAe,GAAGhC,eAAe,GAAGG,SAAS,EAC7C,OAAOK,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGwC,aAAa,CACpD;EAED,MAAMrB,UAA4C,GAAGC,IAAI,GACpD7B,KAAK,IACJ6B,IAAI,CAAC;IACH,GAAG7B,KAAK;IACRwD,SAAS,EAAExB,eAAe;IAC1ByB,kBAAkB,EAAEpB,4BAA4B;IAChDqB,MAAM,EAAEpB,gBAAgB;IACxBqB,gBAAgB,EAAEpB,0BAA0B;IAC5CqB,OAAO,EAAElC,QAAQ;IACjBmC,KAAK,EAAE5B,eAAe;IACtB6B,cAAc,EAAE1B,wBAAwB;IACxC2B,UAAU,EAAE,CAACV,cAAc,EAAEb,oBAAoB,CAAC;IAClDwB,aAAa,EAAEnD,qBAAqB;IACpCoD,YAAY,EAAErD,MAAM;IACpBP,WAAW;IACX6D,SAAS,EAAEC,OAAO,CAACzC,QAAQ;EAC7B,CAAC,CAAC,GACJtB,SAAS;EAEb,MAAM0B,WAA8C,GAAGC,KAAK,GACvD/B,KAAK,IACJ+B,KAAK,CAAC;IACJ,GAAG/B,KAAK;IACRkE,SAAS,EAAEC,OAAO,CAACzC,QAAQ;EAC7B,CAAC,CAAC,GACJtB,SAAS;EAEb,MAAMuB,WAA8C,GAClD,OAAOJ,KAAK,KAAK,UAAU,GACtBvB,KAAK,iBAAK,oBAAC,WAAW,eAAKA,KAAK;IAAE,QAAQ,EAAEO;EAAkB,GAAG,GACjEP,KAAK,IAAKuB,KAAK,CAAC;IAAE,GAAGvB,KAAK;IAAEoE,QAAQ,EAAE7D;EAAkB,CAAC,CAAC;EAEjE,oBACE,oBAAC,MAAM;IACL,KAAK,EAAEkB,KAAM;IACb,MAAM,EAAEb,MAAO;IACf,WAAW,EAAEe,WAAY;IACzB,UAAU,EAAEC,UAAW;IACvB,sBAAsB,EAAEM,sBAAuB;IAC/C,WAAW,EAAEJ,WAAY;IACzB,yBAAyB,EAAE,CAACqB,UAAU,EAAEV,yBAAyB,CAAE;IACnE,wBAAwB,EAAE,CAACW,eAAe,EAAEV,wBAAwB,CAAE;IACtE,yBAAyB,EAAE,CAACY,gBAAgB,EAAEX,yBAAyB,CAAE;IACzE,8BAA8B,EAAE,CAC9BY,eAAe,EACfX,8BAA8B,CAC9B;IACF,WAAW,EAAEE,iBAAkB;IAC/B,qBAAqB,EAAEC;EAAsB,GACzCC,IAAI,EACR;AAEN"}