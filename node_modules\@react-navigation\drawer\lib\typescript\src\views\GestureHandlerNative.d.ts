import { PanGestureHandlerProperties } from 'react-native-gesture-handler';
export declare function PanGestureHandler(props: PanGestureHandlerProperties): JSX.Element;
export type { PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
export { GestureHandlerRootView, State as GestureState, TapGestureHandler, } from 'react-native-gesture-handler';
//# sourceMappingURL=GestureHandlerNative.d.ts.map