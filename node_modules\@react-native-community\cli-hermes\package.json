{"name": "@react-native-community/cli-hermes", "version": "11.4.1", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-platform-android": "11.4.1", "@react-native-community/cli-tools": "11.4.1", "chalk": "^4.1.2", "hermes-profile-transformer": "^0.0.6"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "11.4.1", "@types/ip": "^1.1.0"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-hermes", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-hermes"}, "gitHead": "bf17602f002313980865fc5557fac32923fcd1bd"}