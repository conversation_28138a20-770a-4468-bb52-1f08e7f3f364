{"version": 3, "names": ["projectCommands", "metroCommands", "configCommands", "cleanCommands", "clean", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "upgrade", "profileHermes", "detachedCommands", "init", "doctor"], "sources": ["../../src/commands/index.ts"], "sourcesContent": ["import {Command, DetachedCommand} from '@react-native-community/cli-types';\nimport {commands as cleanCommands} from '@react-native-community/cli-clean';\nimport {commands as doctorCommands} from '@react-native-community/cli-doctor';\nimport {commands as configCommands} from '@react-native-community/cli-config';\nimport {commands as metroCommands} from '@react-native-community/cli-plugin-metro';\nimport profileHermes from '@react-native-community/cli-hermes';\nimport upgrade from './upgrade/upgrade';\nimport init from './init';\n\nexport const projectCommands = [\n  ...metroCommands,\n  ...configCommands,\n  cleanCommands.clean,\n  doctorCommands.info,\n  upgrade,\n  profileHermes,\n] as Command[];\n\nexport const detachedCommands = [\n  init,\n  doctorCommands.doctor,\n] as DetachedCommand[];\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA0B;AAEnB,MAAMA,eAAe,GAAG,CAC7B,GAAGC,0BAAa,EAChB,GAAGC,qBAAc,EACjBC,oBAAa,CAACC,KAAK,EACnBC,qBAAc,CAACC,IAAI,EACnBC,gBAAO,EACPC,oBAAa,CACD;AAAC;AAER,MAAMC,gBAAgB,GAAG,CAC9BC,aAAI,EACJL,qBAAc,CAACM,MAAM,CACD;AAAC"}