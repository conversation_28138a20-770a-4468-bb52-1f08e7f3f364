{"version": 3, "names": ["getProjectInfo", "out", "execa", "sync", "stdout", "project", "JSON", "parse", "error", "message", "includes", "match", "err", "Error"], "sources": ["../../src/tools/getProjectInfo.ts"], "sourcesContent": ["import execa from 'execa';\nimport {IosProjectInfo} from '../types';\n\nexport function getProjectInfo(): IosProjectInfo {\n  try {\n    const out = execa.sync('xcodebuild', ['-list', '-json']).stdout;\n    const {project} = JSON.parse(out);\n    return project;\n  } catch (error) {\n    if (\n      (error as Error)?.message &&\n      (error as Error).message.includes('xcodebuild: error:')\n    ) {\n      const match = (error as Error).message.match(/xcodebuild: error: (.*)/);\n      const err = match ? match[0] : error;\n      throw new Error(err as any);\n    }\n    throw new Error(error as any);\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAGnB,SAASA,cAAc,GAAmB;EAC/C,IAAI;IACF,MAAMC,GAAG,GAAGC,gBAAK,CAACC,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAACC,MAAM;IAC/D,MAAM;MAACC;IAAO,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;IACjC,OAAOI,OAAO;EAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;IAAA;IACd,IACE,SAACA,KAAK,yCAAN,KAAkBC,OAAO,KACxBD,KAAK,CAAWC,OAAO,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EACvD;MACA,MAAMC,KAAK,GAAIH,KAAK,CAAWC,OAAO,CAACE,KAAK,CAAC,yBAAyB,CAAC;MACvE,MAAMC,GAAG,GAAGD,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGH,KAAK;MACpC,MAAM,IAAIK,KAAK,CAACD,GAAG,CAAQ;IAC7B;IACA,MAAM,IAAIC,KAAK,CAACL,KAAK,CAAQ;EAC/B;AACF"}