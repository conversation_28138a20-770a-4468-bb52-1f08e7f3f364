{"version": 3, "names": ["createNavigatorFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "warnOnce", "BottomTabView", "BottomTabNavigator", "id", "initialRouteName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "screenListeners", "screenOptions", "sceneContainerStyle", "restWithDeprecated", "lazy", "tabBarOptions", "rest", "defaultScreenOptions", "Object", "assign", "tabBarHideOnKeyboard", "keyboardHidesTabBar", "tabBarActiveTintColor", "activeTintColor", "tabBarInactiveTintColor", "inactiveTintColor", "tabBarActiveBackgroundColor", "activeBackgroundColor", "tabBarInactiveBackgroundColor", "inactiveBackgroundColor", "tabBarAllowFontScaling", "allowFontScaling", "tabBarShowLabel", "showLabel", "tabBarLabelStyle", "labelStyle", "tabBarIconStyle", "iconStyle", "tabBarItemStyle", "tabStyle", "tabBarLabelPosition", "labelPosition", "adaptive", "undefined", "tabBarStyle", "display", "tabBarVisible", "keys", "for<PERSON>ach", "key", "JSON", "stringify", "state", "descriptors", "navigation", "NavigationContent"], "sourceRoot": "../../../src", "sources": ["navigators/createBottomTabNavigator.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EAKtBC,SAAS,EAETC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAOhC,OAAOC,aAAa,MAAM,wBAAwB;AAWlD,SAASC,kBAAkB,OASjB;EAAA,IATkB;IAC1BC,EAAE;IACFC,gBAAgB;IAChBC,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACbC,mBAAmB;IACnB,GAAGC;EACE,CAAC;EACN,MAAM;IACJ;IACAC,IAAI;IACJ;IACAC,aAAa;IACb,GAAGC;EACL,CAAC,GAAGH,kBAAkB;EAEtB,IAAII,oBAAgD,GAAG,CAAC,CAAC;EAEzD,IAAIF,aAAa,EAAE;IACjBG,MAAM,CAACC,MAAM,CAACF,oBAAoB,EAAE;MAClCG,oBAAoB,EAAEL,aAAa,CAACM,mBAAmB;MACvDC,qBAAqB,EAAEP,aAAa,CAACQ,eAAe;MACpDC,uBAAuB,EAAET,aAAa,CAACU,iBAAiB;MACxDC,2BAA2B,EAAEX,aAAa,CAACY,qBAAqB;MAChEC,6BAA6B,EAAEb,aAAa,CAACc,uBAAuB;MACpEC,sBAAsB,EAAEf,aAAa,CAACgB,gBAAgB;MACtDC,eAAe,EAAEjB,aAAa,CAACkB,SAAS;MACxCC,gBAAgB,EAAEnB,aAAa,CAACoB,UAAU;MAC1CC,eAAe,EAAErB,aAAa,CAACsB,SAAS;MACxCC,eAAe,EAAEvB,aAAa,CAACwB,QAAQ;MACvCC,mBAAmB,EACjBzB,aAAa,CAAC0B,aAAa,KAC1B1B,aAAa,CAAC2B,QAAQ,KAAK,KAAK,GAAG,YAAY,GAAGC,SAAS,CAAC;MAC/DC,WAAW,EAAE,CACX;QAAEC,OAAO,EAAE9B,aAAa,CAAC+B,aAAa,GAAG,MAAM,GAAG;MAAO,CAAC,EAC1D7B,oBAAoB,CAAC2B,WAAW;IAEpC,CAAC,CAAC;IAGA1B,MAAM,CAAC6B,IAAI,CAAC9B,oBAAoB,CAAC,CACjC+B,OAAO,CAAEC,GAAG,IAAK;MACjB,IAAIhC,oBAAoB,CAACgC,GAAG,CAAC,KAAKN,SAAS,EAAE;QAC3C;QACA,OAAO1B,oBAAoB,CAACgC,GAAG,CAAC;MAClC;IACF,CAAC,CAAC;IAEF9C,QAAQ,CACNY,aAAa,EACZ,4LAA2LmC,IAAI,CAACC,SAAS,CACxMlC,oBAAoB,EACpB,IAAI,EACJ,CAAC,CACD,yFAAwF,CAC3F;EACH;EAEA,IAAI,OAAOH,IAAI,KAAK,SAAS,EAAE;IAC7BG,oBAAoB,CAACH,IAAI,GAAGA,IAAI;IAEhCX,QAAQ,CACN,IAAI,EACH,+KAA8K,CAChL;EACH;EAEA,MAAM;IAAEiD,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzDtD,oBAAoB,CAMlBD,SAAS,EAAE;IACXM,EAAE;IACFC,gBAAgB;IAChBC,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACbM;EACF,CAAC,CAAC;EAEJ,oBACE,oBAAC,iBAAiB,qBAChB,oBAAC,aAAa,eACRD,IAAI;IACR,KAAK,EAAEoC,KAAM;IACb,UAAU,EAAEE,UAAW;IACvB,WAAW,EAAED,WAAY;IACzB,mBAAmB,EAAEzC;EAAoB,GACzC,CACgB;AAExB;AAEA,eAAeb,sBAAsB,CAKnCM,kBAAkB,CAAC"}