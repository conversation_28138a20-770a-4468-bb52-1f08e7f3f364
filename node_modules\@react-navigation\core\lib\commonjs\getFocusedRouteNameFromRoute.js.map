{"version": 3, "names": ["getFocusedRouteNameFromRoute", "route", "state", "CHILD_STATE", "params", "routeName", "routes", "index", "type", "length", "name", "screen", "undefined"], "sourceRoot": "../../src", "sources": ["getFocusedRouteNameFromRoute.tsx"], "mappings": ";;;;;;AAEA;AAEe,SAASA,4BAA4B,CAClDC,KAA6B,EACT;EACpB;EACA,MAAMC,KAAK,GAAGD,KAAK,CAACE,0BAAW,CAAC,IAAIF,KAAK,CAACC,KAAK;EAC/C,MAAME,MAAM,GAAGH,KAAK,CAACG,MAA0C;EAE/D,MAAMC,SAAS,GAAGH,KAAK;EACnB;EACAA,KAAK,CAACI,MAAM;EACV;EACA;EACAJ,KAAK,CAACK,KAAK,KACR,OAAOL,KAAK,CAACM,IAAI,KAAK,QAAQ,IAAIN,KAAK,CAACM,IAAI,KAAK,OAAO,GACrD,CAAC,GACDN,KAAK,CAACI,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAC/B,CAACC,IAAI;EACN;EACF,QAAON,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,MAAM,MAAK,QAAQ,GAChCP,MAAM,CAACO,MAAM,GACbC,SAAS;EAEb,OAAOP,SAAS;AAClB"}