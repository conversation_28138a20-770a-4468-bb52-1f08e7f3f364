load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "YOGA_CXX_TARGET",
    "fb_xplat_cxx_test",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

oncall("react_native")

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "imagemanager",
    srcs = glob(
        ["*.cpp"],
        exclude = glob(["tests/**/*.cpp"]),
    ),
    headers = subdir_glob(
        [("", "*.h")],
        prefix = "",
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [("", "*.h")],
        prefix = "react/renderer/imagemanager",
    ),
    compiler_flags_pedantic = True,
    contacts = ["<EMAIL>"],
    cxx_srcs = glob(["platform/cxx/**/*.cpp"]),
    # FIXME: android relies on stubs implemented for the cxx platform
    fbandroid_srcs = glob(["platform/cxx/**/*.cpp"]),
    fbandroid_tests_override = [],
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags() + [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    force_static = True,
    ios_deps = [
        "//xplat/js/react-native-github:RCTCxxBridge",
        "//xplat/js/react-native-github:RCTImage",
    ],
    ios_exported_headers = subdir_glob(
        [
            ("", "*.h"),
            ("platform/ios/react/renderer/imagemanager", "RCTImagePrimitivesConversions.h"),
        ],
        prefix = "react/renderer/imagemanager",
    ),
    ios_frameworks = [
        "$SDKROOT/System/Library/Frameworks/CoreGraphics.framework",
        "$SDKROOT/System/Library/Frameworks/Foundation.framework",
        "$SDKROOT/System/Library/Frameworks/UIKit.framework",
    ],
    ios_headers = subdir_glob(
        [
            ("", "*.h"),
            ("platform/ios/react/renderer/imagemanager", "**/*.h"),
        ],
        prefix = "",
    ),
    ios_srcs = glob(
        [
            "platform/ios/**/*.cpp",
            "platform/ios/**/*.mm",
        ],
    ),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    tests = [":tests"],
    visibility = ["PUBLIC"],
    deps = [
        YOGA_CXX_TARGET,
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/mounting:mounting"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
    ],
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["tests/**/*.cpp"]),
    headers = glob(["tests/**/*.h"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    contacts = ["<EMAIL>"],
    platforms = (APPLE, CXX),
    deps = [
        ":imagemanager",
        "//xplat/third-party/gmock:gtest",
    ],
)
