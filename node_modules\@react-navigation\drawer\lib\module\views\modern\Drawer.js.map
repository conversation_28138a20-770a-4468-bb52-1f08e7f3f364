{"version": 3, "names": ["React", "I18nManager", "InteractionManager", "Keyboard", "Platform", "StatusBar", "StyleSheet", "View", "Animated", "interpolate", "runOnJS", "useAnimatedGestureHandler", "useAnimatedStyle", "useDerivedValue", "useSharedValue", "with<PERSON><PERSON><PERSON>", "DrawerProgressContext", "GestureState", "PanGestureHandler", "Overlay", "SWIPE_DISTANCE_MINIMUM", "DEFAULT_DRAWER_WIDTH", "minmax", "value", "start", "end", "Math", "min", "max", "Drawer", "dimensions", "drawerPosition", "drawerStyle", "drawerType", "gestureHandlerProps", "hideStatusBarOnOpen", "keyboardDismissMode", "onClose", "onOpen", "open", "overlayStyle", "renderDrawerContent", "renderSceneContent", "statusBarAnimation", "swipeDistanceThreshold", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "swipeEnabled", "swipeVelocityThreshold", "overlayAccessibilityLabel", "getDrawer<PERSON>idth", "width", "flatten", "endsWith", "percentage", "Number", "replace", "isFinite", "drawerWidth", "isOpen", "isRight", "getDrawerTranslationX", "useCallback", "hideStatusBar", "hide", "setHidden", "useEffect", "interactionHandleRef", "useRef", "startInteraction", "current", "createInteractionHandle", "endInteraction", "clearInteractionHandle", "hideKeyboard", "dismiss", "onGestureStart", "onGestureFinish", "hitSlop", "right", "undefined", "left", "touchStartX", "touchX", "translationX", "gestureState", "UNDETERMINED", "toggle<PERSON>rawer", "isUserInitiated", "velocity", "translateX", "stiffness", "damping", "mass", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "reduceMotion", "onGestureEvent", "onStart", "event", "ctx", "hasCalledOnStart", "startX", "state", "x", "onActive", "onEnd", "nextOpen", "abs", "velocityX", "onFinish", "touchDistance", "ACTIVE", "isRTL", "getConstants", "drawerAnimatedStyle", "distanceFromEdge", "transform", "contentAnimatedStyle", "progress", "styles", "main", "flexDirection", "content", "OS", "container", "position", "zIndex", "create", "top", "bottom", "max<PERSON><PERSON><PERSON>", "flex", "select", "web", "default", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/modern/Drawer.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EACXC,kBAAkB,EAClBC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVC,IAAI,QACC,cAAc;AACrB,OAAOC,QAAQ,IACbC,WAAW,EACXC,OAAO,EACPC,yBAAyB,EACzBC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,UAAU,QACL,yBAAyB;AAGhC,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,SACEC,YAAY,EACZC,iBAAiB,QAEZ,mBAAmB;AAC1B,OAAOC,OAAO,MAAM,WAAW;AAE/B,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,oBAAoB,GAAG,KAAK;AAQlC,MAAMC,MAAM,GAAG,CAACC,KAAa,EAAEC,KAAa,EAAEC,GAAW,KAAK;EAC5D,SAAS;;EAET,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,EAAEC,KAAK,CAAC,EAAEC,GAAG,CAAC;AAC9C,CAAC;AAED,eAAe,SAASI,MAAM,OAoBd;EAAA,IApBe;IAC7BC,UAAU;IACVC,cAAc;IACdC,WAAW;IACXC,UAAU;IACVC,mBAAmB;IACnBC,mBAAmB;IACnBC,mBAAmB;IACnBC,OAAO;IACPC,MAAM;IACNC,IAAI;IACJC,YAAY;IACZC,mBAAmB;IACnBC,kBAAkB;IAClBC,kBAAkB;IAClBC,sBAAsB;IACtBC,cAAc;IACdC,YAAY;IACZC,sBAAsB;IACtBC;EACW,CAAC;EACZ,MAAMC,cAAc,GAAG,MAAc;IACnC,MAAM;MAAEC,KAAK,GAAG7B;IAAqB,CAAC,GACpCf,UAAU,CAAC6C,OAAO,CAACnB,WAAW,CAAC,IAAI,CAAC,CAAC;IAEvC,IAAI,OAAOkB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD;MACA,MAAMC,UAAU,GAAGC,MAAM,CAACJ,KAAK,CAACK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;MAElD,IAAID,MAAM,CAACE,QAAQ,CAACH,UAAU,CAAC,EAAE;QAC/B,OAAOvB,UAAU,CAACoB,KAAK,IAAIG,UAAU,GAAG,GAAG,CAAC;MAC9C;IACF;IAEA,OAAO,OAAOH,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAAC;EAC9C,CAAC;EAED,MAAMO,WAAW,GAAGR,cAAc,EAAE;EAEpC,MAAMS,MAAM,GAAGzB,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGM,IAAI;EACvD,MAAMoB,OAAO,GAAG5B,cAAc,KAAK,OAAO;EAE1C,MAAM6B,qBAAqB,GAAG5D,KAAK,CAAC6D,WAAW,CAC5CtB,IAAa,IAAK;IACjB,SAAS;;IAET,IAAIR,cAAc,KAAK,MAAM,EAAE;MAC7B,OAAOQ,IAAI,GAAG,CAAC,GAAG,CAACkB,WAAW;IAChC;IAEA,OAAOlB,IAAI,GAAG,CAAC,GAAGkB,WAAW;EAC/B,CAAC,EACD,CAAC1B,cAAc,EAAE0B,WAAW,CAAC,CAC9B;EAED,MAAMK,aAAa,GAAG9D,KAAK,CAAC6D,WAAW,CACpCE,IAAa,IAAK;IACjB,IAAI5B,mBAAmB,EAAE;MACvB9B,SAAS,CAAC2D,SAAS,CAACD,IAAI,EAAEpB,kBAAkB,CAAC;IAC/C;EACF,CAAC,EACD,CAACR,mBAAmB,EAAEQ,kBAAkB,CAAC,CAC1C;EAED3C,KAAK,CAACiE,SAAS,CAAC,MAAM;IACpBH,aAAa,CAACJ,MAAM,CAAC;IAErB,OAAO,MAAMI,aAAa,CAAC,KAAK,CAAC;EACnC,CAAC,EAAE,CAACJ,MAAM,EAAEvB,mBAAmB,EAAEQ,kBAAkB,EAAEmB,aAAa,CAAC,CAAC;EAEpE,MAAMI,oBAAoB,GAAGlE,KAAK,CAACmE,MAAM,CAAgB,IAAI,CAAC;EAE9D,MAAMC,gBAAgB,GAAG,MAAM;IAC7BF,oBAAoB,CAACG,OAAO,GAAGnE,kBAAkB,CAACoE,uBAAuB,EAAE;EAC7E,CAAC;EAED,MAAMC,cAAc,GAAG,MAAM;IAC3B,IAAIL,oBAAoB,CAACG,OAAO,IAAI,IAAI,EAAE;MACxCnE,kBAAkB,CAACsE,sBAAsB,CAACN,oBAAoB,CAACG,OAAO,CAAC;MACvEH,oBAAoB,CAACG,OAAO,GAAG,IAAI;IACrC;EACF,CAAC;EAED,MAAMI,YAAY,GAAG,MAAM;IACzB,IAAIrC,mBAAmB,KAAK,SAAS,EAAE;MACrCjC,QAAQ,CAACuE,OAAO,EAAE;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAG,MAAM;IAC3BP,gBAAgB,EAAE;IAClBK,YAAY,EAAE;IACdX,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMc,eAAe,GAAG,MAAM;IAC5BL,cAAc,EAAE;EAClB,CAAC;;EAED;EACA;EACA,MAAMM,OAAO,GAAGlB,OAAO;EACnB;EACA;EACA;IAAEmB,KAAK,EAAE,CAAC;IAAE5B,KAAK,EAAEQ,MAAM,GAAGqB,SAAS,GAAGlC;EAAe,CAAC,GACxD;IAAEmC,IAAI,EAAE,CAAC;IAAE9B,KAAK,EAAEQ,MAAM,GAAGqB,SAAS,GAAGlC;EAAe,CAAC;EAE3D,MAAMoC,WAAW,GAAGnE,cAAc,CAAC,CAAC,CAAC;EACrC,MAAMoE,MAAM,GAAGpE,cAAc,CAAC,CAAC,CAAC;EAChC,MAAMqE,YAAY,GAAGrE,cAAc,CAAC8C,qBAAqB,CAACrB,IAAI,CAAC,CAAC;EAChE,MAAM6C,YAAY,GAAGtE,cAAc,CAAeG,YAAY,CAACoE,YAAY,CAAC;EAE5E,MAAMC,YAAY,GAAGtF,KAAK,CAAC6D,WAAW,CACpC,SAAwD;IACtD,SAAS;;IAAC,IADX;MAAEtB,IAAI;MAAEgD,eAAe;MAAEC;IAAwB,CAAC;IAGjD,MAAMC,UAAU,GAAG7B,qBAAqB,CAACrB,IAAI,CAAC;IAE9C0C,WAAW,CAAC1D,KAAK,GAAG,CAAC;IACrB2D,MAAM,CAAC3D,KAAK,GAAG,CAAC;IAChB4D,YAAY,CAAC5D,KAAK,GAAGR,UAAU,CAAC0E,UAAU,EAAE;MAC1CD,QAAQ;MACRE,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,CAAC;MACPC,iBAAiB,EAAE,IAAI;MACvBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,IAAI;MACxB;MACAC,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,IAAI,CAACT,eAAe,EAAE;MACpB;IACF;IAEA,IAAIhD,IAAI,EAAE;MACR7B,OAAO,CAAC4B,MAAM,CAAC,EAAE;IACnB,CAAC,MAAM;MACL5B,OAAO,CAAC2B,OAAO,CAAC,EAAE;IACpB;EACF,CAAC,EACD,CAACuB,qBAAqB,EAAEvB,OAAO,EAAEC,MAAM,EAAE2C,WAAW,EAAEC,MAAM,EAAEC,YAAY,CAAC,CAC5E;EAEDnF,KAAK,CAACiE,SAAS,CACb,MAAMqB,YAAY,CAAC;IAAE/C,IAAI;IAAEgD,eAAe,EAAE;EAAM,CAAC,CAAC,EACpD,CAAChD,IAAI,EAAE+C,YAAY,CAAC,CACrB;EAED,MAAMW,cAAc,GAAGtF,yBAAyB,CAG9C;IACAuF,OAAO,EAAE,CAACC,KAAK,EAAEC,GAAG,KAAK;MACvBA,GAAG,CAACC,gBAAgB,GAAG,KAAK;MAC5BD,GAAG,CAACE,MAAM,GAAGnB,YAAY,CAAC5D,KAAK;MAC/B6D,YAAY,CAAC7D,KAAK,GAAG4E,KAAK,CAACI,KAAK;MAChCtB,WAAW,CAAC1D,KAAK,GAAG4E,KAAK,CAACK,CAAC;IAC7B,CAAC;IACDC,QAAQ,EAAE,CAACN,KAAK,EAAEC,GAAG,KAAK;MACxBlB,MAAM,CAAC3D,KAAK,GAAG4E,KAAK,CAACK,CAAC;MACtBrB,YAAY,CAAC5D,KAAK,GAAG6E,GAAG,CAACE,MAAM,GAAGH,KAAK,CAAChB,YAAY;MACpDC,YAAY,CAAC7D,KAAK,GAAG4E,KAAK,CAACI,KAAK;;MAEhC;MACA;MACA;MACA,IAAI,CAACH,GAAG,CAACC,gBAAgB,EAAE;QACzBD,GAAG,CAACC,gBAAgB,GAAG,IAAI;QAC3B3F,OAAO,CAACiE,cAAc,CAAC,EAAE;MAC3B;IACF,CAAC;IACD+B,KAAK,EAAGP,KAAK,IAAK;MAChBf,YAAY,CAAC7D,KAAK,GAAG4E,KAAK,CAACI,KAAK;MAEhC,MAAMI,QAAQ,GACXjF,IAAI,CAACkF,GAAG,CAACT,KAAK,CAAChB,YAAY,CAAC,GAAG/D,sBAAsB,IACpDM,IAAI,CAACkF,GAAG,CAACT,KAAK,CAAChB,YAAY,CAAC,GAAGpC,sBAAsB,IACvDrB,IAAI,CAACkF,GAAG,CAACT,KAAK,CAAChB,YAAY,CAAC,GAAGvC,sBAAsB,GACjDb,cAAc,KAAK,MAAM;MACvB;MACA,CAACoE,KAAK,CAACU,SAAS,KAAK,CAAC,GAAGV,KAAK,CAAChB,YAAY,GAAGgB,KAAK,CAACU,SAAS,IAAI,CAAC;MAClE;MACA,CAACV,KAAK,CAACU,SAAS,KAAK,CAAC,GAAGV,KAAK,CAAChB,YAAY,GAAGgB,KAAK,CAACU,SAAS,IAAI,CAAC,GACpEtE,IAAI;MAEV+C,YAAY,CAAC;QACX/C,IAAI,EAAEoE,QAAQ;QACdpB,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAEW,KAAK,CAACU;MAClB,CAAC,CAAC;IACJ,CAAC;IACDC,QAAQ,EAAE,MAAM;MACdpG,OAAO,CAACkE,eAAe,CAAC,EAAE;IAC5B;EACF,CAAC,CAAC;EAEF,MAAMa,UAAU,GAAG5E,eAAe,CAAC,MAAM;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMkG,aAAa,GACjB9E,UAAU,KAAK,OAAO,IAAImD,YAAY,CAAC7D,KAAK,KAAKN,YAAY,CAAC+F,MAAM,GAChE1F,MAAM,CACJS,cAAc,KAAK,MAAM,GACrBkD,WAAW,CAAC1D,KAAK,GAAGkC,WAAW,GAC/B3B,UAAU,CAACoB,KAAK,GAAGO,WAAW,GAAGwB,WAAW,CAAC1D,KAAK,EACtD,CAAC,EACDO,UAAU,CAACoB,KAAK,CACjB,GACD,CAAC;IAEP,MAAMuC,UAAU,GACd1D,cAAc,KAAK,MAAM,GACrBT,MAAM,CAAC6D,YAAY,CAAC5D,KAAK,GAAGwF,aAAa,EAAE,CAACtD,WAAW,EAAE,CAAC,CAAC,GAC3DnC,MAAM,CAAC6D,YAAY,CAAC5D,KAAK,GAAGwF,aAAa,EAAE,CAAC,EAAEtD,WAAW,CAAC;IAEhE,OAAOgC,UAAU;EACnB,CAAC,CAAC;EAEF,MAAMwB,KAAK,GAAGhH,WAAW,CAACiH,YAAY,EAAE,CAACD,KAAK;EAC9C,MAAME,mBAAmB,GAAGvG,gBAAgB,CAAC,MAAM;IACjD,MAAMwG,gBAAgB,GAAGtF,UAAU,CAACoB,KAAK,GAAGO,WAAW;IAEvD,OAAO;MACL4D,SAAS,EACPpF,UAAU,KAAK,WAAW;MACtB;MACA;MACA,EAAE,GACF,CACE;QACEwD,UAAU;QACR;QACA,CAACxD,UAAU,KAAK,MAAM,GAAG,CAAC,GAAGwD,UAAU,CAAClE,KAAK,KAC5CQ,cAAc,KAAK,MAAM,GACtBkF,KAAK,GACH,CAACG,gBAAgB,GACjB,CAAC,GACHH,KAAK,GACL,CAAC,GACDG,gBAAgB;MACxB,CAAC;IAEX,CAAC;EACH,CAAC,CAAC;EAEF,MAAME,oBAAoB,GAAG1G,gBAAgB,CAAC,MAAM;IAClD,OAAO;MACLyG,SAAS,EACPpF,UAAU,KAAK,WAAW;MACtB;MACA;MACA,EAAE,GACF,CACE;QACEwD,UAAU;QACR;QACAxD,UAAU,KAAK,OAAO,GAClB,CAAC,GACDwD,UAAU,CAAClE,KAAK,GAChBkC,WAAW,IAAI1B,cAAc,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACzD,CAAC;IAEX,CAAC;EACH,CAAC,CAAC;EAEF,MAAMwF,QAAQ,GAAG1G,eAAe,CAAC,MAAM;IACrC,OAAOoB,UAAU,KAAK,WAAW,GAC7B,CAAC,GACDxB,WAAW,CACTgF,UAAU,CAAClE,KAAK,EAChB,CAACqC,qBAAqB,CAAC,KAAK,CAAC,EAAEA,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAC3D,CAAC,CAAC,EAAE,CAAC,CAAC,CACP;EACP,CAAC,CAAC;EAEF,oBACE,oBAAC,qBAAqB,CAAC,QAAQ;IAAC,KAAK,EAAE2D;EAAS,gBAC9C,oBAAC,iBAAiB;IAChB,aAAa,EAAE,CAAC,CAACnG,sBAAsB,EAAEA,sBAAsB,CAAE;IACjE,WAAW,EAAE,CAAC,CAACA,sBAAsB,EAAEA,sBAAsB,CAAE;IAC/D,OAAO,EAAEyD,OAAQ;IACjB,OAAO,EAAE5C,UAAU,KAAK,WAAW,IAAIa,YAAa;IACpD,cAAc,EAAEmD;EAAe,GAC3B/D,mBAAmB,gBAGvB,oBAAC,QAAQ,CAAC,IAAI;IACZ,KAAK,EAAE,CACLsF,MAAM,CAACC,IAAI,EACX;MACEC,aAAa,EACXzF,UAAU,KAAK,WAAW,IAAI,CAAC0B,OAAO,GAAG,aAAa,GAAG;IAC7D,CAAC;EACD,gBAEF,oBAAC,QAAQ,CAAC,IAAI;IAAC,KAAK,EAAE,CAAC6D,MAAM,CAACG,OAAO,EAAEL,oBAAoB;EAAE,gBAC3D,oBAAC,IAAI;IACH,2BAA2B,EAAE5D,MAAM,IAAIzB,UAAU,KAAK,WAAY;IAClE,yBAAyB,EACvByB,MAAM,IAAIzB,UAAU,KAAK,WAAW,GAChC,qBAAqB,GACrB,MACL;IACD,KAAK,EAAEuF,MAAM,CAACG;EAAQ,GAErBjF,kBAAkB,EAAE,CAChB,EACNT,UAAU,KAAK,WAAW,gBACzB,oBAAC,OAAO;IACN,QAAQ,EAAEsF,QAAS;IACnB,OAAO,EAAE,MACPjC,YAAY,CAAC;MAAE/C,IAAI,EAAE,KAAK;MAAEgD,eAAe,EAAE;IAAK,CAAC,CACpD;IACD,KAAK,EAAE/C,YAAa;IACpB,kBAAkB,EAAEQ;EAA0B,EAC9C,GACA,IAAI,CACM,eAChB,oBAAC,QAAQ,CAAC,IAAI;IACZ,qBAAqB,EAAE5C,QAAQ,CAACwH,EAAE,KAAK,KAAM;IAC7C,KAAK,EAAE,CACLJ,MAAM,CAACK,SAAS,EAChB;MACEC,QAAQ,EAAE7F,UAAU,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;MAC9D8F,MAAM,EAAE9F,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG;IACvC,CAAC,EACDkF,mBAAmB,EACnBnF,WAAW;EACX,GAEDS,mBAAmB,EAAE,CACR,CACF,CACE,CACW;AAErC;AAEA,MAAM+E,MAAM,GAAGlH,UAAU,CAAC0H,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,MAAM;IAChBjF,KAAK,EAAE7B;EACT,CAAC;EACDsG,OAAO,EAAE;IACPS,IAAI,EAAE;EACR,CAAC;EACDX,IAAI,EAAE;IACJW,IAAI,EAAE,CAAC;IACP,GAAGhI,QAAQ,CAACiI,MAAM,CAAC;MACjB;MACA;MACAC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAS;IAChC,CAAC;EACH;AACF,CAAC,CAAC"}