{"version": 3, "names": ["cleanAssetCatalog", "catalogDir", "files", "fs", "readdirSync", "filter", "file", "endsWith", "removeSync", "path", "join", "getImageSet", "asset", "scales", "fileName", "assetPathUtils", "getResourceIdentifier", "basePath", "map", "scale", "idx", "suffix", "name", "type", "src", "isCatalogAsset", "writeImageSet", "imageSet", "mkdirsSync", "dest", "copyFileSync", "writeJSONSync", "images", "filename", "idiom", "info", "author", "version"], "sources": ["../../../src/commands/bundle/assetCatalogIOS.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport fs from 'fs-extra';\nimport type {AssetData} from 'metro';\nimport assetPathUtils from './assetPathUtils';\n\nexport function cleanAssetCatalog(catalogDir: string): void {\n  const files = fs\n    .readdirSync(catalogDir)\n    .filter((file) => file.endsWith('.imageset'));\n  for (const file of files) {\n    fs.removeSync(path.join(catalogDir, file));\n  }\n}\n\ntype ImageSet = {\n  basePath: string;\n  files: {name: string; src: string; scale: number}[];\n};\n\nexport function getImageSet(\n  catalogDir: string,\n  asset: AssetData,\n  scales: readonly number[],\n): ImageSet {\n  const fileName = assetPathUtils.getResourceIdentifier(asset);\n  return {\n    basePath: path.join(catalogDir, `${fileName}.imageset`),\n    files: scales.map((scale, idx) => {\n      const suffix = scale === 1 ? '' : `@${scale}x`;\n      return {\n        name: `${fileName + suffix}.${asset.type}`,\n        scale,\n        src: asset.files[idx],\n      };\n    }),\n  };\n}\n\nexport function isCatalogAsset(asset: AssetData): boolean {\n  return asset.type === 'png' || asset.type === 'jpg' || asset.type === 'jpeg';\n}\n\nexport function writeImageSet(imageSet: ImageSet): void {\n  fs.mkdirsSync(imageSet.basePath);\n\n  for (const file of imageSet.files) {\n    const dest = path.join(imageSet.basePath, file.name);\n    fs.copyFileSync(file.src, dest);\n  }\n\n  fs.writeJSONSync(path.join(imageSet.basePath, 'Contents.json'), {\n    images: imageSet.files.map((file) => ({\n      filename: file.name,\n      idiom: 'universal',\n      scale: `${file.scale}x`,\n    })),\n    info: {\n      author: 'xcode',\n      version: 1,\n    },\n  });\n}\n"], "mappings": ";;;;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAA8C;AAX9C;AACA;AACA;AACA;AACA;AACA;AACA;;AAOO,SAASA,iBAAiB,CAACC,UAAkB,EAAQ;EAC1D,MAAMC,KAAK,GAAGC,kBAAE,CACbC,WAAW,CAACH,UAAU,CAAC,CACvBI,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC/C,KAAK,MAAMD,IAAI,IAAIJ,KAAK,EAAE;IACxBC,kBAAE,CAACK,UAAU,CAACC,eAAI,CAACC,IAAI,CAACT,UAAU,EAAEK,IAAI,CAAC,CAAC;EAC5C;AACF;AAOO,SAASK,WAAW,CACzBV,UAAkB,EAClBW,KAAgB,EAChBC,MAAyB,EACf;EACV,MAAMC,QAAQ,GAAGC,uBAAc,CAACC,qBAAqB,CAACJ,KAAK,CAAC;EAC5D,OAAO;IACLK,QAAQ,EAAER,eAAI,CAACC,IAAI,CAACT,UAAU,EAAG,GAAEa,QAAS,WAAU,CAAC;IACvDZ,KAAK,EAAEW,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;MAChC,MAAMC,MAAM,GAAGF,KAAK,KAAK,CAAC,GAAG,EAAE,GAAI,IAAGA,KAAM,GAAE;MAC9C,OAAO;QACLG,IAAI,EAAG,GAAER,QAAQ,GAAGO,MAAO,IAAGT,KAAK,CAACW,IAAK,EAAC;QAC1CJ,KAAK;QACLK,GAAG,EAAEZ,KAAK,CAACV,KAAK,CAACkB,GAAG;MACtB,CAAC;IACH,CAAC;EACH,CAAC;AACH;AAEO,SAASK,cAAc,CAACb,KAAgB,EAAW;EACxD,OAAOA,KAAK,CAACW,IAAI,KAAK,KAAK,IAAIX,KAAK,CAACW,IAAI,KAAK,KAAK,IAAIX,KAAK,CAACW,IAAI,KAAK,MAAM;AAC9E;AAEO,SAASG,aAAa,CAACC,QAAkB,EAAQ;EACtDxB,kBAAE,CAACyB,UAAU,CAACD,QAAQ,CAACV,QAAQ,CAAC;EAEhC,KAAK,MAAMX,IAAI,IAAIqB,QAAQ,CAACzB,KAAK,EAAE;IACjC,MAAM2B,IAAI,GAAGpB,eAAI,CAACC,IAAI,CAACiB,QAAQ,CAACV,QAAQ,EAAEX,IAAI,CAACgB,IAAI,CAAC;IACpDnB,kBAAE,CAAC2B,YAAY,CAACxB,IAAI,CAACkB,GAAG,EAAEK,IAAI,CAAC;EACjC;EAEA1B,kBAAE,CAAC4B,aAAa,CAACtB,eAAI,CAACC,IAAI,CAACiB,QAAQ,CAACV,QAAQ,EAAE,eAAe,CAAC,EAAE;IAC9De,MAAM,EAAEL,QAAQ,CAACzB,KAAK,CAACgB,GAAG,CAAEZ,IAAI,KAAM;MACpC2B,QAAQ,EAAE3B,IAAI,CAACgB,IAAI;MACnBY,KAAK,EAAE,WAAW;MAClBf,KAAK,EAAG,GAAEb,IAAI,CAACa,KAAM;IACvB,CAAC,CAAC,CAAC;IACHgB,IAAI,EAAE;MACJC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;AACJ"}