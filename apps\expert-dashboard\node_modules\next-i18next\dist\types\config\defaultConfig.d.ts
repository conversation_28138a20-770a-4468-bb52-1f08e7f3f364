export declare const defaultConfig: {
    defaultNS: string;
    errorStackTraceLimit: number;
    i18n: {
        defaultLocale: string;
        locales: string[];
    };
    readonly initImmediate: boolean;
    readonly initAsync: boolean;
    interpolation: {
        escapeValue: boolean;
    };
    load: string;
    localeExtension: string;
    localePath: string;
    localeStructure: string;
    react: {
        useSuspense: boolean;
    };
    reloadOnPrerender: boolean;
    serializeConfig: boolean;
    use: never[];
};
