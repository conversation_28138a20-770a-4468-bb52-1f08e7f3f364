parcelRequire=function(e,r,t,n){var i,o="function"==typeof parcelRequire&&parcelRequire,u="function"==typeof require&&require;function f(t,n){if(!r[t]){if(!e[t]){var i="function"==typeof parcelRequire&&parcelRequire;if(!n&&i)return i(t,!0);if(o)return o(t,!0);if(u&&"string"==typeof t)return u(t);var c=new Error("Cannot find module '"+t+"'");throw c.code="MODULE_NOT_FOUND",c}p.resolve=function(r){return e[t][1][r]||r},p.cache={};var l=r[t]=new f.Module(t);e[t][0].call(l.exports,p,l,l.exports,this)}return r[t].exports;function p(e){return f(p.resolve(e))}}f.isParcelRequire=!0,f.Module=function(e){this.id=e,this.bundle=f,this.exports={}},f.modules=e,f.cache=r,f.parent=o,f.register=function(r,t){e[r]=[function(e,r){r.exports=t},{}]};for(var c=0;c<t.length;c++)try{f(t[c])}catch(e){i||(i=e)}if(t.length){var l=f(t[t.length-1]);"object"==typeof exports&&"undefined"!=typeof module?module.exports=l:"function"==typeof define&&define.amd?define(function(){return l}):n&&(this[n]=l)}if(parcelRequire=f,i)throw i;return f}({"SM7r":[function(require,module,exports) {
var define;
var t,r=function(t){"use strict";var r,e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function h(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{h({},"")}catch(S){h=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof d?r:d,a=Object.create(i.prototype),c=new P(n||[]);return o(a,"_invoke",{value:O(t,e,c)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(S){return{type:"throw",arg:S}}}t.wrap=l;var s="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function d(){}function m(){}function w(){}var b={};h(b,a,function(){return this});var L=Object.getPrototypeOf,x=L&&L(L(T([])));x&&x!==e&&n.call(x,a)&&(b=x);var E=w.prototype=d.prototype=Object.create(b);function j(t){["next","throw","return"].forEach(function(r){h(t,r,function(t){return this._invoke(r,t)})})}function _(t,r){var e;o(this,"_invoke",{value:function(o,i){function a(){return new r(function(e,a){!function e(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var h=u.arg,l=h.value;return l&&"object"==typeof l&&n.call(l,"__await")?r.resolve(l.__await).then(function(t){e("next",t,a,c)},function(t){e("throw",t,a,c)}):r.resolve(l).then(function(t){h.value=t,a(h)},function(t){return e("throw",t,a,c)})}c(u.arg)}(o,i,e,a)})}return e=e?e.then(a,a):a()}})}function O(t,r,e){var n=s;return function(o,i){if(n===y)throw new Error("Generator is already running");if(n===v){if("throw"===o)throw i;return F()}for(e.method=o,e.arg=i;;){var a=e.delegate;if(a){var c=k(a,e);if(c){if(c===g)continue;return c}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if(n===s)throw n=v,e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);n=y;var u=f(t,r,e);if("normal"===u.type){if(n=e.done?v:p,u.arg===g)continue;return{value:u.arg,done:e.done}}"throw"===u.type&&(n=v,e.method="throw",e.arg=u.arg)}}}function k(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,k(t,e),"throw"===e.method)?g:("return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g);var i=f(o,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,g;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function G(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function N(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(G,this),this.reset(!0)}function T(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=r,e.done=!0,e};return i.next=i}}return{next:F}}function F(){return{value:r,done:!0}}return m.prototype=w,o(E,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:m,configurable:!0}),m.displayName=h(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,h(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},t.awrap=function(t){return{__await:t}},j(_.prototype),h(_.prototype,c,function(){return this}),t.AsyncIterator=_,t.async=function(r,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(r,e,n,o),i);return t.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},j(E),h(E,u,"Generator"),h(E,a,function(){return this}),h(E,"toString",function(){return"[object Generator]"}),t.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=T,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(N),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return c.type="throw",c.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!h)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),g},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),N(e),g}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;N(e)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},t}("object"==typeof module?module.exports:{});try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}
},{}],"NFhs":[function(require,module,exports) {
module.exports=require("regenerator-runtime");
},{"regenerator-runtime":"SM7r"}],"fNvc":[function(require,module,exports) {
function n(n,t,o,r,e,i,u){try{var c=n[i](u),v=c.value}catch(a){return void o(a)}c.done?t(v):Promise.resolve(v).then(r,e)}function t(t){return function(){var o=this,r=arguments;return new Promise(function(e,i){var u=t.apply(o,r);function c(t){n(u,e,i,c,v,"next",t)}function v(t){n(u,e,i,c,v,"throw",t)}c(void 0)})}}module.exports=t;
},{}],"vKFU":[function(require,module,exports) {

},{}],"rEFh":[function(require,module,exports) {
module.exports="/debugger-ui/blue-icon.7726c7b7.png";
},{}],"LwPU":[function(require,module,exports) {
module.exports="/debugger-ui/gray-icon.c1916de4.png";
},{}],"iMdP":[function(require,module,exports) {
module.exports="/debugger-ui/orange-icon.f455ca44.png";
},{}],"Focm":[function(require,module,exports) {
"use strict";var e=a(require("@babel/runtime/regenerator")),t=a(require("@babel/runtime/helpers/asyncToGenerator"));require("./index.css");var n=a(require("./assets/blue-icon.png")),i=a(require("./assets/gray-icon.png")),r=a(require("./assets/orange-icon.png"));function a(e){return e&&e.__esModule?e:{default:e}}var o=/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform),s=o?"⌘R":"Ctrl R";window.onload=function(){o||(document.getElementById("shortcut").innerHTML="Ctrl⇧J"),c.render()},window.onReloadClicked=function(){var e=new XMLHttpRequest;e.open("GET","".concat(window.location.origin,"/reload"),!0),e.send()};var c=window.Page={state:{isDark:null===localStorage.getItem("darkTheme")?window.matchMedia("(prefers-color-scheme: dark)").matches:"on"===localStorage.getItem("darkTheme"),isPriorityMaintained:"on"===localStorage.getItem("maintainPriority"),status:{type:"disconnected"},visibilityState:document.visibilityState},setState:function(e){c.state=Object.assign({},c.state,e),c.render()},render:function(){var e=c.state,t=e.isDark,a=e.isPriorityMaintained,o=e.status,d=e.visibilityState,u=document.getElementById("status");switch(o.type){case"connected":u.textContent="Debugger session active.";break;case"error":u.textContent=o.error.reason||"Disconnected from proxy. Attempting reconnection. Is node server running?";break;case"connecting":case"disconnected":default:u.innerHTML='Waiting, press <span class="shortcut">'+s+"</span> in simulator to reload and connect."}var l=document.querySelector("link[rel=icon]");"disconnected"===o.type||"error"===o.type?l.href=i.default:l.href="visible"===d||a?n.default:r.default;var g=document.getElementById("dark");document.body.classList.toggle("dark",t),g.checked=t,localStorage.setItem("darkTheme",t?"on":"");var m=document.getElementById("maintain-priority"),y=document.getElementById("silence");y.volume=.1,a?y.play():y.pause(),m.checked=a,localStorage.setItem("maintainPriority",a?"on":"")},toggleDarkTheme:function(){c.setState({isDark:!c.state.isDark})},togglePriorityMaintenance:function(){c.setState({isPriorityMaintained:!c.state.isPriorityMaintained})}};function d(){var n,i=new WebSocket("ws://"+window.location.host+"/debugger-proxy?role=debugger&name=Chrome");function r(){n&&(n.terminate(),n=null,window.onbeforeunload=null)}function a(){n&&!c.state.isPriorityMaintained&&n.postMessage({method:"setDebuggerVisibility",visibilityState:document.visibilityState}),c.setState({visibilityState:document.visibilityState})}i.onopen=function(){c.setState({status:{type:"connecting"}})},i.onmessage=function(){var o=(0,t.default)(e.default.mark(function t(o){var d;return e.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(o.data){e.next=2;break}return e.abrupt("return");case 2:if("client-disconnected"!==(d=JSON.parse(o.data)).$event){e.next=7;break}return r(),c.setState({status:{type:"disconnected"}}),e.abrupt("return");case 7:if(d.method){e.next=9;break}return e.abrupt("return");case 9:"prepareJSRuntime"===d.method?(r(),console.clear(),(n=new Worker("/debugger-ui/debuggerWorker.aca173c4.js")).onmessage=function(e){i.send(JSON.stringify(e.data))},window.onbeforeunload=function(){return"If you reload this page, it is going to break the debugging session. Press "+s+" on the device to reload."},a(),i.send(JSON.stringify({replyID:d.id})),c.setState({status:{type:"connected",id:d.id}})):"$disconnected"===d.method?(r(),c.setState({status:{type:"disconnected"}})):n.postMessage(d);case 10:case"end":return e.stop()}},t)}));return function(e){return o.apply(this,arguments)}}(),i.onclose=function(e){r(),c.setState({status:{type:"error",error:e}}),e.reason&&console.warn(e.reason),setTimeout(d,500)},document.addEventListener("visibilitychange",a,!1)}d();
},{"@babel/runtime/regenerator":"NFhs","@babel/runtime/helpers/asyncToGenerator":"fNvc","./index.css":"vKFU","./assets/blue-icon.png":"rEFh","./assets/gray-icon.png":"LwPU","./assets/orange-icon.png":"iMdP","./debuggerWorker.js":[["debuggerWorker.aca173c4.js","sYUN"],"debuggerWorker.aca173c4.js.map","sYUN"]}]},{},["Focm"], null)
//# sourceMappingURL=/debugger-ui/ui.e31bb0bc.js.map