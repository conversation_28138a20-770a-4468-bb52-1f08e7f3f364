{"version": 3, "names": ["startServerInNewWindow", "port", "terminal", "projectRoot", "reactNativePath", "isWindows", "test", "process", "platform", "scriptFile", "packagerEnvFilename", "packagerEnvFileExportContent", "nodeModulesPath", "resolveNodeModuleDir", "cliPluginMetroPath", "path", "join", "dirname", "require", "resolve", "packagerEnvFile", "launchPackagerScript", "procConfig", "cwd", "fs", "writeFileSync", "encoding", "flag", "copyFileSync", "error", "CLIError", "execa", "sync", "detached", "stdio", "logger"], "sources": ["../../../src/commands/buildAndroid/startServerInNewWindow.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport execa from 'execa';\nimport {\n  CLIError,\n  logger,\n  resolveNodeModuleDir,\n} from '@react-native-community/cli-tools';\n\nexport function startServerInNewWindow(\n  port: number,\n  terminal: string,\n  projectRoot: string,\n  reactNativePath: string,\n) {\n  /**\n   * Set up OS-specific filenames and commands\n   */\n  const isWindows = /^win/.test(process.platform);\n  const scriptFile = isWindows\n    ? 'launchPackager.bat'\n    : 'launchPackager.command';\n  const packagerEnvFilename = isWindows ? '.packager.bat' : '.packager.env';\n  const packagerEnvFileExportContent = isWindows\n    ? `set RCT_METRO_PORT=${port}\\nset PROJECT_ROOT=${projectRoot}\\nset REACT_NATIVE_PATH=${reactNativePath}`\n    : `export RCT_METRO_PORT=${port}\\nexport PROJECT_ROOT=${projectRoot}\\nexport REACT_NATIVE_PATH=${reactNativePath}`;\n  const nodeModulesPath = resolveNodeModuleDir(projectRoot, '.bin');\n  const cliPluginMetroPath = path.join(\n    path.dirname(\n      require.resolve('@react-native-community/cli-plugin-metro/package.json'),\n    ),\n    'build',\n  );\n\n  /**\n   * Set up the `.packager.(env|bat)` file to ensure the packager starts on the right port and in right directory.\n   */\n  const packagerEnvFile = path.join(nodeModulesPath, `${packagerEnvFilename}`);\n\n  /**\n   * Set up the `launchPackager.(command|bat)` file.\n   * It lives next to `.packager.(bat|env)`\n   */\n  const launchPackagerScript = path.join(nodeModulesPath, scriptFile);\n  const procConfig: execa.SyncOptions = {cwd: path.dirname(packagerEnvFile)};\n\n  /**\n   * Ensure we overwrite file by passing the `w` flag\n   */\n  fs.writeFileSync(packagerEnvFile, packagerEnvFileExportContent, {\n    encoding: 'utf8',\n    flag: 'w',\n  });\n\n  /**\n   * Copy files into `node_modules/.bin`.\n   */\n\n  try {\n    if (isWindows) {\n      fs.copyFileSync(\n        path.join(cliPluginMetroPath, 'launchPackager.bat'),\n        path.join(nodeModulesPath, 'launchPackager.bat'),\n      );\n    } else {\n      fs.copyFileSync(\n        path.join(cliPluginMetroPath, 'launchPackager.command'),\n        path.join(nodeModulesPath, 'launchPackager.command'),\n      );\n    }\n  } catch (error) {\n    return new CLIError(\n      `Couldn't copy the script for running bundler. Please check if the \"${scriptFile}\" file exists in the \"node_modules/@react-native-community/cli-plugin-metro\" folder and try again.`,\n      error as any,\n    );\n  }\n\n  if (process.platform === 'darwin') {\n    try {\n      return execa.sync(\n        'open',\n        ['-a', terminal, launchPackagerScript],\n        procConfig,\n      );\n    } catch (error) {\n      return execa.sync('open', [launchPackagerScript], procConfig);\n    }\n  }\n  if (process.platform === 'linux') {\n    try {\n      return execa.sync(terminal, ['-e', `sh ${launchPackagerScript}`], {\n        ...procConfig,\n        detached: true,\n      });\n    } catch (error) {\n      // By default, the child shell process will be attached to the parent\n      return execa.sync('sh', [launchPackagerScript], procConfig);\n    }\n  }\n  if (isWindows) {\n    // Awaiting this causes the CLI to hang indefinitely, so this must execute without await.\n    return execa('cmd.exe', ['/C', launchPackagerScript], {\n      ...procConfig,\n      detached: true,\n      stdio: 'ignore',\n    });\n  }\n  logger.error(\n    `Cannot start the packager. Unknown platform ${process.platform}`,\n  );\n  return;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAI2C;AAEpC,SAASA,sBAAsB,CACpCC,IAAY,EACZC,QAAgB,EAChBC,WAAmB,EACnBC,eAAuB,EACvB;EACA;AACF;AACA;EACE,MAAMC,SAAS,GAAG,MAAM,CAACC,IAAI,CAACC,OAAO,CAACC,QAAQ,CAAC;EAC/C,MAAMC,UAAU,GAAGJ,SAAS,GACxB,oBAAoB,GACpB,wBAAwB;EAC5B,MAAMK,mBAAmB,GAAGL,SAAS,GAAG,eAAe,GAAG,eAAe;EACzE,MAAMM,4BAA4B,GAAGN,SAAS,GACzC,sBAAqBJ,IAAK,sBAAqBE,WAAY,2BAA0BC,eAAgB,EAAC,GACtG,yBAAwBH,IAAK,yBAAwBE,WAAY,8BAA6BC,eAAgB,EAAC;EACpH,MAAMQ,eAAe,GAAG,IAAAC,gCAAoB,EAACV,WAAW,EAAE,MAAM,CAAC;EACjE,MAAMW,kBAAkB,GAAGC,eAAI,CAACC,IAAI,CAClCD,eAAI,CAACE,OAAO,CACVC,OAAO,CAACC,OAAO,CAAC,uDAAuD,CAAC,CACzE,EACD,OAAO,CACR;;EAED;AACF;AACA;EACE,MAAMC,eAAe,GAAGL,eAAI,CAACC,IAAI,CAACJ,eAAe,EAAG,GAAEF,mBAAoB,EAAC,CAAC;;EAE5E;AACF;AACA;AACA;EACE,MAAMW,oBAAoB,GAAGN,eAAI,CAACC,IAAI,CAACJ,eAAe,EAAEH,UAAU,CAAC;EACnE,MAAMa,UAA6B,GAAG;IAACC,GAAG,EAAER,eAAI,CAACE,OAAO,CAACG,eAAe;EAAC,CAAC;;EAE1E;AACF;AACA;EACEI,aAAE,CAACC,aAAa,CAACL,eAAe,EAAET,4BAA4B,EAAE;IAC9De,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;AACF;AACA;;EAEE,IAAI;IACF,IAAItB,SAAS,EAAE;MACbmB,aAAE,CAACI,YAAY,CACbb,eAAI,CAACC,IAAI,CAACF,kBAAkB,EAAE,oBAAoB,CAAC,EACnDC,eAAI,CAACC,IAAI,CAACJ,eAAe,EAAE,oBAAoB,CAAC,CACjD;IACH,CAAC,MAAM;MACLY,aAAE,CAACI,YAAY,CACbb,eAAI,CAACC,IAAI,CAACF,kBAAkB,EAAE,wBAAwB,CAAC,EACvDC,eAAI,CAACC,IAAI,CAACJ,eAAe,EAAE,wBAAwB,CAAC,CACrD;IACH;EACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACd,OAAO,KAAIC,oBAAQ,EAChB,sEAAqErB,UAAW,oGAAmG,EACpLoB,KAAK,CACN;EACH;EAEA,IAAItB,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACjC,IAAI;MACF,OAAOuB,gBAAK,CAACC,IAAI,CACf,MAAM,EACN,CAAC,IAAI,EAAE9B,QAAQ,EAAEmB,oBAAoB,CAAC,EACtCC,UAAU,CACX;IACH,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd,OAAOE,gBAAK,CAACC,IAAI,CAAC,MAAM,EAAE,CAACX,oBAAoB,CAAC,EAAEC,UAAU,CAAC;IAC/D;EACF;EACA,IAAIf,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChC,IAAI;MACF,OAAOuB,gBAAK,CAACC,IAAI,CAAC9B,QAAQ,EAAE,CAAC,IAAI,EAAG,MAAKmB,oBAAqB,EAAC,CAAC,EAAE;QAChE,GAAGC,UAAU;QACbW,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd;MACA,OAAOE,gBAAK,CAACC,IAAI,CAAC,IAAI,EAAE,CAACX,oBAAoB,CAAC,EAAEC,UAAU,CAAC;IAC7D;EACF;EACA,IAAIjB,SAAS,EAAE;IACb;IACA,OAAO,IAAA0B,gBAAK,EAAC,SAAS,EAAE,CAAC,IAAI,EAAEV,oBAAoB,CAAC,EAAE;MACpD,GAAGC,UAAU;MACbW,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EACAC,kBAAM,CAACN,KAAK,CACT,+CAA8CtB,OAAO,CAACC,QAAS,EAAC,CAClE;EACD;AACF"}