{"version": 3, "names": ["React", "useEventEmitter", "listen", "listenRef", "useRef", "useEffect", "current", "listeners", "Object", "create", "useCallback", "target", "removeListener", "type", "callback", "callbacks", "undefined", "index", "indexOf", "splice", "addListener", "push", "removed", "emit", "data", "canPreventDefault", "items", "slice", "concat", "keys", "map", "t", "filter", "cb", "i", "self", "lastIndexOf", "event", "defineProperty", "enumerable", "get", "defaultPrevented", "defineProperties", "preventDefault", "value", "for<PERSON>ach", "useMemo"], "sourceRoot": "../../src", "sources": ["useEventEmitter.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAW9B;AACA;AACA;AACA,eAAe,SAASC,eAAe,CACrCC,MAAyB,EACE;EAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACI,MAAM,CAACF,MAAM,CAAC;EAEtCF,KAAK,CAACK,SAAS,CAAC,MAAM;IACpBF,SAAS,CAACG,OAAO,GAAGJ,MAAM;EAC5B,CAAC,CAAC;EAEF,MAAMK,SAAS,GAAGP,KAAK,CAACI,MAAM,CAC5BI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CACpB;EAED,MAAMA,MAAM,GAAGT,KAAK,CAACU,WAAW,CAAEC,MAAc,IAAK;IACnD,MAAMC,cAAc,GAAG,CAACC,IAAY,EAAEC,QAA6B,KAAK;MACtE,MAAMC,SAAS,GAAGR,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,GACrCN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,GAC/BK,SAAS;MAEb,IAAI,CAACD,SAAS,EAAE;QACd;MACF;MAEA,MAAME,KAAK,GAAGF,SAAS,CAACG,OAAO,CAACJ,QAAQ,CAAC;MAEzC,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACdF,SAAS,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B;IACF,CAAC;IAED,MAAMG,WAAW,GAAG,CAACP,IAAY,EAAEC,QAA6B,KAAK;MACnEP,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,GAAGN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;MACvDN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,GAAGJ,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,IAAI,EAAE;MACvEJ,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,CAACU,IAAI,CAACP,QAAQ,CAAC;MAE9C,IAAIQ,OAAO,GAAG,KAAK;MACnB,OAAO,MAAM;QACX;QACA,IAAI,CAACA,OAAO,EAAE;UACZA,OAAO,GAAG,IAAI;UACdV,cAAc,CAACC,IAAI,EAAEC,QAAQ,CAAC;QAChC;MACF,CAAC;IACH,CAAC;IAED,OAAO;MACLM,WAAW;MACXR;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,IAAI,GAAGvB,KAAK,CAACU,WAAW,CAC5B,QAUM;IAAA;IAAA,IAVL;MACCG,IAAI;MACJW,IAAI;MACJb,MAAM;MACNc;IAMF,CAAC;IACC,MAAMC,KAAK,GAAGnB,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;;IAE3C;IACA,MAAME,SAAS,GACbJ,MAAM,KAAKK,SAAS,oBAChBU,KAAK,CAACf,MAAM,CAAC,kDAAb,cAAegB,KAAK,EAAE,GACrB,EAAE,CACAC,MAAM,CAAC,GAAGpB,MAAM,CAACqB,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAAEC,CAAC,IAAKL,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,CAClDC,MAAM,CAAC,CAACC,EAAE,EAAEC,CAAC,EAAEC,IAAI,KAAKA,IAAI,CAACC,WAAW,CAACH,EAAE,CAAC,KAAKC,CAAC,CAAC;IAE5D,MAAMG,KAA8B,GAAG;MACrC,IAAIxB,IAAI,GAAG;QACT,OAAOA,IAAI;MACb;IACF,CAAC;IAED,IAAIF,MAAM,KAAKK,SAAS,EAAE;MACxBR,MAAM,CAAC8B,cAAc,CAACD,KAAK,EAAE,QAAQ,EAAE;QACrCE,UAAU,EAAE,IAAI;QAChBC,GAAG,GAAG;UACJ,OAAO7B,MAAM;QACf;MACF,CAAC,CAAC;IACJ;IAEA,IAAIa,IAAI,KAAKR,SAAS,EAAE;MACtBR,MAAM,CAAC8B,cAAc,CAACD,KAAK,EAAE,MAAM,EAAE;QACnCE,UAAU,EAAE,IAAI;QAChBC,GAAG,GAAG;UACJ,OAAOhB,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IAEA,IAAIC,iBAAiB,EAAE;MACrB,IAAIgB,gBAAgB,GAAG,KAAK;MAE5BjC,MAAM,CAACkC,gBAAgB,CAACL,KAAK,EAAE;QAC7BI,gBAAgB,EAAE;UAChBF,UAAU,EAAE,IAAI;UAChBC,GAAG,GAAG;YACJ,OAAOC,gBAAgB;UACzB;QACF,CAAC;QACDE,cAAc,EAAE;UACdJ,UAAU,EAAE,IAAI;UAChBK,KAAK,GAAG;YACNH,gBAAgB,GAAG,IAAI;UACzB;QACF;MACF,CAAC,CAAC;IACJ;IAEA,sBAAAtC,SAAS,CAACG,OAAO,uDAAjB,wBAAAH,SAAS,EAAWkC,KAAK,CAAC;IAE1BtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,OAAO,CAAEZ,EAAE,IAAKA,EAAE,CAACI,KAAK,CAAC,CAAC;IAErC,OAAOA,KAAK;EACd,CAAC,EACD,EAAE,CACH;EAED,OAAOrC,KAAK,CAAC8C,OAAO,CAAC,OAAO;IAAErC,MAAM;IAAEc;EAAK,CAAC,CAAC,EAAE,CAACd,MAAM,EAAEc,IAAI,CAAC,CAAC;AAChE"}