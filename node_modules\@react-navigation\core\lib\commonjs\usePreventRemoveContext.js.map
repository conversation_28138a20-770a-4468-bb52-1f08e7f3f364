{"version": 3, "names": ["usePreventRemoveContext", "value", "React", "useContext", "PreventRemoveContext", "Error"], "sourceRoot": "../../src", "sources": ["usePreventRemoveContext.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAA0D;AAAA;AAAA;AAE3C,SAASA,uBAAuB,GAAG;EAChD,MAAMC,KAAK,GAAGC,KAAK,CAACC,UAAU,CAACC,6BAAoB,CAAC;EAEpD,IAAIH,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAII,KAAK,CACb,uFAAuF,CACxF;EACH;EAEA,OAAOJ,KAAK;AACd"}