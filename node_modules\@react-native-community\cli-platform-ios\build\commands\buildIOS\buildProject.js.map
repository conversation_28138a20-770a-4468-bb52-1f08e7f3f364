{"version": 3, "names": ["buildProject", "xcodeProject", "udid", "scheme", "args", "Promise", "resolve", "reject", "xcodebuildArgs", "isWorkspace", "name", "xcconfig", "buildFolder", "mode", "destination", "extraParams", "push", "loader", "<PERSON><PERSON><PERSON><PERSON>", "logger", "info", "chalk", "dim", "join", "xcodebuildOutputFormatter", "verbose", "xcbeautifyAvailable", "child_process", "spawn", "stdio", "process", "stdout", "stderr", "xcprettyAvailable", "buildProcess", "getProcessOptions", "buildOutput", "errorOutput", "on", "data", "stringData", "toString", "stdin", "write", "isVerbose", "debug", "start", "repeat", "length", "code", "end", "stop", "printRunDoctorTip", "CLIError", "undefined", "success", "execSync", "error", "packager", "terminal", "port", "env", "RCT_TERMINAL", "RCT_METRO_PORT", "RCT_NO_LAUNCH_PACKAGER"], "sources": ["../../../src/commands/buildIOS/buildProject.ts"], "sourcesContent": ["import child_process, {\n  ChildProcess,\n  SpawnOptionsWithoutStdio,\n} from 'child_process';\nimport chalk from 'chalk';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport {\n  logger,\n  CLIError,\n  printRunDoctorTip,\n  getLoader,\n} from '@react-native-community/cli-tools';\n\nexport type BuildFlags = {\n  mode: string;\n  packager: boolean;\n  verbose: boolean;\n  xcconfig?: string;\n  buildFolder?: string;\n  port: number;\n  terminal: string | undefined;\n  interactive?: boolean;\n  destination?: string;\n  extraParams?: string[];\n};\n\nexport function buildProject(\n  xcodeProject: IOSProjectInfo,\n  udid: string | undefined,\n  scheme: string,\n  args: BuildFlags,\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const xcodebuildArgs = [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      ...(args.xcconfig ? ['-xcconfig', args.xcconfig] : []),\n      ...(args.buildFolder ? ['-derivedDataPath', args.buildFolder] : []),\n      '-configuration',\n      args.mode,\n      '-scheme',\n      scheme,\n      '-destination',\n      (udid\n        ? `id=${udid}`\n        : args.mode === 'Debug'\n        ? 'generic/platform=iOS Simulator'\n        : 'generic/platform=iOS') +\n        (args.destination ? ',' + args.destination : ''),\n    ];\n\n    if (args.extraParams) {\n      xcodebuildArgs.push(...args.extraParams);\n    }\n\n    const loader = getLoader();\n    logger.info(\n      `Building ${chalk.dim(\n        `(using \"xcodebuild ${xcodebuildArgs.join(' ')}\")`,\n      )}`,\n    );\n    let xcodebuildOutputFormatter: ChildProcess | any;\n    if (!args.verbose) {\n      if (xcbeautifyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcbeautify', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      } else if (xcprettyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcpretty', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      }\n    }\n    const buildProcess = child_process.spawn(\n      'xcodebuild',\n      xcodebuildArgs,\n      getProcessOptions(args),\n    );\n    let buildOutput = '';\n    let errorOutput = '';\n    buildProcess.stdout.on('data', (data: Buffer) => {\n      const stringData = data.toString();\n      buildOutput += stringData;\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.write(data);\n      } else {\n        if (logger.isVerbose()) {\n          logger.debug(stringData);\n        } else {\n          loader.start(\n            `Building the app${'.'.repeat(buildOutput.length % 10)}`,\n          );\n        }\n      }\n    });\n\n    buildProcess.stderr.on('data', (data: Buffer) => {\n      errorOutput += data;\n    });\n    buildProcess.on('close', (code: number) => {\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.end();\n      } else {\n        loader.stop();\n      }\n      if (code !== 0) {\n        printRunDoctorTip();\n        reject(\n          new CLIError(\n            `\n            Failed to build iOS project.\n\n            We ran \"xcodebuild\" command but it exited with error code ${code}. To debug build\n            logs further, consider building your app with Xcode.app, by opening\n            ${xcodeProject.name}.\n          `,\n            xcodebuildOutputFormatter\n              ? undefined\n              : buildOutput + '\\n' + errorOutput,\n          ),\n        );\n        return;\n      }\n      logger.success('Successfully built the app');\n      resolve(buildOutput);\n    });\n  });\n}\n\nfunction xcbeautifyAvailable() {\n  try {\n    child_process.execSync('xcbeautify --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction xcprettyAvailable() {\n  try {\n    child_process.execSync('xcpretty --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction getProcessOptions({\n  packager,\n  terminal,\n  port,\n}: {\n  packager: boolean;\n  terminal: string | undefined;\n  port: number;\n}): SpawnOptionsWithoutStdio {\n  if (packager) {\n    return {\n      env: {\n        ...process.env,\n        RCT_TERMINAL: terminal,\n        RCT_METRO_PORT: port.toString(),\n      },\n    };\n  }\n\n  return {\n    env: {\n      ...process.env,\n      RCT_TERMINAL: terminal,\n      RCT_NO_LAUNCH_PACKAGER: 'true',\n    },\n  };\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAK2C;AAepC,SAASA,YAAY,CAC1BC,YAA4B,EAC5BC,IAAwB,EACxBC,MAAc,EACdC,IAAgB,EACC;EACjB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,cAAc,GAAG,CACrBP,YAAY,CAACQ,WAAW,GAAG,YAAY,GAAG,UAAU,EACpDR,YAAY,CAACS,IAAI,EACjB,IAAIN,IAAI,CAACO,QAAQ,GAAG,CAAC,WAAW,EAAEP,IAAI,CAACO,QAAQ,CAAC,GAAG,EAAE,CAAC,EACtD,IAAIP,IAAI,CAACQ,WAAW,GAAG,CAAC,kBAAkB,EAAER,IAAI,CAACQ,WAAW,CAAC,GAAG,EAAE,CAAC,EACnE,gBAAgB,EAChBR,IAAI,CAACS,IAAI,EACT,SAAS,EACTV,MAAM,EACN,cAAc,EACd,CAACD,IAAI,GACA,MAAKA,IAAK,EAAC,GACZE,IAAI,CAACS,IAAI,KAAK,OAAO,GACrB,gCAAgC,GAChC,sBAAsB,KACvBT,IAAI,CAACU,WAAW,GAAG,GAAG,GAAGV,IAAI,CAACU,WAAW,GAAG,EAAE,CAAC,CACnD;IAED,IAAIV,IAAI,CAACW,WAAW,EAAE;MACpBP,cAAc,CAACQ,IAAI,CAAC,GAAGZ,IAAI,CAACW,WAAW,CAAC;IAC1C;IAEA,MAAME,MAAM,GAAG,IAAAC,qBAAS,GAAE;IAC1BC,kBAAM,CAACC,IAAI,CACR,YAAWC,gBAAK,CAACC,GAAG,CAClB,sBAAqBd,cAAc,CAACe,IAAI,CAAC,GAAG,CAAE,IAAG,CAClD,EAAC,CACJ;IACD,IAAIC,yBAA6C;IACjD,IAAI,CAACpB,IAAI,CAACqB,OAAO,EAAE;MACjB,IAAIC,mBAAmB,EAAE,EAAE;QACzBF,yBAAyB,GAAGG,wBAAa,CAACC,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE;UAChEC,KAAK,EAAE,CAAC,MAAM,EAAEC,OAAO,CAACC,MAAM,EAAED,OAAO,CAACE,MAAM;QAChD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIC,iBAAiB,EAAE,EAAE;QAC9BT,yBAAyB,GAAGG,wBAAa,CAACC,KAAK,CAAC,UAAU,EAAE,EAAE,EAAE;UAC9DC,KAAK,EAAE,CAAC,MAAM,EAAEC,OAAO,CAACC,MAAM,EAAED,OAAO,CAACE,MAAM;QAChD,CAAC,CAAC;MACJ;IACF;IACA,MAAME,YAAY,GAAGP,wBAAa,CAACC,KAAK,CACtC,YAAY,EACZpB,cAAc,EACd2B,iBAAiB,CAAC/B,IAAI,CAAC,CACxB;IACD,IAAIgC,WAAW,GAAG,EAAE;IACpB,IAAIC,WAAW,GAAG,EAAE;IACpBH,YAAY,CAACH,MAAM,CAACO,EAAE,CAAC,MAAM,EAAGC,IAAY,IAAK;MAC/C,MAAMC,UAAU,GAAGD,IAAI,CAACE,QAAQ,EAAE;MAClCL,WAAW,IAAII,UAAU;MACzB,IAAIhB,yBAAyB,EAAE;QAC7BA,yBAAyB,CAACkB,KAAK,CAACC,KAAK,CAACJ,IAAI,CAAC;MAC7C,CAAC,MAAM;QACL,IAAIpB,kBAAM,CAACyB,SAAS,EAAE,EAAE;UACtBzB,kBAAM,CAAC0B,KAAK,CAACL,UAAU,CAAC;QAC1B,CAAC,MAAM;UACLvB,MAAM,CAAC6B,KAAK,CACT,mBAAkB,GAAG,CAACC,MAAM,CAACX,WAAW,CAACY,MAAM,GAAG,EAAE,CAAE,EAAC,CACzD;QACH;MACF;IACF,CAAC,CAAC;IAEFd,YAAY,CAACF,MAAM,CAACM,EAAE,CAAC,MAAM,EAAGC,IAAY,IAAK;MAC/CF,WAAW,IAAIE,IAAI;IACrB,CAAC,CAAC;IACFL,YAAY,CAACI,EAAE,CAAC,OAAO,EAAGW,IAAY,IAAK;MACzC,IAAIzB,yBAAyB,EAAE;QAC7BA,yBAAyB,CAACkB,KAAK,CAACQ,GAAG,EAAE;MACvC,CAAC,MAAM;QACLjC,MAAM,CAACkC,IAAI,EAAE;MACf;MACA,IAAIF,IAAI,KAAK,CAAC,EAAE;QACd,IAAAG,6BAAiB,GAAE;QACnB7C,MAAM,CACJ,KAAI8C,oBAAQ,EACT;AACb;AACA;AACA,wEAAwEJ,IAAK;AAC7E;AACA,cAAchD,YAAY,CAACS,IAAK;AAChC,WAAW,EACCc,yBAAyB,GACrB8B,SAAS,GACTlB,WAAW,GAAG,IAAI,GAAGC,WAAW,CACrC,CACF;QACD;MACF;MACAlB,kBAAM,CAACoC,OAAO,CAAC,4BAA4B,CAAC;MAC5CjD,OAAO,CAAC8B,WAAW,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASV,mBAAmB,GAAG;EAC7B,IAAI;IACFC,wBAAa,CAAC6B,QAAQ,CAAC,sBAAsB,EAAE;MAC7C3B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO4B,KAAK,EAAE;IACd,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAEA,SAASxB,iBAAiB,GAAG;EAC3B,IAAI;IACFN,wBAAa,CAAC6B,QAAQ,CAAC,oBAAoB,EAAE;MAC3C3B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO4B,KAAK,EAAE;IACd,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAEA,SAAStB,iBAAiB,CAAC;EACzBuB,QAAQ;EACRC,QAAQ;EACRC;AAKF,CAAC,EAA4B;EAC3B,IAAIF,QAAQ,EAAE;IACZ,OAAO;MACLG,GAAG,EAAE;QACH,GAAG/B,OAAO,CAAC+B,GAAG;QACdC,YAAY,EAAEH,QAAQ;QACtBI,cAAc,EAAEH,IAAI,CAACnB,QAAQ;MAC/B;IACF,CAAC;EACH;EAEA,OAAO;IACLoB,GAAG,EAAE;MACH,GAAG/B,OAAO,CAAC+B,GAAG;MACdC,YAAY,EAAEH,QAAQ;MACtBK,sBAAsB,EAAE;IAC1B;EACF,CAAC;AACH"}