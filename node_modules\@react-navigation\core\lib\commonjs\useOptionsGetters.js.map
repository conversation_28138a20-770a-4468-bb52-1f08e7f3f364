{"version": 3, "names": ["useOptionsGetters", "key", "options", "navigation", "optionsRef", "React", "useRef", "optionsGettersFromChildRef", "onOptionsChange", "useContext", "NavigationBuilderContext", "addOptionsGetter", "parentAddOptionsGetter", "NavigationStateContext", "optionsChangeListener", "useCallback", "isFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "current", "length", "useEffect", "addListener", "getOptionsFromListener", "hasOwnProperty", "result", "getCurrentOptions", "optionsFromListener", "getter"], "sourceRoot": "../../src", "sources": ["useOptionsGetters.tsx"], "mappings": ";;;;;;AACA;AAEA;AACA;AAA8D;AAAA;AAAA;AAS/C,SAASA,iBAAiB,OAI7B;EAAA,IAJ8B;IACxCC,GAAG;IACHC,OAAO;IACPC;EACO,CAAC;EACR,MAAMC,UAAU,GAAGC,KAAK,CAACC,MAAM,CAAqBJ,OAAO,CAAC;EAC5D,MAAMK,0BAA0B,GAAGF,KAAK,CAACC,MAAM,CAE7C,CAAC,CAAC,CAAC;EAEL,MAAM;IAAEE;EAAgB,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACC,iCAAwB,CAAC;EACtE,MAAM;IAAEC,gBAAgB,EAAEC;EAAuB,CAAC,GAAGP,KAAK,CAACI,UAAU,CACnEI,+BAAsB,CACvB;EAED,MAAMC,qBAAqB,GAAGT,KAAK,CAACU,WAAW,CAAC,MAAM;IACpD,MAAMC,SAAS,GAAG,CAAAb,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,SAAS,EAAE,KAAI,IAAI;IACjD,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACZ,0BAA0B,CAACa,OAAO,CAAC,CAACC,MAAM;IAE1E,IAAIL,SAAS,IAAI,CAACC,WAAW,EAAE;MAC7BT,eAAe,CAACJ,UAAU,CAACgB,OAAO,IAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACjB,UAAU,EAAEK,eAAe,CAAC,CAAC;EAEjCH,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpBlB,UAAU,CAACgB,OAAO,GAAGlB,OAAO;IAC5BY,qBAAqB,EAAE;IAEvB,OAAOX,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,WAAW,CAAC,OAAO,EAAET,qBAAqB,CAAC;EAChE,CAAC,EAAE,CAACX,UAAU,EAAED,OAAO,EAAEY,qBAAqB,CAAC,CAAC;EAEhD,MAAMU,sBAAsB,GAAGnB,KAAK,CAACU,WAAW,CAAC,MAAM;IACrD,KAAK,IAAId,GAAG,IAAIM,0BAA0B,CAACa,OAAO,EAAE;MAClD,IAAIb,0BAA0B,CAACa,OAAO,CAACK,cAAc,CAACxB,GAAG,CAAC,EAAE;QAAA;QAC1D,MAAMyB,MAAM,4BAAG,0BAAAnB,0BAA0B,CAACa,OAAO,EAACnB,GAAG,CAAC,0DAAvC,kDAA2C;;QAE1D;QACA,IAAIyB,MAAM,KAAK,IAAI,EAAE;UACnB,OAAOA,MAAM;QACf;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAGtB,KAAK,CAACU,WAAW,CAAC,MAAM;IAChD,MAAMC,SAAS,GAAG,CAAAb,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,SAAS,EAAE,KAAI,IAAI;IAEjD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA,MAAMY,mBAAmB,GAAGJ,sBAAsB,EAAE;IAEpD,IAAII,mBAAmB,KAAK,IAAI,EAAE;MAChC,OAAOA,mBAAmB;IAC5B;IAEA,OAAOxB,UAAU,CAACgB,OAAO;EAC3B,CAAC,EAAE,CAACjB,UAAU,EAAEqB,sBAAsB,CAAC,CAAC;EAExCnB,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,OAAOV,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAGX,GAAG,EAAG0B,iBAAiB,CAAC;EAC1D,CAAC,EAAE,CAACA,iBAAiB,EAAEf,sBAAsB,EAAEX,GAAG,CAAC,CAAC;EAEpD,MAAMU,gBAAgB,GAAGN,KAAK,CAACU,WAAW,CACxC,CAACd,GAAW,EAAE4B,MAAuC,KAAK;IACxDtB,0BAA0B,CAACa,OAAO,CAACnB,GAAG,CAAC,GAAG4B,MAAM;IAChDf,qBAAqB,EAAE;IAEvB,OAAO,MAAM;MACX;MACA,OAAOP,0BAA0B,CAACa,OAAO,CAACnB,GAAG,CAAC;MAC9Ca,qBAAqB,EAAE;IACzB,CAAC;EACH,CAAC,EACD,CAACA,qBAAqB,CAAC,CACxB;EAED,OAAO;IACLH,gBAAgB;IAChBgB;EACF,CAAC;AACH"}