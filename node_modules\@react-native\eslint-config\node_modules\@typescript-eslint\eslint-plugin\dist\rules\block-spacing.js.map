{"version": 3, "file": "block-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/block-spacing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2D;AAE3D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,eAAe,CAAC,CAAC;AAKpD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,0FAA0F;YAC5F,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAE1B,MAAM,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC;QACjC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,iBAAiB,KAAK,OAAO,CAAC;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/C;;;WAGG;QACH,SAAS,YAAY,CACnB,IAAgC;YAEhC,uBAAuB;YACvB,uDAAuD;YACvD,OAAO,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE;gBACpC,MAAM,EAAE,KAAK,CAAC,EAAE,CACd,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG;aACnE,CAA6B,CAAC;QACjC,CAAC;QAED;;;;;;;;;;WAUG;QACH,SAAS,OAAO,CAAC,IAAoB,EAAE,KAAqB;YAC1D,OAAO,CACL,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;gBACpC,UAAU,CAAC,cAAe,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,MAAM,CACnD,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,wBAAwB,CAAC,IAAgC;YAChE,mDAAmD;YACnD,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAE,CAAC;YAClD,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,EAAE;gBACrD,eAAe,EAAE,IAAI;aACtB,CAAE,CAAC;YACJ,MAAM,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,EAAE;gBACtD,eAAe,EAAE,IAAI;aACtB,CAAE,CAAC;YAEJ,wCAAwC;YACxC,IACE,SAAS,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;gBAC7C,SAAS,CAAC,KAAK,KAAK,GAAG;gBACvB,UAAU,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;gBAC9C,UAAU,CAAC,KAAK,KAAK,GAAG;gBACxB,UAAU,KAAK,UAAU,EACzB;gBACA,OAAO;aACR;YAED,sCAAsC;YACtC,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,EAAE;gBACvD,OAAO;aACR;YAED,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gBACnC,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;gBAExB,IAAI,SAAS,KAAK,OAAO,EAAE;oBACzB,GAAG,GAAG;wBACJ,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG;wBACxB,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;qBAC1B,CAAC;iBACH;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS;oBACT,IAAI,EAAE;wBACJ,QAAQ,EAAE,OAAO;wBACjB,KAAK,EAAE,SAAS,CAAC,KAAK;qBACvB;oBACD,GAAG,CAAC,KAAK;wBACP,IAAI,MAAM,EAAE;4BACV,OAAO,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;yBAChD;wBAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtE,CAAC;iBACF,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gBACnC,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;gBAEzB,IAAI,SAAS,KAAK,OAAO,EAAE;oBACzB,GAAG,GAAG;wBACJ,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG;wBACxB,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;qBAC1B,CAAC;iBACH;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS;oBACT,IAAI,EAAE;wBACJ,QAAQ,EAAE,QAAQ;wBAClB,KAAK,EAAE,UAAU,CAAC,KAAK;qBACxB;oBACD,GAAG,CAAC,KAAK;wBACP,IAAI,MAAM,EAAE;4BACV,OAAO,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;yBAC9C;wBAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtE,CAAC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QACD,uCACK,SAAS;YAEZ,mEAAmE;YACnE,mIAAmI;YACnI,wFAAwF;YACxF,sEAAsE;YACtE,eAAe,EAAE,SAAS,CAAC,cAAuB,EAClD,aAAa,EAAE,SAAS,CAAC,cAAuB,EAChD,iBAAiB,EAAE,wBAAwB,IAC3C;IACJ,CAAC;CACF,CAAC,CAAC"}