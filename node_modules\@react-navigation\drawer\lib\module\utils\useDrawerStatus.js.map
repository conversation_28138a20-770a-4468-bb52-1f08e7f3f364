{"version": 3, "names": ["React", "DrawerStatusContext", "useDrawerStatus", "drawerStatus", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useDrawerStatus.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,mBAAmB,MAAM,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAe,GAAiB;EACtD,MAAMC,YAAY,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAE1D,IAAIE,YAAY,KAAKE,SAAS,EAAE;IAC9B,MAAM,IAAIC,KAAK,CACb,sEAAsE,CACvE;EACH;EAEA,OAAOH,YAAY;AACrB"}