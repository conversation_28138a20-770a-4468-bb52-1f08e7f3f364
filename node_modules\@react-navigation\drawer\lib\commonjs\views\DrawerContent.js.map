{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "descriptors", "state", "rest", "focusedRoute", "routes", "index", "focusedDescriptor", "key", "focusedOptions", "options", "drawerContentStyle", "drawerContentContainerStyle"], "sourceRoot": "../../../src", "sources": ["views/DrawerContent.tsx"], "mappings": ";;;;;;AAAA;AAGA;AACA;AAA8C;AAAA;AAAA;AAAA;AAE/B,SAASA,aAAa,OAIL;EAAA,IAJM;IACpCC,WAAW;IACXC,KAAK;IACL,GAAGC;EACwB,CAAC;EAC5B,MAAMC,YAAY,GAAGF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACI,KAAK,CAAC;EAC9C,MAAMC,iBAAiB,GAAGN,WAAW,CAACG,YAAY,CAACI,GAAG,CAAC;EACvD,MAAMC,cAAc,GAAGF,iBAAiB,CAACG,OAAO;EAEhD,MAAM;IAAEC,kBAAkB;IAAEC;EAA4B,CAAC,GAAGH,cAAc;EAE1E,oBACE,oBAAC,gCAAuB,eAClBN,IAAI;IACR,qBAAqB,EAAES,2BAA4B;IACnD,KAAK,EAAED;EAAmB,iBAE1B,oBAAC,uBAAc;IAAC,WAAW,EAAEV,WAAY;IAAC,KAAK,EAAEC;EAAM,GAAKC,IAAI,EAAI,CAC5C;AAE9B"}