{"version": 3, "file": "adjacent-overload-signatures.js", "sourceRoot": "", "sources": ["../../src/rules/adjacent-overload-signatures.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAchC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,OAAO;SACrB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,iBAAiB,EAAE,6CAA6C;SACjE;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAS3C;;;;WAIG;QACH,SAAS,eAAe,CAAC,MAAqB;;YAC5C,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,IAAI,CAAC;aACb;YAED,MAAM,QAAQ,GAAG,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;YAEvD,QAAQ,MAAM,CAAC,IAAI,EAAE;gBACnB,KAAK,sBAAc,CAAC,wBAAwB,CAAC;gBAC7C,KAAK,sBAAc,CAAC,sBAAsB,CAAC,CAAC;oBAC1C,yCAAyC;oBACzC,uCAAuC;oBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;wBACvB,OAAO,IAAI,CAAC;qBACb;oBAED,OAAO,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;iBAC5C;gBACD,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,mBAAmB,CAAC,CAAC;oBACvC,MAAM,IAAI,GAAG,MAAA,MAAA,MAAM,CAAC,EAAE,0CAAE,IAAI,mCAAI,IAAI,CAAC;oBACrC,IAAI,IAAI,IAAI,IAAI,EAAE;wBAChB,OAAO,IAAI,CAAC;qBACb;oBACD,OAAO;wBACL,IAAI;wBACJ,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;wBACpB,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;qBACjC,CAAC;iBACH;gBACD,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,uCACK,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,KAC7C,MAAM,EAAE,QAAQ,EAChB,aAAa,EAAE,KAAK,IACpB;gBACJ,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,OAAO;wBACL,IAAI,EAAE,MAAM;wBACZ,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,IAAI;wBACnB,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;qBACjC,CAAC;gBACJ,KAAK,sBAAc,CAAC,+BAA+B;oBACjD,OAAO;wBACL,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,KAAK;wBACpB,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;qBACjC,CAAC;gBACJ,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,uCACK,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,KAC7C,MAAM,EAAE,QAAQ,EAChB,aAAa,EAAE,KAAK,IACpB;aACL;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,YAAY,CAAC,OAAe,EAAE,OAAsB;YAC3D,OAAO,CACL,CAAC,CAAC,OAAO;gBACT,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;gBAC7B,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;gBACjC,OAAO,CAAC,aAAa,KAAK,OAAO,CAAC,aAAa;gBAC/C,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAC9B,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,IAAc;YAChC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,sBAAc,CAAC,SAAS,CAAC;gBAC9B,KAAK,sBAAc,CAAC,OAAO,CAAC;gBAC5B,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,sBAAc,CAAC,eAAe,CAAC;gBACpC,KAAK,sBAAc,CAAC,cAAc;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC;gBAEnB,KAAK,sBAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B,CAAC,IAAc;YACjD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,OAAO,EAAE;gBACX,IAAI,UAAU,GAAkB,IAAI,CAAC;gBACrC,MAAM,WAAW,GAAa,EAAE,CAAC;gBAEjC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACvB,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;oBACvC,IAAI,MAAM,IAAI,IAAI,EAAE;wBAClB,UAAU,GAAG,IAAI,CAAC;wBAClB,OAAO;qBACR;oBAED,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAC/C,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CACjC,CAAC;oBACF,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;wBACnD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,MAAM;4BACZ,SAAS,EAAE,mBAAmB;4BAC9B,IAAI,EAAE;gCACJ,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;6BACxD;yBACF,CAAC,CAAC;qBACJ;yBAAM,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;wBACvB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC1B;oBAED,UAAU,GAAG,MAAM,CAAC;gBACtB,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,SAAS,EAAE,2BAA2B;YACtC,OAAO,EAAE,2BAA2B;YACpC,aAAa,EAAE,2BAA2B;YAC1C,aAAa,EAAE,2BAA2B;YAC1C,eAAe,EAAE,2BAA2B;YAC5C,cAAc,EAAE,2BAA2B;SAC5C,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}