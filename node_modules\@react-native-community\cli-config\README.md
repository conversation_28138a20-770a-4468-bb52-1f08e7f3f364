# @react-native-community/cli-config

This package is part of the [React Native CLI](../../README.md). It contains commands for managing the configuration of React Native app.

## Installation

```sh
yarn add @react-native-community/cli-config
```

## Commands

### `config`

Usage:

```sh
react-native config
```

Output project and dependencies configuration in JSON format to stdout. Used by [autolinking](./../../docs/autolinking.md).
