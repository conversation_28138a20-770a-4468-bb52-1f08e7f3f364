!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("react-dom/client"),require("react-dom/test-utils")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","react-dom/client","react-dom/test-utils"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.React,e.ReactDOM,e.ReactDOMClient,e.ReactTestUtils)}(this,(function(e,t,r,n,a){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function i(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=l(t),s=o(r),d=l(n),c=l(a),p={},m={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,a]of Object.entries(r))n[t]={open:`[${a[0]}m`,close:`[${a[1]}m`},r[t]=n[t],e.set(a[0],a[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(m);var f={};Object.defineProperty(f,"__esModule",{value:!0}),f.printIteratorEntries=function(e,t,r,n,a,o,l){void 0===l&&(l=": ");let i="",u=e.next();if(!u.done){i+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){const r=o(u.value[0],t,s,n,a),d=o(u.value[1],t,s,n,a);i+=s+r+l+d,u=e.next(),u.done?t.min||(i+=","):i+=","+t.spacingInner}i+=t.spacingOuter+r}return i},f.printIteratorValues=function(e,t,r,n,a,o){let l="",i=e.next();if(!i.done){l+=t.spacingOuter;const u=r+t.indent;for(;!i.done;)l+=u+o(i.value,t,u,n,a),i=e.next(),i.done?t.min||(l+=","):l+=","+t.spacingInner;l+=t.spacingOuter+r}return l},f.printListItems=function(e,t,r,n,a,o){let l="";if(e.length){l+=t.spacingOuter;const i=r+t.indent;for(let r=0;r<e.length;r++)l+=i,r in e&&(l+=o(e[r],t,i,n,a)),r<e.length-1?l+=","+t.spacingInner:t.min||(l+=",");l+=t.spacingOuter+r}return l},f.printObjectProperties=function(e,t,r,n,a,o){let l="";const i=((e,t)=>{const r=Object.keys(e).sort(t);Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)}));return r})(e,t.compareKeys);if(i.length){l+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<i.length;r++){const s=i[r],d=o(s,t,u,n,a),c=o(e[s],t,u,n,a);l+=u+d+": "+c,r<i.length-1?l+=","+t.spacingInner:t.min||(l+=",")}l+=t.spacingOuter+r}return l};var b={};Object.defineProperty(b,"__esModule",{value:!0}),b.test=b.serialize=b.default=void 0;var v=f,y="undefined"!=typeof globalThis?globalThis:void 0!==y?y:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),h=y["jest-symbol-do-not-touch"]||y.Symbol;const g="function"==typeof h&&h.for?h.for("jest.asymmetricMatcher"):1267621,C=" ",q=(e,t,r,n,a,o)=>{const l=e.toString();return"ArrayContaining"===l||"ArrayNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+C+"["+(0,v.printListItems)(e.sample,t,r,n,a,o)+"]":"ObjectContaining"===l||"ObjectNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+C+"{"+(0,v.printObjectProperties)(e.sample,t,r,n,a,o)+"}":"StringMatching"===l||"StringNotMatching"===l||"StringContaining"===l||"StringNotContaining"===l?l+C+o(e.sample,t,r,n,a):e.toAsymmetricMatcher()};b.serialize=q;const P=e=>e&&e.$$typeof===g;b.test=P;var E={serialize:q,test:P};b.default=E;var w={};Object.defineProperty(w,"__esModule",{value:!0}),w.test=w.serialize=w.default=void 0;var x=O((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),R=O(m.exports);function O(e){return e&&e.__esModule?e:{default:e}}const T=e=>"string"==typeof e&&!!e.match((0,x.default)());w.test=T;const _=(e,t,r,n,a,o)=>o(e.replace((0,x.default)(),(e=>{switch(e){case R.default.red.close:case R.default.green.close:case R.default.cyan.close:case R.default.gray.close:case R.default.white.close:case R.default.yellow.close:case R.default.bgRed.close:case R.default.bgGreen.close:case R.default.bgYellow.close:case R.default.inverse.close:case R.default.dim.close:case R.default.bold.close:case R.default.reset.open:case R.default.reset.close:return"</>";case R.default.red.open:return"<red>";case R.default.green.open:return"<green>";case R.default.cyan.open:return"<cyan>";case R.default.gray.open:return"<gray>";case R.default.white.open:return"<white>";case R.default.yellow.open:return"<yellow>";case R.default.bgRed.open:return"<bgRed>";case R.default.bgGreen.open:return"<bgGreen>";case R.default.bgYellow.open:return"<bgYellow>";case R.default.inverse.open:return"<inverse>";case R.default.dim.open:return"<dim>";case R.default.bold.open:return"<bold>";default:return""}})),t,r,n,a);w.serialize=_;var M={serialize:_,test:T};w.default=M;var A={};Object.defineProperty(A,"__esModule",{value:!0}),A.test=A.serialize=A.default=void 0;var j=f;const S=["DOMStringMap","NamedNodeMap"],B=/^(HTML\w*Collection|NodeList)$/,I=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==S.indexOf(t)||B.test(t));var t};A.test=I;const N=(e,t,r,n,a,o)=>{const l=e.constructor.name;return++n>t.maxDepth?"["+l+"]":(t.min?"":l+" ")+(-1!==S.indexOf(l)?"{"+(0,j.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,a,o)+"}":"["+(0,j.printListItems)(Array.from(e),t,r,n,a,o)+"]")};A.serialize=N;var k={serialize:N,test:I};A.default=k;var F={},L={},U={};Object.defineProperty(U,"__esModule",{value:!0}),U.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(L,"__esModule",{value:!0}),L.printText=L.printProps=L.printElementAsLeaf=L.printElement=L.printComment=L.printChildren=void 0;var D,H=(D=U)&&D.__esModule?D:{default:D};L.printProps=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")};L.printChildren=(e,t,r,n,a,o)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?z(e,t):o(e,t,r,n,a)))).join("");const z=(e,t)=>{const r=t.colors.content;return r.open+(0,H.default)(e)+r.close};L.printText=z;L.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,H.default)(e)+"--\x3e"+r.close};L.printElement=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close};L.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(F,"__esModule",{value:!0}),F.test=F.serialize=F.default=void 0;var V=L;const $=/^((HTML|SVG)\w*)?Element$/,W=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&($.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function G(e){return 11===e.nodeType}F.test=W;const Q=(e,t,r,n,a,o)=>{if(function(e){return 3===e.nodeType}(e))return(0,V.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,V.printComment)(e.data,t);const l=G(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,V.printElementAsLeaf)(l,t):(0,V.printElement)(l,(0,V.printProps)(G(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),G(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,a,o),(0,V.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,a,o),t,r)};F.serialize=Q;var J={serialize:Q,test:W};F.default=J;var X={};Object.defineProperty(X,"__esModule",{value:!0}),X.test=X.serialize=X.default=void 0;var K=f;const Y="@@__IMMUTABLE_ORDERED__@@",Z=e=>"Immutable."+e,ee=e=>"["+e+"]",te=" ";const re=(e,t,r,n,a,o,l)=>++n>t.maxDepth?ee(Z(l)):Z(l)+te+"["+(0,K.printIteratorValues)(e.values(),t,r,n,a,o)+"]",ne=(e,t,r,n,a,o)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,a,o,l)=>++n>t.maxDepth?ee(Z(l)):Z(l)+te+"{"+(0,K.printIteratorEntries)(e.entries(),t,r,n,a,o)+"}")(e,t,r,n,a,o,e[Y]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?re(e,t,r,n,a,o,"List"):e["@@__IMMUTABLE_SET__@@"]?re(e,t,r,n,a,o,e[Y]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?re(e,t,r,n,a,o,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,a,o)=>{const l=Z("Seq");return++n>t.maxDepth?ee(l):e["@@__IMMUTABLE_KEYED__@@"]?l+te+"{"+(e._iter||e._object?(0,K.printIteratorEntries)(e.entries(),t,r,n,a,o):"…")+"}":l+te+"["+(e._iter||e._array||e._collection||e._iterable?(0,K.printIteratorValues)(e.values(),t,r,n,a,o):"…")+"]"})(e,t,r,n,a,o):((e,t,r,n,a,o)=>{const l=Z(e._name||"Record");return++n>t.maxDepth?ee(l):l+te+"{"+(0,K.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,a,o)+"}"})(e,t,r,n,a,o);X.serialize=ne;const ae=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);X.test=ae;var oe={serialize:ne,test:ae};X.default=oe;var le={},ie={exports:{}},ue={},se=60103,de=60106,ce=60107,pe=60108,me=60114,fe=60109,be=60110,ve=60112,ye=60113,he=60120,ge=60115,Ce=60116,qe=60121,Pe=60122,Ee=60117,we=60129,xe=60131;if("function"==typeof Symbol&&Symbol.for){var Re=Symbol.for;se=Re("react.element"),de=Re("react.portal"),ce=Re("react.fragment"),pe=Re("react.strict_mode"),me=Re("react.profiler"),fe=Re("react.provider"),be=Re("react.context"),ve=Re("react.forward_ref"),ye=Re("react.suspense"),he=Re("react.suspense_list"),ge=Re("react.memo"),Ce=Re("react.lazy"),qe=Re("react.block"),Pe=Re("react.server.block"),Ee=Re("react.fundamental"),we=Re("react.debug_trace_mode"),xe=Re("react.legacy_hidden")}function Oe(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case se:switch(e=e.type){case ce:case me:case pe:case ye:case he:return e;default:switch(e=e&&e.$$typeof){case be:case ve:case Ce:case ge:case fe:return e;default:return t}}case de:return t}}}var Te=fe,_e=se,Me=ve,Ae=ce,je=Ce,Se=ge,Be=de,Ie=me,Ne=pe,ke=ye;ue.ContextConsumer=be,ue.ContextProvider=Te,ue.Element=_e,ue.ForwardRef=Me,ue.Fragment=Ae,ue.Lazy=je,ue.Memo=Se,ue.Portal=Be,ue.Profiler=Ie,ue.StrictMode=Ne,ue.Suspense=ke,ue.isAsyncMode=function(){return!1},ue.isConcurrentMode=function(){return!1},ue.isContextConsumer=function(e){return Oe(e)===be},ue.isContextProvider=function(e){return Oe(e)===fe},ue.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===se},ue.isForwardRef=function(e){return Oe(e)===ve},ue.isFragment=function(e){return Oe(e)===ce},ue.isLazy=function(e){return Oe(e)===Ce},ue.isMemo=function(e){return Oe(e)===ge},ue.isPortal=function(e){return Oe(e)===de},ue.isProfiler=function(e){return Oe(e)===me},ue.isStrictMode=function(e){return Oe(e)===pe},ue.isSuspense=function(e){return Oe(e)===ye},ue.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===ce||e===me||e===we||e===pe||e===ye||e===he||e===xe||"object"==typeof e&&null!==e&&(e.$$typeof===Ce||e.$$typeof===ge||e.$$typeof===fe||e.$$typeof===be||e.$$typeof===ve||e.$$typeof===Ee||e.$$typeof===qe||e[0]===Pe)},ue.typeOf=Oe,ie.exports=ue,Object.defineProperty(le,"__esModule",{value:!0}),le.test=le.serialize=le.default=void 0;var Fe=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=Ue(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(ie.exports),Le=L;function Ue(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(Ue=function(e){return e?r:t})(e)}const De=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{De(e,t)})):null!=e&&!1!==e&&t.push(e),t},He=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(Fe.isFragment(e))return"React.Fragment";if(Fe.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(Fe.isContextProvider(e))return"Context.Provider";if(Fe.isContextConsumer(e))return"Context.Consumer";if(Fe.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(Fe.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},ze=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,Le.printElementAsLeaf)(He(e),t):(0,Le.printElement)(He(e),(0,Le.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,a,o),(0,Le.printChildren)(De(e.props.children),t,r+t.indent,n,a,o),t,r);le.serialize=ze;const Ve=e=>null!=e&&Fe.isElement(e);le.test=Ve;var $e={serialize:ze,test:Ve};le.default=$e;var We={};Object.defineProperty(We,"__esModule",{value:!0}),We.test=We.serialize=We.default=void 0;var Ge=L,Qe="undefined"!=typeof globalThis?globalThis:void 0!==Qe?Qe:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),Je=Qe["jest-symbol-do-not-touch"]||Qe.Symbol;const Xe="function"==typeof Je&&Je.for?Je.for("react.test.json"):245830487,Ke=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,Ge.printElementAsLeaf)(e.type,t):(0,Ge.printElement)(e.type,e.props?(0,Ge.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,a,o):"",e.children?(0,Ge.printChildren)(e.children,t,r+t.indent,n,a,o):"",t,r);We.serialize=Ke;const Ye=e=>e&&e.$$typeof===Xe;We.test=Ye;var Ze={serialize:Ke,test:Ye};We.default=Ze,Object.defineProperty(p,"__esModule",{value:!0});var et=p.default=p.DEFAULT_OPTIONS=void 0,tt=p.format=Ft,rt=p.plugins=void 0,nt=pt(m.exports),at=f,ot=pt(b),lt=pt(w),it=pt(A),ut=pt(F),st=pt(X),dt=pt(le),ct=pt(We);function pt(e){return e&&e.__esModule?e:{default:e}}const mt=Object.prototype.toString,ft=Date.prototype.toISOString,bt=Error.prototype.toString,vt=RegExp.prototype.toString,yt=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",ht=/^Symbol\((.*)\)(.*)$/,gt=/\n/gi;class Ct extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function qt(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Pt(e){return String(e).replace(ht,"Symbol($1)")}function Et(e){return"["+bt.call(e)+"]"}function wt(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;if("number"===a)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===a)return function(e){return String(`${e}n`)}(e);if("string"===a)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===a)return qt(e,t);if("symbol"===a)return Pt(e);const o=mt.call(e);return"[object WeakMap]"===o?"WeakMap {}":"[object WeakSet]"===o?"WeakSet {}":"[object Function]"===o||"[object GeneratorFunction]"===o?qt(e,t):"[object Symbol]"===o?Pt(e):"[object Date]"===o?isNaN(+e)?"Date { NaN }":ft.call(e):"[object Error]"===o?Et(e):"[object RegExp]"===o?r?vt.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):vt.call(e):e instanceof Error?Et(e):null}function xt(e,t,r,n,a,o){if(-1!==a.indexOf(e))return"[Circular]";(a=a.slice()).push(e);const l=++n>t.maxDepth,i=t.min;if(t.callToJSON&&!l&&e.toJSON&&"function"==typeof e.toJSON&&!o)return Tt(e.toJSON(),t,r,n,a,!0);const u=mt.call(e);return"[object Arguments]"===u?l?"[Arguments]":(i?"":"Arguments ")+"["+(0,at.printListItems)(e,t,r,n,a,Tt)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?l?"["+e.constructor.name+"]":(i?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,at.printListItems)(e,t,r,n,a,Tt)+"]":"[object Map]"===u?l?"[Map]":"Map {"+(0,at.printIteratorEntries)(e.entries(),t,r,n,a,Tt," => ")+"}":"[object Set]"===u?l?"[Set]":"Set {"+(0,at.printIteratorValues)(e.values(),t,r,n,a,Tt)+"}":l||(e=>"undefined"!=typeof window&&e===window)(e)?"["+yt(e)+"]":(i?"":t.printBasicPrototype||"Object"!==yt(e)?yt(e)+" ":"")+"{"+(0,at.printObjectProperties)(e,t,r,n,a,Tt)+"}"}function Rt(e,t,r,n,a,o){let l;try{l=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,a,o,Tt):e.print(t,(e=>Tt(e,r,n,a,o)),(e=>{const t=n+r.indent;return t+e.replace(gt,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new Ct(e.message,e.stack)}if("string"!=typeof l)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof l}".`);return l}function Ot(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new Ct(e.message,e.stack)}return null}function Tt(e,t,r,n,a,o){const l=Ot(t.plugins,e);if(null!==l)return Rt(l,e,t,r,n,a);const i=wt(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==i?i:xt(e,t,r,n,a,o)}const _t={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},Mt=Object.keys(_t),At={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:_t};var jt=p.DEFAULT_OPTIONS=At;const St=e=>Mt.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:_t[r],a=n&&nt.default[n];if(!a||"string"!=typeof a.close||"string"!=typeof a.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=a,t}),Object.create(null)),Bt=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:At.printFunctionName,It=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:At.escapeRegex,Nt=e=>e&&void 0!==e.escapeString?e.escapeString:At.escapeString,kt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:At.callToJSON,colors:e&&e.highlight?St(e):Mt.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:At.compareKeys,escapeRegex:It(e),escapeString:Nt(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:At.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:At.maxDepth,min:e&&void 0!==e.min?e.min:At.min,plugins:e&&void 0!==e.plugins?e.plugins:At.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:Bt(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function Ft(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!At.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=Ot(t.plugins,e);if(null!==r)return Rt(r,e,kt(t),"",0,[])}const r=wt(e,Bt(t),It(t),Nt(t));return null!==r?r:xt(e,kt(t),"",0,[])}const Lt={AsymmetricMatcher:ot.default,ConvertAnsi:lt.default,DOMCollection:it.default,DOMElement:ut.default,Immutable:st.default,ReactElement:dt.default,ReactTestComponent:ct.default};rt=p.plugins=Lt;var Ut=Ft;et=p.default=Ut;var Dt=i({__proto__:null,get DEFAULT_OPTIONS(){return jt},format:tt,get plugins(){return rt},get default(){return et}},[p]),Ht=Object.prototype.toString;function zt(e){return"function"==typeof e||"[object Function]"===Ht.call(e)}var Vt=Math.pow(2,53)-1;function $t(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Vt)}function Wt(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!zt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var a,o=$t(n.length),l=zt(r)?Object(new r(o)):new Array(o),i=0;i<o;)a=n[i],l[i]=t?t(a,i):a,i+=1;return l.length=o,l}function Gt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Jt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Xt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Gt(this,e),Jt(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&Qt(t.prototype,r),n&&Qt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Kt="undefined"==typeof Set?Set:Xt;function Yt(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var Zt={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},er={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function tr(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=er[t])&&void 0!==n&&n.has(r))}))}(e,t)}function rr(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=Zt[Yt(e)];if(void 0!==t)return t;switch(Yt(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||tr(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||tr(e,r||""))return r}return t}function nr(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function ar(e){return nr(e)&&"caption"===Yt(e)}function or(e){return nr(e)&&"input"===Yt(e)}function lr(e){return nr(e)&&"optgroup"===Yt(e)}function ir(e){return nr(e)&&"table"===Yt(e)}function ur(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}function sr(e){return nr(e)&&"fieldset"===Yt(e)}function dr(e){return nr(e)&&"legend"===Yt(e)}function cr(e){return nr(e)&&"slot"===Yt(e)}function pr(e){return nr(e)&&"svg"===Yt(e)}function mr(e){return function(e){return nr(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===Yt(e)}function fr(e,t){if(nr(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function br(e,t){return!!nr(e)&&-1!==t.indexOf(rr(e))}function vr(e){return e.trim().replace(/\s\s+/g," ")}function yr(e,t){if(!nr(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}function hr(e){return br(e,["button","combobox","listbox","textbox"])||gr(e,"range")}function gr(e,t){if(!nr(e))return!1;if("range"===t)return br(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Cr(e,t){var r=Wt(e.querySelectorAll(t));return fr(e,"aria-owns").forEach((function(e){r.push.apply(r,Wt(e.querySelectorAll(t)))})),r}function qr(e){return nr(t=e)&&"select"===Yt(t)?e.selectedOptions||Cr(e,"[selected]"):Cr(e,'[aria-selected="true"]');var t}function Pr(e){return br(e,["none","presentation"])}function Er(e){return ar(e)}function wr(e){return br(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}function xr(e){return or(e)||nr(t=e)&&"textarea"===Yt(t)?e.value:e.textContent||"";var t}function Rr(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Or(e){var t=Yt(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Tr(e){if(Or(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&nr(e)){var r=Tr(e);null!==r&&(t=r)}})),t}function _r(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Tr(e)}function Mr(e){var t=e.labels;return null===t?t:void 0!==t?Wt(t):Or(e)?Wt(e.ownerDocument.querySelectorAll("label")).filter((function(t){return _r(t)===e})):null}function Ar(e){var t=e.assignedNodes();return 0===t.length?Wt(e.childNodes):t}function jr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new Kt,n=ur(e),a=t.compute,o=void 0===a?"name":a,l=t.computedStyleSupportsPseudoElements,i=void 0===l?void 0!==t.getComputedStyle:l,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,d=t.hidden,c=void 0!==d&&d;function p(e,t){var r="";if(nr(e)&&i){var n=Rr(s(e,"::before"));r="".concat(n," ").concat(r)}if((cr(e)?Ar(e):Wt(e.childNodes).concat(fr(e,"aria-owns"))).forEach((function(e){var n=f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),a="inline"!==(nr(e)?s(e).getPropertyValue("display"):"inline")?" ":"";r+="".concat(a).concat(n).concat(a)})),nr(e)&&i){var a=Rr(s(e,"::after"));r="".concat(r," ").concat(a)}return r.trim()}function m(e){if(!nr(e))return null;function t(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}if(sr(e)){r.add(e);for(var n=Wt(e.childNodes),a=0;a<n.length;a+=1){var o=n[a];if(dr(o))return f(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(ir(e)){r.add(e);for(var l=Wt(e.childNodes),i=0;i<l.length;i+=1){var u=l[i];if(ar(u))return f(u,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(pr(e)){r.add(e);for(var s=Wt(e.childNodes),d=0;d<s.length;d+=1){var c=s[d];if(mr(c))return c.textContent}return null}if("img"===Yt(e)||"area"===Yt(e)){var m=t(e,"alt");if(null!==m)return m}else if(lr(e)){var b=t(e,"label");if(null!==b)return b}}if(or(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var v=t(e,"value");if(null!==v)return v;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var y=Mr(e);if(null!==y&&0!==y.length)return r.add(e),Wt(y).map((function(e){return f(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(or(e)&&"image"===e.type){var h=t(e,"alt");if(null!==h)return h;var g=t(e,"title");return null!==g?g:"Submit Query"}if(br(e,["button"])){var C=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});return""!==C?C:t(e,"title")}return t(e,"title")}function f(e,t){if(r.has(e))return"";if(!c&&yr(e,s)&&!t.isReferenced)return r.add(e),"";var n=fr(e,"aria-labelledby");if("name"===o&&!t.isReferenced&&n.length>0)return n.map((function(e){return f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var a=t.recursion&&hr(e)&&"name"===o;if(!a){var l=(nr(e)&&e.getAttribute("aria-label")||"").trim();if(""!==l&&"name"===o)return r.add(e),l;if(!Pr(e)){var i=m(e);if(null!==i)return r.add(e),i}}if(br(e,["menu"]))return r.add(e),"";if(a||t.isEmbeddedInLabel||t.isReferenced){if(br(e,["combobox","listbox"])){r.add(e);var u=qr(e);return 0===u.length?or(e)?e.value:"":Wt(u).map((function(e){return f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(gr(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(br(e,["textbox"]))return r.add(e),xr(e)}return wr(e)||nr(e)&&t.isReferenced||Er(e)?(r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1})):e.nodeType===e.TEXT_NODE?(r.add(e),e.textContent||""):t.recursion?(r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1})):(r.add(e),"")}return vr(f(e,{isEmbeddedInLabel:!1,isReferenced:"description"===o,recursion:!1}))}function Sr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Br(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sr(Object(r),!0).forEach((function(t){Ir(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ir(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=fr(e,"aria-describedby").map((function(e){return jr(e,Br(Br({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function kr(e){return br(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])}function Fr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return kr(e)?"":jr(e,t)}var Lr={},Ur={};function Dr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Hr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}Object.defineProperty(Ur,"__esModule",{value:!0}),Ur.default=void 0;var zr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],Vr={entries:function(){return zr},get:function(e){var t=zr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return zr.map((function(e){return Dr(e,1)[0]}))},values:function(){return zr.map((function(e){return Dr(e,2)[1]}))}};Ur.default=Vr;var $r={};function Wr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Gr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}Object.defineProperty($r,"__esModule",{value:!0}),$r.default=void 0;var Qr=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],Jr={entries:function(){return Qr},get:function(e){var t=Qr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Qr.map((function(e){return Wr(e,1)[0]}))},values:function(){return Qr.map((function(e){return Wr(e,2)[1]}))}};$r.default=Jr;var Xr={},Kr={},Yr={};Object.defineProperty(Yr,"__esModule",{value:!0}),Yr.default=void 0;var Zr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Yr.default=Zr;var en={};Object.defineProperty(en,"__esModule",{value:!0}),en.default=void 0;var tn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};en.default=tn;var rn={};Object.defineProperty(rn,"__esModule",{value:!0}),rn.default=void 0;var nn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};rn.default=nn;var an={};Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var on={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};an.default=on;var ln={};Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=void 0;var un={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ln.default=un;var sn={};Object.defineProperty(sn,"__esModule",{value:!0}),sn.default=void 0;var dn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};sn.default=dn;var cn={};Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};cn.default=pn;var mn={};Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var fn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};mn.default=fn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var vn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};bn.default=vn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var hn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};yn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Cn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};gn.default=Cn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var Pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};qn.default=Pn,Object.defineProperty(Kr,"__esModule",{value:!0}),Kr.default=void 0;var En=In(Yr),wn=In(en),xn=In(rn),Rn=In(an),On=In(ln),Tn=In(sn),_n=In(cn),Mn=In(mn),An=In(bn),jn=In(yn),Sn=In(gn),Bn=In(qn);function In(e){return e&&e.__esModule?e:{default:e}}var Nn=[["command",En.default],["composite",wn.default],["input",xn.default],["landmark",Rn.default],["range",On.default],["roletype",Tn.default],["section",_n.default],["sectionhead",Mn.default],["select",An.default],["structure",jn.default],["widget",Sn.default],["window",Bn.default]];Kr.default=Nn;var kn={},Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var Ln={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fn.default=Ln;var Un={};Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var Dn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};Un.default=Dn;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.default=void 0;var zn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Hn.default=zn;var Vn={};Object.defineProperty(Vn,"__esModule",{value:!0}),Vn.default=void 0;var $n={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Vn.default=$n;var Wn={};Object.defineProperty(Wn,"__esModule",{value:!0}),Wn.default=void 0;var Gn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Wn.default=Gn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qn.default=Jn;var Xn={};Object.defineProperty(Xn,"__esModule",{value:!0}),Xn.default=void 0;var Kn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Xn.default=Kn;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0}),Yn.default=void 0;var Zn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yn.default=Zn;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};ra.default=na;var aa={};Object.defineProperty(aa,"__esModule",{value:!0}),aa.default=void 0;var oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};aa.default=oa;var la={};Object.defineProperty(la,"__esModule",{value:!0}),la.default=void 0;var ia={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};la.default=ia;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};ua.default=sa;var da={};Object.defineProperty(da,"__esModule",{value:!0}),da.default=void 0;var ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};da.default=ca;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};pa.default=ma;var fa={};Object.defineProperty(fa,"__esModule",{value:!0}),fa.default=void 0;var ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};fa.default=ba;var va={};Object.defineProperty(va,"__esModule",{value:!0}),va.default=void 0;var ya={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};va.default=ya;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};ha.default=ga;var Ca={};Object.defineProperty(Ca,"__esModule",{value:!0}),Ca.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Ca.default=qa;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ea={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Pa.default=Ea;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var xa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};wa.default=xa;var Ra={};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.default=void 0;var Oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Ra.default=Oa;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var _a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ta.default=_a;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0}),Ma.default=void 0;var Aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ma.default=Aa;var ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ja.default=Sa;var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0}),Ba.default=void 0;var Ia={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};Ba.default=Ia;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};Na.default=ka;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0}),Fa.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fa.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ha.default=za;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var $a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Va.default=$a;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0}),Wa.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Wa.default=Ga;var Qa={};Object.defineProperty(Qa,"__esModule",{value:!0}),Qa.default=void 0;var Ja={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Qa.default=Ja;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Xa.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ya.default=Za;var eo={};Object.defineProperty(eo,"__esModule",{value:!0}),eo.default=void 0;var to={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};eo.default=to;var ro={};Object.defineProperty(ro,"__esModule",{value:!0}),ro.default=void 0;var no={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ro.default=no;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var oo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ao.default=oo;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};lo.default=io;var uo={};Object.defineProperty(uo,"__esModule",{value:!0}),uo.default=void 0;var so={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};uo.default=so;var co={};Object.defineProperty(co,"__esModule",{value:!0}),co.default=void 0;var po={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};co.default=po;var mo={};Object.defineProperty(mo,"__esModule",{value:!0}),mo.default=void 0;var fo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};mo.default=fo;var bo={};Object.defineProperty(bo,"__esModule",{value:!0}),bo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};bo.default=vo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var ho={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};yo.default=ho;var go={};Object.defineProperty(go,"__esModule",{value:!0}),go.default=void 0;var Co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};go.default=Co;var qo={};Object.defineProperty(qo,"__esModule",{value:!0}),qo.default=void 0;var Po={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};qo.default=Po;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var wo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Eo.default=wo;var xo={};Object.defineProperty(xo,"__esModule",{value:!0}),xo.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xo.default=Ro;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var To={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};Oo.default=To;var _o={};Object.defineProperty(_o,"__esModule",{value:!0}),_o.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_o.default=Mo;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var jo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ao.default=jo;var So={};Object.defineProperty(So,"__esModule",{value:!0}),So.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};So.default=Bo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var No={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Io.default=No;var ko={};Object.defineProperty(ko,"__esModule",{value:!0}),ko.default=void 0;var Fo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ko.default=Fo;var Lo={};Object.defineProperty(Lo,"__esModule",{value:!0}),Lo.default=void 0;var Uo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Lo.default=Uo;var Do={};Object.defineProperty(Do,"__esModule",{value:!0}),Do.default=void 0;var Ho={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Do.default=Ho;var zo={};Object.defineProperty(zo,"__esModule",{value:!0}),zo.default=void 0;var Vo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};zo.default=Vo;var $o={};Object.defineProperty($o,"__esModule",{value:!0}),$o.default=void 0;var Wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};$o.default=Wo;var Go={};Object.defineProperty(Go,"__esModule",{value:!0}),Go.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Go.default=Qo;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Xo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Jo.default=Xo;var Ko={};Object.defineProperty(Ko,"__esModule",{value:!0}),Ko.default=void 0;var Yo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};Ko.default=Yo;var Zo={};Object.defineProperty(Zo,"__esModule",{value:!0}),Zo.default=void 0;var el={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Zo.default=el;var tl={};Object.defineProperty(tl,"__esModule",{value:!0}),tl.default=void 0;var rl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};tl.default=rl;var nl={};Object.defineProperty(nl,"__esModule",{value:!0}),nl.default=void 0;var al={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};nl.default=al;var ol={};Object.defineProperty(ol,"__esModule",{value:!0}),ol.default=void 0;var ll={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ol.default=ll;var il={};Object.defineProperty(il,"__esModule",{value:!0}),il.default=void 0;var ul={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};il.default=ul;var sl={};Object.defineProperty(sl,"__esModule",{value:!0}),sl.default=void 0;var dl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};sl.default=dl;var cl={};Object.defineProperty(cl,"__esModule",{value:!0}),cl.default=void 0;var pl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};cl.default=pl;var ml={};Object.defineProperty(ml,"__esModule",{value:!0}),ml.default=void 0;var fl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};ml.default=fl;var bl={};Object.defineProperty(bl,"__esModule",{value:!0}),bl.default=void 0;var vl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};bl.default=vl;var yl={};Object.defineProperty(yl,"__esModule",{value:!0}),yl.default=void 0;var hl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};yl.default=hl;var gl={};Object.defineProperty(gl,"__esModule",{value:!0}),gl.default=void 0;var Cl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};gl.default=Cl;var ql={};Object.defineProperty(ql,"__esModule",{value:!0}),ql.default=void 0;var Pl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ql.default=Pl;var El={};Object.defineProperty(El,"__esModule",{value:!0}),El.default=void 0;var wl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};El.default=wl;var xl={};Object.defineProperty(xl,"__esModule",{value:!0}),xl.default=void 0;var Rl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};xl.default=Rl;var Ol={};Object.defineProperty(Ol,"__esModule",{value:!0}),Ol.default=void 0;var Tl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ol.default=Tl;var _l={};Object.defineProperty(_l,"__esModule",{value:!0}),_l.default=void 0;var Ml={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};_l.default=Ml;var Al={};Object.defineProperty(Al,"__esModule",{value:!0}),Al.default=void 0;var jl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};Al.default=jl;var Sl={};Object.defineProperty(Sl,"__esModule",{value:!0}),Sl.default=void 0;var Bl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Sl.default=Bl;var Il={};Object.defineProperty(Il,"__esModule",{value:!0}),Il.default=void 0;var Nl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Il.default=Nl;var kl={};Object.defineProperty(kl,"__esModule",{value:!0}),kl.default=void 0;var Fl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};kl.default=Fl;var Ll={};Object.defineProperty(Ll,"__esModule",{value:!0}),Ll.default=void 0;var Ul={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};Ll.default=Ul,Object.defineProperty(kn,"__esModule",{value:!0}),kn.default=void 0;var Dl=bu(Fn),Hl=bu(Un),zl=bu(Hn),Vl=bu(Vn),$l=bu(Wn),Wl=bu(Qn),Gl=bu(Xn),Ql=bu(Yn),Jl=bu(ea),Xl=bu(ra),Kl=bu(aa),Yl=bu(la),Zl=bu(ua),ei=bu(da),ti=bu(pa),ri=bu(fa),ni=bu(va),ai=bu(ha),oi=bu(Ca),li=bu(Pa),ii=bu(wa),ui=bu(Ra),si=bu(Ta),di=bu(Ma),ci=bu(ja),pi=bu(Ba),mi=bu(Na),fi=bu(Fa),bi=bu(Ua),vi=bu(Ha),yi=bu(Va),hi=bu(Wa),gi=bu(Qa),Ci=bu(Xa),qi=bu(Ya),Pi=bu(eo),Ei=bu(ro),wi=bu(ao),xi=bu(lo),Ri=bu(uo),Oi=bu(co),Ti=bu(mo),_i=bu(bo),Mi=bu(yo),Ai=bu(go),ji=bu(qo),Si=bu(Eo),Bi=bu(xo),Ii=bu(Oo),Ni=bu(_o),ki=bu(Ao),Fi=bu(So),Li=bu(Io),Ui=bu(ko),Di=bu(Lo),Hi=bu(Do),zi=bu(zo),Vi=bu($o),$i=bu(Go),Wi=bu(Jo),Gi=bu(Ko),Qi=bu(Zo),Ji=bu(tl),Xi=bu(nl),Ki=bu(ol),Yi=bu(il),Zi=bu(sl),eu=bu(cl),tu=bu(ml),ru=bu(bl),nu=bu(yl),au=bu(gl),ou=bu(ql),lu=bu(El),iu=bu(xl),uu=bu(Ol),su=bu(_l),du=bu(Al),cu=bu(Sl),pu=bu(Il),mu=bu(kl),fu=bu(Ll);function bu(e){return e&&e.__esModule?e:{default:e}}var vu=[["alert",Dl.default],["alertdialog",Hl.default],["application",zl.default],["article",Vl.default],["banner",$l.default],["blockquote",Wl.default],["button",Gl.default],["caption",Ql.default],["cell",Jl.default],["checkbox",Xl.default],["code",Kl.default],["columnheader",Yl.default],["combobox",Zl.default],["complementary",ei.default],["contentinfo",ti.default],["definition",ri.default],["deletion",ni.default],["dialog",ai.default],["directory",oi.default],["document",li.default],["emphasis",ii.default],["feed",ui.default],["figure",si.default],["form",di.default],["generic",ci.default],["grid",pi.default],["gridcell",mi.default],["group",fi.default],["heading",bi.default],["img",vi.default],["insertion",yi.default],["link",hi.default],["list",gi.default],["listbox",Ci.default],["listitem",qi.default],["log",Pi.default],["main",Ei.default],["marquee",wi.default],["math",xi.default],["menu",Ri.default],["menubar",Oi.default],["menuitem",Ti.default],["menuitemcheckbox",_i.default],["menuitemradio",Mi.default],["meter",Ai.default],["navigation",ji.default],["none",Si.default],["note",Bi.default],["option",Ii.default],["paragraph",Ni.default],["presentation",ki.default],["progressbar",Fi.default],["radio",Li.default],["radiogroup",Ui.default],["region",Di.default],["row",Hi.default],["rowgroup",zi.default],["rowheader",Vi.default],["scrollbar",$i.default],["search",Wi.default],["searchbox",Gi.default],["separator",Qi.default],["slider",Ji.default],["spinbutton",Xi.default],["status",Ki.default],["strong",Yi.default],["subscript",Zi.default],["superscript",eu.default],["switch",tu.default],["tab",ru.default],["table",nu.default],["tablist",au.default],["tabpanel",ou.default],["term",lu.default],["textbox",iu.default],["time",uu.default],["timer",su.default],["toolbar",du.default],["tooltip",cu.default],["tree",pu.default],["treegrid",mu.default],["treeitem",fu.default]];kn.default=vu;var yu={},hu={};Object.defineProperty(hu,"__esModule",{value:!0}),hu.default=void 0;var gu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};hu.default=gu;var Cu={};Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.default=void 0;var qu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Cu.default=qu;var Pu={};Object.defineProperty(Pu,"__esModule",{value:!0}),Pu.default=void 0;var Eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Pu.default=Eu;var wu={};Object.defineProperty(wu,"__esModule",{value:!0}),wu.default=void 0;var xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};wu.default=xu;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Ru.default=Ou;var Tu={};Object.defineProperty(Tu,"__esModule",{value:!0}),Tu.default=void 0;var _u={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Tu.default=_u;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Mu.default=Au;var ju={};Object.defineProperty(ju,"__esModule",{value:!0}),ju.default=void 0;var Su={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};ju.default=Su;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bu.default=Iu;var Nu={};Object.defineProperty(Nu,"__esModule",{value:!0}),Nu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Nu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Lu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Fu.default=Lu;var Uu={};Object.defineProperty(Uu,"__esModule",{value:!0}),Uu.default=void 0;var Du={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Uu.default=Du;var Hu={};Object.defineProperty(Hu,"__esModule",{value:!0}),Hu.default=void 0;var zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Hu.default=zu;var Vu={};Object.defineProperty(Vu,"__esModule",{value:!0}),Vu.default=void 0;var $u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Vu.default=$u;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var Gu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wu.default=Gu;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Qu.default=Ju;var Xu={};Object.defineProperty(Xu,"__esModule",{value:!0}),Xu.default=void 0;var Ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Xu.default=Ku;var Yu={};Object.defineProperty(Yu,"__esModule",{value:!0}),Yu.default=void 0;var Zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yu.default=Zu;var es={};Object.defineProperty(es,"__esModule",{value:!0}),es.default=void 0;var ts={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};es.default=ts;var rs={};Object.defineProperty(rs,"__esModule",{value:!0}),rs.default=void 0;var ns={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};rs.default=ns;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};as.default=os;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ls.default=is;var us={};Object.defineProperty(us,"__esModule",{value:!0}),us.default=void 0;var ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};us.default=ss;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ds.default=cs;var ps={};Object.defineProperty(ps,"__esModule",{value:!0}),ps.default=void 0;var ms={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};ps.default=ms;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};fs.default=bs;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};vs.default=ys;var hs={};Object.defineProperty(hs,"__esModule",{value:!0}),hs.default=void 0;var gs={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};hs.default=gs;var Cs={};Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var qs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Cs.default=qs;var Ps={};Object.defineProperty(Ps,"__esModule",{value:!0}),Ps.default=void 0;var Es={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};Ps.default=Es;var ws={};Object.defineProperty(ws,"__esModule",{value:!0}),ws.default=void 0;var xs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};ws.default=xs;var Rs={};Object.defineProperty(Rs,"__esModule",{value:!0}),Rs.default=void 0;var Os={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Rs.default=Os;var Ts={};Object.defineProperty(Ts,"__esModule",{value:!0}),Ts.default=void 0;var _s={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ts.default=_s;var Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;var As={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ms.default=As;var js={};Object.defineProperty(js,"__esModule",{value:!0}),js.default=void 0;var Ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};js.default=Ss;var Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.default=void 0;var Is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Bs.default=Is;var Ns={};Object.defineProperty(Ns,"__esModule",{value:!0}),Ns.default=void 0;var ks={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};Ns.default=ks;var Fs={};Object.defineProperty(Fs,"__esModule",{value:!0}),Fs.default=void 0;var Ls={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Fs.default=Ls;var Us={};Object.defineProperty(Us,"__esModule",{value:!0}),Us.default=void 0;var Ds={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Us.default=Ds,Object.defineProperty(yu,"__esModule",{value:!0}),yu.default=void 0;var Hs=Od(hu),zs=Od(Cu),Vs=Od(Pu),$s=Od(wu),Ws=Od(Ru),Gs=Od(Tu),Qs=Od(Mu),Js=Od(ju),Xs=Od(Bu),Ks=Od(Nu),Ys=Od(Fu),Zs=Od(Uu),ed=Od(Hu),td=Od(Vu),rd=Od(Wu),nd=Od(Qu),ad=Od(Xu),od=Od(Yu),ld=Od(es),id=Od(rs),ud=Od(as),sd=Od(ls),dd=Od(us),cd=Od(ds),pd=Od(ps),md=Od(fs),fd=Od(vs),bd=Od(hs),vd=Od(Cs),yd=Od(Ps),hd=Od(ws),gd=Od(Rs),Cd=Od(Ts),qd=Od(Ms),Pd=Od(js),Ed=Od(Bs),wd=Od(Ns),xd=Od(Fs),Rd=Od(Us);function Od(e){return e&&e.__esModule?e:{default:e}}var Td=[["doc-abstract",Hs.default],["doc-acknowledgments",zs.default],["doc-afterword",Vs.default],["doc-appendix",$s.default],["doc-backlink",Ws.default],["doc-biblioentry",Gs.default],["doc-bibliography",Qs.default],["doc-biblioref",Js.default],["doc-chapter",Xs.default],["doc-colophon",Ks.default],["doc-conclusion",Ys.default],["doc-cover",Zs.default],["doc-credit",ed.default],["doc-credits",td.default],["doc-dedication",rd.default],["doc-endnote",nd.default],["doc-endnotes",ad.default],["doc-epigraph",od.default],["doc-epilogue",ld.default],["doc-errata",id.default],["doc-example",ud.default],["doc-footnote",sd.default],["doc-foreword",dd.default],["doc-glossary",cd.default],["doc-glossref",pd.default],["doc-index",md.default],["doc-introduction",fd.default],["doc-noteref",bd.default],["doc-notice",vd.default],["doc-pagebreak",yd.default],["doc-pagelist",hd.default],["doc-part",gd.default],["doc-preface",Cd.default],["doc-prologue",qd.default],["doc-pullquote",Pd.default],["doc-qna",Ed.default],["doc-subtitle",wd.default],["doc-tip",xd.default],["doc-toc",Rd.default]];yu.default=Td,Object.defineProperty(Xr,"__esModule",{value:!0}),Xr.default=void 0;var _d=jd(Kr),Md=jd(kn),Ad=jd(yu);function jd(e){return e&&e.__esModule?e:{default:e}}function Sd(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Bd(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Nd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}function Id(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||Nd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nd(e,t){if(e){if("string"==typeof e)return kd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kd(e,t):void 0}}function kd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Fd=[].concat(_d.default,Md.default,Ad.default);Fd.forEach((function(e){var t,r=Id(e,2)[1],n=Bd(r.superClass);try{for(n.s();!(t=n.n()).done;){var a,o=Bd(t.value);try{var l=function(){var e=a.value,t=Fd.find((function(t){return Id(t,1)[0]===e}));if(t)for(var n=t[1],o=0,l=Object.keys(n.props);o<l.length;o++){var i=l[o];Object.prototype.hasOwnProperty.call(r.props,i)||Object.assign(r.props,Sd({},i,n.props[i]))}};for(o.s();!(a=o.n()).done;)l()}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Ld={entries:function(){return Fd},get:function(e){var t=Fd.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Fd.map((function(e){return Id(e,1)[0]}))},values:function(){return Fd.map((function(e){return Id(e,2)[1]}))}};Xr.default=Ld;var Ud={};Object.defineProperty(Ud,"__esModule",{value:!0}),Ud.default=void 0;var Dd=function(e){return e&&e.__esModule?e:{default:e}}(Xr);function Hd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return zd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zd(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Vd=[],$d=Dd.default.keys(),Wd=0;Wd<$d.length;Wd++){var Gd=$d[Wd],Qd=Dd.default.get(Gd);if(Qd)for(var Jd=[].concat(Qd.baseConcepts,Qd.relatedConcepts),Xd=0;Xd<Jd.length;Xd++){var Kd=Jd[Xd];if("HTML"===Kd.module){var Yd=Kd.concept;Yd&&function(){var e=JSON.stringify(Yd),t=Vd.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,a=0;a<r.length;a++)if(r[a]===Gd){n=!1;break}n&&r.push(Gd),Vd.push([Yd,r])}()}}}var Zd={entries:function(){return Vd},get:function(e){var t=Vd.find((function(t){return JSON.stringify(t[0])===JSON.stringify(e)}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Vd.map((function(e){return Hd(e,1)[0]}))},values:function(){return Vd.map((function(e){return Hd(e,2)[1]}))}};Ud.default=Zd;var ec={};Object.defineProperty(ec,"__esModule",{value:!0}),ec.default=void 0;var tc=function(e){return e&&e.__esModule?e:{default:e}}(Xr);function rc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return nc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var ac=[],oc=tc.default.keys(),lc=function(e){var t=oc[e],r=tc.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),a=0;a<n.length;a++){var o=n[a];if("HTML"===o.module){var l=o.concept;if(l){var i=ac.find((function(e){return e[0]===t})),u=void 0;(u=i?i[1]:[]).push(l),ac.push([t,u])}}}},ic=0;ic<oc.length;ic++)lc(ic);var uc={entries:function(){return ac},get:function(e){var t=ac.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return ac.map((function(e){return rc(e,1)[0]}))},values:function(){return ac.map((function(e){return rc(e,2)[1]}))}};ec.default=uc,Object.defineProperty(Lr,"__esModule",{value:!0});var sc=Lr.roles=Pc=Lr.roleElements=Lr.elementRoles=Lr.dom=Lr.aria=void 0,dc=bc(Ur),cc=bc($r),pc=bc(Xr),mc=bc(Ud),fc=bc(ec);function bc(e){return e&&e.__esModule?e:{default:e}}var vc=dc.default;Lr.aria=vc;var yc=cc.default;Lr.dom=yc;var hc=pc.default;sc=Lr.roles=hc;var gc=mc.default,Cc=Lr.elementRoles=gc,qc=fc.default,Pc=Lr.roleElements=qc,Ec={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function a(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return a(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,a=t.length;n<a;n++){var l=t.charCodeAt(n);r[2*n]=l>>>8,r[2*n+1]=l%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,a=r.length;n<a;n++)r[n]=256*t[2*n]+t[2*n+1];var l=[];return r.forEach((function(t){l.push(e(t))})),o.decompress(l.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return a(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,a,o,l={},i={},u="",s="",d="",c=2,p=3,m=2,f=[],b=0,v=0;for(o=0;o<e.length;o+=1)if(u=e.charAt(o),Object.prototype.hasOwnProperty.call(l,u)||(l[u]=p++,i[u]=!0),s=d+u,Object.prototype.hasOwnProperty.call(l,s))d=s;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++),l[s]=p++,d=String(u)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++)}for(a=2,n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;for(;;){if(b<<=1,v==t-1){f.push(r(b));break}v++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var a,o,l,i,u,s,d,c=[],p=4,m=4,f=3,b="",v=[],y={val:n(0),position:r,index:1};for(a=0;a<3;a+=1)c[a]=a;for(l=0,u=Math.pow(2,2),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 2:return""}for(c[3]=d,o=d,v.push(d);;){if(y.index>t)return"";for(l=0,u=Math.pow(2,f),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(d=l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[d])b=c[d];else{if(d!==m)return null;b=o+o.charAt(0)}v.push(b),c[m++]=o+b.charAt(0),o=b,0==--p&&(p=Math.pow(2,f),f++)}}};return o}();null!=e&&(e.exports=t)}(Ec);var wc=Ec.exports;function xc(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const Rc=(e,t)=>{const r=t.colors.content;return r.open+xc(e)+r.close},Oc=/^((HTML|SVG)\w*)?Element$/;function Tc(e){return 11===e.nodeType}function _c(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(Oc.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)},serialize:(t,r,n,a,o,l)=>{if(function(e){return 3===e.nodeType}(t))return Rc(t.data,r);if(function(e){return 8===e.nodeType}(t))return((e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+xc(e)+"--\x3e"+r.close})(t.data,r);const i=Tc(t)?"DocumentFragment":t.tagName.toLowerCase();return++a>r.maxDepth?((e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close})(i,r):((e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close})(i,((e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")})(Tc(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Tc(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,a,o,l),((e,t,r,n,a,o)=>e.map((e=>{const l="string"==typeof e?Rc(e,t):o(e,t,r,n,a);return""===l&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+l})).join(""))(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,a,o,l),r,n)}}}let Mc=null,Ac=null,jc=null;try{const e=module&&module.require;Ac=e.call(module,"fs").readFileSync,jc=e.call(module,"@babel/code-frame").codeFrameColumns,Mc=e.call(module,"chalk")}catch{}function Sc(){if(!Ac||!jc)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),a=n.split(":"),[o,l,i]=[a[0],parseInt(a[1],10),parseInt(a[2],10)];let u="";try{u=Ac(o,"utf-8")}catch{return""}const s=jc(u,{start:{line:l,column:i}},{highlightCode:!0,linesBelow:0});return Mc.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}function Bc(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Ic(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Nc(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function kc(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const{DOMCollection:Fc}=rt;function Lc(e){return 8!==e.nodeType&&(1!==e.nodeType||!e.matches(Vc().defaultIgnore))}function Uc(e,t,r){if(void 0===r&&(r={}),e||(e=Ic().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:a=Lc,...o}=r,l=tt(e,{plugins:[_c(a),Fc],printFunctionName:!1,highlight:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node,...o});return void 0!==t&&e.outerHTML.length>t?l.slice(0,t)+"...":l}const Dc=function(){const e=Sc();e?console.log(Uc(...arguments)+"\n\n"+e):console.log(Uc(...arguments))};let Hc={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=Uc(t),n=new Error([e,"Ignored nodes: comments, "+Hc.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function zc(e){"function"==typeof e&&(e=e(Hc)),Hc={...Hc,...e}}function Vc(){return Hc}const $c=["button","meter","output","progress","select","textarea","input"];function Wc(e){return $c.includes(e.nodeName.toLowerCase())?"":3===e.nodeType?e.textContent:Array.from(e.childNodes).map((e=>Wc(e))).join("")}function Gc(e){let t;return t="label"===e.tagName.toLowerCase()?Wc(e):e.value||e.textContent,t}function Qc(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function Jc(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const a=t.getAttribute("aria-labelledby"),o=a?a.split(" "):[];return o.length?o.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Gc(r),formControl:null}:{content:"",formControl:null}})):Array.from(Qc(t)).map((e=>({content:Gc(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function Xc(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Kc(e,t,r,n){if("string"!=typeof e)return!1;Xc(r);const a=n(e);return"string"==typeof r||"number"==typeof r?a.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(a,t):tp(r,a)}function Yc(e,t,r,n){if("string"!=typeof e)return!1;Xc(r);const a=n(e);return r instanceof Function?r(a,t):r instanceof RegExp?tp(r,a):a===String(r)}function Zc(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function ep(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Zc({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function tp(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function rp(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>3===e.nodeType&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const np=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[a,o]of e.entries())n=[...n,{match:r(a),roles:Array.from(o),specificity:t(a)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Cc);function ap(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function op(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=ap}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function lp(e){for(const{match:t,roles:r}of np)if(t(e))return[...r];return[]}function ip(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===op(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):lp(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function up(e,t){let{hidden:r,includeDescription:n}=t;const a=ip(e,{hidden:r});return Object.entries(a).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const a="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+Fr(e,{computedStyleSupportsPseudoElements:Vc().computedStyleSupportsPseudoElements})+'":\n',r=Uc(e.cloneNode(!1));if(n){return""+t+('Description "'+Nr(e,{computedStyleSupportsPseudoElements:Vc().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+a})).join("\n")}function sp(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const dp=Zc();function cp(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function pp(e,t,r,n){let{variant:a,name:o}=n,l="";const i={},u=[["Role","TestId"].includes(e)?r:cp(r)];o&&(i.name=cp(o)),"Role"===e&&op(t)&&(i.hidden=!0,l="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(i).length>0&&u.push(i);const s=a+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:a,warning:l,toString(){l&&console.warn(l);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function mp(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function fp(e,t,r){var n,a;if(void 0===t&&(t="get"),e.matches(Vc().defaultIgnore))return;const o=null!=(n=e.getAttribute("role"))?n:null==(a=lp(e))?void 0:a[0];if("generic"!==o&&mp("Role",r,o))return pp("Role",e,o,{variant:t,name:Fr(e,{computedStyleSupportsPseudoElements:Vc().computedStyleSupportsPseudoElements})});const l=Jc(document,e).map((e=>e.content)).join(" ");if(mp("LabelText",r,l))return pp("LabelText",e,l,{variant:t});const i=e.getAttribute("placeholder");if(mp("PlaceholderText",r,i))return pp("PlaceholderText",e,i,{variant:t});const u=dp(rp(e));if(mp("Text",r,u))return pp("Text",e,u,{variant:t});if(mp("DisplayValue",r,e.value))return pp("DisplayValue",e,dp(e.value),{variant:t});const s=e.getAttribute("alt");if(mp("AltText",r,s))return pp("AltText",e,s,{variant:t});const d=e.getAttribute("title");if(mp("Title",r,d))return pp("Title",e,d,{variant:t});const c=e.getAttribute(Vc().testIdAttribute);return mp("TestId",r,c)?pp("TestId",e,c,{variant:t}):void 0}function bp(e,t){e.stack=t.stack.replace(t.message,e.message)}function vp(e,t){let{container:r=Ic(),timeout:n=Vc().asyncUtilTimeout,showOriginalStackTrace:a=Vc().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:i=(e=>(e.message=Vc().getElementError(e.message,r).message,e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let d,c,p,m=!1,f="idle";const b=setTimeout((function(){let e;d?(e=d,a||"TestingLibraryElementError"!==e.name||bp(e,o)):(e=new Error("Timed out in waitFor."),a||bp(e,o)),y(i(e),null)}),n),v=Bc();if(v){const{unstable_advanceTimersWrapper:e}=Vc();for(g();!m;){if(!Bc()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||bp(e,o),void s(e)}if(e((()=>{jest.advanceTimersByTime(l)})),g(),m)break;await e((async()=>{await new Promise((e=>{setTimeout(e,0),jest.advanceTimersByTime(0)}))}))}}else{try{kc(r)}catch(e){return void s(e)}c=setInterval(h,l);const{MutationObserver:e}=Nc(r);p=new e(h),p.observe(r,u),g()}function y(e,r){m=!0,clearTimeout(b),v||(clearInterval(c),p.disconnect()),e?s(e):t(r)}function h(){if(Bc()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||bp(e,o),s(e)}return g()}function g(){if("pending"!==f)try{const t=function(e){try{return Hc._disableExpensiveErrorDiagnostics=!0,e()}finally{Hc._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(f="pending",t.then((e=>{f="resolved",y(null,e)}),(e=>{f="rejected",d=e}))):y(null,t)}catch(e){d=e}}}))}function yp(e,t){const r=new Error("STACK_TRACE_MESSAGE");return Vc().asyncWrapper((()=>vp(e,{stackTraceError:r,...t})))}function hp(e,t){return Vc().getElementError(e,t)}function gp(e,t){return hp(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Cp(e,t,r,n){let{exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===n?{}:n;const u=a?Yc:Kc,s=ep({collapseWhitespace:o,trim:l,normalizer:i});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function qp(e,t,r,n){const a=Cp(e,t,r,n);if(a.length>1)throw gp("Found multiple elements by ["+e+"="+r+"]",t);return a[0]||null}function Pp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(l.length>1){const e=l.map((e=>hp(null,e).message)).join("\n\n");throw gp(t(r,...a)+"\n\nHere are the matching elements:\n\n"+e,r)}return l[0]||null}}function Ep(e,t){return Vc().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function wp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(!l.length)throw Vc().getElementError(t(r,...a),r);return l}}function xp(e){return(t,r,n,a)=>yp((()=>e(t,r,n)),{container:t,...a})}const Rp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=Vc().throwSuggestions}={}]=o.slice(-1);if(i&&u){const e=fp(i,r);if(e&&!t.endsWith(e.queryName))throw Ep(e.toString(),n)}return i},Op=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=Vc().throwSuggestions}={}]=o.slice(-1);if(i.length&&u){const e=[...new Set(i.map((e=>{var t;return null==(t=fp(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(fp(i[0],r).queryName))throw Ep(e[0],n)}return i};function Tp(e,t,r){const n=Rp(Pp(e,t),e.name,"query"),a=wp(e,r),o=Pp(a,t),l=Rp(o,e.name,"get");return[n,Op(a,e.name.replace("query","get"),"getAll"),l,xp(Op(a,e.name,"findAll")),xp(Rp(o,e.name,"find"))]}var _p=Object.freeze({__proto__:null,getElementError:hp,wrapAllByQueryWithSuggestion:Op,wrapSingleQueryWithSuggestion:Rp,getMultipleElementsFoundError:gp,queryAllByAttribute:Cp,queryByAttribute:qp,makeSingleQuery:Pp,makeGetAllQuery:wp,makeFindQuery:xp,buildQueries:Tp});const Mp=function(e,t,r){let{exact:n=!0,trim:a,collapseWhitespace:o,normalizer:l}=void 0===r?{}:r;const i=n?Yc:Kc,u=ep({collapseWhitespace:o,trim:a,normalizer:l}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Gc(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return i(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},Ap=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===r?{}:r;kc(e);const u=a?Yc:Kc,s=ep({collapseWhitespace:o,trim:l,normalizer:i}),d=Array.from(e.querySelectorAll("*")).filter((e=>Qc(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,a)=>{const o=Jc(e,a,{selector:n});o.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const l=o.filter((e=>Boolean(e.content))).map((e=>e.content));return u(l.join(" "),a,t,s)&&r.push(a),l.length>1&&l.forEach(((e,n)=>{u(e,a,t,s)&&r.push(a);const o=[...l];o.splice(n,1),o.length>1&&u(o.join(" "),a,t,s)&&r.push(a)})),r}),[]).concat(Cp("aria-label",e,t,{exact:a,normalizer:s}));return Array.from(new Set(d)).filter((e=>e.matches(n)))},jp=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];const o=Ap(e,t,...n);if(!o.length){const r=Mp(e,t,...n);if(r.length){const n=r.map((t=>Sp(e,t))).filter((e=>!!e));throw n.length?Vc().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):Vc().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw Vc().getElementError("Unable to find a label with the text of: "+t,e)}return o};function Sp(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}const Bp=(e,t)=>"Found multiple elements with the text of: "+t,Ip=Rp(Pp(Ap,Bp),Ap.name,"query"),Np=Pp(jp,Bp),kp=xp(Op(jp,jp.name,"findAll")),Fp=xp(Rp(Np,jp.name,"find")),Lp=Op(jp,jp.name,"getAll"),Up=Rp(Np,jp.name,"get"),Dp=Op(Ap,Ap.name,"queryAll"),Hp=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return kc(t[0]),Cp("placeholder",...t)},zp=Op(Hp,Hp.name,"queryAll"),[Vp,$p,Wp,Gp,Qp]=Tp(Hp,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),Jp=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,ignore:i=Vc().defaultIgnore,normalizer:u}=void 0===r?{}:r;kc(e);const s=a?Yc:Kc,d=ep({collapseWhitespace:o,trim:l,normalizer:u});let c=[];return"function"==typeof e.matches&&e.matches(n)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(n))].filter((e=>!i||!e.matches(i))).filter((e=>s(rp(e),e,t,d)))},Xp=Op(Jp,Jp.name,"queryAll"),[Kp,Yp,Zp,em,tm]=Tp(Jp,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:a,normalizer:o}=r,l=ep({collapseWhitespace:n,trim:a,normalizer:o})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),rm=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;kc(e);const i=n?Yc:Kc,u=ep({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>i(rp(e),e,t,u)))}return i(e.value,e,t,u)}))},nm=Op(rm,rm.name,"queryAll"),[am,om,lm,im,um]=Tp(rm,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),sm=/^(img|input|area|.+-.+)$/i,dm=function(e,t,r){return void 0===r&&(r={}),kc(e),Cp("alt",e,t,r).filter((e=>sm.test(e.tagName)))},cm=Op(dm,dm.name,"queryAll"),[pm,mm,fm,bm,vm]=Tp(dm,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),ym=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;kc(e);const i=n?Yc:Kc,u=ep({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>i(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&i(rp(e),e,t,u)))},hm=Op(ym,ym.name,"queryAll"),[gm,Cm,qm,Pm,Em]=Tp(ym,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+"."));function wm(e,t,r){let{exact:n=!0,collapseWhitespace:a,hidden:o=Vc().defaultHidden,name:l,description:i,trim:u,normalizer:s,queryFallbacks:d=!1,selected:c,checked:p,pressed:m,current:f,level:b,expanded:v}=void 0===r?{}:r;kc(e);const y=n?Yc:Kc,h=ep({collapseWhitespace:a,trim:u,normalizer:s});var g,C,q,P,E;if(void 0!==c&&void 0===(null==(g=sc.get(t))?void 0:g.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==p&&void 0===(null==(C=sc.get(t))?void 0:C.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(q=sc.get(t))?void 0:q.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(P=sc.get(t))?void 0:P.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==b&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==v&&void 0===(null==(E=sc.get(t))?void 0:E.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const w=new WeakMap;function x(e){return w.has(e)||w.set(e,ap(e)),w.get(e)}return Array.from(e.querySelectorAll(function(e,t,r){var n;if("string"!=typeof e)return"*";const a=t&&!r?'*[role~="'+e+'"]':"*[role]",o=null!=(n=Pc.get(e))?n:new Set,l=new Set(Array.from(o).map((e=>{let{name:t}=e;return t})));return[a].concat(Array.from(l)).join(",")}(t,n,s?h:void 0))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(d)return r.split(" ").filter(Boolean).some((r=>y(r,e,t,h)));if(s)return y(r,e,t,h);const[n]=r.split(" ");return y(n,e,t,h)}return lp(e).some((r=>y(r,e,t,h)))})).filter((e=>void 0!==c?c===function(e){return"OPTION"===e.tagName?e.selected:sp(e,"aria-selected")}(e):void 0!==p?p===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:sp(e,"aria-checked")}(e):void 0!==m?m===function(e){return sp(e,"aria-pressed")}(e):void 0!==f?f===function(e){var t,r;return null!=(t=null!=(r=sp(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e):void 0!==v?v===function(e){return sp(e,"aria-expanded")}(e):void 0===b||b===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e))).filter((e=>void 0===l||Yc(Fr(e,{computedStyleSupportsPseudoElements:Vc().computedStyleSupportsPseudoElements}),e,l,(e=>e)))).filter((e=>void 0===i||Yc(Nr(e,{computedStyleSupportsPseudoElements:Vc().computedStyleSupportsPseudoElements}),e,i,(e=>e)))).filter((e=>!1!==o||!1===op(e,{isSubtreeInaccessible:x})))}const xm=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},Rm=Op(wm,wm.name,"queryAll"),[Om,Tm,_m,Mm,Am]=Tp(wm,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+xm(n)}),(function(e,t,r){let{hidden:n=Vc().defaultHidden,name:a,description:o}=void 0===r?{}:r;if(Vc()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+xm(a);let l,i="";Array.from(e.children).forEach((e=>{i+=up(e,{hidden:n,includeDescription:void 0!==o})})),l=0===i.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+i.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===a?"":"string"==typeof a?' and name "'+a+'"':" and name `"+a+"`";let s="";return s=void 0===o?"":"string"==typeof o?' and description "'+o+'"':" and description `"+o+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+l).trim()})),jm=()=>Vc().testIdAttribute,Sm=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return kc(t[0]),Cp(jm(),...t)},Bm=Op(Sm,Sm.name,"queryAll"),[Im,Nm,km,Fm,Lm]=Tp(Sm,((e,t)=>"Found multiple elements by: ["+jm()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+jm()+'="'+t+'"]'));var Um=Object.freeze({__proto__:null,queryAllByLabelText:Dp,queryByLabelText:Ip,getAllByLabelText:Lp,getByLabelText:Up,findAllByLabelText:kp,findByLabelText:Fp,queryByPlaceholderText:Vp,queryAllByPlaceholderText:zp,getByPlaceholderText:Wp,getAllByPlaceholderText:$p,findAllByPlaceholderText:Gp,findByPlaceholderText:Qp,queryByText:Kp,queryAllByText:Xp,getByText:Zp,getAllByText:Yp,findAllByText:em,findByText:tm,queryByDisplayValue:am,queryAllByDisplayValue:nm,getByDisplayValue:lm,getAllByDisplayValue:om,findAllByDisplayValue:im,findByDisplayValue:um,queryByAltText:pm,queryAllByAltText:cm,getByAltText:fm,getAllByAltText:mm,findAllByAltText:bm,findByAltText:vm,queryByTitle:gm,queryAllByTitle:hm,getByTitle:qm,getAllByTitle:Cm,findAllByTitle:Pm,findByTitle:Em,queryByRole:Om,queryAllByRole:Rm,getAllByRole:Tm,getByRole:_m,findAllByRole:Mm,findByRole:Am,queryByTestId:Im,queryAllByTestId:Bm,getByTestId:km,getAllByTestId:Nm,findAllByTestId:Fm,findByTestId:Lm});function Dm(e,t,r){return void 0===t&&(t=Um),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const a=t[n];return r[n]=a.bind(null,e),r}),r)}const Hm=e=>!e||Array.isArray(e)&&!e.length;function zm(e){if(Hm(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const Vm={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}}},$m={doubleClick:"dblClick"};function Wm(e,t){return Vc().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function Gm(e,t,r,n){let{EventType:a="Event",defaultInit:o={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...r},{target:{value:i,files:u,...s}={}}=l;void 0!==i&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:a}=Object.getOwnPropertyDescriptor(n,"value")||{};if(a&&r!==a)a.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,i),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const d=Nc(t),c=d[a]||d.Event;let p;if("function"==typeof c)p=new c(e,l);else{p=d.document.createEvent(a);const{bubbles:t,cancelable:r,detail:n,...o}=l;p.initEvent(e,t,r,n),Object.keys(o).forEach((e=>{p[e]=o[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=l[e];"object"==typeof t&&("function"==typeof d.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new d.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function Qm(e){return"https://testing-playground.com/#markup="+(t=e,wc.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(Vm).forEach((e=>{const{EventType:t,defaultInit:r}=Vm[e],n=e.toLowerCase();Gm[e]=(e,a)=>Gm(n,e,a,{EventType:t,defaultInit:r}),Wm[e]=(t,r)=>Wm(t,Gm[e](t,r))})),Object.keys($m).forEach((e=>{const t=$m[e];Wm[e]=function(){return Wm[t](...arguments)}}));const Jm={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>Dc(e,t,r))):Dc(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Ic().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=Qm(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},Xm="undefined"!=typeof document&&document.body?Dm(document.body,Um,Jm):Object.keys(Um).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),Jm),Km=c.act;function Ym(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function Zm(e){Ym().IS_REACT_ACT_ENVIRONMENT=e}function ef(){return Ym().IS_REACT_ACT_ENVIRONMENT}const tf=(rf=Km,e=>{const t=ef();Zm(!0);try{let r=!1;const n=rf((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{Zm(t),e(r)}),(e=>{Zm(t),r(e)}))}}:(Zm(t),n)}catch(e){throw Zm(t),e}});var rf;const nf=function(){return Wm(...arguments)};Object.keys(Wm).forEach((e=>{nf[e]=function(){return Wm[e](...arguments)}}));const af=nf.mouseEnter,of=nf.mouseLeave;nf.mouseEnter=function(){return af(...arguments),nf.mouseOver(...arguments)},nf.mouseLeave=function(){return of(...arguments),nf.mouseOut(...arguments)};const lf=nf.pointerEnter,uf=nf.pointerLeave;nf.pointerEnter=function(){return lf(...arguments),nf.pointerOver(...arguments)},nf.pointerLeave=function(){return uf(...arguments),nf.pointerOut(...arguments)};const sf=nf.select;nf.select=(e,t)=>{sf(e,t),e.focus(),nf.keyUp(e,t)};const df=nf.blur,cf=nf.focus;nf.blur=function(){return nf.focusOut(...arguments),df(...arguments)},nf.focus=function(){return nf.focusIn(...arguments),cf(...arguments)},zc({unstable_advanceTimersWrapper:e=>tf(e),asyncWrapper:async e=>{const t=ef();Zm(!1);try{return await e()}finally{Zm(t)}},eventWrapper:e=>{let t;return tf((()=>{t=e()})),t}});const pf=new Set,mf=[];function ff(e,t){let r,{hydrate:n,ui:a,wrapper:o}=t;return n?tf((()=>{r=d.hydrateRoot(e,o?u.createElement(o,null,a):a)})):r=d.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function bf(e){return{hydrate(t){s.default.hydrate(t,e)},render(t){s.default.render(t,e)},unmount(){s.default.unmountComponentAtNode(e)}}}function vf(e,t){let{baseElement:r,container:n,hydrate:a,queries:o,root:l,wrapper:i}=t;const s=e=>i?u.createElement(i,null,e):e;return tf((()=>{a?l.hydrate(s(e),n):l.render(s(e),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(Uc(e,t,n)))):console.log(Uc(e,t,n))},unmount:()=>{tf((()=>{l.unmount()}))},rerender:e=>{vf(s(e),{container:n,baseElement:r,root:l})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...Dm(r,o)}}function yf(e,t){let r,{container:n,baseElement:a=n,legacyRoot:o=!1,queries:l,hydrate:i=!1,wrapper:u}=void 0===t?{}:t;if(a||(a=document.body),n||(n=a.appendChild(document.createElement("div"))),pf.has(n))mf.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(o?bf:ff)(n,{hydrate:i,ui:e,wrapper:u}),mf.push({container:n,root:r}),pf.add(n)}return vf(e,{container:n,baseElement:a,queries:l,hydrate:i,wrapper:u,root:r})}e.act=tf,e.buildQueries=Tp,e.cleanup=function(){mf.forEach((e=>{let{root:t,container:r}=e;tf((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),mf.length=0,pf.clear()},e.configure=zc,e.createEvent=Gm,e.findAllByAltText=bm,e.findAllByDisplayValue=im,e.findAllByLabelText=kp,e.findAllByPlaceholderText=Gp,e.findAllByRole=Mm,e.findAllByTestId=Fm,e.findAllByText=em,e.findAllByTitle=Pm,e.findByAltText=vm,e.findByDisplayValue=um,e.findByLabelText=Fp,e.findByPlaceholderText=Qp,e.findByRole=Am,e.findByTestId=Lm,e.findByText=tm,e.findByTitle=Em,e.fireEvent=nf,e.getAllByAltText=mm,e.getAllByDisplayValue=om,e.getAllByLabelText=Lp,e.getAllByPlaceholderText=$p,e.getAllByRole=Tm,e.getAllByTestId=Nm,e.getAllByText=Yp,e.getAllByTitle=Cm,e.getByAltText=fm,e.getByDisplayValue=lm,e.getByLabelText=Up,e.getByPlaceholderText=Wp,e.getByRole=_m,e.getByTestId=km,e.getByText=Zp,e.getByTitle=qm,e.getConfig=Vc,e.getDefaultNormalizer=Zc,e.getElementError=hp,e.getMultipleElementsFoundError=gp,e.getNodeText=rp,e.getQueriesForElement=Dm,e.getRoles=ip,e.getSuggestedQuery=fp,e.isInaccessible=op,e.logDOM=Dc,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(up(e,{hidden:r}))},e.makeFindQuery=xp,e.makeGetAllQuery=wp,e.makeSingleQuery=Pp,e.prettyDOM=Uc,e.prettyFormat=Dt,e.queries=Um,e.queryAllByAltText=cm,e.queryAllByAttribute=Cp,e.queryAllByDisplayValue=nm,e.queryAllByLabelText=Dp,e.queryAllByPlaceholderText=zp,e.queryAllByRole=Rm,e.queryAllByTestId=Bm,e.queryAllByText=Xp,e.queryAllByTitle=hm,e.queryByAltText=pm,e.queryByAttribute=qp,e.queryByDisplayValue=am,e.queryByLabelText=Ip,e.queryByPlaceholderText=Vp,e.queryByRole=Om,e.queryByTestId=Im,e.queryByText=Kp,e.queryByTitle=gm,e.queryHelpers=_p,e.render=yf,e.renderHook=function(e,t){void 0===t&&(t={});const{initialProps:r,...n}=t,a=u.createRef();function o(t){let{renderCallbackProps:r}=t;const n=e(r);return u.useEffect((()=>{a.current=n})),null}const{rerender:l,unmount:i}=yf(u.createElement(o,{renderCallbackProps:r}),n);return{result:a,rerender:function(e){return l(u.createElement(o,{renderCallbackProps:e}))},unmount:i}},e.screen=Xm,e.waitFor=yp,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){zm(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return zm(e()),yp((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!Hm(t))throw r}),t)},e.within=Dm,e.wrapAllByQueryWithSuggestion=Op,e.wrapSingleQueryWithSuggestion=Rp,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.pure.umd.min.js.map
