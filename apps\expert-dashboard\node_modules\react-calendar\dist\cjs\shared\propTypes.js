"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.tileProps = exports.tileGroupProps = exports.rangeOf = exports.isView = exports.isViews = exports.isValue = exports.isRef = exports.isMaxDate = exports.isMinDate = exports.isClassName = exports.isCalendarType = void 0;
var prop_types_1 = __importDefault(require("prop-types"));
var const_js_1 = require("./const.js");
var calendarTypes = Object.values(const_js_1.CALENDAR_TYPES);
var deprecatedCalendarTypes = Object.values(const_js_1.DEPRECATED_CALENDAR_TYPES);
var allViews = ['century', 'decade', 'year', 'month'];
exports.isCalendarType = prop_types_1.default.oneOf(__spreadArray(__spreadArray([], calendarTypes, true), deprecatedCalendarTypes, true));
exports.isClassName = prop_types_1.default.oneOfType([
    prop_types_1.default.string,
    prop_types_1.default.arrayOf(prop_types_1.default.string),
]);
var isMinDate = function isMinDate(props, propName, componentName) {
    var _a = props, _b = propName, minDate = _a[_b];
    if (!minDate) {
        return null;
    }
    if (!(minDate instanceof Date)) {
        return new Error("Invalid prop `".concat(propName, "` of type `").concat(typeof minDate, "` supplied to `").concat(componentName, "`, expected instance of `Date`."));
    }
    var maxDate = props.maxDate;
    if (maxDate && minDate > maxDate) {
        return new Error("Invalid prop `".concat(propName, "` of type `").concat(typeof minDate, "` supplied to `").concat(componentName, "`, minDate cannot be larger than maxDate."));
    }
    return null;
};
exports.isMinDate = isMinDate;
var isMaxDate = function isMaxDate(props, propName, componentName) {
    var _a = props, _b = propName, maxDate = _a[_b];
    if (!maxDate) {
        return null;
    }
    if (!(maxDate instanceof Date)) {
        return new Error("Invalid prop `".concat(propName, "` of type `").concat(typeof maxDate, "` supplied to `").concat(componentName, "`, expected instance of `Date`."));
    }
    var minDate = props.minDate;
    if (minDate && maxDate < minDate) {
        return new Error("Invalid prop `".concat(propName, "` of type `").concat(typeof maxDate, "` supplied to `").concat(componentName, "`, maxDate cannot be smaller than minDate."));
    }
    return null;
};
exports.isMaxDate = isMaxDate;
exports.isRef = prop_types_1.default.oneOfType([
    prop_types_1.default.func,
    prop_types_1.default.exact({
        current: prop_types_1.default.any,
    }),
]);
var isRange = prop_types_1.default.arrayOf(prop_types_1.default.oneOfType([prop_types_1.default.instanceOf(Date), prop_types_1.default.oneOf([null])]).isRequired);
exports.isValue = prop_types_1.default.oneOfType([
    prop_types_1.default.instanceOf(Date),
    prop_types_1.default.oneOf([null]),
    isRange,
]);
exports.isViews = prop_types_1.default.arrayOf(prop_types_1.default.oneOf(allViews));
var isView = function isView(props, propName, componentName) {
    var _a = props, _b = propName, view = _a[_b];
    if (view !== undefined && (typeof view !== 'string' || allViews.indexOf(view) === -1)) {
        return new Error("Invalid prop `".concat(propName, "` of value `").concat(view, "` supplied to `").concat(componentName, "`, expected one of [").concat(allViews
            .map(function (a) { return "\"".concat(a, "\""); })
            .join(', '), "]."));
    }
    // Everything is fine
    return null;
};
exports.isView = isView;
exports.isView.isRequired = function isViewIsRequired(props, propName, componentName, location, propFullName) {
    var _a = props, _b = propName, view = _a[_b];
    if (!view) {
        return new Error("The prop `".concat(propName, "` is marked as required in `").concat(componentName, "`, but its value is `").concat(view, "`."));
    }
    return (0, exports.isView)(props, propName, componentName, location, propFullName);
};
var rangeOf = function (type) {
    return prop_types_1.default.arrayOf(type);
};
exports.rangeOf = rangeOf;
exports.tileGroupProps = {
    activeStartDate: prop_types_1.default.instanceOf(Date).isRequired,
    hover: prop_types_1.default.instanceOf(Date),
    locale: prop_types_1.default.string,
    maxDate: exports.isMaxDate,
    minDate: exports.isMinDate,
    onClick: prop_types_1.default.func,
    onMouseOver: prop_types_1.default.func,
    tileClassName: prop_types_1.default.oneOfType([prop_types_1.default.func, exports.isClassName]),
    tileContent: prop_types_1.default.oneOfType([prop_types_1.default.func, prop_types_1.default.node]),
    value: exports.isValue,
    valueType: prop_types_1.default.oneOf(['century', 'decade', 'year', 'month', 'day']).isRequired,
};
exports.tileProps = {
    activeStartDate: prop_types_1.default.instanceOf(Date).isRequired,
    classes: prop_types_1.default.arrayOf(prop_types_1.default.string.isRequired).isRequired,
    date: prop_types_1.default.instanceOf(Date).isRequired,
    locale: prop_types_1.default.string,
    maxDate: exports.isMaxDate,
    minDate: exports.isMinDate,
    onClick: prop_types_1.default.func,
    onMouseOver: prop_types_1.default.func,
    style: prop_types_1.default.objectOf(prop_types_1.default.oneOfType([prop_types_1.default.string, prop_types_1.default.number])),
    tileClassName: prop_types_1.default.oneOfType([prop_types_1.default.func, exports.isClassName]),
    tileContent: prop_types_1.default.oneOfType([prop_types_1.default.func, prop_types_1.default.node]),
    tileDisabled: prop_types_1.default.func,
};
