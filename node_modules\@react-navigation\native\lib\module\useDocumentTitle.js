import * as React from 'react';
/**
 * Set the document title for the active screen
 */
export default function useDocumentTitle(ref) {
  let {
    enabled = true,
    formatter = (options, route) => (options === null || options === void 0 ? void 0 : options.title) ?? (route === null || route === void 0 ? void 0 : route.name)
  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  React.useEffect(() => {
    if (!enabled) {
      return;
    }
    const navigation = ref.current;
    if (navigation) {
      const title = formatter(navigation.getCurrentOptions(), navigation.getCurrentRoute());
      document.title = title;
    }
    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('options', e => {
      const title = formatter(e.data.options, navigation === null || navigation === void 0 ? void 0 : navigation.getCurrentRoute());
      document.title = title;
    });
  });
}
//# sourceMappingURL=useDocumentTitle.js.map