{"version": 3, "names": ["React", "NavigationContent", "render", "children", "useComponent", "renderRef", "useRef", "current", "useEffect", "Error"], "sourceRoot": "../../src", "sources": ["useComponent.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,MAAMC,iBAAiB,GAAG,QAAiC;EAAA,IAAhC;IAAEC,MAAM;IAAEC;EAAgB,CAAC;EACpD,OAAOD,MAAM,CAACC,QAAQ,CAAC;AACzB,CAAC;AAED,eAAe,SAASC,YAAY,CAACF,MAAc,EAAE;EACnD,MAAMG,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAgBJ,MAAM,CAAC;;EAErD;EACA;EACA;EACAG,SAAS,CAACE,OAAO,GAAGL,MAAM;EAE1BF,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpBH,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,OAAOP,KAAK,CAACM,MAAM,CAAC,SAAiD;IAAA,IAAhD;MAAEH;IAAwC,CAAC;IAC9D,MAAMD,MAAM,GAAGG,SAAS,CAACE,OAAO;IAEhC,IAAIL,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM,IAAIO,KAAK,CACb,+EAA+E,CAChF;IACH;IAEA,oBAAO,oBAAC,iBAAiB;MAAC,MAAM,EAAEP;IAAO,GAAEC,QAAQ,CAAqB;EAC1E,CAAC,CAAC,CAACI,OAAO;AACZ"}