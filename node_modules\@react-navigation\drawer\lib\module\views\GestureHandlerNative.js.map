{"version": 3, "names": ["React", "PanGestureHandler", "PanGestureHandlerNative", "DrawerGestureContext", "props", "gestureRef", "useRef", "GestureHandlerRootView", "State", "GestureState", "TapGestureHandler"], "sourceRoot": "../../../src", "sources": ["views/GestureHandlerNative.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,iBAAiB,IAAIC,uBAAuB,QAEvC,8BAA8B;AAErC,OAAOC,oBAAoB,MAAM,+BAA+B;AAEhE,OAAO,SAASF,iBAAiB,CAACG,KAAkC,EAAE;EACpE,MAAMC,UAAU,GAAGL,KAAK,CAACM,MAAM,CAA0B,IAAI,CAAC;EAE9D,oBACE,oBAAC,oBAAoB,CAAC,QAAQ;IAAC,KAAK,EAAED;EAAW,gBAC/C,oBAAC,uBAAuB,EAAKD,KAAK,CAAI,CACR;AAEpC;AAGA,SACEG,sBAAsB,EACtBC,KAAK,IAAIC,YAAY,EACrBC,iBAAiB,QACZ,8BAA8B"}