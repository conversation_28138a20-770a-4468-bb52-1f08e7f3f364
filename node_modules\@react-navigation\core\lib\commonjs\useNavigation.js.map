{"version": 3, "names": ["useNavigation", "root", "React", "useContext", "NavigationContainerRefContext", "navigation", "NavigationContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useNavigation.tsx"], "mappings": ";;;;;;AACA;AAEA;AACA;AAAoD;AAAA;AAAA;AAGpD;AACA;AACA;AACA;AACA;AACe,SAASA,aAAa,GAI9B;EACL,MAAMC,IAAI,GAAGC,KAAK,CAACC,UAAU,CAACC,sCAA6B,CAAC;EAC5D,MAAMC,UAAU,GAAGH,KAAK,CAACC,UAAU,CAACG,0BAAiB,CAAC;EAEtD,IAAID,UAAU,KAAKE,SAAS,IAAIN,IAAI,KAAKM,SAAS,EAAE;IAClD,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;EACH;;EAEA;EACA,OAAQH,UAAU,IAAIJ,IAAI;AAC5B"}