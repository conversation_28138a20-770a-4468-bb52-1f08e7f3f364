{"name": "react-calendar", "version": "4.8.0", "description": "Ultimate calendar for your React app.", "type": "module", "sideEffects": ["*.css"], "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "source": "./src/index.ts", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./dist/Calendar.css": "./dist/Calendar.css"}, "scripts": {"build": "yarn build-js && yarn copy-styles", "build-js": "yarn build-js-esm && yarn build-js-cjs && yarn build-js-cjs-package", "build-js-esm": "tsc --project tsconfig.build.json --outDir dist/esm", "build-js-cjs": "tsc --project tsconfig.build.json --outDir dist/cjs --module commonjs --moduleResolution node --verbatimModuleSyntax false", "build-js-cjs-package": "echo '{\n  \"type\": \"commonjs\"\n}' > dist/cjs/package.json", "clean": "<PERSON><PERSON><PERSON> dist", "copy-styles": "cpy 'src/**/*.css' dist", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "prepack": "yarn clean && yarn build", "prettier": "prettier --check . --cache", "test": "yarn lint && yarn tsc && yarn prettier && yarn unit", "tsc": "tsc", "unit": "vitest", "watch": "yarn build-js-esm --watch & yarn build-js-cjs --watch & nodemon --watch src --ext css --exec \"yarn copy-styles\""}, "keywords": ["calendar", "date", "date-picker", "month-picker", "react"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@wojtekmaj/date-utils": "^1.1.3", "clsx": "^2.0.0", "get-user-locale": "^2.2.1", "prop-types": "^15.6.0", "warning": "^4.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@types/node": "*", "@types/react": "*", "@types/warning": "^3.0.0", "cpy-cli": "^5.0.0", "eslint": "^8.26.0", "eslint-config-wojtekmaj": "^0.9.0", "happy-dom": "^12.6.0", "nodemon": "^3.0.0", "prettier": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.0", "typescript": "^5.3.2", "vitest": "^1.0.2"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "https://github.com/wojtekmaj/react-calendar.git", "directory": "packages/react-calendar"}, "funding": "https://github.com/wojtekmaj/react-calendar?sponsor=1"}