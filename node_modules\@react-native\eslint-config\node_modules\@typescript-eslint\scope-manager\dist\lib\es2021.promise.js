"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2021_promise = void 0;
const base_config_1 = require("./base-config");
exports.es2021_promise = {
    AggregateError: base_config_1.TYPE_VALUE,
    AggregateErrorConstructor: base_config_1.TYPE,
    PromiseConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2021.promise.js.map