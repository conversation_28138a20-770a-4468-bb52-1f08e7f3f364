{"version": 3, "names": ["extractPathFromURL", "prefixes", "url", "prefix", "protocol", "match", "host", "replace", "RegExp", "escapeStringRegexp", "prefixRegex", "split", "map", "it", "join", "originAndPath", "searchParams", "normalizedURL", "concat", "test", "undefined"], "sourceRoot": "../../src", "sources": ["extractPathFromURL.tsx"], "mappings": ";;;;;;AAAA;AAAsD;AAEvC,SAASA,kBAAkB,CAACC,QAAkB,EAAEC,GAAW,EAAE;EAC1E,KAAK,MAAMC,MAAM,IAAIF,QAAQ,EAAE;IAAA;IAC7B,MAAMG,QAAQ,GAAG,kBAAAD,MAAM,CAACE,KAAK,CAAC,SAAS,CAAC,kDAAvB,cAA0B,CAAC,CAAC,KAAI,EAAE;IACnD,MAAMC,IAAI,GAAGH,MAAM,CAChBI,OAAO,CAAC,IAAIC,MAAM,CAAE,IAAG,IAAAC,2BAAkB,EAACL,QAAQ,CAAE,EAAC,CAAC,EAAE,EAAE,CAAC,CAC3DG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;;IAEvB,MAAMG,WAAW,GAAG,IAAIF,MAAM,CAC3B,IAAG,IAAAC,2BAAkB,EAACL,QAAQ,CAAE,OAAME,IAAI,CACxCK,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,EAAE,IAAMA,EAAE,KAAK,GAAG,GAAG,OAAO,GAAG,IAAAJ,2BAAkB,EAACI,EAAE,CAAE,CAAC,CAC5DC,IAAI,CAAC,KAAK,CAAE,EAAC,CACjB;IAED,MAAM,CAACC,aAAa,EAAEC,YAAY,CAAC,GAAGd,GAAG,CAACS,KAAK,CAAC,GAAG,CAAC;IACpD,MAAMM,aAAa,GAAGF,aAAa,CAChCR,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBW,MAAM,CAACF,YAAY,GAAI,IAAGA,YAAa,EAAC,GAAG,EAAE,CAAC;IAEjD,IAAIN,WAAW,CAACS,IAAI,CAACF,aAAa,CAAC,EAAE;MACnC,OAAOA,aAAa,CAACV,OAAO,CAACG,WAAW,EAAE,EAAE,CAAC;IAC/C;EACF;EAEA,OAAOU,SAAS;AAClB"}