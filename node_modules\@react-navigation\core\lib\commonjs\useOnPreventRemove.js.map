{"version": 3, "names": ["VISITED_ROUTE_KEYS", "Symbol", "shouldPreventRemove", "emitter", "beforeRemoveListeners", "currentRoutes", "nextRoutes", "action", "nextR<PERSON>e<PERSON>eys", "map", "route", "key", "removedRoutes", "filter", "includes", "reverse", "visitedRouteKeys", "Set", "beforeRemoveAction", "has", "isPrevented", "add", "event", "emit", "type", "target", "data", "canPreventDefault", "defaultPrevented", "useOnPreventRemove", "getState", "addKeyedListener", "React", "useContext", "NavigationBuilderContext", "NavigationRouteContext", "routeKey", "useEffect", "state", "routes"], "sourceRoot": "../../src", "sources": ["useOnPreventRemove.tsx"], "mappings": ";;;;;;;AAIA;AAEA;AAGA;AAA8D;AAAA;AAAA;AAU9D,MAAMA,kBAAkB,GAAGC,MAAM,CAAC,oBAAoB,CAAC;AAEhD,MAAMC,mBAAmB,GAAG,CACjCC,OAAkD,EAClDC,qBAA4E,EAC5EC,aAAgC,EAChCC,UAA0C,EAC1CC,MAAwB,KACrB;EACH,MAAMC,aAAa,GAAGF,UAAU,CAACG,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACC,GAAG,CAAC;;EAE1D;EACA,MAAMC,aAAa,GAAGP,aAAa,CAChCQ,MAAM,CAAEH,KAAK,IAAK,CAACF,aAAa,CAACM,QAAQ,CAACJ,KAAK,CAACC,GAAG,CAAC,CAAC,CACrDI,OAAO,EAAE;EAEZ,MAAMC,gBAA6B;EACjC;EACAT,MAAM,CAACP,kBAAkB,CAAC,IAAI,IAAIiB,GAAG,EAAU;EAEjD,MAAMC,kBAAkB,GAAG;IACzB,GAAGX,MAAM;IACT,CAACP,kBAAkB,GAAGgB;EACxB,CAAC;EAED,KAAK,MAAMN,KAAK,IAAIE,aAAa,EAAE;IAAA;IACjC,IAAII,gBAAgB,CAACG,GAAG,CAACT,KAAK,CAACC,GAAG,CAAC,EAAE;MACnC;MACA;IACF;;IAEA;IACA,MAAMS,WAAW,4BAAGhB,qBAAqB,CAACM,KAAK,CAACC,GAAG,CAAC,0DAAhC,2BAAAP,qBAAqB,EAAcc,kBAAkB,CAAC;IAE1E,IAAIE,WAAW,EAAE;MACf,OAAO,IAAI;IACb;IAEAJ,gBAAgB,CAACK,GAAG,CAACX,KAAK,CAACC,GAAG,CAAC;IAE/B,MAAMW,KAAK,GAAGnB,OAAO,CAACoB,IAAI,CAAC;MACzBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAEf,KAAK,CAACC,GAAG;MACjBe,IAAI,EAAE;QAAEnB,MAAM,EAAEW;MAAmB,CAAC;MACpCS,iBAAiB,EAAE;IACrB,CAAC,CAAC;IAEF,IAAIL,KAAK,CAACM,gBAAgB,EAAE;MAC1B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC;AAAC;AAEa,SAASC,kBAAkB,OAI9B;EAAA,IAJ+B;IACzCC,QAAQ;IACR3B,OAAO;IACPC;EACO,CAAC;EACR,MAAM;IAAE2B;EAAiB,CAAC,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAwB,CAAC;EACvE,MAAMxB,KAAK,GAAGsB,KAAK,CAACC,UAAU,CAACE,+BAAsB,CAAC;EACtD,MAAMC,QAAQ,GAAG1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,GAAG;EAE3BqB,KAAK,CAACK,SAAS,CAAC,MAAM;IACpB,IAAID,QAAQ,EAAE;MACZ,OAAOL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,cAAc,EAAEK,QAAQ,EAAG7B,MAAM,IAAK;QAC9D,MAAM+B,KAAK,GAAGR,QAAQ,EAAE;QAExB,OAAO5B,mBAAmB,CACxBC,OAAO,EACPC,qBAAqB,EACrBkC,KAAK,CAACC,MAAM,EACZ,EAAE,EACFhC,MAAM,CACP;MACH,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACwB,gBAAgB,EAAE3B,qBAAqB,EAAED,OAAO,EAAE2B,QAAQ,EAAEM,QAAQ,CAAC,CAAC;AAC5E"}