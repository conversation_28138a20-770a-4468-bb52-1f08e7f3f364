{"version": 3, "names": ["getLatestRelease", "name", "currentVersion", "logger", "debug", "includes", "cachedLatest", "cacheManager", "get", "aWeek", "lastChecked", "now", "Date", "Number", "eTag", "latestVersion", "getLatestRnDiffPurgeVersion", "semver", "compare", "prerelease", "version", "changelogUrl", "buildChangelogUrl", "diffUrl", "buildDiffUrl", "e", "undefined", "options", "headers", "data", "status", "fetch", "body", "substring", "eTagHeader", "set"], "sources": ["../../src/releaseChecker/getLatestRelease.ts"], "sourcesContent": ["import semver from 'semver';\nimport cacheManager from './releaseCacheManager';\nimport {fetch} from '../fetch';\nimport logger from '../logger';\n\nexport type Release = {\n  version: string;\n  changelogUrl: string;\n  diffUrl: string;\n};\n\n/**\n * Checks via GitHub API if there is a newer stable React Native release and,\n * if it exists, returns the release data.\n *\n * If the latest release is not newer or if it's a prerelease, the function\n * will return undefined.\n */\nexport default async function getLatestRelease(\n  name: string,\n  currentVersion: string,\n): Promise<Release | undefined> {\n  logger.debug('Checking for a newer version of React Native');\n  try {\n    logger.debug(`Current version: ${currentVersion}`);\n\n    // if the version is a 1000.0.0 version or 0.0.0, we want to bail\n    // since they are nightlies or unreleased versions\n    if (\n      currentVersion.includes('1000.0.0') ||\n      currentVersion.includes('0.0.0')\n    ) {\n      return;\n    }\n\n    const cachedLatest = cacheManager.get(name, 'latestVersion');\n\n    if (cachedLatest) {\n      logger.debug(`Cached release version: ${cachedLatest}`);\n    }\n\n    const aWeek = 7 * 24 * 60 * 60 * 1000;\n    const lastChecked = cacheManager.get(name, 'lastChecked');\n    const now = new Date();\n    if (lastChecked && Number(now) - Number(new Date(lastChecked)) < aWeek) {\n      logger.debug('Cached release is still recent, skipping remote check');\n      return;\n    }\n\n    logger.debug('Checking for newer releases on GitHub');\n    const eTag = cacheManager.get(name, 'eTag');\n    const latestVersion = await getLatestRnDiffPurgeVersion(name, eTag);\n    logger.debug(`Latest release: ${latestVersion}`);\n\n    if (\n      semver.compare(latestVersion, currentVersion) === 1 &&\n      !semver.prerelease(latestVersion)\n    ) {\n      return {\n        version: latestVersion,\n        changelogUrl: buildChangelogUrl(latestVersion),\n        diffUrl: buildDiffUrl(currentVersion),\n      };\n    }\n  } catch (e) {\n    logger.debug(\n      'Something went wrong with remote version checking, moving on',\n    );\n    logger.debug(e as any);\n  }\n  return undefined;\n}\n\nfunction buildChangelogUrl(version: string) {\n  return `https://github.com/facebook/react-native/releases/tag/v${version}`;\n}\n\nfunction buildDiffUrl(version: string) {\n  return `https://react-native-community.github.io/upgrade-helper/?from=${version}`;\n}\n\n/**\n * Returns the most recent React Native version available to upgrade to.\n */\nasync function getLatestRnDiffPurgeVersion(\n  name: string,\n  eTag?: string,\n): Promise<string> {\n  const options = {\n    // https://developer.github.com/v3/#user-agent-required\n    headers: {'User-Agent': 'React-Native-CLI'} as Headers,\n  };\n\n  if (eTag) {\n    options.headers['If-None-Match'] = eTag;\n  }\n\n  const {data, status, headers} = await fetch(\n    'https://api.github.com/repos/react-native-community/rn-diff-purge/tags',\n    options,\n  );\n\n  // Remote is newer.\n  if (status === 200) {\n    const body: Array<any> = data;\n    const latestVersion = body[0].name.substring(8);\n    const eTagHeader = headers.get('eTag');\n\n    // Update cache only if newer release is stable.\n    if (!semver.prerelease(latestVersion) && eTagHeader) {\n      logger.debug(`Saving ${eTagHeader} to cache`);\n      cacheManager.set(name, 'eTag', eTagHeader);\n      cacheManager.set(name, 'latestVersion', latestVersion);\n    }\n\n    return latestVersion;\n  }\n\n  // Cache is still valid.\n  if (status === 304) {\n    const latestVersion = cacheManager.get(name, 'latestVersion');\n    if (latestVersion) {\n      return latestVersion;\n    }\n  }\n\n  // Should be returned only if something went wrong.\n  return '0.0.0';\n}\n\ntype Headers = {\n  'User-Agent': string;\n  [header: string]: string;\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAA+B;AAQ/B;AACA;AACA;AACA;AACA;AACA;AACA;AACe,eAAeA,gBAAgB,CAC5CC,IAAY,EACZC,cAAsB,EACQ;EAC9BC,eAAM,CAACC,KAAK,CAAC,8CAA8C,CAAC;EAC5D,IAAI;IACFD,eAAM,CAACC,KAAK,CAAE,oBAAmBF,cAAe,EAAC,CAAC;;IAElD;IACA;IACA,IACEA,cAAc,CAACG,QAAQ,CAAC,UAAU,CAAC,IACnCH,cAAc,CAACG,QAAQ,CAAC,OAAO,CAAC,EAChC;MACA;IACF;IAEA,MAAMC,YAAY,GAAGC,4BAAY,CAACC,GAAG,CAACP,IAAI,EAAE,eAAe,CAAC;IAE5D,IAAIK,YAAY,EAAE;MAChBH,eAAM,CAACC,KAAK,CAAE,2BAA0BE,YAAa,EAAC,CAAC;IACzD;IAEA,MAAMG,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACrC,MAAMC,WAAW,GAAGH,4BAAY,CAACC,GAAG,CAACP,IAAI,EAAE,aAAa,CAAC;IACzD,MAAMU,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,IAAIF,WAAW,IAAIG,MAAM,CAACF,GAAG,CAAC,GAAGE,MAAM,CAAC,IAAID,IAAI,CAACF,WAAW,CAAC,CAAC,GAAGD,KAAK,EAAE;MACtEN,eAAM,CAACC,KAAK,CAAC,uDAAuD,CAAC;MACrE;IACF;IAEAD,eAAM,CAACC,KAAK,CAAC,uCAAuC,CAAC;IACrD,MAAMU,IAAI,GAAGP,4BAAY,CAACC,GAAG,CAACP,IAAI,EAAE,MAAM,CAAC;IAC3C,MAAMc,aAAa,GAAG,MAAMC,2BAA2B,CAACf,IAAI,EAAEa,IAAI,CAAC;IACnEX,eAAM,CAACC,KAAK,CAAE,mBAAkBW,aAAc,EAAC,CAAC;IAEhD,IACEE,iBAAM,CAACC,OAAO,CAACH,aAAa,EAAEb,cAAc,CAAC,KAAK,CAAC,IACnD,CAACe,iBAAM,CAACE,UAAU,CAACJ,aAAa,CAAC,EACjC;MACA,OAAO;QACLK,OAAO,EAAEL,aAAa;QACtBM,YAAY,EAAEC,iBAAiB,CAACP,aAAa,CAAC;QAC9CQ,OAAO,EAAEC,YAAY,CAACtB,cAAc;MACtC,CAAC;IACH;EACF,CAAC,CAAC,OAAOuB,CAAC,EAAE;IACVtB,eAAM,CAACC,KAAK,CACV,8DAA8D,CAC/D;IACDD,eAAM,CAACC,KAAK,CAACqB,CAAC,CAAQ;EACxB;EACA,OAAOC,SAAS;AAClB;AAEA,SAASJ,iBAAiB,CAACF,OAAe,EAAE;EAC1C,OAAQ,0DAAyDA,OAAQ,EAAC;AAC5E;AAEA,SAASI,YAAY,CAACJ,OAAe,EAAE;EACrC,OAAQ,iEAAgEA,OAAQ,EAAC;AACnF;;AAEA;AACA;AACA;AACA,eAAeJ,2BAA2B,CACxCf,IAAY,EACZa,IAAa,EACI;EACjB,MAAMa,OAAO,GAAG;IACd;IACAC,OAAO,EAAE;MAAC,YAAY,EAAE;IAAkB;EAC5C,CAAC;EAED,IAAId,IAAI,EAAE;IACRa,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAGd,IAAI;EACzC;EAEA,MAAM;IAACe,IAAI;IAAEC,MAAM;IAAEF;EAAO,CAAC,GAAG,MAAM,IAAAG,YAAK,EACzC,wEAAwE,EACxEJ,OAAO,CACR;;EAED;EACA,IAAIG,MAAM,KAAK,GAAG,EAAE;IAClB,MAAME,IAAgB,GAAGH,IAAI;IAC7B,MAAMd,aAAa,GAAGiB,IAAI,CAAC,CAAC,CAAC,CAAC/B,IAAI,CAACgC,SAAS,CAAC,CAAC,CAAC;IAC/C,MAAMC,UAAU,GAAGN,OAAO,CAACpB,GAAG,CAAC,MAAM,CAAC;;IAEtC;IACA,IAAI,CAACS,iBAAM,CAACE,UAAU,CAACJ,aAAa,CAAC,IAAImB,UAAU,EAAE;MACnD/B,eAAM,CAACC,KAAK,CAAE,UAAS8B,UAAW,WAAU,CAAC;MAC7C3B,4BAAY,CAAC4B,GAAG,CAAClC,IAAI,EAAE,MAAM,EAAEiC,UAAU,CAAC;MAC1C3B,4BAAY,CAAC4B,GAAG,CAAClC,IAAI,EAAE,eAAe,EAAEc,aAAa,CAAC;IACxD;IAEA,OAAOA,aAAa;EACtB;;EAEA;EACA,IAAIe,MAAM,KAAK,GAAG,EAAE;IAClB,MAAMf,aAAa,GAAGR,4BAAY,CAACC,GAAG,CAACP,IAAI,EAAE,eAAe,CAAC;IAC7D,IAAIc,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;EACF;;EAEA;EACA,OAAO,OAAO;AAChB"}