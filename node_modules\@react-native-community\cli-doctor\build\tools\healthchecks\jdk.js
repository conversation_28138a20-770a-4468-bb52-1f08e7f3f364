"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _path() {
  const data = require("path");
  _path = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var _versionRanges = _interopRequireDefault(require("../versionRanges"));
var _checkInstallation = require("../checkInstallation");
var _downloadAndUnzip = require("../downloadAndUnzip");
var _environmentVariables = require("../windows/environmentVariables");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var _default = {
  label: 'JDK',
  description: 'Required to compile Java code',
  getDiagnostics: async ({
    Languages
  }) => ({
    needsToBeFixed: (0, _checkInstallation.doesSoftwareNeedToBeFixed)({
      version: typeof Languages.Java === 'string' ? Languages.Java : Languages.Java.version,
      versionRange: _versionRanges.default.JAVA
    }),
    version: typeof Languages.Java === 'string' ? Languages.Java : Languages.Java.version,
    versionRange: _versionRanges.default.JAVA
  }),
  win32AutomaticFix: async ({
    loader
  }) => {
    try {
      // Installing JDK 11 because later versions seem to cause issues with gradle at the moment
      const installerUrl = 'https://download.java.net/java/GA/jdk11/9/GPL/openjdk-11.0.2_windows-x64_bin.zip';
      const installPath = process.env.LOCALAPPDATA || ''; // The zip is in a folder `jdk-11.02` so it can be unzipped directly there

      await (0, _downloadAndUnzip.downloadAndUnzip)({
        loader,
        downloadUrl: installerUrl,
        component: 'JDK',
        installPath
      });
      loader.text = 'Updating environment variables';
      const jdkPath = (0, _path().join)(installPath, 'jdk-11.0.2');
      await (0, _environmentVariables.setEnvironment)('JAVA_HOME', jdkPath);
      await (0, _environmentVariables.updateEnvironment)('PATH', (0, _path().join)(jdkPath, 'bin'));
      loader.succeed('JDK installed successfully. Please restart your shell to see the changes');
    } catch (e) {
      loader.fail(e);
    }
  },
  runAutomaticFix: async ({
    logManualInstallation,
    loader
  }) => {
    loader.fail();
    logManualInstallation({
      healthcheck: 'JDK',
      url: _cliTools().link.docs('environment-setup', {
        hash: 'jdk-studio',
        guide: 'native',
        platform: 'android'
      })
    });
  }
};
exports.default = _default;

//# sourceMappingURL=jdk.ts.map