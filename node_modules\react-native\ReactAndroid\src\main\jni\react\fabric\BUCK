load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_target", "react_native_xplat_target", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "jni",
    srcs = glob(["*.cpp"]),
    headers = glob(["*.h"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/fabric",
    ),
    fbandroid_allow_jni_merging = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    soname = "libfabricjni.$(ext)",
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("butter:butter"),
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
        react_native_xplat_target("react/config:config"),
        react_native_xplat_target("react/renderer/animations:animations"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/scheduler:scheduler"),
        react_native_xplat_target("react/renderer/mounting:mounting"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        react_native_xplat_target("react/renderer/components/scrollview:scrollview"),
        react_native_xplat_target("runtimeexecutor:runtimeexecutor"),
        react_native_target("jni/react/jni:jni"),
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/jsi:JSIDynamic",
        "//xplat/jsi:jsi",
        "//xplat/third-party/linker_lib:atomic",
        FBJNI_TARGET,
        # TODO T71316899: Extract CoreComponentsRegistry out of this module
        # The following dependencies are required by CoreComponentsRegistry
        "//xplat/js/react-native-github:generated_components-rncore",
        react_native_xplat_target("react/renderer/components/image:image"),
        react_native_xplat_target("react/renderer/components/modal:modal"),
        react_native_xplat_target("react/renderer/components/switch:androidswitch"),
        react_native_xplat_target("react/renderer/components/progressbar:androidprogressbar"),
        react_native_xplat_target("react/renderer/components/text:text"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("react/renderer/components/textinput:androidtextinput"),
    ],
)
