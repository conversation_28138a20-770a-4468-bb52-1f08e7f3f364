{"version": 3, "names": ["getHeaderTitle", "HeaderShownContext", "StackActions", "React", "useSafeAreaInsets", "debounce", "ModalPresentationContext", "HeaderSegment", "memo", "Header", "back", "layout", "progress", "options", "route", "navigation", "styleInterpolator", "insets", "previousTitle", "headerBackTitle", "undefined", "title", "goBack", "useCallback", "isFocused", "canGoBack", "dispatch", "pop", "source", "key", "isModal", "useContext", "isParentHeaderShown", "statusBarHeight", "headerStatusBarHeight", "top", "name"], "sourceRoot": "../../../../src", "sources": ["views/Header/Header.tsx"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,4BAA4B;AAC/E,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAGlE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,4BAAeJ,KAAK,CAACK,IAAI,CAAC,SAASC,MAAM,OAQpB;EAAA,IARqB;IACxCC,IAAI;IACJC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC;EACgB,CAAC;EACjB,MAAMC,MAAM,GAAGb,iBAAiB,EAAE;EAElC,IAAIc,aAAa;;EAEjB;EACA;EACA,IAAIL,OAAO,CAACM,eAAe,KAAKC,SAAS,EAAE;IACzCF,aAAa,GAAGL,OAAO,CAACM,eAAe;EACzC,CAAC,MAAM,IAAIT,IAAI,EAAE;IACfQ,aAAa,GAAGR,IAAI,CAACW,KAAK;EAC5B;;EAEA;EACA,MAAMC,MAAM,GAAGnB,KAAK,CAACoB,WAAW,CAC9BlB,QAAQ,CAAC,MAAM;IACb,IAAIU,UAAU,CAACS,SAAS,EAAE,IAAIT,UAAU,CAACU,SAAS,EAAE,EAAE;MACpDV,UAAU,CAACW,QAAQ,CAAC;QAClB,GAAGxB,YAAY,CAACyB,GAAG,EAAE;QACrBC,MAAM,EAAEd,KAAK,CAACe;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC,EACN,CAACd,UAAU,EAAED,KAAK,CAACe,GAAG,CAAC,CACxB;EAED,MAAMC,OAAO,GAAG3B,KAAK,CAAC4B,UAAU,CAACzB,wBAAwB,CAAC;EAC1D,MAAM0B,mBAAmB,GAAG7B,KAAK,CAAC4B,UAAU,CAAC9B,kBAAkB,CAAC;EAEhE,MAAMgC,eAAe,GACnBpB,OAAO,CAACqB,qBAAqB,KAAKd,SAAS,GACvCP,OAAO,CAACqB,qBAAqB,GAC7BJ,OAAO,IAAIE,mBAAmB,GAC9B,CAAC,GACDf,MAAM,CAACkB,GAAG;EAEhB,oBACE,oBAAC,aAAa,eACRtB,OAAO;IACX,KAAK,EAAEb,cAAc,CAACa,OAAO,EAAEC,KAAK,CAACsB,IAAI,CAAE;IAC3C,QAAQ,EAAExB,QAAS;IACnB,MAAM,EAAED,MAAO;IACf,KAAK,EAAEmB,OAAQ;IACf,eAAe,EACbjB,OAAO,CAACM,eAAe,KAAKC,SAAS,GACjCP,OAAO,CAACM,eAAe,GACvBD,aACL;IACD,qBAAqB,EAAEe,eAAgB;IACvC,QAAQ,EAAEvB,IAAI,GAAGY,MAAM,GAAGF,SAAU;IACpC,iBAAiB,EAAEJ;EAAkB,GACrC;AAEN,CAAC,CAAC"}