/**
 * @license React
 * scheduler-unstable_mock.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(d,v){"object"===typeof exports&&"undefined"!==typeof module?v(exports):"function"===typeof define&&define.amd?define(["exports"],v):(d="undefined"!==typeof globalThis?globalThis:d||self,v(d.SchedulerMock={}))})(this,function(d){function v(a,b){var c=a.length;a.push(b);a:for(;0<c;){var e=c-1>>>1,f=a[e];if(0<F(f,b))a[e]=b,a[c]=f,c=e;else break a}}function p(a){return 0===a.length?null:a[0]}function G(a){if(0===a.length)return null;var b=a[0],c=a.pop();if(c!==b){a[0]=c;a:for(var e=
0,f=a.length,B=f>>>1;e<B;){var w=2*(e+1)-1,C=a[w],x=w+1,H=a[x];if(0>F(C,c))x<f&&0>F(H,C)?(a[e]=H,a[x]=c,e=x):(a[e]=C,a[w]=c,e=w);else if(x<f&&0>F(H,c))a[e]=H,a[x]=c,e=x;else break a}}return b}function F(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}function I(a){for(var b=p(t);null!==b;){if(null===b.callback)G(t);else if(b.startTime<=a)G(t),b.sortIndex=b.expirationTime,v(q,b);else break;b=p(t)}}function L(a){D=!1;I(a);if(!y)if(null!==p(q))y=!0,g=M;else{var b=p(t);null!==b&&(a=b.startTime-
a,r=L,u=k+a)}}function M(a,b){y=!1;D&&(D=!1,r=null,u=-1);J=!0;var c=h;try{a:{I(b);for(m=p(q);null!==m&&(!(m.expirationTime>b)||a&&!O());){var e=m.callback;if("function"===typeof e){m.callback=null;h=m.priorityLevel;var f=e(m.expirationTime<=b);b=k;if("function"===typeof f){if(m.callback=f,I(b),K){var B=E=!0;break a}}else m===p(q)&&G(q),I(b)}else G(q);m=p(q)}if(null!==m)B=!0;else{var w=p(t);if(null!==w){var C=w.startTime-b;r=L;u=k+C}B=!1}}return B}finally{m=null,h=c,J=!1}}function O(){return 0===z&&
null===n||-1!==z&&null!==n&&n.length>=z||K&&E?A=!0:!1}function P(){if(l)throw Error("Already flushing work.");if(null!==g){var a=g;l=!0;try{var b=!0;do b=a(!0,k);while(b);b||(g=null);return!0}finally{l=!1}}else return!1}var q=[],t=[],Q=1,m=null,h=3,J=!1,y=!1,D=!1,k=0,g=null,r=null,u=-1,n=null,z=-1,A=!1,l=!1,E=!1,K=!1,N=!1;d.log=function(a){"disabledLog"===console.log.name||N||(null===n?n=[a]:n.push(a))};d.reset=function(){if(l)throw Error("Cannot reset while already flushing work.");k=0;r=g=null;
u=-1;n=null;z=-1;E=l=A=!1};d.unstable_IdlePriority=5;d.unstable_ImmediatePriority=1;d.unstable_LowPriority=4;d.unstable_NormalPriority=3;d.unstable_Profiling=null;d.unstable_UserBlockingPriority=2;d.unstable_advanceTime=function(a){"disabledLog"===console.log.name||N||(k+=a,null!==r&&u<=k&&(r(k),u=-1,r=null))};d.unstable_cancelCallback=function(a){a.callback=null};d.unstable_clearLog=function(){if(null===n)return[];var a=n;n=null;return a};d.unstable_continueExecution=function(){y||J||(y=!0,g=M)};
d.unstable_flushAll=function(){if(null!==n)throw Error("Log is not empty. Assert on the log of yielded values before flushing additional work.");P();if(null!==n)throw Error("While flushing work, something yielded a value. Use an assertion helper to assert on the log of yielded values, e.g. expect(Scheduler).toFlushAndYield([...])");};d.unstable_flushAllWithoutAsserting=P;d.unstable_flushExpired=function(){if(l)throw Error("Already flushing work.");if(null!==g){l=!0;try{g(!1,k)||(g=null)}finally{l=
!1}}};d.unstable_flushNumberOfYields=function(a){if(l)throw Error("Already flushing work.");if(null!==g){var b=g;z=a;l=!0;try{a=!0;do a=b(!0,k);while(a&&!A);a||(g=null)}finally{z=-1,l=A=!1}}};d.unstable_flushUntilNextPaint=function(){if(l)throw Error("Already flushing work.");if(null!==g){var a=g;K=!0;E=!1;l=!0;try{var b=!0;do b=a(!0,k);while(b&&!A);b||(g=null)}finally{l=A=K=!1}}return!1};d.unstable_forceFrameRate=function(){};d.unstable_getCurrentPriorityLevel=function(){return h};d.unstable_getFirstCallbackNode=
function(){return p(q)};d.unstable_hasPendingWork=function(){return null!==g};d.unstable_next=function(a){switch(h){case 1:case 2:case 3:var b=3;break;default:b=h}var c=h;h=b;try{return a()}finally{h=c}};d.unstable_now=function(){return k};d.unstable_pauseExecution=function(){};d.unstable_requestPaint=function(){E=!0};d.unstable_runWithPriority=function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var c=h;h=a;try{return b()}finally{h=c}};d.unstable_scheduleCallback=function(a,
b,c){var e=k;"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?e+c:e):c=e;switch(a){case 1:var f=-1;break;case 2:f=250;break;case 5:f=1073741823;break;case 4:f=1E4;break;default:f=5E3}f=c+f;a={id:Q++,callback:b,priorityLevel:a,startTime:c,expirationTime:f,sortIndex:-1};c>e?(a.sortIndex=c,v(t,a),null===p(q)&&a===p(t)&&(D?(r=null,u=-1):D=!0,r=L,u=k+(c-e))):(a.sortIndex=f,v(q,a),y||J||(y=!0,g=M));return a};d.unstable_setDisableYieldValue=function(a){N=a};d.unstable_shouldYield=O;d.unstable_wrapCallback=
function(a){var b=h;return function(){var c=h;h=b;try{return a.apply(this,arguments)}finally{h=c}}}});
})();
