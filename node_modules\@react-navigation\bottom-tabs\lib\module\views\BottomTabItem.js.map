{"version": 3, "names": ["Link", "useTheme", "Color", "React", "Platform", "Pressable", "StyleSheet", "Text", "TabBarIcon", "BottomTabBarItem", "focused", "route", "descriptor", "label", "icon", "badge", "badgeStyle", "to", "button", "children", "style", "onPress", "accessibilityRole", "rest", "OS", "styles", "e", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "preventDefault", "accessibilityLabel", "testID", "onLongPress", "horizontal", "activeTintColor", "customActiveTintColor", "inactiveTintColor", "customInactiveTintColor", "activeBackgroundColor", "inactiveBackgroundColor", "showLabel", "allowFontScaling", "labelStyle", "iconStyle", "colors", "undefined", "primary", "text", "mix", "card", "hex", "renderLabel", "color", "labelBeside", "labelBeneath", "options", "tabBarLabel", "title", "name", "position", "renderIcon", "activeOpacity", "inactiveOpacity", "scene", "backgroundColor", "select", "ios", "default", "accessibilityState", "selected", "accessibilityStates", "tab", "tabLandscape", "tabPortrait", "create", "flex", "alignItems", "justifyContent", "flexDirection", "textAlign", "fontSize", "marginLeft", "marginTop", "display"], "sourceRoot": "../../../src", "sources": ["views/BottomTabItem.tsx"], "mappings": ";AAAA,SAASA,IAAI,EAASC,QAAQ,QAAQ,0BAA0B;AAChE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAEEC,QAAQ,EACRC,SAAS,EAETC,UAAU,EACVC,IAAI,QAGC,cAAc;AAOrB,OAAOC,UAAU,MAAM,cAAc;AA+GrC,eAAe,SAASC,gBAAgB,OAiE9B;EAAA,IAjE+B;IACvCC,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,UAAU;IACVC,EAAE;IACFC,MAAM,GAAG,SAOsB;MAAA,IAPrB;QACRC,QAAQ;QACRC,KAAK;QACLC,OAAO;QACPJ,EAAE;QACFK,iBAAiB;QACjB,GAAGC;MACoB,CAAC;MACxB,IAAInB,QAAQ,CAACoB,EAAE,KAAK,KAAK,IAAIP,EAAE,EAAE;QAC/B;QACA;QACA,oBACE,oBAAC,IAAI,eACCM,IAAI;UACR,EAAE,EAAEN,EAAG;UACP,KAAK,EAAE,CAACQ,MAAM,CAACP,MAAM,EAAEE,KAAK,CAAE;UAC9B,OAAO,EAAGM,CAAM,IAAK;YACnB,IACE,EAAEA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ,CAAC;YAAI;YACtDJ,CAAC,CAACR,MAAM,IAAI,IAAI,IAAIQ,CAAC,CAACR,MAAM,KAAK,CAAC,CAAC,CAAC;YAAA,EACrC;cACAQ,CAAC,CAACK,cAAc,EAAE;cAClBV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGK,CAAC,CAAC;YACd;UACF;QAAE,IAEDP,QAAQ,CACJ;MAEX,CAAC,MAAM;QACL,oBACE,oBAAC,SAAS,eACJI,IAAI;UACR,iBAAiB,EAAED,iBAAkB;UACrC,OAAO,EAAED,OAAQ;UACjB,KAAK,EAAED;QAAM,IAEZD,QAAQ,CACC;MAEhB;IACF,CAAC;IACDa,kBAAkB;IAClBC,MAAM;IACNZ,OAAO;IACPa,WAAW;IACXC,UAAU;IACVC,eAAe,EAAEC,qBAAqB;IACtCC,iBAAiB,EAAEC,uBAAuB;IAC1CC,qBAAqB,GAAG,aAAa;IACrCC,uBAAuB,GAAG,aAAa;IACvCC,SAAS,GAAG,IAAI;IAChBC,gBAAgB;IAChBC,UAAU;IACVC,SAAS;IACTzB;EACK,CAAC;EACN,MAAM;IAAE0B;EAAO,CAAC,GAAG7C,QAAQ,EAAE;EAE7B,MAAMmC,eAAe,GACnBC,qBAAqB,KAAKU,SAAS,GAC/BD,MAAM,CAACE,OAAO,GACdX,qBAAqB;EAE3B,MAAMC,iBAAiB,GACrBC,uBAAuB,KAAKQ,SAAS,GACjC7C,KAAK,CAAC4C,MAAM,CAACG,IAAI,CAAC,CAACC,GAAG,CAAChD,KAAK,CAAC4C,MAAM,CAACK,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,GAAG,EAAE,GACrDb,uBAAuB;EAE7B,MAAMc,WAAW,GAAG,SAAuC;IAAA,IAAtC;MAAE3C;IAA8B,CAAC;IACpD,IAAIgC,SAAS,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI;IACb;IAEA,MAAMY,KAAK,GAAG5C,OAAO,GAAG0B,eAAe,GAAGE,iBAAiB;IAE3D,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;MAC7B,oBACE,oBAAC,IAAI;QACH,aAAa,EAAE,CAAE;QACjB,KAAK,EAAE,CACLY,MAAM,CAACZ,KAAK,EACZ;UAAEyC;QAAM,CAAC,EACTnB,UAAU,GAAGV,MAAM,CAAC8B,WAAW,GAAG9B,MAAM,CAAC+B,YAAY,EACrDZ,UAAU,CACV;QACF,gBAAgB,EAAED;MAAiB,GAElC9B,KAAK,CACD;IAEX;IAEA,MAAM;MAAE4C;IAAQ,CAAC,GAAG7C,UAAU;IAC9B,MAAMO,QAAQ,GACZ,OAAOsC,OAAO,CAACC,WAAW,KAAK,QAAQ,GACnCD,OAAO,CAACC,WAAW,GACnBD,OAAO,CAACE,KAAK,KAAKZ,SAAS,GAC3BU,OAAO,CAACE,KAAK,GACbhD,KAAK,CAACiD,IAAI;IAEhB,OAAO/C,KAAK,CAAC;MACXH,OAAO;MACP4C,KAAK;MACLO,QAAQ,EAAE1B,UAAU,GAAG,aAAa,GAAG,YAAY;MACnDhB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2C,UAAU,GAAG,SAAuC;IAAA,IAAtC;MAAEpD;IAA8B,CAAC;IACnD,IAAII,IAAI,KAAKiC,SAAS,EAAE;MACtB,OAAO,IAAI;IACb;IAEA,MAAMgB,aAAa,GAAGrD,OAAO,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMsD,eAAe,GAAGtD,OAAO,GAAG,CAAC,GAAG,CAAC;IAEvC,oBACE,oBAAC,UAAU;MACT,KAAK,EAAEC,KAAM;MACb,UAAU,EAAEwB,UAAW;MACvB,KAAK,EAAEpB,KAAM;MACb,UAAU,EAAEC,UAAW;MACvB,aAAa,EAAE+C,aAAc;MAC7B,eAAe,EAAEC,eAAgB;MACjC,eAAe,EAAE5B,eAAgB;MACjC,iBAAiB,EAAEE,iBAAkB;MACrC,UAAU,EAAExB,IAAK;MACjB,KAAK,EAAE+B;IAAU,EACjB;EAEN,CAAC;EAED,MAAMoB,KAAK,GAAG;IAAEtD,KAAK;IAAED;EAAQ,CAAC;EAEhC,MAAMwD,eAAe,GAAGxD,OAAO,GAC3B8B,qBAAqB,GACrBC,uBAAuB;EAE3B,OAAOvB,MAAM,CAAC;IACZD,EAAE;IACFI,OAAO;IACPa,WAAW;IACXD,MAAM;IACND,kBAAkB;IAClB;IACAV,iBAAiB,EAAElB,QAAQ,CAAC+D,MAAM,CAAC;MAAEC,GAAG,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEC,kBAAkB,EAAE;MAAEC,QAAQ,EAAE7D;IAAQ,CAAC;IACzC;IACA8D,mBAAmB,EAAE9D,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE;IAChDU,KAAK,EAAE,CACLK,MAAM,CAACgD,GAAG,EACV;MAAEP;IAAgB,CAAC,EACnB/B,UAAU,GAAGV,MAAM,CAACiD,YAAY,GAAGjD,MAAM,CAACkD,WAAW,EACrDvD,KAAK,CACN;IACDD,QAAQ,eACN,oBAAC,KAAK,CAAC,QAAQ,QACZ2C,UAAU,CAACG,KAAK,CAAC,EACjBZ,WAAW,CAACY,KAAK,CAAC;EAGzB,CAAC,CAAC;AACJ;AAEA,MAAMxC,MAAM,GAAGnB,UAAU,CAACsE,MAAM,CAAC;EAC/BH,GAAG,EAAE;IACHI,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC;EACDH,WAAW,EAAE;IACXI,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;EACjB,CAAC;EACDN,YAAY,EAAE;IACZK,cAAc,EAAE,QAAQ;IACxBC,aAAa,EAAE;EACjB,CAAC;EACDnE,KAAK,EAAE;IACLoE,SAAS,EAAE,QAAQ;IACnBf,eAAe,EAAE;EACnB,CAAC;EACDV,YAAY,EAAE;IACZ0B,QAAQ,EAAE;EACZ,CAAC;EACD3B,WAAW,EAAE;IACX2B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACb,CAAC;EACDlE,MAAM,EAAE;IACNmE,OAAO,EAAE;EACX;AACF,CAAC,CAAC"}