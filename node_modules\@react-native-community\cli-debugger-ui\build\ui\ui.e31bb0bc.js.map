{"version": 3, "sources": ["../../../../node_modules/regenerator-runtime/runtime.js", "../../../../node_modules/@babel/runtime/regenerator/index.js", "../../../../node_modules/@babel/runtime/helpers/asyncToGenerator.js", "index.js"], "names": ["isMacLike", "test", "navigator", "platform", "refreshShortcut", "window", "onload", "document", "getElementById", "innerHTML", "Page", "render", "onReloadClicked", "xhr", "XMLHttpRequest", "open", "location", "origin", "send", "state", "isDark", "localStorage", "getItem", "matchMedia", "matches", "isPriorityMaintained", "status", "type", "visibilityState", "setState", "partialState", "Object", "assign", "statusNode", "textContent", "error", "reason", "linkNode", "querySelector", "href", "grayIcon", "blueIcon", "orangeIcon", "darkCheckbox", "body", "classList", "toggle", "checked", "setItem", "maintainPriorityCheckbox", "silence", "volume", "play", "pause", "toggleDarkTheme", "togglePriorityMaintenance", "connectToDebuggerProxy", "ws", "worker", "WebSocket", "host", "shutdownJSRuntime", "terminate", "onbeforeunload", "updateVisibility", "postMessage", "method", "onopen", "onmessage", "message", "data", "object", "JSON", "parse", "$event", "console", "clear", "Worker", "stringify", "replyID", "id", "onclose", "warn", "setTimeout", "addEventListener"], "mappings": ";;AAyvBA,IAAA,EAlvBA,EAAA,SAAA,GACA,aAEA,IAGA,EAHA,EAAA,OAAA,UACA,EAAA,EAAA,eACA,EAAA,OAAA,gBAAA,SAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,OAEA,EAAA,mBAAA,OAAA,OAAA,GACA,EAAA,EAAA,UAAA,aACA,EAAA,EAAA,eAAA,kBACA,EAAA,EAAA,aAAA,gBAEA,SAAA,EAAA,EAAA,EAAA,GAOA,OANA,OAAA,eAAA,EAAA,EAAA,CACA,MAAA,EACA,YAAA,EACA,cAAA,EACA,UAAA,IAEA,EAAA,GAEA,IAEA,EAAA,GAAA,IACA,MAAA,GACA,EAAA,SAAA,EAAA,EAAA,GACA,OAAA,EAAA,GAAA,GAIA,SAAA,EAAA,EAAA,EAAA,EAAA,GAEA,IAAA,EAAA,GAAA,EAAA,qBAAA,EAAA,EAAA,EACA,EAAA,OAAA,OAAA,EAAA,WACA,EAAA,IAAA,EAAA,GAAA,IAMA,OAFA,EAAA,EAAA,UAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAEA,EAcA,SAAA,EAAA,EAAA,EAAA,GACA,IACA,MAAA,CAAA,KAAA,SAAA,IAAA,EAAA,KAAA,EAAA,IACA,MAAA,GACA,MAAA,CAAA,KAAA,QAAA,IAAA,IAhBA,EAAA,KAAA,EAoBA,IAAA,EAAA,iBACA,EAAA,iBACA,EAAA,YACA,EAAA,YAIA,EAAA,GAMA,SAAA,KACA,SAAA,KACA,SAAA,KAIA,IAAA,EAAA,GACA,EAAA,EAAA,EAAA,WACA,OAAA,OAGA,IAAA,EAAA,OAAA,eACA,EAAA,GAAA,EAAA,EAAA,EAAA,MACA,GACA,IAAA,GACA,EAAA,KAAA,EAAA,KAGA,EAAA,GAGA,IAAA,EAAA,EAAA,UACA,EAAA,UAAA,OAAA,OAAA,GAgBA,SAAA,EAAA,GACA,CAAA,OAAA,QAAA,UAAA,QAAA,SAAA,GACA,EAAA,EAAA,EAAA,SAAA,GACA,OAAA,KAAA,QAAA,EAAA,OAkCA,SAAA,EAAA,EAAA,GAgCA,IAAA,EAgCA,EAAA,KAAA,UAAA,CAAA,MA9BA,SAAA,EAAA,GACA,SAAA,IACA,OAAA,IAAA,EAAA,SAAA,EAAA,IAnCA,SAAA,EAAA,EAAA,EAAA,EAAA,GACA,IAAA,EAAA,EAAA,EAAA,GAAA,EAAA,GACA,GAAA,UAAA,EAAA,KAEA,CACA,IAAA,EAAA,EAAA,IACA,EAAA,EAAA,MACA,OAAA,GACA,iBAAA,GACA,EAAA,KAAA,EAAA,WACA,EAAA,QAAA,EAAA,SAAA,KAAA,SAAA,GACA,EAAA,OAAA,EAAA,EAAA,IACA,SAAA,GACA,EAAA,QAAA,EAAA,EAAA,KAIA,EAAA,QAAA,GAAA,KAAA,SAAA,GAIA,EAAA,MAAA,EACA,EAAA,IACA,SAAA,GAGA,OAAA,EAAA,QAAA,EAAA,EAAA,KAvBA,EAAA,EAAA,KAiCA,CAAA,EAAA,EAAA,EAAA,KAIA,OAAA,EAaA,EAAA,EAAA,KACA,EAGA,GACA,OAgCA,SAAA,EAAA,EAAA,EAAA,GACA,IAAA,EAAA,EAEA,OAAA,SAAA,EAAA,GACA,GAAA,IAAA,EACA,MAAA,IAAA,MAAA,gCAGA,GAAA,IAAA,EAAA,CACA,GAAA,UAAA,EACA,MAAA,EAKA,OAAA,IAMA,IAHA,EAAA,OAAA,EACA,EAAA,IAAA,IAEA,CACA,IAAA,EAAA,EAAA,SACA,GAAA,EAAA,CACA,IAAA,EAAA,EAAA,EAAA,GACA,GAAA,EAAA,CACA,GAAA,IAAA,EAAA,SACA,OAAA,GAIA,GAAA,SAAA,EAAA,OAGA,EAAA,KAAA,EAAA,MAAA,EAAA,SAEA,GAAA,UAAA,EAAA,OAAA,CACA,GAAA,IAAA,EAEA,MADA,EAAA,EACA,EAAA,IAGA,EAAA,kBAAA,EAAA,SAEA,WAAA,EAAA,QACA,EAAA,OAAA,SAAA,EAAA,KAGA,EAAA,EAEA,IAAA,EAAA,EAAA,EAAA,EAAA,GACA,GAAA,WAAA,EAAA,KAAA,CAOA,GAJA,EAAA,EAAA,KACA,EACA,EAEA,EAAA,MAAA,EACA,SAGA,MAAA,CACA,MAAA,EAAA,IACA,KAAA,EAAA,MAGA,UAAA,EAAA,OACA,EAAA,EAGA,EAAA,OAAA,QACA,EAAA,IAAA,EAAA,OAUA,SAAA,EAAA,EAAA,GACA,IAAA,EAAA,EAAA,OACA,EAAA,EAAA,SAAA,GACA,GAAA,IAAA,EAOA,OAHA,EAAA,SAAA,KAGA,UAAA,GAAA,EAAA,SAAA,SAGA,EAAA,OAAA,SACA,EAAA,IAAA,EACA,EAAA,EAAA,GAEA,UAAA,EAAA,QAGA,GAGA,WAAA,IACA,EAAA,OAAA,QACA,EAAA,IAAA,IAAA,UACA,oCAAA,EAAA,aAGA,GAGA,IAAA,EAAA,EAAA,EAAA,EAAA,SAAA,EAAA,KAEA,GAAA,UAAA,EAAA,KAIA,OAHA,EAAA,OAAA,QACA,EAAA,IAAA,EAAA,IACA,EAAA,SAAA,KACA,EAGA,IAAA,EAAA,EAAA,IAEA,OAAA,EAOA,EAAA,MAGA,EAAA,EAAA,YAAA,EAAA,MAGA,EAAA,KAAA,EAAA,QAQA,WAAA,EAAA,SACA,EAAA,OAAA,OACA,EAAA,IAAA,GAUA,EAAA,SAAA,KACA,GANA,GA3BA,EAAA,OAAA,QACA,EAAA,IAAA,IAAA,UAAA,oCACA,EAAA,SAAA,KACA,GAoDA,SAAA,EAAA,GACA,IAAA,EAAA,CAAA,OAAA,EAAA,IAEA,KAAA,IACA,EAAA,SAAA,EAAA,IAGA,KAAA,IACA,EAAA,WAAA,EAAA,GACA,EAAA,SAAA,EAAA,IAGA,KAAA,WAAA,KAAA,GAGA,SAAA,EAAA,GACA,IAAA,EAAA,EAAA,YAAA,GACA,EAAA,KAAA,gBACA,EAAA,IACA,EAAA,WAAA,EAGA,SAAA,EAAA,GAIA,KAAA,WAAA,CAAA,CAAA,OAAA,SACA,EAAA,QAAA,EAAA,MACA,KAAA,OAAA,GA+BA,SAAA,EAAA,GACA,GAAA,EAAA,CACA,IAAA,EAAA,EAAA,GACA,GAAA,EACA,OAAA,EAAA,KAAA,GAGA,GAAA,mBAAA,EAAA,KACA,OAAA,EAGA,IAAA,MAAA,EAAA,QAAA,CACA,IAAA,GAAA,EAAA,EAAA,SAAA,IACA,OAAA,EAAA,EAAA,QACA,GAAA,EAAA,KAAA,EAAA,GAGA,OAFA,EAAA,MAAA,EAAA,GACA,EAAA,MAAA,EACA,EAOA,OAHA,EAAA,MAAA,EACA,EAAA,MAAA,EAEA,GAGA,OAAA,EAAA,KAAA,GAKA,MAAA,CAAA,KAAA,GAIA,SAAA,IACA,MAAA,CAAA,MAAA,EAAA,MAAA,GA+MA,OAnnBA,EAAA,UAAA,EACA,EAAA,EAAA,cAAA,CAAA,MAAA,EAAA,cAAA,IACA,EACA,EACA,cACA,CAAA,MAAA,EAAA,cAAA,IAEA,EAAA,YAAA,EACA,EACA,EACA,qBAaA,EAAA,oBAAA,SAAA,GACA,IAAA,EAAA,mBAAA,GAAA,EAAA,YACA,QAAA,IACA,IAAA,GAGA,uBAAA,EAAA,aAAA,EAAA,QAIA,EAAA,KAAA,SAAA,GAQA,OAPA,OAAA,eACA,OAAA,eAAA,EAAA,IAEA,EAAA,UAAA,EACA,EAAA,EAAA,EAAA,sBAEA,EAAA,UAAA,OAAA,OAAA,GACA,GAOA,EAAA,MAAA,SAAA,GACA,MAAA,CAAA,QAAA,IAsEA,EAAA,EAAA,WACA,EAAA,EAAA,UAAA,EAAA,WACA,OAAA,OAEA,EAAA,cAAA,EAKA,EAAA,MAAA,SAAA,EAAA,EAAA,EAAA,EAAA,QACA,IAAA,IAAA,EAAA,SAEA,IAAA,EAAA,IAAA,EACA,EAAA,EAAA,EAAA,EAAA,GACA,GAGA,OAAA,EAAA,oBAAA,GACA,EACA,EAAA,OAAA,KAAA,SAAA,GACA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,UAwKA,EAAA,GAEA,EAAA,EAAA,EAAA,aAOA,EAAA,EAAA,EAAA,WACA,OAAA,OAGA,EAAA,EAAA,WAAA,WACA,MAAA,uBAkCA,EAAA,KAAA,SAAA,GACA,IAAA,EAAA,OAAA,GACA,EAAA,GACA,IAAA,IAAA,KAAA,EACA,EAAA,KAAA,GAMA,OAJA,EAAA,UAIA,SAAA,IACA,KAAA,EAAA,QAAA,CACA,IAAA,EAAA,EAAA,MACA,GAAA,KAAA,EAGA,OAFA,EAAA,MAAA,EACA,EAAA,MAAA,EACA,EAQA,OADA,EAAA,MAAA,EACA,IAsCA,EAAA,OAAA,EAMA,EAAA,UAAA,CACA,YAAA,EAEA,MAAA,SAAA,GAcA,GAbA,KAAA,KAAA,EACA,KAAA,KAAA,EAGA,KAAA,KAAA,KAAA,MAAA,EACA,KAAA,MAAA,EACA,KAAA,SAAA,KAEA,KAAA,OAAA,OACA,KAAA,IAAA,EAEA,KAAA,WAAA,QAAA,IAEA,EACA,IAAA,IAAA,KAAA,KAEA,MAAA,EAAA,OAAA,IACA,EAAA,KAAA,KAAA,KACA,OAAA,EAAA,MAAA,MACA,KAAA,GAAA,IAMA,KAAA,WACA,KAAA,MAAA,EAEA,IACA,EADA,KAAA,WAAA,GACA,WACA,GAAA,UAAA,EAAA,KACA,MAAA,EAAA,IAGA,OAAA,KAAA,MAGA,kBAAA,SAAA,GACA,GAAA,KAAA,KACA,MAAA,EAGA,IAAA,EAAA,KACA,SAAA,EAAA,EAAA,GAYA,OAXA,EAAA,KAAA,QACA,EAAA,IAAA,EACA,EAAA,KAAA,EAEA,IAGA,EAAA,OAAA,OACA,EAAA,IAAA,KAGA,EAGA,IAAA,IAAA,EAAA,KAAA,WAAA,OAAA,EAAA,GAAA,IAAA,EAAA,CACA,IAAA,EAAA,KAAA,WAAA,GACA,EAAA,EAAA,WAEA,GAAA,SAAA,EAAA,OAIA,OAAA,EAAA,OAGA,GAAA,EAAA,QAAA,KAAA,KAAA,CACA,IAAA,EAAA,EAAA,KAAA,EAAA,YACA,EAAA,EAAA,KAAA,EAAA,cAEA,GAAA,GAAA,EAAA,CACA,GAAA,KAAA,KAAA,EAAA,SACA,OAAA,EAAA,EAAA,UAAA,GACA,GAAA,KAAA,KAAA,EAAA,WACA,OAAA,EAAA,EAAA,iBAGA,GAAA,GACA,GAAA,KAAA,KAAA,EAAA,SACA,OAAA,EAAA,EAAA,UAAA,OAGA,CAAA,IAAA,EAMA,MAAA,IAAA,MAAA,0CALA,GAAA,KAAA,KAAA,EAAA,WACA,OAAA,EAAA,EAAA,gBAUA,OAAA,SAAA,EAAA,GACA,IAAA,IAAA,EAAA,KAAA,WAAA,OAAA,EAAA,GAAA,IAAA,EAAA,CACA,IAAA,EAAA,KAAA,WAAA,GACA,GAAA,EAAA,QAAA,KAAA,MACA,EAAA,KAAA,EAAA,eACA,KAAA,KAAA,EAAA,WAAA,CACA,IAAA,EAAA,EACA,OAIA,IACA,UAAA,GACA,aAAA,IACA,EAAA,QAAA,GACA,GAAA,EAAA,aAGA,EAAA,MAGA,IAAA,EAAA,EAAA,EAAA,WAAA,GAIA,OAHA,EAAA,KAAA,EACA,EAAA,IAAA,EAEA,GACA,KAAA,OAAA,OACA,KAAA,KAAA,EAAA,WACA,GAGA,KAAA,SAAA,IAGA,SAAA,SAAA,EAAA,GACA,GAAA,UAAA,EAAA,KACA,MAAA,EAAA,IAcA,MAXA,UAAA,EAAA,MACA,aAAA,EAAA,KACA,KAAA,KAAA,EAAA,IACA,WAAA,EAAA,MACA,KAAA,KAAA,KAAA,IAAA,EAAA,IACA,KAAA,OAAA,SACA,KAAA,KAAA,OACA,WAAA,EAAA,MAAA,IACA,KAAA,KAAA,GAGA,GAGA,OAAA,SAAA,GACA,IAAA,IAAA,EAAA,KAAA,WAAA,OAAA,EAAA,GAAA,IAAA,EAAA,CACA,IAAA,EAAA,KAAA,WAAA,GACA,GAAA,EAAA,aAAA,EAGA,OAFA,KAAA,SAAA,EAAA,WAAA,EAAA,UACA,EAAA,GACA,IAKA,MAAA,SAAA,GACA,IAAA,IAAA,EAAA,KAAA,WAAA,OAAA,EAAA,GAAA,IAAA,EAAA,CACA,IAAA,EAAA,KAAA,WAAA,GACA,GAAA,EAAA,SAAA,EAAA,CACA,IAAA,EAAA,EAAA,WACA,GAAA,UAAA,EAAA,KAAA,CACA,IAAA,EAAA,EAAA,IACA,EAAA,GAEA,OAAA,GAMA,MAAA,IAAA,MAAA,0BAGA,cAAA,SAAA,EAAA,EAAA,GAaA,OAZA,KAAA,SAAA,CACA,SAAA,EAAA,GACA,WAAA,EACA,QAAA,GAGA,SAAA,KAAA,SAGA,KAAA,IAAA,GAGA,IAQA,EArtBA,CA4tBA,iBAAA,OAAA,OAAA,QAAA,IAGA,IACA,mBAAA,EACA,MAAA,GAWA,iBAAA,WACA,WAAA,mBAAA,EAEA,SAAA,IAAA,yBAAA,CAAA;;ACtvBA,OAAA,QAAA,QAAA;;ACAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GACA,IACA,IAAA,EAAA,EAAA,GAAA,GACA,EAAA,EAAA,MACA,MAAA,GAEA,YADA,EAAA,GAIA,EAAA,KACA,EAAA,GAEA,QAAA,QAAA,GAAA,KAAA,EAAA,GAIA,SAAA,EAAA,GACA,OAAA,WACA,IAAA,EAAA,KACA,EAAA,UACA,OAAA,IAAA,QAAA,SAAA,EAAA,GACA,IAAA,EAAA,EAAA,MAAA,EAAA,GAEA,SAAA,EAAA,GACA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,OAAA,GAGA,SAAA,EAAA,GACA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,QAAA,GAGA,OAAA,MAKA,OAAA,QAAA;;;;;;;;;;ACsKsB,aAAA,IAAA,EAAA,EAAA,QAAA,+BAAA,EAAA,EAAA,QAAA,4CAjMtB,QAAA,eACA,IAAA,EAAA,EAAA,QAAA,2BACA,EAAA,EAAA,QAAA,2BACA,EAAA,EAAA,QAAA,6BA8LsB,SAAA,EAAA,GAAA,OAAA,GAAA,EAAA,WAAA,EAAA,CAAA,QAAA,GA5LtB,IAAMA,EAAY,0BAA0BC,KAAKC,UAAUC,UACrDC,EAAkBJ,EAAY,KAAO,SAC3CK,OAAOC,OAAS,WACTN,IACHO,SAASC,eAAe,YAAYC,UAAY,UAElDC,EAAKC,UAGPN,OAAOO,gBAAkB,WACnBC,IAAAA,EAAM,IAAIC,eACdD,EAAIE,KAAK,MAAUV,GAAAA,OAAAA,OAAOW,SAASC,OAAiB,YAAA,GACpDJ,EAAIK,QAGN,IAAMR,EAAQL,OAAOK,KAAO,CAC1BS,MAAO,CACLC,OACwC,OAAtCC,aAAaC,QAAQ,aACjBjB,OAAOkB,WAAW,gCAAgCC,QACZ,OAAtCH,aAAaC,QAAQ,aAC3BG,qBAAmE,OAA7CJ,aAAaC,QAAQ,oBAC3CI,OAAQ,CAACC,KAAM,gBACfC,gBAAiBrB,SAASqB,iBAG5BC,SAASC,SAAAA,GACPpB,EAAKS,MAAQY,OAAOC,OAAO,GAAItB,EAAKS,MAAOW,GAC3CpB,EAAKC,UAGPA,OAAS,WACyDD,IAAAA,EAAAA,EAAKS,MAA9DC,EAAAA,EAAAA,OAAQK,EAAAA,EAAAA,qBAAsBC,EAAAA,EAAAA,OAAQE,EAAAA,EAAAA,gBAEvCK,EAAa1B,SAASC,eAAe,UACnCkB,OAAAA,EAAOC,MACR,IAAA,YACHM,EAAWC,YAAc,2BACzB,MACG,IAAA,QACHD,EAAWC,YACTR,EAAOS,MAAMC,QACb,4EACF,MACG,IAAA,aACA,IAAA,eAEL,QACEH,EAAWxB,UACT,yCACAL,EACA,8CAIAiC,IAAAA,EAAW9B,SAAS+B,cAAc,kBACpB,iBAAhBZ,EAAOC,MAA2C,UAAhBD,EAAOC,KAC3CU,EAASE,KAAOC,EAAhB,QAGEH,EAASE,KADa,YAApBX,GAAiCH,EACnBgB,EAAhB,QAEgBC,EAAhB,QAIEC,IAAAA,EAAepC,SAASC,eAAe,QAC7CD,SAASqC,KAAKC,UAAUC,OAAO,OAAQ1B,GACvCuB,EAAaI,QAAU3B,EACvBC,aAAa2B,QAAQ,YAAa5B,EAAS,KAAO,IAE5C6B,IAAAA,EAA2B1C,SAASC,eACxC,qBAEI0C,EAAU3C,SAASC,eAAe,WACxC0C,EAAQC,OAAS,GACb1B,EACFyB,EAAQE,OAERF,EAAQG,QAEVJ,EAAyBF,QAAUtB,EACnCJ,aAAa2B,QAAQ,mBAAoBvB,EAAuB,KAAO,KAGzE6B,gBAAkB,WAChB5C,EAAKmB,SAAS,CAACT,QAASV,EAAKS,MAAMC,UAGrCmC,0BAA4B,WAC1B7C,EAAKmB,SAAS,CAACJ,sBAAuBf,EAAKS,MAAMM,yBAIrD,SAAS+B,IACDC,IAKFC,EALED,EAAK,IAAIE,UACb,QACEtD,OAAOW,SAAS4C,KAChB,6CAuBKC,SAAAA,IACHH,IACFA,EAAOI,YACPJ,EAAS,KACTrD,OAAO0D,eAAiB,MAInBC,SAAAA,IACHN,IAAWhD,EAAKS,MAAMM,sBACxBiC,EAAOO,YAAY,CACjBC,OAAQ,wBACRtC,gBAAiBrB,SAASqB,kBAG9BlB,EAAKmB,SAAS,CAACD,gBAAiBrB,SAASqB,kBAG3C6B,EAAGU,OAAS,WACVzD,EAAKmB,SAAS,CAACH,OAAQ,CAACC,KAAM,iBAGhC8B,EAAGW,UAAH,WAAe,IAAA,GAAA,EAAA,EAAA,SAAA,EAAA,QAAA,KAAA,SAAgBC,EAAAA,GAAhB,IAAA,EAAA,OAAA,EAAA,QAAA,KAAA,SAAA,GAAA,OAAA,OAAA,EAAA,KAAA,EAAA,MAAA,KAAA,EACRA,GAAAA,EAAQC,KADA,CAAA,EAAA,KAAA,EAAA,MAAA,OAAA,EAAA,OAAA,UAAA,KAAA,EAMTC,GAAkB,yBAFhBA,EAASC,KAAKC,MAAMJ,EAAQC,OAEvBI,OANE,CAAA,EAAA,KAAA,EAAA,MAAA,OAOXb,IACAnD,EAAKmB,SAAS,CAACH,OAAQ,CAACC,KAAM,kBARnB,EAAA,OAAA,UAAA,KAAA,EAYR4C,GAAAA,EAAOL,OAZC,CAAA,EAAA,KAAA,EAAA,MAAA,OAAA,EAAA,OAAA,UAAA,KAAA,EAiBS,qBAAlBK,EAAOL,QACTL,IACAc,QAAQC,SAxDVlB,EAAS,IAAImB,OAAO,qDACbT,UAAY,SAAUC,GAC3BZ,EAAGvC,KAAKsD,KAAKM,UAAUT,EAAQC,QAEjCjE,OAAO0D,eAAiB,WAEpB,MAAA,8EAEA3D,EACA,6BAGJ4D,IA8CEP,EAAGvC,KAAKsD,KAAKM,UAAU,CAACC,QAASR,EAAOS,MACxCtE,EAAKmB,SAAS,CAACH,OAAQ,CAACC,KAAM,YAAaqD,GAAIT,EAAOS,OAC3B,kBAAlBT,EAAOL,QAChBL,IACAnD,EAAKmB,SAAS,CAACH,OAAQ,CAACC,KAAM,mBAE9B+B,EAAOO,YAAYM,GA3BR,KAAA,GAAA,IAAA,MAAA,OAAA,EAAA,SAAA,MAAf,OAAA,SAAA,GAAA,OAAA,EAAA,MAAA,KAAA,YAAA,GA+BAd,EAAGwB,QAAU,SAAU9C,GACrB0B,IACAnD,EAAKmB,SAAS,CAACH,OAAQ,CAACC,KAAM,QAASQ,MAAAA,KACnCA,EAAMC,QACRuC,QAAQO,KAAK/C,EAAMC,QAErB+C,WAAW3B,EAAwB,MAKrCjD,SAAS6E,iBAAiB,mBAAoBpB,GAAkB,GAGlER", "file": "ui.e31bb0bc.js", "sourceRoot": "../../src/ui", "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; };\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    defineProperty(generator, \"_invoke\", { value: makeInvokeMethod(innerFn, self, context) });\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  defineProperty(Gp, \"constructor\", { value: GeneratorFunctionPrototype, configurable: true });\n  defineProperty(\n    GeneratorFunctionPrototype,\n    \"constructor\",\n    { value: GeneratorFunction, configurable: true }\n  );\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    defineProperty(this, \"_invoke\", { value: enqueue });\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method;\n    var method = delegate.iterator[methodName];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method, or a missing .next mehtod, always terminate the\n      // yield* loop.\n      context.delegate = null;\n\n      // Note: [\"return\"] must be used for ES3 parsing compatibility.\n      if (methodName === \"throw\" && delegate.iterator[\"return\"]) {\n        // If the delegate iterator has a return method, give it a\n        // chance to clean up.\n        context.method = \"return\";\n        context.arg = undefined;\n        maybeInvokeDelegate(delegate, context);\n\n        if (context.method === \"throw\") {\n          // If maybeInvokeDelegate(context) changed context.method from\n          // \"return\" to \"throw\", let that override the TypeError below.\n          return ContinueSentinel;\n        }\n      }\n      if (methodName !== \"return\") {\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a '\" + methodName + \"' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(val) {\n    var object = Object(val);\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "module.exports = require(\"regenerator-runtime\");\n", "function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\n\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n        args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n\n      _next(undefined);\n    });\n  };\n}\n\nmodule.exports = _asyncToGenerator;", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/* eslint-env browser */\nimport './index.css';\nimport blueIcon from './assets/blue-icon.png';\nimport grayIcon from './assets/gray-icon.png';\nimport orangeIcon from './assets/orange-icon.png';\n\nconst isMacLike = /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);\nconst refreshShortcut = isMacLike ? '⌘R' : 'Ctrl R';\nwindow.onload = function () {\n  if (!isMacLike) {\n    document.getElementById('shortcut').innerHTML = 'Ctrl⇧J';\n  }\n  Page.render();\n};\n\nwindow.onReloadClicked = function () {\n  var xhr = new XMLHttpRequest();\n  xhr.open('GET', `${window.location.origin}/reload`, true);\n  xhr.send();\n};\n\nconst Page = (window.Page = {\n  state: {\n    isDark:\n      localStorage.getItem('darkTheme') === null\n        ? window.matchMedia('(prefers-color-scheme: dark)').matches\n        : localStorage.getItem('darkTheme') === 'on',\n    isPriorityMaintained: localStorage.getItem('maintainPriority') === 'on',\n    status: {type: 'disconnected'},\n    visibilityState: document.visibilityState,\n  },\n\n  setState(partialState) {\n    Page.state = Object.assign({}, Page.state, partialState);\n    Page.render();\n  },\n\n  render() {\n    const {isDark, isPriorityMaintained, status, visibilityState} = Page.state;\n\n    const statusNode = document.getElementById('status');\n    switch (status.type) {\n      case 'connected':\n        statusNode.textContent = 'Debugger session active.';\n        break;\n      case 'error':\n        statusNode.textContent =\n          status.error.reason ||\n          'Disconnected from proxy. Attempting reconnection. Is node server running?';\n        break;\n      case 'connecting':\n      case 'disconnected':\n      // Fall through.\n      default:\n        statusNode.innerHTML =\n          'Waiting, press <span class=\"shortcut\">' +\n          refreshShortcut +\n          '</span> in simulator to reload and connect.';\n        break;\n    }\n\n    const linkNode = document.querySelector('link[rel=icon]');\n    if (status.type === 'disconnected' || status.type === 'error') {\n      linkNode.href = grayIcon;\n    } else {\n      if (visibilityState === 'visible' || isPriorityMaintained) {\n        linkNode.href = blueIcon;\n      } else {\n        linkNode.href = orangeIcon;\n      }\n    }\n\n    const darkCheckbox = document.getElementById('dark');\n    document.body.classList.toggle('dark', isDark);\n    darkCheckbox.checked = isDark;\n    localStorage.setItem('darkTheme', isDark ? 'on' : '');\n\n    const maintainPriorityCheckbox = document.getElementById(\n      'maintain-priority',\n    );\n    const silence = document.getElementById('silence');\n    silence.volume = 0.1;\n    if (isPriorityMaintained) {\n      silence.play();\n    } else {\n      silence.pause();\n    }\n    maintainPriorityCheckbox.checked = isPriorityMaintained;\n    localStorage.setItem('maintainPriority', isPriorityMaintained ? 'on' : '');\n  },\n\n  toggleDarkTheme() {\n    Page.setState({isDark: !Page.state.isDark});\n  },\n\n  togglePriorityMaintenance() {\n    Page.setState({isPriorityMaintained: !Page.state.isPriorityMaintained});\n  },\n});\n\nfunction connectToDebuggerProxy() {\n  const ws = new WebSocket(\n    'ws://' +\n      window.location.host +\n      '/debugger-proxy?role=debugger&name=Chrome',\n  );\n  let worker;\n\n  function createJSRuntime() {\n    // This worker will run the application JavaScript code,\n    // making sure that it's run in an environment without a global\n    // document, to make it consistent with the JSC executor environment.\n    worker = new Worker('./debuggerWorker.js');\n    worker.onmessage = function (message) {\n      ws.send(JSON.stringify(message.data));\n    };\n    window.onbeforeunload = function () {\n      return (\n        'If you reload this page, it is going to break the debugging session. ' +\n        'Press ' +\n        refreshShortcut +\n        ' on the device to reload.'\n      );\n    };\n    updateVisibility();\n  }\n\n  function shutdownJSRuntime() {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      window.onbeforeunload = null;\n    }\n  }\n\n  function updateVisibility() {\n    if (worker && !Page.state.isPriorityMaintained) {\n      worker.postMessage({\n        method: 'setDebuggerVisibility',\n        visibilityState: document.visibilityState,\n      });\n    }\n    Page.setState({visibilityState: document.visibilityState});\n  }\n\n  ws.onopen = function () {\n    Page.setState({status: {type: 'connecting'}});\n  };\n\n  ws.onmessage = async function (message) {\n    if (!message.data) {\n      return;\n    }\n    const object = JSON.parse(message.data);\n\n    if (object.$event === 'client-disconnected') {\n      shutdownJSRuntime();\n      Page.setState({status: {type: 'disconnected'}});\n      return;\n    }\n\n    if (!object.method) {\n      return;\n    }\n\n    // Special message that asks for a new JS runtime\n    if (object.method === 'prepareJSRuntime') {\n      shutdownJSRuntime();\n      console.clear();\n      createJSRuntime();\n      ws.send(JSON.stringify({replyID: object.id}));\n      Page.setState({status: {type: 'connected', id: object.id}});\n    } else if (object.method === '$disconnected') {\n      shutdownJSRuntime();\n      Page.setState({status: {type: 'disconnected'}});\n    } else {\n      worker.postMessage(object);\n    }\n  };\n\n  ws.onclose = function (error) {\n    shutdownJSRuntime();\n    Page.setState({status: {type: 'error', error}});\n    if (error.reason) {\n      console.warn(error.reason);\n    }\n    setTimeout(connectToDebuggerProxy, 500);\n  };\n\n  // Let debuggerWorker.js know when we're not visible so that we can warn about\n  // poor performance when using remote debugging.\n  document.addEventListener('visibilitychange', updateVisibility, false);\n}\n\nconnectToDebuggerProxy();\n"]}