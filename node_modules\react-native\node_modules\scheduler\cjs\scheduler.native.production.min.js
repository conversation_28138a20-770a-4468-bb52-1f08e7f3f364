/**
 * @license React
 * scheduler.native.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';function f(a,b){var c=a.length;a.push(b);a:for(;0<c;){var d=c-1>>>1,e=a[d];if(0<g(e,b))a[d]=b,a[c]=e,c=d;else break a}}function h(a){return 0===a.length?null:a[0]}function k(a){if(0===a.length)return null;var b=a[0],c=a.pop();if(c!==b){a[0]=c;a:for(var d=0,e=a.length,t=e>>>1;d<t;){var m=2*(d+1)-1,E=a[m],n=m+1,A=a[n];if(0>g(E,c))n<e&&0>g(A,E)?(a[d]=A,a[n]=c,d=n):(a[d]=E,a[m]=c,d=m);else if(n<e&&0>g(A,c))a[d]=A,a[n]=c,d=n;else break a}}return b}
function g(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}var l;if("object"===typeof performance&&"function"===typeof performance.now){var p=performance;l=function(){return p.now()}}else{var q=Date,r=q.now();l=function(){return q.now()-r}}var u=[],v=[],w=1,x=null,y=3,z=!1,B=!1,C=!1,D="function"===typeof setTimeout?setTimeout:null,F="function"===typeof clearTimeout?clearTimeout:null,G="undefined"!==typeof setImmediate?setImmediate:null;
"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending?navigator.scheduling.isInputPending.bind(navigator.scheduling):null;function H(a){for(var b=h(v);null!==b;){if(null===b.callback)k(v);else if(b.startTime<=a)k(v),b.sortIndex=b.expirationTime,f(u,b);else break;b=h(v)}}function I(a){C=!1;H(a);if(!B)if(null!==h(u))B=!0,J=K,L||(L=!0,M());else{var b=h(v);null!==b&&N(I,b.startTime-a)}}
function K(a,b){B=!1;C&&(C=!1,F(O),O=-1);z=!0;var c=y;try{a:{H(b);for(x=h(u);null!==x&&(!(x.expirationTime>b)||a&&!P());){var d=x.callback;if("function"===typeof d){x.callback=null;y=x.priorityLevel;var e=d(x.expirationTime<=b);b=l();if("function"===typeof e){x.callback=e;H(b);var t=!0;break a}else x===h(u)&&k(u),H(b)}else k(u);x=h(u)}if(null!==x)t=!0;else{var m=h(v);null!==m&&N(I,m.startTime-b);t=!1}}return t}finally{x=null,y=c,z=!1}}
function Q(a,b,c){var d=l();"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?d+c:d):c=d;switch(a){case 1:var e=-1;break;case 2:e=250;break;case 5:e=1073741823;break;case 4:e=1E4;break;default:e=5E3}e=c+e;a={id:w++,callback:b,priorityLevel:a,startTime:c,expirationTime:e,sortIndex:-1};c>d?(a.sortIndex=c,f(v,a),null===h(u)&&a===h(v)&&(C?(F(O),O=-1):C=!0,N(I,c-d))):(a.sortIndex=e,f(u,a),B||z||(B=!0,J=K,L||(L=!0,M())));return a}function R(a){a.callback=null}function S(){return y}
var L=!1,J=null,O=-1,T=-1;function P(){return 5>l()-T?!1:!0}function U(){}function V(){if(null!==J){var a=l();T=a;var b=!0;try{b=J(!0,a)}finally{b?M():(L=!1,J=null)}}else L=!1}var M;if("function"===typeof G)M=function(){G(V)};else if("undefined"!==typeof MessageChannel){var W=new MessageChannel,X=W.port2;W.port1.onmessage=V;M=function(){X.postMessage(null)}}else M=function(){D(V,0)};function N(a,b){O=D(function(){a(l())},b)}
var Y="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_UserBlockingPriority:2,aa="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_NormalPriority:3,ba="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_LowPriority:4,ca="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_ImmediatePriority:1,da="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_scheduleCallback:Q,ea="undefined"!==
typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_cancelCallback:R,fa="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_getCurrentPriorityLevel:S,ha="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_shouldYield:P,ia="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_requestPaint:U,ja="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_now:l;
function Z(){throw Error("Not implemented.");}exports.unstable_IdlePriority="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_IdlePriority:5;exports.unstable_ImmediatePriority=ca;exports.unstable_LowPriority=ba;exports.unstable_NormalPriority=aa;exports.unstable_Profiling=null;exports.unstable_UserBlockingPriority=Y;exports.unstable_cancelCallback=ea;exports.unstable_continueExecution=Z;exports.unstable_forceFrameRate=Z;exports.unstable_getCurrentPriorityLevel=fa;
exports.unstable_getFirstCallbackNode=Z;exports.unstable_next=Z;exports.unstable_now=ja;exports.unstable_pauseExecution=Z;exports.unstable_requestPaint=ia;exports.unstable_runWithPriority=Z;exports.unstable_scheduleCallback=da;exports.unstable_shouldYield=ha;exports.unstable_wrapCallback=Z;
