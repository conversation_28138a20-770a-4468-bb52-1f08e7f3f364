{"version": 3, "names": ["React", "Platform", "Pressable", "StyleSheet", "Animated", "interpolate", "interpolateDeprecated", "interpolateNode", "cond", "greaterThan", "PROGRESS_EPSILON", "Overlay", "forwardRef", "ref", "progress", "onPress", "style", "accessibilityLabel", "props", "animatedStyle", "opacity", "inputRange", "OS", "outputRange", "zIndex", "styles", "overlay", "overlayStyle", "pressable", "select", "web", "WebkitTapHighlightColor", "default", "create", "absoluteFillObject", "backgroundColor", "flex"], "sourceRoot": "../../../../src", "sources": ["views/legacy/Overlay.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,cAAc;AAC9D,OAAOC,QAAQ,MAAM,yBAAyB;AAE9C,MAAM;EACJ;EACAC,WAAW,EAAEC,qBAAqB;EAClCC,eAAe;EACfC,IAAI;EACJC;AACF,CAAC,GAAGL,QAAQ;AAEZ,MAAMC,WAAmC,GACvCE,eAAe,IAAID,qBAAqB;AAE1C,MAAMI,gBAAgB,GAAG,IAAI;AAQ7B,MAAMC,OAAO,gBAAGX,KAAK,CAACY,UAAU,CAAC,SAASD,OAAO,OAQ/CE,GAA6B,EAC7B;EAAA,IARA;IACEC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,kBAAkB,GAAG,cAAc;IACnC,GAAGC;EACE,CAAC;EAGR,MAAMC,aAAa,GAAG;IACpBC,OAAO,EAAEf,WAAW,CAACS,QAAQ,EAAE;MAC7B;MACA;MACA;MACA;MACAO,UAAU,EACRpB,QAAQ,CAACqB,EAAE,KAAK,SAAS,IAAIrB,QAAQ,CAACqB,EAAE,KAAK,OAAO,GAChD,CAAC,CAAC,EAAE,CAAC,CAAC,GACN,CAACZ,gBAAgB,EAAE,CAAC,CAAC;MAC3Ba,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;IACF;IACA;IACA;IACAC,MAAM,EAAEhB,IAAI,CAACC,WAAW,CAACK,QAAQ,EAAEJ,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,oBACE,oBAAC,QAAQ,CAAC,IAAI,eACRQ,KAAK;IACT,GAAG,EAAEL,GAAI;IACT,KAAK,EAAE,CAACY,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAER,aAAa,EAAEH,KAAK;EAAE,iBAE5D,oBAAC,SAAS;IACR,OAAO,EAAED,OAAQ;IACjB,KAAK,EAAEU,MAAM,CAACG,SAAU;IACxB,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAEX;EAAmB,EACvC,CACY;AAEpB,CAAC,CAAC;AAEF,MAAMU,YAAY,GAAG1B,QAAQ,CAAC4B,MAAM,CAAyB;EAC3DC,GAAG,EAAE;IACH;IACA;IACAC,uBAAuB,EAAE;EAC3B,CAAC;EACDC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAMP,MAAM,GAAGtB,UAAU,CAAC8B,MAAM,CAAC;EAC/BP,OAAO,EAAE;IACP,GAAGvB,UAAU,CAAC+B,kBAAkB;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDP,SAAS,EAAE;IACTQ,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAezB,OAAO"}