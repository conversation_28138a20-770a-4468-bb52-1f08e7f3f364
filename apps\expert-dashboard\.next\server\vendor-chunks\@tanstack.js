"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #focused;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#setup = (onFocus)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const listener = ()=>onFocus();\n                window.addEventListener(\"visibilitychange\", listener, false);\n                return ()=>{\n                    window.removeEventListener(\"visibilitychange\", listener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup((focused)=>{\n            if (typeof focused === \"boolean\") {\n                this.setFocused(focused);\n            } else {\n                this.onFocus();\n            }\n        });\n    }\n    setFocused(focused) {\n        const changed = this.#focused !== focused;\n        if (changed) {\n            this.#focused = focused;\n            this.onFocus();\n        }\n    }\n    onFocus() {\n        const isFocused = this.isFocused();\n        this.listeners.forEach((listener)=>{\n            listener(isFocused);\n        });\n    }\n    isFocused() {\n        if (typeof this.#focused === \"boolean\") {\n            return this.#focused;\n        }\n        return globalThis.document?.visibilityState !== \"hidden\";\n    }\n};\nvar focusManager = new FocusManager();\n //# sourceMappingURL=focusManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n    return {\n        onFetch: (context, query)=>{\n            const options = context.options;\n            const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n            const oldPages = context.state.data?.pages || [];\n            const oldPageParams = context.state.data?.pageParams || [];\n            let result = {\n                pages: [],\n                pageParams: []\n            };\n            let currentPage = 0;\n            const fetchFn = async ()=>{\n                let cancelled = false;\n                const addSignalProperty = (object)=>{\n                    Object.defineProperty(object, \"signal\", {\n                        enumerable: true,\n                        get: ()=>{\n                            if (context.signal.aborted) {\n                                cancelled = true;\n                            } else {\n                                context.signal.addEventListener(\"abort\", ()=>{\n                                    cancelled = true;\n                                });\n                            }\n                            return context.signal;\n                        }\n                    });\n                };\n                const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n                const fetchPage = async (data, param, previous)=>{\n                    if (cancelled) {\n                        return Promise.reject();\n                    }\n                    if (param == null && data.pages.length) {\n                        return Promise.resolve(data);\n                    }\n                    const createQueryFnContext = ()=>{\n                        const queryFnContext2 = {\n                            client: context.client,\n                            queryKey: context.queryKey,\n                            pageParam: param,\n                            direction: previous ? \"backward\" : \"forward\",\n                            meta: context.options.meta\n                        };\n                        addSignalProperty(queryFnContext2);\n                        return queryFnContext2;\n                    };\n                    const queryFnContext = createQueryFnContext();\n                    const page = await queryFn(queryFnContext);\n                    const { maxPages } = context.options;\n                    const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n                    return {\n                        pages: addTo(data.pages, page, maxPages),\n                        pageParams: addTo(data.pageParams, param, maxPages)\n                    };\n                };\n                if (direction && oldPages.length) {\n                    const previous = direction === \"backward\";\n                    const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n                    const oldData = {\n                        pages: oldPages,\n                        pageParams: oldPageParams\n                    };\n                    const param = pageParamFn(options, oldData);\n                    result = await fetchPage(oldData, param, previous);\n                } else {\n                    const remainingPages = pages ?? oldPages.length;\n                    do {\n                        const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n                        if (currentPage > 0 && param == null) {\n                            break;\n                        }\n                        result = await fetchPage(result, param);\n                        currentPage++;\n                    }while (currentPage < remainingPages);\n                }\n                return result;\n            };\n            if (context.options.persister) {\n                context.fetchFn = ()=>{\n                    return context.options.persister?.(fetchFn, {\n                        client: context.client,\n                        queryKey: context.queryKey,\n                        meta: context.options.meta,\n                        signal: context.signal\n                    }, query);\n                };\n            } else {\n                context.fetchFn = fetchFn;\n            }\n        }\n    };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n    const lastIndex = pages.length - 1;\n    return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n    return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n    if (!data) return false;\n    return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data) != null;\n}\n //# sourceMappingURL=infiniteQueryBehavior.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #observers;\n    #mutationCache;\n    #retryer;\n    constructor(config){\n        super();\n        this.mutationId = config.mutationId;\n        this.#mutationCache = config.mutationCache;\n        this.#observers = [];\n        this.state = config.state || getDefaultState();\n        this.setOptions(config.options);\n        this.scheduleGc();\n    }\n    setOptions(options) {\n        this.options = options;\n        this.updateGcTime(this.options.gcTime);\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    addObserver(observer) {\n        if (!this.#observers.includes(observer)) {\n            this.#observers.push(observer);\n            this.clearGcTimeout();\n            this.#mutationCache.notify({\n                type: \"observerAdded\",\n                mutation: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        this.#observers = this.#observers.filter((x)=>x !== observer);\n        this.scheduleGc();\n        this.#mutationCache.notify({\n            type: \"observerRemoved\",\n            mutation: this,\n            observer\n        });\n    }\n    optionalRemove() {\n        if (!this.#observers.length) {\n            if (this.state.status === \"pending\") {\n                this.scheduleGc();\n            } else {\n                this.#mutationCache.remove(this);\n            }\n        }\n    }\n    continue() {\n        return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n        this.execute(this.state.variables);\n    }\n    async execute(variables) {\n        const onContinue = ()=>{\n            this.#dispatch({\n                type: \"continue\"\n            });\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n            fn: ()=>{\n                if (!this.options.mutationFn) {\n                    return Promise.reject(new Error(\"No mutationFn found\"));\n                }\n                return this.options.mutationFn(variables);\n            },\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue,\n            retry: this.options.retry ?? 0,\n            retryDelay: this.options.retryDelay,\n            networkMode: this.options.networkMode,\n            canRun: ()=>this.#mutationCache.canRun(this)\n        });\n        const restored = this.state.status === \"pending\";\n        const isPaused = !this.#retryer.canStart();\n        try {\n            if (restored) {\n                onContinue();\n            } else {\n                this.#dispatch({\n                    type: \"pending\",\n                    variables,\n                    isPaused\n                });\n                await this.#mutationCache.config.onMutate?.(variables, this);\n                const context = await this.options.onMutate?.(variables);\n                if (context !== this.state.context) {\n                    this.#dispatch({\n                        type: \"pending\",\n                        context,\n                        variables,\n                        isPaused\n                    });\n                }\n            }\n            const data = await this.#retryer.start();\n            await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n            await this.options.onSuccess?.(data, variables, this.state.context);\n            await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n            await this.options.onSettled?.(data, null, variables, this.state.context);\n            this.#dispatch({\n                type: \"success\",\n                data\n            });\n            return data;\n        } catch (error) {\n            try {\n                await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n                await this.options.onError?.(error, variables, this.state.context);\n                await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n                await this.options.onSettled?.(void 0, error, variables, this.state.context);\n                throw error;\n            } finally{\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n        } finally{\n            this.#mutationCache.runNext(this);\n        }\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        failureCount: action.failureCount,\n                        failureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        isPaused: true\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        isPaused: false\n                    };\n                case \"pending\":\n                    return {\n                        ...state,\n                        context: action.context,\n                        data: void 0,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        isPaused: action.isPaused,\n                        status: \"pending\",\n                        variables: action.variables,\n                        submittedAt: Date.now()\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        status: \"success\",\n                        isPaused: false\n                    };\n                case \"error\":\n                    return {\n                        ...state,\n                        data: void 0,\n                        error: action.error,\n                        failureCount: state.failureCount + 1,\n                        failureReason: action.error,\n                        isPaused: false,\n                        status: \"error\"\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#observers.forEach((observer)=>{\n                observer.onMutationUpdate(action);\n            });\n            this.#mutationCache.notify({\n                mutation: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction getDefaultState() {\n    return {\n        context: void 0,\n        data: void 0,\n        error: null,\n        failureCount: 0,\n        failureReason: null,\n        isPaused: false,\n        status: \"idle\",\n        variables: void 0,\n        submittedAt: 0\n    };\n}\n //# sourceMappingURL=mutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#mutations = /* @__PURE__ */ new Set();\n        this.#scopes = /* @__PURE__ */ new Map();\n        this.#mutationId = 0;\n    }\n    #mutations;\n    #scopes;\n    #mutationId;\n    build(client, options, state) {\n        const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n            mutationCache: this,\n            mutationId: ++this.#mutationId,\n            options: client.defaultMutationOptions(options),\n            state\n        });\n        this.add(mutation);\n        return mutation;\n    }\n    add(mutation) {\n        this.#mutations.add(mutation);\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const scopedMutations = this.#scopes.get(scope);\n            if (scopedMutations) {\n                scopedMutations.push(mutation);\n            } else {\n                this.#scopes.set(scope, [\n                    mutation\n                ]);\n            }\n        }\n        this.notify({\n            type: \"added\",\n            mutation\n        });\n    }\n    remove(mutation) {\n        if (this.#mutations.delete(mutation)) {\n            const scope = scopeFor(mutation);\n            if (typeof scope === \"string\") {\n                const scopedMutations = this.#scopes.get(scope);\n                if (scopedMutations) {\n                    if (scopedMutations.length > 1) {\n                        const index = scopedMutations.indexOf(mutation);\n                        if (index !== -1) {\n                            scopedMutations.splice(index, 1);\n                        }\n                    } else if (scopedMutations[0] === mutation) {\n                        this.#scopes.delete(scope);\n                    }\n                }\n            }\n        }\n        this.notify({\n            type: \"removed\",\n            mutation\n        });\n    }\n    canRun(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const mutationsWithSameScope = this.#scopes.get(scope);\n            const firstPendingMutation = mutationsWithSameScope?.find((m)=>m.state.status === \"pending\");\n            return !firstPendingMutation || firstPendingMutation === mutation;\n        } else {\n            return true;\n        }\n    }\n    runNext(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const foundMutation = this.#scopes.get(scope)?.find((m)=>m !== mutation && m.state.isPaused);\n            return foundMutation?.continue() ?? Promise.resolve();\n        } else {\n            return Promise.resolve();\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#mutations.forEach((mutation)=>{\n                this.notify({\n                    type: \"removed\",\n                    mutation\n                });\n            });\n            this.#mutations.clear();\n            this.#scopes.clear();\n        });\n    }\n    getAll() {\n        return Array.from(this.#mutations);\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation));\n    }\n    findAll(filters = {}) {\n        return this.getAll().filter((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    resumePausedMutations() {\n        const pausedMutations = this.getAll().filter((x)=>x.state.isPaused);\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>Promise.all(pausedMutations.map((mutation)=>mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))));\n    }\n};\nfunction scopeFor(mutation) {\n    return mutation.options.scope?.id;\n}\n //# sourceMappingURL=mutationCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = (cb)=>setTimeout(cb, 0);\nfunction createNotifyManager() {\n    let queue = [];\n    let transactions = 0;\n    let notifyFn = (callback)=>{\n        callback();\n    };\n    let batchNotifyFn = (callback)=>{\n        callback();\n    };\n    let scheduleFn = defaultScheduler;\n    const schedule = (callback)=>{\n        if (transactions) {\n            queue.push(callback);\n        } else {\n            scheduleFn(()=>{\n                notifyFn(callback);\n            });\n        }\n    };\n    const flush = ()=>{\n        const originalQueue = queue;\n        queue = [];\n        if (originalQueue.length) {\n            scheduleFn(()=>{\n                batchNotifyFn(()=>{\n                    originalQueue.forEach((callback)=>{\n                        notifyFn(callback);\n                    });\n                });\n            });\n        }\n    };\n    return {\n        batch: (callback)=>{\n            let result;\n            transactions++;\n            try {\n                result = callback();\n            } finally{\n                transactions--;\n                if (!transactions) {\n                    flush();\n                }\n            }\n            return result;\n        },\n        /**\n     * All calls to the wrapped function will be batched.\n     */ batchCalls: (callback)=>{\n            return (...args)=>{\n                schedule(()=>{\n                    callback(...args);\n                });\n            };\n        },\n        schedule,\n        /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */ setNotifyFunction: (fn)=>{\n            notifyFn = fn;\n        },\n        /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */ setBatchNotifyFunction: (fn)=>{\n            batchNotifyFn = fn;\n        },\n        setScheduler: (fn)=>{\n            scheduleFn = fn;\n        }\n    };\n}\nvar notifyManager = createNotifyManager();\n //# sourceMappingURL=notifyManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #online;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#online = true;\n        this.#setup = (onOnline)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const onlineListener = ()=>onOnline(true);\n                const offlineListener = ()=>onOnline(false);\n                window.addEventListener(\"online\", onlineListener, false);\n                window.addEventListener(\"offline\", offlineListener, false);\n                return ()=>{\n                    window.removeEventListener(\"online\", onlineListener);\n                    window.removeEventListener(\"offline\", offlineListener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup(this.setOnline.bind(this));\n    }\n    setOnline(online) {\n        const changed = this.#online !== online;\n        if (changed) {\n            this.#online = online;\n            this.listeners.forEach((listener)=>{\n                listener(online);\n            });\n        }\n    }\n    isOnline() {\n        return this.#online;\n    }\n};\nvar onlineManager = new OnlineManager();\n //# sourceMappingURL=onlineManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #initialState;\n    #revertState;\n    #cache;\n    #client;\n    #retryer;\n    #defaultOptions;\n    #abortSignalConsumed;\n    constructor(config){\n        super();\n        this.#abortSignalConsumed = false;\n        this.#defaultOptions = config.defaultOptions;\n        this.setOptions(config.options);\n        this.observers = [];\n        this.#client = config.client;\n        this.#cache = this.#client.getQueryCache();\n        this.queryKey = config.queryKey;\n        this.queryHash = config.queryHash;\n        this.#initialState = getDefaultState(this.options);\n        this.state = config.state ?? this.#initialState;\n        this.scheduleGc();\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    get promise() {\n        return this.#retryer?.promise;\n    }\n    setOptions(options) {\n        this.options = {\n            ...this.#defaultOptions,\n            ...options\n        };\n        this.updateGcTime(this.options.gcTime);\n    }\n    optionalRemove() {\n        if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n            this.#cache.remove(this);\n        }\n    }\n    setData(newData, options) {\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n        this.#dispatch({\n            data,\n            type: \"success\",\n            dataUpdatedAt: options?.updatedAt,\n            manual: options?.manual\n        });\n        return data;\n    }\n    setState(state, setStateOptions) {\n        this.#dispatch({\n            type: \"setState\",\n            state,\n            setStateOptions\n        });\n    }\n    cancel(options) {\n        const promise = this.#retryer?.promise;\n        this.#retryer?.cancel(options);\n        return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n    }\n    destroy() {\n        super.destroy();\n        this.cancel({\n            silent: true\n        });\n    }\n    reset() {\n        this.destroy();\n        this.setState(this.#initialState);\n    }\n    isActive() {\n        return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false);\n    }\n    isDisabled() {\n        if (this.getObserversCount() > 0) {\n            return !this.isActive();\n        }\n        return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n    }\n    isStatic() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\");\n        }\n        return false;\n    }\n    isStale() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>observer.getCurrentResult().isStale);\n        }\n        return this.state.data === void 0 || this.state.isInvalidated;\n    }\n    isStaleByTime(staleTime = 0) {\n        if (this.state.data === void 0) {\n            return true;\n        }\n        if (staleTime === \"static\") {\n            return false;\n        }\n        if (this.state.isInvalidated) {\n            return true;\n        }\n        return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n    }\n    onFocus() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnWindowFocus());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    onOnline() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnReconnect());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    addObserver(observer) {\n        if (!this.observers.includes(observer)) {\n            this.observers.push(observer);\n            this.clearGcTimeout();\n            this.#cache.notify({\n                type: \"observerAdded\",\n                query: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        if (this.observers.includes(observer)) {\n            this.observers = this.observers.filter((x)=>x !== observer);\n            if (!this.observers.length) {\n                if (this.#retryer) {\n                    if (this.#abortSignalConsumed) {\n                        this.#retryer.cancel({\n                            revert: true\n                        });\n                    } else {\n                        this.#retryer.cancelRetry();\n                    }\n                }\n                this.scheduleGc();\n            }\n            this.#cache.notify({\n                type: \"observerRemoved\",\n                query: this,\n                observer\n            });\n        }\n    }\n    getObserversCount() {\n        return this.observers.length;\n    }\n    invalidate() {\n        if (!this.state.isInvalidated) {\n            this.#dispatch({\n                type: \"invalidate\"\n            });\n        }\n    }\n    fetch(options, fetchOptions) {\n        if (this.state.fetchStatus !== \"idle\") {\n            if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n                this.cancel({\n                    silent: true\n                });\n            } else if (this.#retryer) {\n                this.#retryer.continueRetry();\n                return this.#retryer.promise;\n            }\n        }\n        if (options) {\n            this.setOptions(options);\n        }\n        if (!this.options.queryFn) {\n            const observer = this.observers.find((x)=>x.options.queryFn);\n            if (observer) {\n                this.setOptions(observer.options);\n            }\n        }\n        if (true) {\n            if (!Array.isArray(this.options.queryKey)) {\n                console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n            }\n        }\n        const abortController = new AbortController();\n        const addSignalProperty = (object)=>{\n            Object.defineProperty(object, \"signal\", {\n                enumerable: true,\n                get: ()=>{\n                    this.#abortSignalConsumed = true;\n                    return abortController.signal;\n                }\n            });\n        };\n        const fetchFn = ()=>{\n            const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n            const createQueryFnContext = ()=>{\n                const queryFnContext2 = {\n                    client: this.#client,\n                    queryKey: this.queryKey,\n                    meta: this.meta\n                };\n                addSignalProperty(queryFnContext2);\n                return queryFnContext2;\n            };\n            const queryFnContext = createQueryFnContext();\n            this.#abortSignalConsumed = false;\n            if (this.options.persister) {\n                return this.options.persister(queryFn, queryFnContext, this);\n            }\n            return queryFn(queryFnContext);\n        };\n        const createFetchContext = ()=>{\n            const context2 = {\n                fetchOptions,\n                options: this.options,\n                queryKey: this.queryKey,\n                client: this.#client,\n                state: this.state,\n                fetchFn\n            };\n            addSignalProperty(context2);\n            return context2;\n        };\n        const context = createFetchContext();\n        this.options.behavior?.onFetch(context, this);\n        this.#revertState = this.state;\n        if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n            this.#dispatch({\n                type: \"fetch\",\n                meta: context.fetchOptions?.meta\n            });\n        }\n        const onError = (error)=>{\n            if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n            if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n                this.#cache.config.onError?.(error, this);\n                this.#cache.config.onSettled?.(this.state.data, error, this);\n            }\n            this.scheduleGc();\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n            initialPromise: fetchOptions?.initialPromise,\n            fn: context.fetchFn,\n            abort: abortController.abort.bind(abortController),\n            onSuccess: (data)=>{\n                if (data === void 0) {\n                    if (true) {\n                        console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n                    }\n                    onError(new Error(`${this.queryHash} data is undefined`));\n                    return;\n                }\n                try {\n                    this.setData(data);\n                } catch (error) {\n                    onError(error);\n                    return;\n                }\n                this.#cache.config.onSuccess?.(data, this);\n                this.#cache.config.onSettled?.(data, this.state.error, this);\n                this.scheduleGc();\n            },\n            onError,\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue: ()=>{\n                this.#dispatch({\n                    type: \"continue\"\n                });\n            },\n            retry: context.options.retry,\n            retryDelay: context.options.retryDelay,\n            networkMode: context.options.networkMode,\n            canRun: ()=>true\n        });\n        return this.#retryer.start();\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        fetchFailureCount: action.failureCount,\n                        fetchFailureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        fetchStatus: \"paused\"\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        fetchStatus: \"fetching\"\n                    };\n                case \"fetch\":\n                    return {\n                        ...state,\n                        ...fetchState(state.data, this.options),\n                        fetchMeta: action.meta ?? null\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        dataUpdateCount: state.dataUpdateCount + 1,\n                        dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n                        error: null,\n                        isInvalidated: false,\n                        status: \"success\",\n                        ...!action.manual && {\n                            fetchStatus: \"idle\",\n                            fetchFailureCount: 0,\n                            fetchFailureReason: null\n                        }\n                    };\n                case \"error\":\n                    const error = action.error;\n                    if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n                        return {\n                            ...this.#revertState,\n                            fetchStatus: \"idle\"\n                        };\n                    }\n                    return {\n                        ...state,\n                        error,\n                        errorUpdateCount: state.errorUpdateCount + 1,\n                        errorUpdatedAt: Date.now(),\n                        fetchFailureCount: state.fetchFailureCount + 1,\n                        fetchFailureReason: error,\n                        fetchStatus: \"idle\",\n                        status: \"error\"\n                    };\n                case \"invalidate\":\n                    return {\n                        ...state,\n                        isInvalidated: true\n                    };\n                case \"setState\":\n                    return {\n                        ...state,\n                        ...action.state\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.observers.forEach((observer)=>{\n                observer.onQueryUpdate();\n            });\n            this.#cache.notify({\n                query: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction fetchState(data, options) {\n    return {\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n        ...data === void 0 && {\n            error: null,\n            status: \"pending\"\n        }\n    };\n}\nfunction getDefaultState(options) {\n    const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n    const hasData = data !== void 0;\n    const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    return {\n        data,\n        dataUpdateCount: 0,\n        dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n        error: null,\n        errorUpdateCount: 0,\n        errorUpdatedAt: 0,\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchMeta: null,\n        isInvalidated: false,\n        status: hasData ? \"success\" : \"pending\",\n        fetchStatus: \"idle\"\n    };\n}\n //# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQSxlQUFlO0FBU0s7QUFDK0I7QUFDc0I7QUFDOUI7QUFDM0MsSUFBSVksUUFBUSxjQUFjRCxvREFBU0E7SUFDakMsQ0FBQ0UsWUFBWSxDQUFDO0lBQ2QsQ0FBQ0MsV0FBVyxDQUFDO0lBQ2IsQ0FBQ0MsS0FBSyxDQUFDO0lBQ1AsQ0FBQ0MsTUFBTSxDQUFDO0lBQ1IsQ0FBQ0MsT0FBTyxDQUFDO0lBQ1QsQ0FBQ0MsY0FBYyxDQUFDO0lBQ2hCLENBQUNDLG1CQUFtQixDQUFDO0lBQ3JCQyxZQUFZQyxNQUFNLENBQUU7UUFDbEIsS0FBSztRQUNMLElBQUksQ0FBQyxDQUFDRixtQkFBbUIsR0FBRztRQUM1QixJQUFJLENBQUMsQ0FBQ0QsY0FBYyxHQUFHRyxPQUFPSCxjQUFjO1FBQzVDLElBQUksQ0FBQ0ksVUFBVSxDQUFDRCxPQUFPRSxPQUFPO1FBQzlCLElBQUksQ0FBQ0MsU0FBUyxHQUFHLEVBQUU7UUFDbkIsSUFBSSxDQUFDLENBQUNSLE1BQU0sR0FBR0ssT0FBT0wsTUFBTTtRQUM1QixJQUFJLENBQUMsQ0FBQ0QsS0FBSyxHQUFHLElBQUksQ0FBQyxDQUFDQyxNQUFNLENBQUNTLGFBQWE7UUFDeEMsSUFBSSxDQUFDQyxRQUFRLEdBQUdMLE9BQU9LLFFBQVE7UUFDL0IsSUFBSSxDQUFDQyxTQUFTLEdBQUdOLE9BQU9NLFNBQVM7UUFDakMsSUFBSSxDQUFDLENBQUNkLFlBQVksR0FBR2UsZ0JBQWdCLElBQUksQ0FBQ0wsT0FBTztRQUNqRCxJQUFJLENBQUNNLEtBQUssR0FBR1IsT0FBT1EsS0FBSyxJQUFJLElBQUksQ0FBQyxDQUFDaEIsWUFBWTtRQUMvQyxJQUFJLENBQUNpQixVQUFVO0lBQ2pCO0lBQ0EsSUFBSUMsT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDUixPQUFPLENBQUNRLElBQUk7SUFDMUI7SUFDQSxJQUFJQyxVQUFVO1FBQ1osT0FBTyxJQUFJLENBQUMsQ0FBQ2YsT0FBTyxFQUFFZTtJQUN4QjtJQUNBVixXQUFXQyxPQUFPLEVBQUU7UUFDbEIsSUFBSSxDQUFDQSxPQUFPLEdBQUc7WUFBRSxHQUFHLElBQUksQ0FBQyxDQUFDTCxjQUFjO1lBQUUsR0FBR0ssT0FBTztRQUFDO1FBQ3JELElBQUksQ0FBQ1UsWUFBWSxDQUFDLElBQUksQ0FBQ1YsT0FBTyxDQUFDVyxNQUFNO0lBQ3ZDO0lBQ0FDLGlCQUFpQjtRQUNmLElBQUksQ0FBQyxJQUFJLENBQUNYLFNBQVMsQ0FBQ1ksTUFBTSxJQUFJLElBQUksQ0FBQ1AsS0FBSyxDQUFDUSxXQUFXLEtBQUssUUFBUTtZQUMvRCxJQUFJLENBQUMsQ0FBQ3RCLEtBQUssQ0FBQ3VCLE1BQU0sQ0FBQyxJQUFJO1FBQ3pCO0lBQ0Y7SUFDQUMsUUFBUUMsT0FBTyxFQUFFakIsT0FBTyxFQUFFO1FBQ3hCLE1BQU1rQixPQUFPdkMsc0RBQVdBLENBQUMsSUFBSSxDQUFDMkIsS0FBSyxDQUFDWSxJQUFJLEVBQUVELFNBQVMsSUFBSSxDQUFDakIsT0FBTztRQUMvRCxJQUFJLENBQUMsQ0FBQ21CLFFBQVEsQ0FBQztZQUNiRDtZQUNBRSxNQUFNO1lBQ05DLGVBQWVyQixTQUFTc0I7WUFDeEJDLFFBQVF2QixTQUFTdUI7UUFDbkI7UUFDQSxPQUFPTDtJQUNUO0lBQ0FNLFNBQVNsQixLQUFLLEVBQUVtQixlQUFlLEVBQUU7UUFDL0IsSUFBSSxDQUFDLENBQUNOLFFBQVEsQ0FBQztZQUFFQyxNQUFNO1lBQVlkO1lBQU9tQjtRQUFnQjtJQUM1RDtJQUNBQyxPQUFPMUIsT0FBTyxFQUFFO1FBQ2QsTUFBTVMsVUFBVSxJQUFJLENBQUMsQ0FBQ2YsT0FBTyxFQUFFZTtRQUMvQixJQUFJLENBQUMsQ0FBQ2YsT0FBTyxFQUFFZ0MsT0FBTzFCO1FBQ3RCLE9BQU9TLFVBQVVBLFFBQVFrQixJQUFJLENBQUNqRCwyQ0FBSUEsRUFBRWtELEtBQUssQ0FBQ2xELDJDQUFJQSxJQUFJbUQsUUFBUUMsT0FBTztJQUNuRTtJQUNBQyxVQUFVO1FBQ1IsS0FBSyxDQUFDQTtRQUNOLElBQUksQ0FBQ0wsTUFBTSxDQUFDO1lBQUVNLFFBQVE7UUFBSztJQUM3QjtJQUNBQyxRQUFRO1FBQ04sSUFBSSxDQUFDRixPQUFPO1FBQ1osSUFBSSxDQUFDUCxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUNsQyxZQUFZO0lBQ2xDO0lBQ0E0QyxXQUFXO1FBQ1QsT0FBTyxJQUFJLENBQUNqQyxTQUFTLENBQUNrQyxJQUFJLENBQ3hCLENBQUNDLFdBQWF4RCx5REFBY0EsQ0FBQ3dELFNBQVNwQyxPQUFPLENBQUNxQyxPQUFPLEVBQUUsSUFBSSxNQUFNO0lBRXJFO0lBQ0FDLGFBQWE7UUFDWCxJQUFJLElBQUksQ0FBQ0MsaUJBQWlCLEtBQUssR0FBRztZQUNoQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxRQUFRO1FBQ3ZCO1FBQ0EsT0FBTyxJQUFJLENBQUNsQyxPQUFPLENBQUN3QyxPQUFPLEtBQUsxRCxnREFBU0EsSUFBSSxJQUFJLENBQUN3QixLQUFLLENBQUNtQyxlQUFlLEdBQUcsSUFBSSxDQUFDbkMsS0FBSyxDQUFDb0MsZ0JBQWdCLEtBQUs7SUFDNUc7SUFDQUMsV0FBVztRQUNULElBQUksSUFBSSxDQUFDSixpQkFBaUIsS0FBSyxHQUFHO1lBQ2hDLE9BQU8sSUFBSSxDQUFDdEMsU0FBUyxDQUFDa0MsSUFBSSxDQUN4QixDQUFDQyxXQUFhdkQsMkRBQWdCQSxDQUFDdUQsU0FBU3BDLE9BQU8sQ0FBQzRDLFNBQVMsRUFBRSxJQUFJLE1BQU07UUFFekU7UUFDQSxPQUFPO0lBQ1Q7SUFDQUMsVUFBVTtRQUNSLElBQUksSUFBSSxDQUFDTixpQkFBaUIsS0FBSyxHQUFHO1lBQ2hDLE9BQU8sSUFBSSxDQUFDdEMsU0FBUyxDQUFDa0MsSUFBSSxDQUN4QixDQUFDQyxXQUFhQSxTQUFTVSxnQkFBZ0IsR0FBR0QsT0FBTztRQUVyRDtRQUNBLE9BQU8sSUFBSSxDQUFDdkMsS0FBSyxDQUFDWSxJQUFJLEtBQUssS0FBSyxLQUFLLElBQUksQ0FBQ1osS0FBSyxDQUFDeUMsYUFBYTtJQUMvRDtJQUNBQyxjQUFjSixZQUFZLENBQUMsRUFBRTtRQUMzQixJQUFJLElBQUksQ0FBQ3RDLEtBQUssQ0FBQ1ksSUFBSSxLQUFLLEtBQUssR0FBRztZQUM5QixPQUFPO1FBQ1Q7UUFDQSxJQUFJMEIsY0FBYyxVQUFVO1lBQzFCLE9BQU87UUFDVDtRQUNBLElBQUksSUFBSSxDQUFDdEMsS0FBSyxDQUFDeUMsYUFBYSxFQUFFO1lBQzVCLE9BQU87UUFDVDtRQUNBLE9BQU8sQ0FBQ2hFLHlEQUFjQSxDQUFDLElBQUksQ0FBQ3VCLEtBQUssQ0FBQ2UsYUFBYSxFQUFFdUI7SUFDbkQ7SUFDQUssVUFBVTtRQUNSLE1BQU1iLFdBQVcsSUFBSSxDQUFDbkMsU0FBUyxDQUFDaUQsSUFBSSxDQUFDLENBQUNDLElBQU1BLEVBQUVDLHdCQUF3QjtRQUN0RWhCLFVBQVVpQixRQUFRO1lBQUVDLGVBQWU7UUFBTTtRQUN6QyxJQUFJLENBQUMsQ0FBQzVELE9BQU8sRUFBRTZEO0lBQ2pCO0lBQ0FDLFdBQVc7UUFDVCxNQUFNcEIsV0FBVyxJQUFJLENBQUNuQyxTQUFTLENBQUNpRCxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRU0sc0JBQXNCO1FBQ3BFckIsVUFBVWlCLFFBQVE7WUFBRUMsZUFBZTtRQUFNO1FBQ3pDLElBQUksQ0FBQyxDQUFDNUQsT0FBTyxFQUFFNkQ7SUFDakI7SUFDQUcsWUFBWXRCLFFBQVEsRUFBRTtRQUNwQixJQUFJLENBQUMsSUFBSSxDQUFDbkMsU0FBUyxDQUFDMEQsUUFBUSxDQUFDdkIsV0FBVztZQUN0QyxJQUFJLENBQUNuQyxTQUFTLENBQUMyRCxJQUFJLENBQUN4QjtZQUNwQixJQUFJLENBQUN5QixjQUFjO1lBQ25CLElBQUksQ0FBQyxDQUFDckUsS0FBSyxDQUFDc0UsTUFBTSxDQUFDO2dCQUFFMUMsTUFBTTtnQkFBaUIyQyxPQUFPLElBQUk7Z0JBQUUzQjtZQUFTO1FBQ3BFO0lBQ0Y7SUFDQTRCLGVBQWU1QixRQUFRLEVBQUU7UUFDdkIsSUFBSSxJQUFJLENBQUNuQyxTQUFTLENBQUMwRCxRQUFRLENBQUN2QixXQUFXO1lBQ3JDLElBQUksQ0FBQ25DLFNBQVMsR0FBRyxJQUFJLENBQUNBLFNBQVMsQ0FBQ2dFLE1BQU0sQ0FBQyxDQUFDZCxJQUFNQSxNQUFNZjtZQUNwRCxJQUFJLENBQUMsSUFBSSxDQUFDbkMsU0FBUyxDQUFDWSxNQUFNLEVBQUU7Z0JBQzFCLElBQUksSUFBSSxDQUFDLENBQUNuQixPQUFPLEVBQUU7b0JBQ2pCLElBQUksSUFBSSxDQUFDLENBQUNFLG1CQUFtQixFQUFFO3dCQUM3QixJQUFJLENBQUMsQ0FBQ0YsT0FBTyxDQUFDZ0MsTUFBTSxDQUFDOzRCQUFFd0MsUUFBUTt3QkFBSztvQkFDdEMsT0FBTzt3QkFDTCxJQUFJLENBQUMsQ0FBQ3hFLE9BQU8sQ0FBQ3lFLFdBQVc7b0JBQzNCO2dCQUNGO2dCQUNBLElBQUksQ0FBQzVELFVBQVU7WUFDakI7WUFDQSxJQUFJLENBQUMsQ0FBQ2YsS0FBSyxDQUFDc0UsTUFBTSxDQUFDO2dCQUFFMUMsTUFBTTtnQkFBbUIyQyxPQUFPLElBQUk7Z0JBQUUzQjtZQUFTO1FBQ3RFO0lBQ0Y7SUFDQUcsb0JBQW9CO1FBQ2xCLE9BQU8sSUFBSSxDQUFDdEMsU0FBUyxDQUFDWSxNQUFNO0lBQzlCO0lBQ0F1RCxhQUFhO1FBQ1gsSUFBSSxDQUFDLElBQUksQ0FBQzlELEtBQUssQ0FBQ3lDLGFBQWEsRUFBRTtZQUM3QixJQUFJLENBQUMsQ0FBQzVCLFFBQVEsQ0FBQztnQkFBRUMsTUFBTTtZQUFhO1FBQ3RDO0lBQ0Y7SUFDQWlELE1BQU1yRSxPQUFPLEVBQUVzRSxZQUFZLEVBQUU7UUFDM0IsSUFBSSxJQUFJLENBQUNoRSxLQUFLLENBQUNRLFdBQVcsS0FBSyxRQUFRO1lBQ3JDLElBQUksSUFBSSxDQUFDUixLQUFLLENBQUNZLElBQUksS0FBSyxLQUFLLEtBQUtvRCxjQUFjaEIsZUFBZTtnQkFDN0QsSUFBSSxDQUFDNUIsTUFBTSxDQUFDO29CQUFFTSxRQUFRO2dCQUFLO1lBQzdCLE9BQU8sSUFBSSxJQUFJLENBQUMsQ0FBQ3RDLE9BQU8sRUFBRTtnQkFDeEIsSUFBSSxDQUFDLENBQUNBLE9BQU8sQ0FBQzZFLGFBQWE7Z0JBQzNCLE9BQU8sSUFBSSxDQUFDLENBQUM3RSxPQUFPLENBQUNlLE9BQU87WUFDOUI7UUFDRjtRQUNBLElBQUlULFNBQVM7WUFDWCxJQUFJLENBQUNELFVBQVUsQ0FBQ0M7UUFDbEI7UUFDQSxJQUFJLENBQUMsSUFBSSxDQUFDQSxPQUFPLENBQUN3QyxPQUFPLEVBQUU7WUFDekIsTUFBTUosV0FBVyxJQUFJLENBQUNuQyxTQUFTLENBQUNpRCxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRW5ELE9BQU8sQ0FBQ3dDLE9BQU87WUFDN0QsSUFBSUosVUFBVTtnQkFDWixJQUFJLENBQUNyQyxVQUFVLENBQUNxQyxTQUFTcEMsT0FBTztZQUNsQztRQUNGO1FBQ0EsSUFBSXdFLElBQXFDLEVBQUU7WUFDekMsSUFBSSxDQUFDQyxNQUFNQyxPQUFPLENBQUMsSUFBSSxDQUFDMUUsT0FBTyxDQUFDRyxRQUFRLEdBQUc7Z0JBQ3pDd0UsUUFBUUMsS0FBSyxDQUNYLENBQUMsbUlBQW1JLENBQUM7WUFFekk7UUFDRjtRQUNBLE1BQU1DLGtCQUFrQixJQUFJQztRQUM1QixNQUFNQyxvQkFBb0IsQ0FBQ0M7WUFDekJDLE9BQU9DLGNBQWMsQ0FBQ0YsUUFBUSxVQUFVO2dCQUN0Q0csWUFBWTtnQkFDWkMsS0FBSztvQkFDSCxJQUFJLENBQUMsQ0FBQ3hGLG1CQUFtQixHQUFHO29CQUM1QixPQUFPaUYsZ0JBQWdCUSxNQUFNO2dCQUMvQjtZQUNGO1FBQ0Y7UUFDQSxNQUFNQyxVQUFVO1lBQ2QsTUFBTTlDLFVBQVUvRCx3REFBYUEsQ0FBQyxJQUFJLENBQUN1QixPQUFPLEVBQUVzRTtZQUM1QyxNQUFNaUIsdUJBQXVCO2dCQUMzQixNQUFNQyxrQkFBa0I7b0JBQ3RCL0YsUUFBUSxJQUFJLENBQUMsQ0FBQ0EsTUFBTTtvQkFDcEJVLFVBQVUsSUFBSSxDQUFDQSxRQUFRO29CQUN2QkssTUFBTSxJQUFJLENBQUNBLElBQUk7Z0JBQ2pCO2dCQUNBdUUsa0JBQWtCUztnQkFDbEIsT0FBT0E7WUFDVDtZQUNBLE1BQU1DLGlCQUFpQkY7WUFDdkIsSUFBSSxDQUFDLENBQUMzRixtQkFBbUIsR0FBRztZQUM1QixJQUFJLElBQUksQ0FBQ0ksT0FBTyxDQUFDMEYsU0FBUyxFQUFFO2dCQUMxQixPQUFPLElBQUksQ0FBQzFGLE9BQU8sQ0FBQzBGLFNBQVMsQ0FDM0JsRCxTQUNBaUQsZ0JBQ0EsSUFBSTtZQUVSO1lBQ0EsT0FBT2pELFFBQVFpRDtRQUNqQjtRQUNBLE1BQU1FLHFCQUFxQjtZQUN6QixNQUFNQyxXQUFXO2dCQUNmdEI7Z0JBQ0F0RSxTQUFTLElBQUksQ0FBQ0EsT0FBTztnQkFDckJHLFVBQVUsSUFBSSxDQUFDQSxRQUFRO2dCQUN2QlYsUUFBUSxJQUFJLENBQUMsQ0FBQ0EsTUFBTTtnQkFDcEJhLE9BQU8sSUFBSSxDQUFDQSxLQUFLO2dCQUNqQmdGO1lBQ0Y7WUFDQVAsa0JBQWtCYTtZQUNsQixPQUFPQTtRQUNUO1FBQ0EsTUFBTUMsVUFBVUY7UUFDaEIsSUFBSSxDQUFDM0YsT0FBTyxDQUFDOEYsUUFBUSxFQUFFQyxRQUFRRixTQUFTLElBQUk7UUFDNUMsSUFBSSxDQUFDLENBQUN0RyxXQUFXLEdBQUcsSUFBSSxDQUFDZSxLQUFLO1FBQzlCLElBQUksSUFBSSxDQUFDQSxLQUFLLENBQUNRLFdBQVcsS0FBSyxVQUFVLElBQUksQ0FBQ1IsS0FBSyxDQUFDMEYsU0FBUyxLQUFLSCxRQUFRdkIsWUFBWSxFQUFFOUQsTUFBTTtZQUM1RixJQUFJLENBQUMsQ0FBQ1csUUFBUSxDQUFDO2dCQUFFQyxNQUFNO2dCQUFTWixNQUFNcUYsUUFBUXZCLFlBQVksRUFBRTlEO1lBQUs7UUFDbkU7UUFDQSxNQUFNeUYsVUFBVSxDQUFDckI7WUFDZixJQUFJLENBQUV6RixDQUFBQSw2REFBZ0JBLENBQUN5RixVQUFVQSxNQUFNNUMsTUFBTSxHQUFHO2dCQUM5QyxJQUFJLENBQUMsQ0FBQ2IsUUFBUSxDQUFDO29CQUNiQyxNQUFNO29CQUNOd0Q7Z0JBQ0Y7WUFDRjtZQUNBLElBQUksQ0FBQ3pGLDZEQUFnQkEsQ0FBQ3lGLFFBQVE7Z0JBQzVCLElBQUksQ0FBQyxDQUFDcEYsS0FBSyxDQUFDTSxNQUFNLENBQUNtRyxPQUFPLEdBQ3hCckIsT0FDQSxJQUFJO2dCQUVOLElBQUksQ0FBQyxDQUFDcEYsS0FBSyxDQUFDTSxNQUFNLENBQUNvRyxTQUFTLEdBQzFCLElBQUksQ0FBQzVGLEtBQUssQ0FBQ1ksSUFBSSxFQUNmMEQsT0FDQSxJQUFJO1lBRVI7WUFDQSxJQUFJLENBQUNyRSxVQUFVO1FBQ2pCO1FBQ0EsSUFBSSxDQUFDLENBQUNiLE9BQU8sR0FBR1IsMERBQWFBLENBQUM7WUFDNUJpSCxnQkFBZ0I3QixjQUFjNkI7WUFDOUJDLElBQUlQLFFBQVFQLE9BQU87WUFDbkJlLE9BQU94QixnQkFBZ0J3QixLQUFLLENBQUNDLElBQUksQ0FBQ3pCO1lBQ2xDMEIsV0FBVyxDQUFDckY7Z0JBQ1YsSUFBSUEsU0FBUyxLQUFLLEdBQUc7b0JBQ25CLElBQUlzRCxJQUFxQyxFQUFFO3dCQUN6Q0csUUFBUUMsS0FBSyxDQUNYLENBQUMsc0lBQXNJLEVBQUUsSUFBSSxDQUFDeEUsU0FBUyxDQUFDLENBQUM7b0JBRTdKO29CQUNBNkYsUUFBUSxJQUFJTyxNQUFNLENBQUMsRUFBRSxJQUFJLENBQUNwRyxTQUFTLENBQUMsa0JBQWtCLENBQUM7b0JBQ3ZEO2dCQUNGO2dCQUNBLElBQUk7b0JBQ0YsSUFBSSxDQUFDWSxPQUFPLENBQUNFO2dCQUNmLEVBQUUsT0FBTzBELE9BQU87b0JBQ2RxQixRQUFRckI7b0JBQ1I7Z0JBQ0Y7Z0JBQ0EsSUFBSSxDQUFDLENBQUNwRixLQUFLLENBQUNNLE1BQU0sQ0FBQ3lHLFNBQVMsR0FBR3JGLE1BQU0sSUFBSTtnQkFDekMsSUFBSSxDQUFDLENBQUMxQixLQUFLLENBQUNNLE1BQU0sQ0FBQ29HLFNBQVMsR0FDMUJoRixNQUNBLElBQUksQ0FBQ1osS0FBSyxDQUFDc0UsS0FBSyxFQUNoQixJQUFJO2dCQUVOLElBQUksQ0FBQ3JFLFVBQVU7WUFDakI7WUFDQTBGO1lBQ0FRLFFBQVEsQ0FBQ0MsY0FBYzlCO2dCQUNyQixJQUFJLENBQUMsQ0FBQ3pELFFBQVEsQ0FBQztvQkFBRUMsTUFBTTtvQkFBVXNGO29CQUFjOUI7Z0JBQU07WUFDdkQ7WUFDQStCLFNBQVM7Z0JBQ1AsSUFBSSxDQUFDLENBQUN4RixRQUFRLENBQUM7b0JBQUVDLE1BQU07Z0JBQVE7WUFDakM7WUFDQXdGLFlBQVk7Z0JBQ1YsSUFBSSxDQUFDLENBQUN6RixRQUFRLENBQUM7b0JBQUVDLE1BQU07Z0JBQVc7WUFDcEM7WUFDQXlGLE9BQU9oQixRQUFRN0YsT0FBTyxDQUFDNkcsS0FBSztZQUM1QkMsWUFBWWpCLFFBQVE3RixPQUFPLENBQUM4RyxVQUFVO1lBQ3RDQyxhQUFhbEIsUUFBUTdGLE9BQU8sQ0FBQytHLFdBQVc7WUFDeENDLFFBQVEsSUFBTTtRQUNoQjtRQUNBLE9BQU8sSUFBSSxDQUFDLENBQUN0SCxPQUFPLENBQUN1SCxLQUFLO0lBQzVCO0lBQ0EsQ0FBQzlGLFFBQVEsQ0FBQytGLE1BQU07UUFDZCxNQUFNQyxVQUFVLENBQUM3RztZQUNmLE9BQVE0RyxPQUFPOUYsSUFBSTtnQkFDakIsS0FBSztvQkFDSCxPQUFPO3dCQUNMLEdBQUdkLEtBQUs7d0JBQ1I4RyxtQkFBbUJGLE9BQU9SLFlBQVk7d0JBQ3RDVyxvQkFBb0JILE9BQU90QyxLQUFLO29CQUNsQztnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR3RFLEtBQUs7d0JBQ1JRLGFBQWE7b0JBQ2Y7Z0JBQ0YsS0FBSztvQkFDSCxPQUFPO3dCQUNMLEdBQUdSLEtBQUs7d0JBQ1JRLGFBQWE7b0JBQ2Y7Z0JBQ0YsS0FBSztvQkFDSCxPQUFPO3dCQUNMLEdBQUdSLEtBQUs7d0JBQ1IsR0FBR2dILFdBQVdoSCxNQUFNWSxJQUFJLEVBQUUsSUFBSSxDQUFDbEIsT0FBTyxDQUFDO3dCQUN2Q2dHLFdBQVdrQixPQUFPMUcsSUFBSSxJQUFJO29CQUM1QjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR0YsS0FBSzt3QkFDUlksTUFBTWdHLE9BQU9oRyxJQUFJO3dCQUNqQnVCLGlCQUFpQm5DLE1BQU1tQyxlQUFlLEdBQUc7d0JBQ3pDcEIsZUFBZTZGLE9BQU83RixhQUFhLElBQUlrRyxLQUFLQyxHQUFHO3dCQUMvQzVDLE9BQU87d0JBQ1A3QixlQUFlO3dCQUNmMEUsUUFBUTt3QkFDUixHQUFHLENBQUNQLE9BQU8zRixNQUFNLElBQUk7NEJBQ25CVCxhQUFhOzRCQUNic0csbUJBQW1COzRCQUNuQkMsb0JBQW9CO3dCQUN0QixDQUFDO29CQUNIO2dCQUNGLEtBQUs7b0JBQ0gsTUFBTXpDLFFBQVFzQyxPQUFPdEMsS0FBSztvQkFDMUIsSUFBSXpGLDZEQUFnQkEsQ0FBQ3lGLFVBQVVBLE1BQU1WLE1BQU0sSUFBSSxJQUFJLENBQUMsQ0FBQzNFLFdBQVcsRUFBRTt3QkFDaEUsT0FBTzs0QkFBRSxHQUFHLElBQUksQ0FBQyxDQUFDQSxXQUFXOzRCQUFFdUIsYUFBYTt3QkFBTztvQkFDckQ7b0JBQ0EsT0FBTzt3QkFDTCxHQUFHUixLQUFLO3dCQUNSc0U7d0JBQ0FsQyxrQkFBa0JwQyxNQUFNb0MsZ0JBQWdCLEdBQUc7d0JBQzNDZ0YsZ0JBQWdCSCxLQUFLQyxHQUFHO3dCQUN4QkosbUJBQW1COUcsTUFBTThHLGlCQUFpQixHQUFHO3dCQUM3Q0Msb0JBQW9CekM7d0JBQ3BCOUQsYUFBYTt3QkFDYjJHLFFBQVE7b0JBQ1Y7Z0JBQ0YsS0FBSztvQkFDSCxPQUFPO3dCQUNMLEdBQUduSCxLQUFLO3dCQUNSeUMsZUFBZTtvQkFDakI7Z0JBQ0YsS0FBSztvQkFDSCxPQUFPO3dCQUNMLEdBQUd6QyxLQUFLO3dCQUNSLEdBQUc0RyxPQUFPNUcsS0FBSztvQkFDakI7WUFDSjtRQUNGO1FBQ0EsSUFBSSxDQUFDQSxLQUFLLEdBQUc2RyxRQUFRLElBQUksQ0FBQzdHLEtBQUs7UUFDL0J0Qiw0REFBYUEsQ0FBQzJJLEtBQUssQ0FBQztZQUNsQixJQUFJLENBQUMxSCxTQUFTLENBQUMySCxPQUFPLENBQUMsQ0FBQ3hGO2dCQUN0QkEsU0FBU3lGLGFBQWE7WUFDeEI7WUFDQSxJQUFJLENBQUMsQ0FBQ3JJLEtBQUssQ0FBQ3NFLE1BQU0sQ0FBQztnQkFBRUMsT0FBTyxJQUFJO2dCQUFFM0MsTUFBTTtnQkFBVzhGO1lBQU87UUFDNUQ7SUFDRjtBQUNGO0FBQ0EsU0FBU0ksV0FBV3BHLElBQUksRUFBRWxCLE9BQU87SUFDL0IsT0FBTztRQUNMb0gsbUJBQW1CO1FBQ25CQyxvQkFBb0I7UUFDcEJ2RyxhQUFhN0IscURBQVFBLENBQUNlLFFBQVErRyxXQUFXLElBQUksYUFBYTtRQUMxRCxHQUFHN0YsU0FBUyxLQUFLLEtBQUs7WUFDcEIwRCxPQUFPO1lBQ1A2QyxRQUFRO1FBQ1YsQ0FBQztJQUNIO0FBQ0Y7QUFDQSxTQUFTcEgsZ0JBQWdCTCxPQUFPO0lBQzlCLE1BQU1rQixPQUFPLE9BQU9sQixRQUFROEgsV0FBVyxLQUFLLGFBQWE5SCxRQUFROEgsV0FBVyxLQUFLOUgsUUFBUThILFdBQVc7SUFDcEcsTUFBTUMsVUFBVTdHLFNBQVMsS0FBSztJQUM5QixNQUFNOEcsdUJBQXVCRCxVQUFVLE9BQU8vSCxRQUFRZ0ksb0JBQW9CLEtBQUssYUFBYWhJLFFBQVFnSSxvQkFBb0IsS0FBS2hJLFFBQVFnSSxvQkFBb0IsR0FBRztJQUM1SixPQUFPO1FBQ0w5RztRQUNBdUIsaUJBQWlCO1FBQ2pCcEIsZUFBZTBHLFVBQVVDLHdCQUF3QlQsS0FBS0MsR0FBRyxLQUFLO1FBQzlENUMsT0FBTztRQUNQbEMsa0JBQWtCO1FBQ2xCZ0YsZ0JBQWdCO1FBQ2hCTixtQkFBbUI7UUFDbkJDLG9CQUFvQjtRQUNwQnJCLFdBQVc7UUFDWGpELGVBQWU7UUFDZjBFLFFBQVFNLFVBQVUsWUFBWTtRQUM5QmpILGFBQWE7SUFDZjtBQUNGO0FBSUUsQ0FDRixpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2V4cGVydC1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeS5qcz9mOThkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9xdWVyeS50c1xuaW1wb3J0IHtcbiAgZW5zdXJlUXVlcnlGbixcbiAgbm9vcCxcbiAgcmVwbGFjZURhdGEsXG4gIHJlc29sdmVFbmFibGVkLFxuICByZXNvbHZlU3RhbGVUaW1lLFxuICBza2lwVG9rZW4sXG4gIHRpbWVVbnRpbFN0YWxlXG59IGZyb20gXCIuL3V0aWxzLmpzXCI7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgY2FuRmV0Y2gsIGNyZWF0ZVJldHJ5ZXIsIGlzQ2FuY2VsbGVkRXJyb3IgfSBmcm9tIFwiLi9yZXRyeWVyLmpzXCI7XG5pbXBvcnQgeyBSZW1vdmFibGUgfSBmcm9tIFwiLi9yZW1vdmFibGUuanNcIjtcbnZhciBRdWVyeSA9IGNsYXNzIGV4dGVuZHMgUmVtb3ZhYmxlIHtcbiAgI2luaXRpYWxTdGF0ZTtcbiAgI3JldmVydFN0YXRlO1xuICAjY2FjaGU7XG4gICNjbGllbnQ7XG4gICNyZXRyeWVyO1xuICAjZGVmYXVsdE9wdGlvbnM7XG4gICNhYm9ydFNpZ25hbENvbnN1bWVkO1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSBmYWxzZTtcbiAgICB0aGlzLiNkZWZhdWx0T3B0aW9ucyA9IGNvbmZpZy5kZWZhdWx0T3B0aW9ucztcbiAgICB0aGlzLnNldE9wdGlvbnMoY29uZmlnLm9wdGlvbnMpO1xuICAgIHRoaXMub2JzZXJ2ZXJzID0gW107XG4gICAgdGhpcy4jY2xpZW50ID0gY29uZmlnLmNsaWVudDtcbiAgICB0aGlzLiNjYWNoZSA9IHRoaXMuI2NsaWVudC5nZXRRdWVyeUNhY2hlKCk7XG4gICAgdGhpcy5xdWVyeUtleSA9IGNvbmZpZy5xdWVyeUtleTtcbiAgICB0aGlzLnF1ZXJ5SGFzaCA9IGNvbmZpZy5xdWVyeUhhc2g7XG4gICAgdGhpcy4jaW5pdGlhbFN0YXRlID0gZ2V0RGVmYXVsdFN0YXRlKHRoaXMub3B0aW9ucyk7XG4gICAgdGhpcy5zdGF0ZSA9IGNvbmZpZy5zdGF0ZSA/PyB0aGlzLiNpbml0aWFsU3RhdGU7XG4gICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gIH1cbiAgZ2V0IG1ldGEoKSB7XG4gICAgcmV0dXJuIHRoaXMub3B0aW9ucy5tZXRhO1xuICB9XG4gIGdldCBwcm9taXNlKCkge1xuICAgIHJldHVybiB0aGlzLiNyZXRyeWVyPy5wcm9taXNlO1xuICB9XG4gIHNldE9wdGlvbnMob3B0aW9ucykge1xuICAgIHRoaXMub3B0aW9ucyA9IHsgLi4udGhpcy4jZGVmYXVsdE9wdGlvbnMsIC4uLm9wdGlvbnMgfTtcbiAgICB0aGlzLnVwZGF0ZUdjVGltZSh0aGlzLm9wdGlvbnMuZ2NUaW1lKTtcbiAgfVxuICBvcHRpb25hbFJlbW92ZSgpIHtcbiAgICBpZiAoIXRoaXMub2JzZXJ2ZXJzLmxlbmd0aCAmJiB0aGlzLnN0YXRlLmZldGNoU3RhdHVzID09PSBcImlkbGVcIikge1xuICAgICAgdGhpcy4jY2FjaGUucmVtb3ZlKHRoaXMpO1xuICAgIH1cbiAgfVxuICBzZXREYXRhKG5ld0RhdGEsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBkYXRhID0gcmVwbGFjZURhdGEodGhpcy5zdGF0ZS5kYXRhLCBuZXdEYXRhLCB0aGlzLm9wdGlvbnMpO1xuICAgIHRoaXMuI2Rpc3BhdGNoKHtcbiAgICAgIGRhdGEsXG4gICAgICB0eXBlOiBcInN1Y2Nlc3NcIixcbiAgICAgIGRhdGFVcGRhdGVkQXQ6IG9wdGlvbnM/LnVwZGF0ZWRBdCxcbiAgICAgIG1hbnVhbDogb3B0aW9ucz8ubWFudWFsXG4gICAgfSk7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cbiAgc2V0U3RhdGUoc3RhdGUsIHNldFN0YXRlT3B0aW9ucykge1xuICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJzZXRTdGF0ZVwiLCBzdGF0ZSwgc2V0U3RhdGVPcHRpb25zIH0pO1xuICB9XG4gIGNhbmNlbChvcHRpb25zKSB7XG4gICAgY29uc3QgcHJvbWlzZSA9IHRoaXMuI3JldHJ5ZXI/LnByb21pc2U7XG4gICAgdGhpcy4jcmV0cnllcj8uY2FuY2VsKG9wdGlvbnMpO1xuICAgIHJldHVybiBwcm9taXNlID8gcHJvbWlzZS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gIH1cbiAgZGVzdHJveSgpIHtcbiAgICBzdXBlci5kZXN0cm95KCk7XG4gICAgdGhpcy5jYW5jZWwoeyBzaWxlbnQ6IHRydWUgfSk7XG4gIH1cbiAgcmVzZXQoKSB7XG4gICAgdGhpcy5kZXN0cm95KCk7XG4gICAgdGhpcy5zZXRTdGF0ZSh0aGlzLiNpbml0aWFsU3RhdGUpO1xuICB9XG4gIGlzQWN0aXZlKCkge1xuICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5zb21lKFxuICAgICAgKG9ic2VydmVyKSA9PiByZXNvbHZlRW5hYmxlZChvYnNlcnZlci5vcHRpb25zLmVuYWJsZWQsIHRoaXMpICE9PSBmYWxzZVxuICAgICk7XG4gIH1cbiAgaXNEaXNhYmxlZCgpIHtcbiAgICBpZiAodGhpcy5nZXRPYnNlcnZlcnNDb3VudCgpID4gMCkge1xuICAgICAgcmV0dXJuICF0aGlzLmlzQWN0aXZlKCk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLm9wdGlvbnMucXVlcnlGbiA9PT0gc2tpcFRva2VuIHx8IHRoaXMuc3RhdGUuZGF0YVVwZGF0ZUNvdW50ICsgdGhpcy5zdGF0ZS5lcnJvclVwZGF0ZUNvdW50ID09PSAwO1xuICB9XG4gIGlzU3RhdGljKCkge1xuICAgIGlmICh0aGlzLmdldE9ic2VydmVyc0NvdW50KCkgPiAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMuc29tZShcbiAgICAgICAgKG9ic2VydmVyKSA9PiByZXNvbHZlU3RhbGVUaW1lKG9ic2VydmVyLm9wdGlvbnMuc3RhbGVUaW1lLCB0aGlzKSA9PT0gXCJzdGF0aWNcIlxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlzU3RhbGUoKSB7XG4gICAgaWYgKHRoaXMuZ2V0T2JzZXJ2ZXJzQ291bnQoKSA+IDApIHtcbiAgICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5zb21lKFxuICAgICAgICAob2JzZXJ2ZXIpID0+IG9ic2VydmVyLmdldEN1cnJlbnRSZXN1bHQoKS5pc1N0YWxlXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5zdGF0ZS5kYXRhID09PSB2b2lkIDAgfHwgdGhpcy5zdGF0ZS5pc0ludmFsaWRhdGVkO1xuICB9XG4gIGlzU3RhbGVCeVRpbWUoc3RhbGVUaW1lID0gMCkge1xuICAgIGlmICh0aGlzLnN0YXRlLmRhdGEgPT09IHZvaWQgMCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGlmIChzdGFsZVRpbWUgPT09IFwic3RhdGljXCIpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKHRoaXMuc3RhdGUuaXNJbnZhbGlkYXRlZCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiAhdGltZVVudGlsU3RhbGUodGhpcy5zdGF0ZS5kYXRhVXBkYXRlZEF0LCBzdGFsZVRpbWUpO1xuICB9XG4gIG9uRm9jdXMoKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKCh4KSA9PiB4LnNob3VsZEZldGNoT25XaW5kb3dGb2N1cygpKTtcbiAgICBvYnNlcnZlcj8ucmVmZXRjaCh7IGNhbmNlbFJlZmV0Y2g6IGZhbHNlIH0pO1xuICAgIHRoaXMuI3JldHJ5ZXI/LmNvbnRpbnVlKCk7XG4gIH1cbiAgb25PbmxpbmUoKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKCh4KSA9PiB4LnNob3VsZEZldGNoT25SZWNvbm5lY3QoKSk7XG4gICAgb2JzZXJ2ZXI/LnJlZmV0Y2goeyBjYW5jZWxSZWZldGNoOiBmYWxzZSB9KTtcbiAgICB0aGlzLiNyZXRyeWVyPy5jb250aW51ZSgpO1xuICB9XG4gIGFkZE9ic2VydmVyKG9ic2VydmVyKSB7XG4gICAgaWYgKCF0aGlzLm9ic2VydmVycy5pbmNsdWRlcyhvYnNlcnZlcikpIHtcbiAgICAgIHRoaXMub2JzZXJ2ZXJzLnB1c2gob2JzZXJ2ZXIpO1xuICAgICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgICAgdGhpcy4jY2FjaGUubm90aWZ5KHsgdHlwZTogXCJvYnNlcnZlckFkZGVkXCIsIHF1ZXJ5OiB0aGlzLCBvYnNlcnZlciB9KTtcbiAgICB9XG4gIH1cbiAgcmVtb3ZlT2JzZXJ2ZXIob2JzZXJ2ZXIpIHtcbiAgICBpZiAodGhpcy5vYnNlcnZlcnMuaW5jbHVkZXMob2JzZXJ2ZXIpKSB7XG4gICAgICB0aGlzLm9ic2VydmVycyA9IHRoaXMub2JzZXJ2ZXJzLmZpbHRlcigoeCkgPT4geCAhPT0gb2JzZXJ2ZXIpO1xuICAgICAgaWYgKCF0aGlzLm9ic2VydmVycy5sZW5ndGgpIHtcbiAgICAgICAgaWYgKHRoaXMuI3JldHJ5ZXIpIHtcbiAgICAgICAgICBpZiAodGhpcy4jYWJvcnRTaWduYWxDb25zdW1lZCkge1xuICAgICAgICAgICAgdGhpcy4jcmV0cnllci5jYW5jZWwoeyByZXZlcnQ6IHRydWUgfSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuI3JldHJ5ZXIuY2FuY2VsUmV0cnkoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgICB9XG4gICAgICB0aGlzLiNjYWNoZS5ub3RpZnkoeyB0eXBlOiBcIm9ic2VydmVyUmVtb3ZlZFwiLCBxdWVyeTogdGhpcywgb2JzZXJ2ZXIgfSk7XG4gICAgfVxuICB9XG4gIGdldE9ic2VydmVyc0NvdW50KCkge1xuICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5sZW5ndGg7XG4gIH1cbiAgaW52YWxpZGF0ZSgpIHtcbiAgICBpZiAoIXRoaXMuc3RhdGUuaXNJbnZhbGlkYXRlZCkge1xuICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImludmFsaWRhdGVcIiB9KTtcbiAgICB9XG4gIH1cbiAgZmV0Y2gob3B0aW9ucywgZmV0Y2hPcHRpb25zKSB7XG4gICAgaWYgKHRoaXMuc3RhdGUuZmV0Y2hTdGF0dXMgIT09IFwiaWRsZVwiKSB7XG4gICAgICBpZiAodGhpcy5zdGF0ZS5kYXRhICE9PSB2b2lkIDAgJiYgZmV0Y2hPcHRpb25zPy5jYW5jZWxSZWZldGNoKSB7XG4gICAgICAgIHRoaXMuY2FuY2VsKHsgc2lsZW50OiB0cnVlIH0pO1xuICAgICAgfSBlbHNlIGlmICh0aGlzLiNyZXRyeWVyKSB7XG4gICAgICAgIHRoaXMuI3JldHJ5ZXIuY29udGludWVSZXRyeSgpO1xuICAgICAgICByZXR1cm4gdGhpcy4jcmV0cnllci5wcm9taXNlO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAob3B0aW9ucykge1xuICAgICAgdGhpcy5zZXRPcHRpb25zKG9wdGlvbnMpO1xuICAgIH1cbiAgICBpZiAoIXRoaXMub3B0aW9ucy5xdWVyeUZuKSB7XG4gICAgICBjb25zdCBvYnNlcnZlciA9IHRoaXMub2JzZXJ2ZXJzLmZpbmQoKHgpID0+IHgub3B0aW9ucy5xdWVyeUZuKTtcbiAgICAgIGlmIChvYnNlcnZlcikge1xuICAgICAgICB0aGlzLnNldE9wdGlvbnMob2JzZXJ2ZXIub3B0aW9ucyk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheSh0aGlzLm9wdGlvbnMucXVlcnlLZXkpKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgYEFzIG9mIHY0LCBxdWVyeUtleSBuZWVkcyB0byBiZSBhbiBBcnJheS4gSWYgeW91IGFyZSB1c2luZyBhIHN0cmluZyBsaWtlICdyZXBvRGF0YScsIHBsZWFzZSBjaGFuZ2UgaXQgdG8gYW4gQXJyYXksIGUuZy4gWydyZXBvRGF0YSddYFxuICAgICAgICApO1xuICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBhYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgYWRkU2lnbmFsUHJvcGVydHkgPSAob2JqZWN0KSA9PiB7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqZWN0LCBcInNpZ25hbFwiLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogKCkgPT4ge1xuICAgICAgICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSB0cnVlO1xuICAgICAgICAgIHJldHVybiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IGZldGNoRm4gPSAoKSA9PiB7XG4gICAgICBjb25zdCBxdWVyeUZuID0gZW5zdXJlUXVlcnlGbih0aGlzLm9wdGlvbnMsIGZldGNoT3B0aW9ucyk7XG4gICAgICBjb25zdCBjcmVhdGVRdWVyeUZuQ29udGV4dCA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgcXVlcnlGbkNvbnRleHQyID0ge1xuICAgICAgICAgIGNsaWVudDogdGhpcy4jY2xpZW50LFxuICAgICAgICAgIHF1ZXJ5S2V5OiB0aGlzLnF1ZXJ5S2V5LFxuICAgICAgICAgIG1ldGE6IHRoaXMubWV0YVxuICAgICAgICB9O1xuICAgICAgICBhZGRTaWduYWxQcm9wZXJ0eShxdWVyeUZuQ29udGV4dDIpO1xuICAgICAgICByZXR1cm4gcXVlcnlGbkNvbnRleHQyO1xuICAgICAgfTtcbiAgICAgIGNvbnN0IHF1ZXJ5Rm5Db250ZXh0ID0gY3JlYXRlUXVlcnlGbkNvbnRleHQoKTtcbiAgICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSBmYWxzZTtcbiAgICAgIGlmICh0aGlzLm9wdGlvbnMucGVyc2lzdGVyKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm9wdGlvbnMucGVyc2lzdGVyKFxuICAgICAgICAgIHF1ZXJ5Rm4sXG4gICAgICAgICAgcXVlcnlGbkNvbnRleHQsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHF1ZXJ5Rm4ocXVlcnlGbkNvbnRleHQpO1xuICAgIH07XG4gICAgY29uc3QgY3JlYXRlRmV0Y2hDb250ZXh0ID0gKCkgPT4ge1xuICAgICAgY29uc3QgY29udGV4dDIgPSB7XG4gICAgICAgIGZldGNoT3B0aW9ucyxcbiAgICAgICAgb3B0aW9uczogdGhpcy5vcHRpb25zLFxuICAgICAgICBxdWVyeUtleTogdGhpcy5xdWVyeUtleSxcbiAgICAgICAgY2xpZW50OiB0aGlzLiNjbGllbnQsXG4gICAgICAgIHN0YXRlOiB0aGlzLnN0YXRlLFxuICAgICAgICBmZXRjaEZuXG4gICAgICB9O1xuICAgICAgYWRkU2lnbmFsUHJvcGVydHkoY29udGV4dDIpO1xuICAgICAgcmV0dXJuIGNvbnRleHQyO1xuICAgIH07XG4gICAgY29uc3QgY29udGV4dCA9IGNyZWF0ZUZldGNoQ29udGV4dCgpO1xuICAgIHRoaXMub3B0aW9ucy5iZWhhdmlvcj8ub25GZXRjaChjb250ZXh0LCB0aGlzKTtcbiAgICB0aGlzLiNyZXZlcnRTdGF0ZSA9IHRoaXMuc3RhdGU7XG4gICAgaWYgKHRoaXMuc3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwiaWRsZVwiIHx8IHRoaXMuc3RhdGUuZmV0Y2hNZXRhICE9PSBjb250ZXh0LmZldGNoT3B0aW9ucz8ubWV0YSkge1xuICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImZldGNoXCIsIG1ldGE6IGNvbnRleHQuZmV0Y2hPcHRpb25zPy5tZXRhIH0pO1xuICAgIH1cbiAgICBjb25zdCBvbkVycm9yID0gKGVycm9yKSA9PiB7XG4gICAgICBpZiAoIShpc0NhbmNlbGxlZEVycm9yKGVycm9yKSAmJiBlcnJvci5zaWxlbnQpKSB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHtcbiAgICAgICAgICB0eXBlOiBcImVycm9yXCIsXG4gICAgICAgICAgZXJyb3JcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBpZiAoIWlzQ2FuY2VsbGVkRXJyb3IoZXJyb3IpKSB7XG4gICAgICAgIHRoaXMuI2NhY2hlLmNvbmZpZy5vbkVycm9yPy4oXG4gICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgICB0aGlzLiNjYWNoZS5jb25maWcub25TZXR0bGVkPy4oXG4gICAgICAgICAgdGhpcy5zdGF0ZS5kYXRhLFxuICAgICAgICAgIGVycm9yLFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICAgIH07XG4gICAgdGhpcy4jcmV0cnllciA9IGNyZWF0ZVJldHJ5ZXIoe1xuICAgICAgaW5pdGlhbFByb21pc2U6IGZldGNoT3B0aW9ucz8uaW5pdGlhbFByb21pc2UsXG4gICAgICBmbjogY29udGV4dC5mZXRjaEZuLFxuICAgICAgYWJvcnQ6IGFib3J0Q29udHJvbGxlci5hYm9ydC5iaW5kKGFib3J0Q29udHJvbGxlciksXG4gICAgICBvblN1Y2Nlc3M6IChkYXRhKSA9PiB7XG4gICAgICAgIGlmIChkYXRhID09PSB2b2lkIDApIHtcbiAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBgUXVlcnkgZGF0YSBjYW5ub3QgYmUgdW5kZWZpbmVkLiBQbGVhc2UgbWFrZSBzdXJlIHRvIHJldHVybiBhIHZhbHVlIG90aGVyIHRoYW4gdW5kZWZpbmVkIGZyb20geW91ciBxdWVyeSBmdW5jdGlvbi4gQWZmZWN0ZWQgcXVlcnkga2V5OiAke3RoaXMucXVlcnlIYXNofWBcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICAgIG9uRXJyb3IobmV3IEVycm9yKGAke3RoaXMucXVlcnlIYXNofSBkYXRhIGlzIHVuZGVmaW5lZGApKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICB0aGlzLnNldERhdGEoZGF0YSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgb25FcnJvcihlcnJvcik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuI2NhY2hlLmNvbmZpZy5vblN1Y2Nlc3M/LihkYXRhLCB0aGlzKTtcbiAgICAgICAgdGhpcy4jY2FjaGUuY29uZmlnLm9uU2V0dGxlZD8uKFxuICAgICAgICAgIGRhdGEsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5lcnJvcixcbiAgICAgICAgICB0aGlzXG4gICAgICAgICk7XG4gICAgICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICAgICAgfSxcbiAgICAgIG9uRXJyb3IsXG4gICAgICBvbkZhaWw6IChmYWlsdXJlQ291bnQsIGVycm9yKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJmYWlsZWRcIiwgZmFpbHVyZUNvdW50LCBlcnJvciB9KTtcbiAgICAgIH0sXG4gICAgICBvblBhdXNlOiAoKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJwYXVzZVwiIH0pO1xuICAgICAgfSxcbiAgICAgIG9uQ29udGludWU6ICgpID0+IHtcbiAgICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImNvbnRpbnVlXCIgfSk7XG4gICAgICB9LFxuICAgICAgcmV0cnk6IGNvbnRleHQub3B0aW9ucy5yZXRyeSxcbiAgICAgIHJldHJ5RGVsYXk6IGNvbnRleHQub3B0aW9ucy5yZXRyeURlbGF5LFxuICAgICAgbmV0d29ya01vZGU6IGNvbnRleHQub3B0aW9ucy5uZXR3b3JrTW9kZSxcbiAgICAgIGNhblJ1bjogKCkgPT4gdHJ1ZVxuICAgIH0pO1xuICAgIHJldHVybiB0aGlzLiNyZXRyeWVyLnN0YXJ0KCk7XG4gIH1cbiAgI2Rpc3BhdGNoKGFjdGlvbikge1xuICAgIGNvbnN0IHJlZHVjZXIgPSAoc3RhdGUpID0+IHtcbiAgICAgIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICAgICAgY2FzZSBcImZhaWxlZFwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiBhY3Rpb24uZmFpbHVyZUNvdW50LFxuICAgICAgICAgICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBhY3Rpb24uZXJyb3JcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwicGF1c2VcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBmZXRjaFN0YXR1czogXCJwYXVzZWRcIlxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJjb250aW51ZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGZldGNoU3RhdHVzOiBcImZldGNoaW5nXCJcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiZmV0Y2hcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICAuLi5mZXRjaFN0YXRlKHN0YXRlLmRhdGEsIHRoaXMub3B0aW9ucyksXG4gICAgICAgICAgICBmZXRjaE1ldGE6IGFjdGlvbi5tZXRhID8/IG51bGxcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwic3VjY2Vzc1wiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGRhdGE6IGFjdGlvbi5kYXRhLFxuICAgICAgICAgICAgZGF0YVVwZGF0ZUNvdW50OiBzdGF0ZS5kYXRhVXBkYXRlQ291bnQgKyAxLFxuICAgICAgICAgICAgZGF0YVVwZGF0ZWRBdDogYWN0aW9uLmRhdGFVcGRhdGVkQXQgPz8gRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgICAgaXNJbnZhbGlkYXRlZDogZmFsc2UsXG4gICAgICAgICAgICBzdGF0dXM6IFwic3VjY2Vzc1wiLFxuICAgICAgICAgICAgLi4uIWFjdGlvbi5tYW51YWwgJiYge1xuICAgICAgICAgICAgICBmZXRjaFN0YXR1czogXCJpZGxlXCIsXG4gICAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgICAgICAgICAgICBmZXRjaEZhaWx1cmVSZWFzb246IG51bGxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgICBjb25zdCBlcnJvciA9IGFjdGlvbi5lcnJvcjtcbiAgICAgICAgICBpZiAoaXNDYW5jZWxsZWRFcnJvcihlcnJvcikgJiYgZXJyb3IucmV2ZXJ0ICYmIHRoaXMuI3JldmVydFN0YXRlKSB7XG4gICAgICAgICAgICByZXR1cm4geyAuLi50aGlzLiNyZXZlcnRTdGF0ZSwgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiIH07XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgZXJyb3JVcGRhdGVDb3VudDogc3RhdGUuZXJyb3JVcGRhdGVDb3VudCArIDEsXG4gICAgICAgICAgICBlcnJvclVwZGF0ZWRBdDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiBzdGF0ZS5mZXRjaEZhaWx1cmVDb3VudCArIDEsXG4gICAgICAgICAgICBmZXRjaEZhaWx1cmVSZWFzb246IGVycm9yLFxuICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiLFxuICAgICAgICAgICAgc3RhdHVzOiBcImVycm9yXCJcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiaW52YWxpZGF0ZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGlzSW52YWxpZGF0ZWQ6IHRydWVcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwic2V0U3RhdGVcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICAuLi5hY3Rpb24uc3RhdGVcbiAgICAgICAgICB9O1xuICAgICAgfVxuICAgIH07XG4gICAgdGhpcy5zdGF0ZSA9IHJlZHVjZXIodGhpcy5zdGF0ZSk7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICB0aGlzLm9ic2VydmVycy5mb3JFYWNoKChvYnNlcnZlcikgPT4ge1xuICAgICAgICBvYnNlcnZlci5vblF1ZXJ5VXBkYXRlKCk7XG4gICAgICB9KTtcbiAgICAgIHRoaXMuI2NhY2hlLm5vdGlmeSh7IHF1ZXJ5OiB0aGlzLCB0eXBlOiBcInVwZGF0ZWRcIiwgYWN0aW9uIH0pO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gZmV0Y2hTdGF0ZShkYXRhLCBvcHRpb25zKSB7XG4gIHJldHVybiB7XG4gICAgZmV0Y2hGYWlsdXJlQ291bnQ6IDAsXG4gICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBudWxsLFxuICAgIGZldGNoU3RhdHVzOiBjYW5GZXRjaChvcHRpb25zLm5ldHdvcmtNb2RlKSA/IFwiZmV0Y2hpbmdcIiA6IFwicGF1c2VkXCIsXG4gICAgLi4uZGF0YSA9PT0gdm9pZCAwICYmIHtcbiAgICAgIGVycm9yOiBudWxsLFxuICAgICAgc3RhdHVzOiBcInBlbmRpbmdcIlxuICAgIH1cbiAgfTtcbn1cbmZ1bmN0aW9uIGdldERlZmF1bHRTdGF0ZShvcHRpb25zKSB7XG4gIGNvbnN0IGRhdGEgPSB0eXBlb2Ygb3B0aW9ucy5pbml0aWFsRGF0YSA9PT0gXCJmdW5jdGlvblwiID8gb3B0aW9ucy5pbml0aWFsRGF0YSgpIDogb3B0aW9ucy5pbml0aWFsRGF0YTtcbiAgY29uc3QgaGFzRGF0YSA9IGRhdGEgIT09IHZvaWQgMDtcbiAgY29uc3QgaW5pdGlhbERhdGFVcGRhdGVkQXQgPSBoYXNEYXRhID8gdHlwZW9mIG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQgPT09IFwiZnVuY3Rpb25cIiA/IG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQoKSA6IG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQgOiAwO1xuICByZXR1cm4ge1xuICAgIGRhdGEsXG4gICAgZGF0YVVwZGF0ZUNvdW50OiAwLFxuICAgIGRhdGFVcGRhdGVkQXQ6IGhhc0RhdGEgPyBpbml0aWFsRGF0YVVwZGF0ZWRBdCA/PyBEYXRlLm5vdygpIDogMCxcbiAgICBlcnJvcjogbnVsbCxcbiAgICBlcnJvclVwZGF0ZUNvdW50OiAwLFxuICAgIGVycm9yVXBkYXRlZEF0OiAwLFxuICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgIGZldGNoRmFpbHVyZVJlYXNvbjogbnVsbCxcbiAgICBmZXRjaE1ldGE6IG51bGwsXG4gICAgaXNJbnZhbGlkYXRlZDogZmFsc2UsXG4gICAgc3RhdHVzOiBoYXNEYXRhID8gXCJzdWNjZXNzXCIgOiBcInBlbmRpbmdcIixcbiAgICBmZXRjaFN0YXR1czogXCJpZGxlXCJcbiAgfTtcbn1cbmV4cG9ydCB7XG4gIFF1ZXJ5LFxuICBmZXRjaFN0YXRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cXVlcnkuanMubWFwIl0sIm5hbWVzIjpbImVuc3VyZVF1ZXJ5Rm4iLCJub29wIiwicmVwbGFjZURhdGEiLCJyZXNvbHZlRW5hYmxlZCIsInJlc29sdmVTdGFsZVRpbWUiLCJza2lwVG9rZW4iLCJ0aW1lVW50aWxTdGFsZSIsIm5vdGlmeU1hbmFnZXIiLCJjYW5GZXRjaCIsImNyZWF0ZVJldHJ5ZXIiLCJpc0NhbmNlbGxlZEVycm9yIiwiUmVtb3ZhYmxlIiwiUXVlcnkiLCJpbml0aWFsU3RhdGUiLCJyZXZlcnRTdGF0ZSIsImNhY2hlIiwiY2xpZW50IiwicmV0cnllciIsImRlZmF1bHRPcHRpb25zIiwiYWJvcnRTaWduYWxDb25zdW1lZCIsImNvbnN0cnVjdG9yIiwiY29uZmlnIiwic2V0T3B0aW9ucyIsIm9wdGlvbnMiLCJvYnNlcnZlcnMiLCJnZXRRdWVyeUNhY2hlIiwicXVlcnlLZXkiLCJxdWVyeUhhc2giLCJnZXREZWZhdWx0U3RhdGUiLCJzdGF0ZSIsInNjaGVkdWxlR2MiLCJtZXRhIiwicHJvbWlzZSIsInVwZGF0ZUdjVGltZSIsImdjVGltZSIsIm9wdGlvbmFsUmVtb3ZlIiwibGVuZ3RoIiwiZmV0Y2hTdGF0dXMiLCJyZW1vdmUiLCJzZXREYXRhIiwibmV3RGF0YSIsImRhdGEiLCJkaXNwYXRjaCIsInR5cGUiLCJkYXRhVXBkYXRlZEF0IiwidXBkYXRlZEF0IiwibWFudWFsIiwic2V0U3RhdGUiLCJzZXRTdGF0ZU9wdGlvbnMiLCJjYW5jZWwiLCJ0aGVuIiwiY2F0Y2giLCJQcm9taXNlIiwicmVzb2x2ZSIsImRlc3Ryb3kiLCJzaWxlbnQiLCJyZXNldCIsImlzQWN0aXZlIiwic29tZSIsIm9ic2VydmVyIiwiZW5hYmxlZCIsImlzRGlzYWJsZWQiLCJnZXRPYnNlcnZlcnNDb3VudCIsInF1ZXJ5Rm4iLCJkYXRhVXBkYXRlQ291bnQiLCJlcnJvclVwZGF0ZUNvdW50IiwiaXNTdGF0aWMiLCJzdGFsZVRpbWUiLCJpc1N0YWxlIiwiZ2V0Q3VycmVudFJlc3VsdCIsImlzSW52YWxpZGF0ZWQiLCJpc1N0YWxlQnlUaW1lIiwib25Gb2N1cyIsImZpbmQiLCJ4Iiwic2hvdWxkRmV0Y2hPbldpbmRvd0ZvY3VzIiwicmVmZXRjaCIsImNhbmNlbFJlZmV0Y2giLCJjb250aW51ZSIsIm9uT25saW5lIiwic2hvdWxkRmV0Y2hPblJlY29ubmVjdCIsImFkZE9ic2VydmVyIiwiaW5jbHVkZXMiLCJwdXNoIiwiY2xlYXJHY1RpbWVvdXQiLCJub3RpZnkiLCJxdWVyeSIsInJlbW92ZU9ic2VydmVyIiwiZmlsdGVyIiwicmV2ZXJ0IiwiY2FuY2VsUmV0cnkiLCJpbnZhbGlkYXRlIiwiZmV0Y2giLCJmZXRjaE9wdGlvbnMiLCJjb250aW51ZVJldHJ5IiwicHJvY2VzcyIsIkFycmF5IiwiaXNBcnJheSIsImNvbnNvbGUiLCJlcnJvciIsImFib3J0Q29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsImFkZFNpZ25hbFByb3BlcnR5Iiwib2JqZWN0IiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJlbnVtZXJhYmxlIiwiZ2V0Iiwic2lnbmFsIiwiZmV0Y2hGbiIsImNyZWF0ZVF1ZXJ5Rm5Db250ZXh0IiwicXVlcnlGbkNvbnRleHQyIiwicXVlcnlGbkNvbnRleHQiLCJwZXJzaXN0ZXIiLCJjcmVhdGVGZXRjaENvbnRleHQiLCJjb250ZXh0MiIsImNvbnRleHQiLCJiZWhhdmlvciIsIm9uRmV0Y2giLCJmZXRjaE1ldGEiLCJvbkVycm9yIiwib25TZXR0bGVkIiwiaW5pdGlhbFByb21pc2UiLCJmbiIsImFib3J0IiwiYmluZCIsIm9uU3VjY2VzcyIsIkVycm9yIiwib25GYWlsIiwiZmFpbHVyZUNvdW50Iiwib25QYXVzZSIsIm9uQ29udGludWUiLCJyZXRyeSIsInJldHJ5RGVsYXkiLCJuZXR3b3JrTW9kZSIsImNhblJ1biIsInN0YXJ0IiwiYWN0aW9uIiwicmVkdWNlciIsImZldGNoRmFpbHVyZUNvdW50IiwiZmV0Y2hGYWlsdXJlUmVhc29uIiwiZmV0Y2hTdGF0ZSIsIkRhdGUiLCJub3ciLCJzdGF0dXMiLCJlcnJvclVwZGF0ZWRBdCIsImJhdGNoIiwiZm9yRWFjaCIsIm9uUXVlcnlVcGRhdGUiLCJpbml0aWFsRGF0YSIsImhhc0RhdGEiLCJpbml0aWFsRGF0YVVwZGF0ZWRBdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#queries = /* @__PURE__ */ new Map();\n    }\n    #queries;\n    build(client, options, state) {\n        const queryKey = options.queryKey;\n        const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n        let query = this.get(queryHash);\n        if (!query) {\n            query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n                client,\n                queryKey,\n                queryHash,\n                options: client.defaultQueryOptions(options),\n                state,\n                defaultOptions: client.getQueryDefaults(queryKey)\n            });\n            this.add(query);\n        }\n        return query;\n    }\n    add(query) {\n        if (!this.#queries.has(query.queryHash)) {\n            this.#queries.set(query.queryHash, query);\n            this.notify({\n                type: \"added\",\n                query\n            });\n        }\n    }\n    remove(query) {\n        const queryInMap = this.#queries.get(query.queryHash);\n        if (queryInMap) {\n            query.destroy();\n            if (queryInMap === query) {\n                this.#queries.delete(query.queryHash);\n            }\n            this.notify({\n                type: \"removed\",\n                query\n            });\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                this.remove(query);\n            });\n        });\n    }\n    get(queryHash) {\n        return this.#queries.get(queryHash);\n    }\n    getAll() {\n        return [\n            ...this.#queries.values()\n        ];\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query));\n    }\n    findAll(filters = {}) {\n        const queries = this.getAll();\n        return Object.keys(filters).length > 0 ? queries.filter((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    onFocus() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onFocus();\n            });\n        });\n    }\n    onOnline() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onOnline();\n            });\n        });\n    }\n};\n //# sourceMappingURL=queryCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n    #queryCache;\n    #mutationCache;\n    #defaultOptions;\n    #queryDefaults;\n    #mutationDefaults;\n    #mountCount;\n    #unsubscribeFocus;\n    #unsubscribeOnline;\n    constructor(config = {}){\n        this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n        this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n        this.#defaultOptions = config.defaultOptions || {};\n        this.#queryDefaults = /* @__PURE__ */ new Map();\n        this.#mutationDefaults = /* @__PURE__ */ new Map();\n        this.#mountCount = 0;\n    }\n    mount() {\n        this.#mountCount++;\n        if (this.#mountCount !== 1) return;\n        this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused)=>{\n            if (focused) {\n                await this.resumePausedMutations();\n                this.#queryCache.onFocus();\n            }\n        });\n        this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online)=>{\n            if (online) {\n                await this.resumePausedMutations();\n                this.#queryCache.onOnline();\n            }\n        });\n    }\n    unmount() {\n        this.#mountCount--;\n        if (this.#mountCount !== 0) return;\n        this.#unsubscribeFocus?.();\n        this.#unsubscribeFocus = void 0;\n        this.#unsubscribeOnline?.();\n        this.#unsubscribeOnline = void 0;\n    }\n    isFetching(filters) {\n        return this.#queryCache.findAll({\n            ...filters,\n            fetchStatus: \"fetching\"\n        }).length;\n    }\n    isMutating(filters) {\n        return this.#mutationCache.findAll({\n            ...filters,\n            status: \"pending\"\n        }).length;\n    }\n    /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */ getQueryData(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state.data;\n    }\n    ensureQueryData(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        const query = this.#queryCache.build(this, defaultedOptions);\n        const cachedData = query.state.data;\n        if (cachedData === void 0) {\n            return this.fetchQuery(options);\n        }\n        if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n            void this.prefetchQuery(defaultedOptions);\n        }\n        return Promise.resolve(cachedData);\n    }\n    getQueriesData(filters) {\n        return this.#queryCache.findAll(filters).map(({ queryKey, state })=>{\n            const data = state.data;\n            return [\n                queryKey,\n                data\n            ];\n        });\n    }\n    setQueryData(queryKey, updater, options) {\n        const defaultedOptions = this.defaultQueryOptions({\n            queryKey\n        });\n        const query = this.#queryCache.get(defaultedOptions.queryHash);\n        const prevData = query?.state.data;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n        if (data === void 0) {\n            return void 0;\n        }\n        return this.#queryCache.build(this, defaultedOptions).setData(data, {\n            ...options,\n            manual: true\n        });\n    }\n    setQueriesData(filters, updater, options) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map(({ queryKey })=>[\n                    queryKey,\n                    this.setQueryData(queryKey, updater, options)\n                ]));\n    }\n    getQueryState(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state;\n    }\n    removeQueries(filters) {\n        const queryCache = this.#queryCache;\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                queryCache.remove(query);\n            });\n        });\n    }\n    resetQueries(filters, options) {\n        const queryCache = this.#queryCache;\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                query.reset();\n            });\n            return this.refetchQueries({\n                type: \"active\",\n                ...filters\n            }, options);\n        });\n    }\n    cancelQueries(filters, cancelOptions = {}) {\n        const defaultedCancelOptions = {\n            revert: true,\n            ...cancelOptions\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map((query)=>query.cancel(defaultedCancelOptions)));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    invalidateQueries(filters, options = {}) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            this.#queryCache.findAll(filters).forEach((query)=>{\n                query.invalidate();\n            });\n            if (filters?.refetchType === \"none\") {\n                return Promise.resolve();\n            }\n            return this.refetchQueries({\n                ...filters,\n                type: filters?.refetchType ?? filters?.type ?? \"active\"\n            }, options);\n        });\n    }\n    refetchQueries(filters, options = {}) {\n        const fetchOptions = {\n            ...options,\n            cancelRefetch: options.cancelRefetch ?? true\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).filter((query)=>!query.isDisabled() && !query.isStatic()).map((query)=>{\n                let promise = query.fetch(void 0, fetchOptions);\n                if (!fetchOptions.throwOnError) {\n                    promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n                }\n                return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n            }));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchQuery(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        if (defaultedOptions.retry === void 0) {\n            defaultedOptions.retry = false;\n        }\n        const query = this.#queryCache.build(this, defaultedOptions);\n        return query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n    }\n    prefetchQuery(options) {\n        return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchInfiniteQuery(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.fetchQuery(options);\n    }\n    prefetchInfiniteQuery(options) {\n        return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    ensureInfiniteQueryData(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.ensureQueryData(options);\n    }\n    resumePausedMutations() {\n        if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n            return this.#mutationCache.resumePausedMutations();\n        }\n        return Promise.resolve();\n    }\n    getQueryCache() {\n        return this.#queryCache;\n    }\n    getMutationCache() {\n        return this.#mutationCache;\n    }\n    getDefaultOptions() {\n        return this.#defaultOptions;\n    }\n    setDefaultOptions(options) {\n        this.#defaultOptions = options;\n    }\n    setQueryDefaults(queryKey, options) {\n        this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n            queryKey,\n            defaultOptions: options\n        });\n    }\n    getQueryDefaults(queryKey) {\n        const defaults = [\n            ...this.#queryDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    setMutationDefaults(mutationKey, options) {\n        this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n            mutationKey,\n            defaultOptions: options\n        });\n    }\n    getMutationDefaults(mutationKey) {\n        const defaults = [\n            ...this.#mutationDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    defaultQueryOptions(options) {\n        if (options._defaulted) {\n            return options;\n        }\n        const defaultedOptions = {\n            ...this.#defaultOptions.queries,\n            ...this.getQueryDefaults(options.queryKey),\n            ...options,\n            _defaulted: true\n        };\n        if (!defaultedOptions.queryHash) {\n            defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n        }\n        if (defaultedOptions.refetchOnReconnect === void 0) {\n            defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n        }\n        if (defaultedOptions.throwOnError === void 0) {\n            defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n        }\n        if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n            defaultedOptions.networkMode = \"offlineFirst\";\n        }\n        if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n            defaultedOptions.enabled = false;\n        }\n        return defaultedOptions;\n    }\n    defaultMutationOptions(options) {\n        if (options?._defaulted) {\n            return options;\n        }\n        return {\n            ...this.#defaultOptions.mutations,\n            ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n            ...options,\n            _defaulted: true\n        };\n    }\n    clear() {\n        this.#queryCache.clear();\n        this.#mutationCache.clear();\n    }\n};\n //# sourceMappingURL=queryClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeUNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLHFCQUFxQjtBQVNEO0FBQ3lCO0FBQ007QUFDRjtBQUNFO0FBQ0E7QUFDZ0I7QUFDbkUsSUFBSWEsY0FBYztJQUNoQixDQUFDQyxVQUFVLENBQUM7SUFDWixDQUFDQyxhQUFhLENBQUM7SUFDZixDQUFDQyxjQUFjLENBQUM7SUFDaEIsQ0FBQ0MsYUFBYSxDQUFDO0lBQ2YsQ0FBQ0MsZ0JBQWdCLENBQUM7SUFDbEIsQ0FBQ0MsVUFBVSxDQUFDO0lBQ1osQ0FBQ0MsZ0JBQWdCLENBQUM7SUFDbEIsQ0FBQ0MsaUJBQWlCLENBQUM7SUFDbkJDLFlBQVlDLFNBQVMsQ0FBQyxDQUFDLENBQUU7UUFDdkIsSUFBSSxDQUFDLENBQUNULFVBQVUsR0FBR1MsT0FBT1QsVUFBVSxJQUFJLElBQUlQLHNEQUFVQTtRQUN0RCxJQUFJLENBQUMsQ0FBQ1EsYUFBYSxHQUFHUSxPQUFPUixhQUFhLElBQUksSUFBSVAsNERBQWFBO1FBQy9ELElBQUksQ0FBQyxDQUFDUSxjQUFjLEdBQUdPLE9BQU9QLGNBQWMsSUFBSSxDQUFDO1FBQ2pELElBQUksQ0FBQyxDQUFDQyxhQUFhLEdBQUcsYUFBYSxHQUFHLElBQUlPO1FBQzFDLElBQUksQ0FBQyxDQUFDTixnQkFBZ0IsR0FBRyxhQUFhLEdBQUcsSUFBSU07UUFDN0MsSUFBSSxDQUFDLENBQUNMLFVBQVUsR0FBRztJQUNyQjtJQUNBTSxRQUFRO1FBQ04sSUFBSSxDQUFDLENBQUNOLFVBQVU7UUFDaEIsSUFBSSxJQUFJLENBQUMsQ0FBQ0EsVUFBVSxLQUFLLEdBQUc7UUFDNUIsSUFBSSxDQUFDLENBQUNDLGdCQUFnQixHQUFHWCwwREFBWUEsQ0FBQ2lCLFNBQVMsQ0FBQyxPQUFPQztZQUNyRCxJQUFJQSxTQUFTO2dCQUNYLE1BQU0sSUFBSSxDQUFDQyxxQkFBcUI7Z0JBQ2hDLElBQUksQ0FBQyxDQUFDZCxVQUFVLENBQUNlLE9BQU87WUFDMUI7UUFDRjtRQUNBLElBQUksQ0FBQyxDQUFDUixpQkFBaUIsR0FBR1gsNERBQWFBLENBQUNnQixTQUFTLENBQUMsT0FBT0k7WUFDdkQsSUFBSUEsUUFBUTtnQkFDVixNQUFNLElBQUksQ0FBQ0YscUJBQXFCO2dCQUNoQyxJQUFJLENBQUMsQ0FBQ2QsVUFBVSxDQUFDaUIsUUFBUTtZQUMzQjtRQUNGO0lBQ0Y7SUFDQUMsVUFBVTtRQUNSLElBQUksQ0FBQyxDQUFDYixVQUFVO1FBQ2hCLElBQUksSUFBSSxDQUFDLENBQUNBLFVBQVUsS0FBSyxHQUFHO1FBQzVCLElBQUksQ0FBQyxDQUFDQyxnQkFBZ0I7UUFDdEIsSUFBSSxDQUFDLENBQUNBLGdCQUFnQixHQUFHLEtBQUs7UUFDOUIsSUFBSSxDQUFDLENBQUNDLGlCQUFpQjtRQUN2QixJQUFJLENBQUMsQ0FBQ0EsaUJBQWlCLEdBQUcsS0FBSztJQUNqQztJQUNBWSxXQUFXQyxPQUFPLEVBQUU7UUFDbEIsT0FBTyxJQUFJLENBQUMsQ0FBQ3BCLFVBQVUsQ0FBQ3FCLE9BQU8sQ0FBQztZQUFFLEdBQUdELE9BQU87WUFBRUUsYUFBYTtRQUFXLEdBQUdDLE1BQU07SUFDakY7SUFDQUMsV0FBV0osT0FBTyxFQUFFO1FBQ2xCLE9BQU8sSUFBSSxDQUFDLENBQUNuQixhQUFhLENBQUNvQixPQUFPLENBQUM7WUFBRSxHQUFHRCxPQUFPO1lBQUVLLFFBQVE7UUFBVSxHQUFHRixNQUFNO0lBQzlFO0lBQ0E7Ozs7OztHQU1DLEdBQ0RHLGFBQWFDLFFBQVEsRUFBRTtRQUNyQixNQUFNQyxVQUFVLElBQUksQ0FBQ0MsbUJBQW1CLENBQUM7WUFBRUY7UUFBUztRQUNwRCxPQUFPLElBQUksQ0FBQyxDQUFDM0IsVUFBVSxDQUFDOEIsR0FBRyxDQUFDRixRQUFRRyxTQUFTLEdBQUdDLE1BQU1DO0lBQ3hEO0lBQ0FDLGdCQUFnQk4sT0FBTyxFQUFFO1FBQ3ZCLE1BQU1PLG1CQUFtQixJQUFJLENBQUNOLG1CQUFtQixDQUFDRDtRQUNsRCxNQUFNUSxRQUFRLElBQUksQ0FBQyxDQUFDcEMsVUFBVSxDQUFDcUMsS0FBSyxDQUFDLElBQUksRUFBRUY7UUFDM0MsTUFBTUcsYUFBYUYsTUFBTUosS0FBSyxDQUFDQyxJQUFJO1FBQ25DLElBQUlLLGVBQWUsS0FBSyxHQUFHO1lBQ3pCLE9BQU8sSUFBSSxDQUFDQyxVQUFVLENBQUNYO1FBQ3pCO1FBQ0EsSUFBSUEsUUFBUVksaUJBQWlCLElBQUlKLE1BQU1LLGFBQWEsQ0FBQ2xELDJEQUFnQkEsQ0FBQzRDLGlCQUFpQk8sU0FBUyxFQUFFTixTQUFTO1lBQ3pHLEtBQUssSUFBSSxDQUFDTyxhQUFhLENBQUNSO1FBQzFCO1FBQ0EsT0FBT1MsUUFBUUMsT0FBTyxDQUFDUDtJQUN6QjtJQUNBUSxlQUFlMUIsT0FBTyxFQUFFO1FBQ3RCLE9BQU8sSUFBSSxDQUFDLENBQUNwQixVQUFVLENBQUNxQixPQUFPLENBQUNELFNBQVMyQixHQUFHLENBQUMsQ0FBQyxFQUFFcEIsUUFBUSxFQUFFSyxLQUFLLEVBQUU7WUFDL0QsTUFBTUMsT0FBT0QsTUFBTUMsSUFBSTtZQUN2QixPQUFPO2dCQUFDTjtnQkFBVU07YUFBSztRQUN6QjtJQUNGO0lBQ0FlLGFBQWFyQixRQUFRLEVBQUVzQixPQUFPLEVBQUVyQixPQUFPLEVBQUU7UUFDdkMsTUFBTU8sbUJBQW1CLElBQUksQ0FBQ04sbUJBQW1CLENBQUM7WUFBRUY7UUFBUztRQUM3RCxNQUFNUyxRQUFRLElBQUksQ0FBQyxDQUFDcEMsVUFBVSxDQUFDOEIsR0FBRyxDQUNoQ0ssaUJBQWlCSixTQUFTO1FBRTVCLE1BQU1tQixXQUFXZCxPQUFPSixNQUFNQztRQUM5QixNQUFNQSxPQUFPL0MsMkRBQWdCQSxDQUFDK0QsU0FBU0M7UUFDdkMsSUFBSWpCLFNBQVMsS0FBSyxHQUFHO1lBQ25CLE9BQU8sS0FBSztRQUNkO1FBQ0EsT0FBTyxJQUFJLENBQUMsQ0FBQ2pDLFVBQVUsQ0FBQ3FDLEtBQUssQ0FBQyxJQUFJLEVBQUVGLGtCQUFrQmdCLE9BQU8sQ0FBQ2xCLE1BQU07WUFBRSxHQUFHTCxPQUFPO1lBQUV3QixRQUFRO1FBQUs7SUFDakc7SUFDQUMsZUFBZWpDLE9BQU8sRUFBRTZCLE9BQU8sRUFBRXJCLE9BQU8sRUFBRTtRQUN4QyxPQUFPL0IsNERBQWFBLENBQUN5RCxLQUFLLENBQ3hCLElBQU0sSUFBSSxDQUFDLENBQUN0RCxVQUFVLENBQUNxQixPQUFPLENBQUNELFNBQVMyQixHQUFHLENBQUMsQ0FBQyxFQUFFcEIsUUFBUSxFQUFFLEdBQUs7b0JBQzVEQTtvQkFDQSxJQUFJLENBQUNxQixZQUFZLENBQUNyQixVQUFVc0IsU0FBU3JCO2lCQUN0QztJQUVMO0lBQ0EyQixjQUFjNUIsUUFBUSxFQUFFO1FBQ3RCLE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQztZQUFFRjtRQUFTO1FBQ3BELE9BQU8sSUFBSSxDQUFDLENBQUMzQixVQUFVLENBQUM4QixHQUFHLENBQ3pCRixRQUFRRyxTQUFTLEdBQ2hCQztJQUNMO0lBQ0F3QixjQUFjcEMsT0FBTyxFQUFFO1FBQ3JCLE1BQU1wQixhQUFhLElBQUksQ0FBQyxDQUFDQSxVQUFVO1FBQ25DSCw0REFBYUEsQ0FBQ3lELEtBQUssQ0FBQztZQUNsQnRELFdBQVdxQixPQUFPLENBQUNELFNBQVNxQyxPQUFPLENBQUMsQ0FBQ3JCO2dCQUNuQ3BDLFdBQVcwRCxNQUFNLENBQUN0QjtZQUNwQjtRQUNGO0lBQ0Y7SUFDQXVCLGFBQWF2QyxPQUFPLEVBQUVRLE9BQU8sRUFBRTtRQUM3QixNQUFNNUIsYUFBYSxJQUFJLENBQUMsQ0FBQ0EsVUFBVTtRQUNuQyxPQUFPSCw0REFBYUEsQ0FBQ3lELEtBQUssQ0FBQztZQUN6QnRELFdBQVdxQixPQUFPLENBQUNELFNBQVNxQyxPQUFPLENBQUMsQ0FBQ3JCO2dCQUNuQ0EsTUFBTXdCLEtBQUs7WUFDYjtZQUNBLE9BQU8sSUFBSSxDQUFDQyxjQUFjLENBQ3hCO2dCQUNFQyxNQUFNO2dCQUNOLEdBQUcxQyxPQUFPO1lBQ1osR0FDQVE7UUFFSjtJQUNGO0lBQ0FtQyxjQUFjM0MsT0FBTyxFQUFFNEMsZ0JBQWdCLENBQUMsQ0FBQyxFQUFFO1FBQ3pDLE1BQU1DLHlCQUF5QjtZQUFFQyxRQUFRO1lBQU0sR0FBR0YsYUFBYTtRQUFDO1FBQ2hFLE1BQU1HLFdBQVd0RSw0REFBYUEsQ0FBQ3lELEtBQUssQ0FDbEMsSUFBTSxJQUFJLENBQUMsQ0FBQ3RELFVBQVUsQ0FBQ3FCLE9BQU8sQ0FBQ0QsU0FBUzJCLEdBQUcsQ0FBQyxDQUFDWCxRQUFVQSxNQUFNZ0MsTUFBTSxDQUFDSDtRQUV0RSxPQUFPckIsUUFBUXlCLEdBQUcsQ0FBQ0YsVUFBVUcsSUFBSSxDQUFDakYsMkNBQUlBLEVBQUVrRixLQUFLLENBQUNsRiwyQ0FBSUE7SUFDcEQ7SUFDQW1GLGtCQUFrQnBELE9BQU8sRUFBRVEsVUFBVSxDQUFDLENBQUMsRUFBRTtRQUN2QyxPQUFPL0IsNERBQWFBLENBQUN5RCxLQUFLLENBQUM7WUFDekIsSUFBSSxDQUFDLENBQUN0RCxVQUFVLENBQUNxQixPQUFPLENBQUNELFNBQVNxQyxPQUFPLENBQUMsQ0FBQ3JCO2dCQUN6Q0EsTUFBTXFDLFVBQVU7WUFDbEI7WUFDQSxJQUFJckQsU0FBU3NELGdCQUFnQixRQUFRO2dCQUNuQyxPQUFPOUIsUUFBUUMsT0FBTztZQUN4QjtZQUNBLE9BQU8sSUFBSSxDQUFDZ0IsY0FBYyxDQUN4QjtnQkFDRSxHQUFHekMsT0FBTztnQkFDVjBDLE1BQU0xQyxTQUFTc0QsZUFBZXRELFNBQVMwQyxRQUFRO1lBQ2pELEdBQ0FsQztRQUVKO0lBQ0Y7SUFDQWlDLGVBQWV6QyxPQUFPLEVBQUVRLFVBQVUsQ0FBQyxDQUFDLEVBQUU7UUFDcEMsTUFBTStDLGVBQWU7WUFDbkIsR0FBRy9DLE9BQU87WUFDVmdELGVBQWVoRCxRQUFRZ0QsYUFBYSxJQUFJO1FBQzFDO1FBQ0EsTUFBTVQsV0FBV3RFLDREQUFhQSxDQUFDeUQsS0FBSyxDQUNsQyxJQUFNLElBQUksQ0FBQyxDQUFDdEQsVUFBVSxDQUFDcUIsT0FBTyxDQUFDRCxTQUFTeUQsTUFBTSxDQUFDLENBQUN6QyxRQUFVLENBQUNBLE1BQU0wQyxVQUFVLE1BQU0sQ0FBQzFDLE1BQU0yQyxRQUFRLElBQUloQyxHQUFHLENBQUMsQ0FBQ1g7Z0JBQ3ZHLElBQUk0QyxVQUFVNUMsTUFBTTZDLEtBQUssQ0FBQyxLQUFLLEdBQUdOO2dCQUNsQyxJQUFJLENBQUNBLGFBQWFPLFlBQVksRUFBRTtvQkFDOUJGLFVBQVVBLFFBQVFULEtBQUssQ0FBQ2xGLDJDQUFJQTtnQkFDOUI7Z0JBQ0EsT0FBTytDLE1BQU1KLEtBQUssQ0FBQ1YsV0FBVyxLQUFLLFdBQVdzQixRQUFRQyxPQUFPLEtBQUttQztZQUNwRTtRQUVGLE9BQU9wQyxRQUFReUIsR0FBRyxDQUFDRixVQUFVRyxJQUFJLENBQUNqRiwyQ0FBSUE7SUFDeEM7SUFDQWtELFdBQVdYLE9BQU8sRUFBRTtRQUNsQixNQUFNTyxtQkFBbUIsSUFBSSxDQUFDTixtQkFBbUIsQ0FBQ0Q7UUFDbEQsSUFBSU8saUJBQWlCZ0QsS0FBSyxLQUFLLEtBQUssR0FBRztZQUNyQ2hELGlCQUFpQmdELEtBQUssR0FBRztRQUMzQjtRQUNBLE1BQU0vQyxRQUFRLElBQUksQ0FBQyxDQUFDcEMsVUFBVSxDQUFDcUMsS0FBSyxDQUFDLElBQUksRUFBRUY7UUFDM0MsT0FBT0MsTUFBTUssYUFBYSxDQUN4QmxELDJEQUFnQkEsQ0FBQzRDLGlCQUFpQk8sU0FBUyxFQUFFTixVQUMzQ0EsTUFBTTZDLEtBQUssQ0FBQzlDLG9CQUFvQlMsUUFBUUMsT0FBTyxDQUFDVCxNQUFNSixLQUFLLENBQUNDLElBQUk7SUFDdEU7SUFDQVUsY0FBY2YsT0FBTyxFQUFFO1FBQ3JCLE9BQU8sSUFBSSxDQUFDVyxVQUFVLENBQUNYLFNBQVMwQyxJQUFJLENBQUNqRiwyQ0FBSUEsRUFBRWtGLEtBQUssQ0FBQ2xGLDJDQUFJQTtJQUN2RDtJQUNBK0YsbUJBQW1CeEQsT0FBTyxFQUFFO1FBQzFCQSxRQUFReUQsUUFBUSxHQUFHdkYsZ0ZBQXFCQSxDQUFDOEIsUUFBUTBELEtBQUs7UUFDdEQsT0FBTyxJQUFJLENBQUMvQyxVQUFVLENBQUNYO0lBQ3pCO0lBQ0EyRCxzQkFBc0IzRCxPQUFPLEVBQUU7UUFDN0IsT0FBTyxJQUFJLENBQUN3RCxrQkFBa0IsQ0FBQ3hELFNBQVMwQyxJQUFJLENBQUNqRiwyQ0FBSUEsRUFBRWtGLEtBQUssQ0FBQ2xGLDJDQUFJQTtJQUMvRDtJQUNBbUcsd0JBQXdCNUQsT0FBTyxFQUFFO1FBQy9CQSxRQUFReUQsUUFBUSxHQUFHdkYsZ0ZBQXFCQSxDQUFDOEIsUUFBUTBELEtBQUs7UUFDdEQsT0FBTyxJQUFJLENBQUNwRCxlQUFlLENBQUNOO0lBQzlCO0lBQ0FkLHdCQUF3QjtRQUN0QixJQUFJbEIsNERBQWFBLENBQUM2RixRQUFRLElBQUk7WUFDNUIsT0FBTyxJQUFJLENBQUMsQ0FBQ3hGLGFBQWEsQ0FBQ2EscUJBQXFCO1FBQ2xEO1FBQ0EsT0FBTzhCLFFBQVFDLE9BQU87SUFDeEI7SUFDQTZDLGdCQUFnQjtRQUNkLE9BQU8sSUFBSSxDQUFDLENBQUMxRixVQUFVO0lBQ3pCO0lBQ0EyRixtQkFBbUI7UUFDakIsT0FBTyxJQUFJLENBQUMsQ0FBQzFGLGFBQWE7SUFDNUI7SUFDQTJGLG9CQUFvQjtRQUNsQixPQUFPLElBQUksQ0FBQyxDQUFDMUYsY0FBYztJQUM3QjtJQUNBMkYsa0JBQWtCakUsT0FBTyxFQUFFO1FBQ3pCLElBQUksQ0FBQyxDQUFDMUIsY0FBYyxHQUFHMEI7SUFDekI7SUFDQWtFLGlCQUFpQm5FLFFBQVEsRUFBRUMsT0FBTyxFQUFFO1FBQ2xDLElBQUksQ0FBQyxDQUFDekIsYUFBYSxDQUFDNEYsR0FBRyxDQUFDNUcsa0RBQU9BLENBQUN3QyxXQUFXO1lBQ3pDQTtZQUNBekIsZ0JBQWdCMEI7UUFDbEI7SUFDRjtJQUNBb0UsaUJBQWlCckUsUUFBUSxFQUFFO1FBQ3pCLE1BQU1zRSxXQUFXO2VBQUksSUFBSSxDQUFDLENBQUM5RixhQUFhLENBQUMrRixNQUFNO1NBQUc7UUFDbEQsTUFBTUMsU0FBUyxDQUFDO1FBQ2hCRixTQUFTeEMsT0FBTyxDQUFDLENBQUMyQztZQUNoQixJQUFJOUcsMERBQWVBLENBQUNxQyxVQUFVeUUsYUFBYXpFLFFBQVEsR0FBRztnQkFDcEQwRSxPQUFPQyxNQUFNLENBQUNILFFBQVFDLGFBQWFsRyxjQUFjO1lBQ25EO1FBQ0Y7UUFDQSxPQUFPaUc7SUFDVDtJQUNBSSxvQkFBb0JDLFdBQVcsRUFBRTVFLE9BQU8sRUFBRTtRQUN4QyxJQUFJLENBQUMsQ0FBQ3hCLGdCQUFnQixDQUFDMkYsR0FBRyxDQUFDNUcsa0RBQU9BLENBQUNxSCxjQUFjO1lBQy9DQTtZQUNBdEcsZ0JBQWdCMEI7UUFDbEI7SUFDRjtJQUNBNkUsb0JBQW9CRCxXQUFXLEVBQUU7UUFDL0IsTUFBTVAsV0FBVztlQUFJLElBQUksQ0FBQyxDQUFDN0YsZ0JBQWdCLENBQUM4RixNQUFNO1NBQUc7UUFDckQsTUFBTUMsU0FBUyxDQUFDO1FBQ2hCRixTQUFTeEMsT0FBTyxDQUFDLENBQUMyQztZQUNoQixJQUFJOUcsMERBQWVBLENBQUNrSCxhQUFhSixhQUFhSSxXQUFXLEdBQUc7Z0JBQzFESCxPQUFPQyxNQUFNLENBQUNILFFBQVFDLGFBQWFsRyxjQUFjO1lBQ25EO1FBQ0Y7UUFDQSxPQUFPaUc7SUFDVDtJQUNBdEUsb0JBQW9CRCxPQUFPLEVBQUU7UUFDM0IsSUFBSUEsUUFBUThFLFVBQVUsRUFBRTtZQUN0QixPQUFPOUU7UUFDVDtRQUNBLE1BQU1PLG1CQUFtQjtZQUN2QixHQUFHLElBQUksQ0FBQyxDQUFDakMsY0FBYyxDQUFDeUcsT0FBTztZQUMvQixHQUFHLElBQUksQ0FBQ1gsZ0JBQWdCLENBQUNwRSxRQUFRRCxRQUFRLENBQUM7WUFDMUMsR0FBR0MsT0FBTztZQUNWOEUsWUFBWTtRQUNkO1FBQ0EsSUFBSSxDQUFDdkUsaUJBQWlCSixTQUFTLEVBQUU7WUFDL0JJLGlCQUFpQkosU0FBUyxHQUFHM0MsZ0VBQXFCQSxDQUNoRCtDLGlCQUFpQlIsUUFBUSxFQUN6QlE7UUFFSjtRQUNBLElBQUlBLGlCQUFpQnlFLGtCQUFrQixLQUFLLEtBQUssR0FBRztZQUNsRHpFLGlCQUFpQnlFLGtCQUFrQixHQUFHekUsaUJBQWlCMEUsV0FBVyxLQUFLO1FBQ3pFO1FBQ0EsSUFBSTFFLGlCQUFpQitDLFlBQVksS0FBSyxLQUFLLEdBQUc7WUFDNUMvQyxpQkFBaUIrQyxZQUFZLEdBQUcsQ0FBQyxDQUFDL0MsaUJBQWlCMkUsUUFBUTtRQUM3RDtRQUNBLElBQUksQ0FBQzNFLGlCQUFpQjBFLFdBQVcsSUFBSTFFLGlCQUFpQjRFLFNBQVMsRUFBRTtZQUMvRDVFLGlCQUFpQjBFLFdBQVcsR0FBRztRQUNqQztRQUNBLElBQUkxRSxpQkFBaUI2RSxPQUFPLEtBQUt4SCxnREFBU0EsRUFBRTtZQUMxQzJDLGlCQUFpQjhFLE9BQU8sR0FBRztRQUM3QjtRQUNBLE9BQU85RTtJQUNUO0lBQ0ErRSx1QkFBdUJ0RixPQUFPLEVBQUU7UUFDOUIsSUFBSUEsU0FBUzhFLFlBQVk7WUFDdkIsT0FBTzlFO1FBQ1Q7UUFDQSxPQUFPO1lBQ0wsR0FBRyxJQUFJLENBQUMsQ0FBQzFCLGNBQWMsQ0FBQ2lILFNBQVM7WUFDakMsR0FBR3ZGLFNBQVM0RSxlQUFlLElBQUksQ0FBQ0MsbUJBQW1CLENBQUM3RSxRQUFRNEUsV0FBVyxDQUFDO1lBQ3hFLEdBQUc1RSxPQUFPO1lBQ1Y4RSxZQUFZO1FBQ2Q7SUFDRjtJQUNBVSxRQUFRO1FBQ04sSUFBSSxDQUFDLENBQUNwSCxVQUFVLENBQUNvSCxLQUFLO1FBQ3RCLElBQUksQ0FBQyxDQUFDbkgsYUFBYSxDQUFDbUgsS0FBSztJQUMzQjtBQUNGO0FBR0UsQ0FDRix1Q0FBdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2V4cGVydC1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeUNsaWVudC5qcz8zMDNkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9xdWVyeUNsaWVudC50c1xuaW1wb3J0IHtcbiAgZnVuY3Rpb25hbFVwZGF0ZSxcbiAgaGFzaEtleSxcbiAgaGFzaFF1ZXJ5S2V5QnlPcHRpb25zLFxuICBub29wLFxuICBwYXJ0aWFsTWF0Y2hLZXksXG4gIHJlc29sdmVTdGFsZVRpbWUsXG4gIHNraXBUb2tlblxufSBmcm9tIFwiLi91dGlscy5qc1wiO1xuaW1wb3J0IHsgUXVlcnlDYWNoZSB9IGZyb20gXCIuL3F1ZXJ5Q2FjaGUuanNcIjtcbmltcG9ydCB7IE11dGF0aW9uQ2FjaGUgfSBmcm9tIFwiLi9tdXRhdGlvbkNhY2hlLmpzXCI7XG5pbXBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tIFwiLi9mb2N1c01hbmFnZXIuanNcIjtcbmltcG9ydCB7IG9ubGluZU1hbmFnZXIgfSBmcm9tIFwiLi9vbmxpbmVNYW5hZ2VyLmpzXCI7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgaW5maW5pdGVRdWVyeUJlaGF2aW9yIH0gZnJvbSBcIi4vaW5maW5pdGVRdWVyeUJlaGF2aW9yLmpzXCI7XG52YXIgUXVlcnlDbGllbnQgPSBjbGFzcyB7XG4gICNxdWVyeUNhY2hlO1xuICAjbXV0YXRpb25DYWNoZTtcbiAgI2RlZmF1bHRPcHRpb25zO1xuICAjcXVlcnlEZWZhdWx0cztcbiAgI211dGF0aW9uRGVmYXVsdHM7XG4gICNtb3VudENvdW50O1xuICAjdW5zdWJzY3JpYmVGb2N1cztcbiAgI3Vuc3Vic2NyaWJlT25saW5lO1xuICBjb25zdHJ1Y3Rvcihjb25maWcgPSB7fSkge1xuICAgIHRoaXMuI3F1ZXJ5Q2FjaGUgPSBjb25maWcucXVlcnlDYWNoZSB8fCBuZXcgUXVlcnlDYWNoZSgpO1xuICAgIHRoaXMuI211dGF0aW9uQ2FjaGUgPSBjb25maWcubXV0YXRpb25DYWNoZSB8fCBuZXcgTXV0YXRpb25DYWNoZSgpO1xuICAgIHRoaXMuI2RlZmF1bHRPcHRpb25zID0gY29uZmlnLmRlZmF1bHRPcHRpb25zIHx8IHt9O1xuICAgIHRoaXMuI3F1ZXJ5RGVmYXVsdHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMuI211dGF0aW9uRGVmYXVsdHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMuI21vdW50Q291bnQgPSAwO1xuICB9XG4gIG1vdW50KCkge1xuICAgIHRoaXMuI21vdW50Q291bnQrKztcbiAgICBpZiAodGhpcy4jbW91bnRDb3VudCAhPT0gMSkgcmV0dXJuO1xuICAgIHRoaXMuI3Vuc3Vic2NyaWJlRm9jdXMgPSBmb2N1c01hbmFnZXIuc3Vic2NyaWJlKGFzeW5jIChmb2N1c2VkKSA9PiB7XG4gICAgICBpZiAoZm9jdXNlZCkge1xuICAgICAgICBhd2FpdCB0aGlzLnJlc3VtZVBhdXNlZE11dGF0aW9ucygpO1xuICAgICAgICB0aGlzLiNxdWVyeUNhY2hlLm9uRm9jdXMoKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICB0aGlzLiN1bnN1YnNjcmliZU9ubGluZSA9IG9ubGluZU1hbmFnZXIuc3Vic2NyaWJlKGFzeW5jIChvbmxpbmUpID0+IHtcbiAgICAgIGlmIChvbmxpbmUpIHtcbiAgICAgICAgYXdhaXQgdGhpcy5yZXN1bWVQYXVzZWRNdXRhdGlvbnMoKTtcbiAgICAgICAgdGhpcy4jcXVlcnlDYWNoZS5vbk9ubGluZSgpO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIHVubW91bnQoKSB7XG4gICAgdGhpcy4jbW91bnRDb3VudC0tO1xuICAgIGlmICh0aGlzLiNtb3VudENvdW50ICE9PSAwKSByZXR1cm47XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVGb2N1cz8uKCk7XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVGb2N1cyA9IHZvaWQgMDtcbiAgICB0aGlzLiN1bnN1YnNjcmliZU9ubGluZT8uKCk7XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVPbmxpbmUgPSB2b2lkIDA7XG4gIH1cbiAgaXNGZXRjaGluZyhmaWx0ZXJzKSB7XG4gICAgcmV0dXJuIHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbCh7IC4uLmZpbHRlcnMsIGZldGNoU3RhdHVzOiBcImZldGNoaW5nXCIgfSkubGVuZ3RoO1xuICB9XG4gIGlzTXV0YXRpbmcoZmlsdGVycykge1xuICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlLmZpbmRBbGwoeyAuLi5maWx0ZXJzLCBzdGF0dXM6IFwicGVuZGluZ1wiIH0pLmxlbmd0aDtcbiAgfVxuICAvKipcbiAgICogSW1wZXJhdGl2ZSAobm9uLXJlYWN0aXZlKSB3YXkgdG8gcmV0cmlldmUgZGF0YSBmb3IgYSBRdWVyeUtleS5cbiAgICogU2hvdWxkIG9ubHkgYmUgdXNlZCBpbiBjYWxsYmFja3Mgb3IgZnVuY3Rpb25zIHdoZXJlIHJlYWRpbmcgdGhlIGxhdGVzdCBkYXRhIGlzIG5lY2Vzc2FyeSwgZS5nLiBmb3Igb3B0aW1pc3RpYyB1cGRhdGVzLlxuICAgKlxuICAgKiBIaW50OiBEbyBub3QgdXNlIHRoaXMgZnVuY3Rpb24gaW5zaWRlIGEgY29tcG9uZW50LCBiZWNhdXNlIGl0IHdvbid0IHJlY2VpdmUgdXBkYXRlcy5cbiAgICogVXNlIGB1c2VRdWVyeWAgdG8gY3JlYXRlIGEgYFF1ZXJ5T2JzZXJ2ZXJgIHRoYXQgc3Vic2NyaWJlcyB0byBjaGFuZ2VzLlxuICAgKi9cbiAgZ2V0UXVlcnlEYXRhKHF1ZXJ5S2V5KSB7XG4gICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMuZGVmYXVsdFF1ZXJ5T3B0aW9ucyh7IHF1ZXJ5S2V5IH0pO1xuICAgIHJldHVybiB0aGlzLiNxdWVyeUNhY2hlLmdldChvcHRpb25zLnF1ZXJ5SGFzaCk/LnN0YXRlLmRhdGE7XG4gIH1cbiAgZW5zdXJlUXVlcnlEYXRhKG9wdGlvbnMpIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRPcHRpb25zID0gdGhpcy5kZWZhdWx0UXVlcnlPcHRpb25zKG9wdGlvbnMpO1xuICAgIGNvbnN0IHF1ZXJ5ID0gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICBjb25zdCBjYWNoZWREYXRhID0gcXVlcnkuc3RhdGUuZGF0YTtcbiAgICBpZiAoY2FjaGVkRGF0YSA9PT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5mZXRjaFF1ZXJ5KG9wdGlvbnMpO1xuICAgIH1cbiAgICBpZiAob3B0aW9ucy5yZXZhbGlkYXRlSWZTdGFsZSAmJiBxdWVyeS5pc1N0YWxlQnlUaW1lKHJlc29sdmVTdGFsZVRpbWUoZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUsIHF1ZXJ5KSkpIHtcbiAgICAgIHZvaWQgdGhpcy5wcmVmZXRjaFF1ZXJ5KGRlZmF1bHRlZE9wdGlvbnMpO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKGNhY2hlZERhdGEpO1xuICB9XG4gIGdldFF1ZXJpZXNEYXRhKGZpbHRlcnMpIHtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLm1hcCgoeyBxdWVyeUtleSwgc3RhdGUgfSkgPT4ge1xuICAgICAgY29uc3QgZGF0YSA9IHN0YXRlLmRhdGE7XG4gICAgICByZXR1cm4gW3F1ZXJ5S2V5LCBkYXRhXTtcbiAgICB9KTtcbiAgfVxuICBzZXRRdWVyeURhdGEocXVlcnlLZXksIHVwZGF0ZXIsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRPcHRpb25zID0gdGhpcy5kZWZhdWx0UXVlcnlPcHRpb25zKHsgcXVlcnlLZXkgfSk7XG4gICAgY29uc3QgcXVlcnkgPSB0aGlzLiNxdWVyeUNhY2hlLmdldChcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNoXG4gICAgKTtcbiAgICBjb25zdCBwcmV2RGF0YSA9IHF1ZXJ5Py5zdGF0ZS5kYXRhO1xuICAgIGNvbnN0IGRhdGEgPSBmdW5jdGlvbmFsVXBkYXRlKHVwZGF0ZXIsIHByZXZEYXRhKTtcbiAgICBpZiAoZGF0YSA9PT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm4gdm9pZCAwO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKS5zZXREYXRhKGRhdGEsIHsgLi4ub3B0aW9ucywgbWFudWFsOiB0cnVlIH0pO1xuICB9XG4gIHNldFF1ZXJpZXNEYXRhKGZpbHRlcnMsIHVwZGF0ZXIsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbm90aWZ5TWFuYWdlci5iYXRjaChcbiAgICAgICgpID0+IHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbChmaWx0ZXJzKS5tYXAoKHsgcXVlcnlLZXkgfSkgPT4gW1xuICAgICAgICBxdWVyeUtleSxcbiAgICAgICAgdGhpcy5zZXRRdWVyeURhdGEocXVlcnlLZXksIHVwZGF0ZXIsIG9wdGlvbnMpXG4gICAgICBdKVxuICAgICk7XG4gIH1cbiAgZ2V0UXVlcnlTdGF0ZShxdWVyeUtleSkge1xuICAgIGNvbnN0IG9wdGlvbnMgPSB0aGlzLmRlZmF1bHRRdWVyeU9wdGlvbnMoeyBxdWVyeUtleSB9KTtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5nZXQoXG4gICAgICBvcHRpb25zLnF1ZXJ5SGFzaFxuICAgICk/LnN0YXRlO1xuICB9XG4gIHJlbW92ZVF1ZXJpZXMoZmlsdGVycykge1xuICAgIGNvbnN0IHF1ZXJ5Q2FjaGUgPSB0aGlzLiNxdWVyeUNhY2hlO1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHF1ZXJ5Q2FjaGUucmVtb3ZlKHF1ZXJ5KTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9XG4gIHJlc2V0UXVlcmllcyhmaWx0ZXJzLCBvcHRpb25zKSB7XG4gICAgY29uc3QgcXVlcnlDYWNoZSA9IHRoaXMuI3F1ZXJ5Q2FjaGU7XG4gICAgcmV0dXJuIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHF1ZXJ5LnJlc2V0KCk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiB0aGlzLnJlZmV0Y2hRdWVyaWVzKFxuICAgICAgICB7XG4gICAgICAgICAgdHlwZTogXCJhY3RpdmVcIixcbiAgICAgICAgICAuLi5maWx0ZXJzXG4gICAgICAgIH0sXG4gICAgICAgIG9wdGlvbnNcbiAgICAgICk7XG4gICAgfSk7XG4gIH1cbiAgY2FuY2VsUXVlcmllcyhmaWx0ZXJzLCBjYW5jZWxPcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRDYW5jZWxPcHRpb25zID0geyByZXZlcnQ6IHRydWUsIC4uLmNhbmNlbE9wdGlvbnMgfTtcbiAgICBjb25zdCBwcm9taXNlcyA9IG5vdGlmeU1hbmFnZXIuYmF0Y2goXG4gICAgICAoKSA9PiB0aGlzLiNxdWVyeUNhY2hlLmZpbmRBbGwoZmlsdGVycykubWFwKChxdWVyeSkgPT4gcXVlcnkuY2FuY2VsKGRlZmF1bHRlZENhbmNlbE9wdGlvbnMpKVxuICAgICk7XG4gICAgcmV0dXJuIFByb21pc2UuYWxsKHByb21pc2VzKS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApO1xuICB9XG4gIGludmFsaWRhdGVRdWVyaWVzKGZpbHRlcnMsIG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiBub3RpZnlNYW5hZ2VyLmJhdGNoKCgpID0+IHtcbiAgICAgIHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbChmaWx0ZXJzKS5mb3JFYWNoKChxdWVyeSkgPT4ge1xuICAgICAgICBxdWVyeS5pbnZhbGlkYXRlKCk7XG4gICAgICB9KTtcbiAgICAgIGlmIChmaWx0ZXJzPy5yZWZldGNoVHlwZSA9PT0gXCJub25lXCIpIHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRoaXMucmVmZXRjaFF1ZXJpZXMoXG4gICAgICAgIHtcbiAgICAgICAgICAuLi5maWx0ZXJzLFxuICAgICAgICAgIHR5cGU6IGZpbHRlcnM/LnJlZmV0Y2hUeXBlID8/IGZpbHRlcnM/LnR5cGUgPz8gXCJhY3RpdmVcIlxuICAgICAgICB9LFxuICAgICAgICBvcHRpb25zXG4gICAgICApO1xuICAgIH0pO1xuICB9XG4gIHJlZmV0Y2hRdWVyaWVzKGZpbHRlcnMsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IGZldGNoT3B0aW9ucyA9IHtcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBjYW5jZWxSZWZldGNoOiBvcHRpb25zLmNhbmNlbFJlZmV0Y2ggPz8gdHJ1ZVxuICAgIH07XG4gICAgY29uc3QgcHJvbWlzZXMgPSBub3RpZnlNYW5hZ2VyLmJhdGNoKFxuICAgICAgKCkgPT4gdGhpcy4jcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZpbHRlcigocXVlcnkpID0+ICFxdWVyeS5pc0Rpc2FibGVkKCkgJiYgIXF1ZXJ5LmlzU3RhdGljKCkpLm1hcCgocXVlcnkpID0+IHtcbiAgICAgICAgbGV0IHByb21pc2UgPSBxdWVyeS5mZXRjaCh2b2lkIDAsIGZldGNoT3B0aW9ucyk7XG4gICAgICAgIGlmICghZmV0Y2hPcHRpb25zLnRocm93T25FcnJvcikge1xuICAgICAgICAgIHByb21pc2UgPSBwcm9taXNlLmNhdGNoKG5vb3ApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBxdWVyeS5zdGF0ZS5mZXRjaFN0YXR1cyA9PT0gXCJwYXVzZWRcIiA/IFByb21pc2UucmVzb2x2ZSgpIDogcHJvbWlzZTtcbiAgICAgIH0pXG4gICAgKTtcbiAgICByZXR1cm4gUHJvbWlzZS5hbGwocHJvbWlzZXMpLnRoZW4obm9vcCk7XG4gIH1cbiAgZmV0Y2hRdWVyeShvcHRpb25zKSB7XG4gICAgY29uc3QgZGVmYXVsdGVkT3B0aW9ucyA9IHRoaXMuZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKTtcbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5yZXRyeSA9PT0gdm9pZCAwKSB7XG4gICAgICBkZWZhdWx0ZWRPcHRpb25zLnJldHJ5ID0gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IHF1ZXJ5ID0gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICByZXR1cm4gcXVlcnkuaXNTdGFsZUJ5VGltZShcbiAgICAgIHJlc29sdmVTdGFsZVRpbWUoZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUsIHF1ZXJ5KVxuICAgICkgPyBxdWVyeS5mZXRjaChkZWZhdWx0ZWRPcHRpb25zKSA6IFByb21pc2UucmVzb2x2ZShxdWVyeS5zdGF0ZS5kYXRhKTtcbiAgfVxuICBwcmVmZXRjaFF1ZXJ5KG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5mZXRjaFF1ZXJ5KG9wdGlvbnMpLnRoZW4obm9vcCkuY2F0Y2gobm9vcCk7XG4gIH1cbiAgZmV0Y2hJbmZpbml0ZVF1ZXJ5KG9wdGlvbnMpIHtcbiAgICBvcHRpb25zLmJlaGF2aW9yID0gaW5maW5pdGVRdWVyeUJlaGF2aW9yKG9wdGlvbnMucGFnZXMpO1xuICAgIHJldHVybiB0aGlzLmZldGNoUXVlcnkob3B0aW9ucyk7XG4gIH1cbiAgcHJlZmV0Y2hJbmZpbml0ZVF1ZXJ5KG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5mZXRjaEluZmluaXRlUXVlcnkob3B0aW9ucykudGhlbihub29wKS5jYXRjaChub29wKTtcbiAgfVxuICBlbnN1cmVJbmZpbml0ZVF1ZXJ5RGF0YShvcHRpb25zKSB7XG4gICAgb3B0aW9ucy5iZWhhdmlvciA9IGluZmluaXRlUXVlcnlCZWhhdmlvcihvcHRpb25zLnBhZ2VzKTtcbiAgICByZXR1cm4gdGhpcy5lbnN1cmVRdWVyeURhdGEob3B0aW9ucyk7XG4gIH1cbiAgcmVzdW1lUGF1c2VkTXV0YXRpb25zKCkge1xuICAgIGlmIChvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkpIHtcbiAgICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlLnJlc3VtZVBhdXNlZE11dGF0aW9ucygpO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gIH1cbiAgZ2V0UXVlcnlDYWNoZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZTtcbiAgfVxuICBnZXRNdXRhdGlvbkNhY2hlKCkge1xuICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlO1xuICB9XG4gIGdldERlZmF1bHRPcHRpb25zKCkge1xuICAgIHJldHVybiB0aGlzLiNkZWZhdWx0T3B0aW9ucztcbiAgfVxuICBzZXREZWZhdWx0T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdGhpcy4jZGVmYXVsdE9wdGlvbnMgPSBvcHRpb25zO1xuICB9XG4gIHNldFF1ZXJ5RGVmYXVsdHMocXVlcnlLZXksIG9wdGlvbnMpIHtcbiAgICB0aGlzLiNxdWVyeURlZmF1bHRzLnNldChoYXNoS2V5KHF1ZXJ5S2V5KSwge1xuICAgICAgcXVlcnlLZXksXG4gICAgICBkZWZhdWx0T3B0aW9uczogb3B0aW9uc1xuICAgIH0pO1xuICB9XG4gIGdldFF1ZXJ5RGVmYXVsdHMocXVlcnlLZXkpIHtcbiAgICBjb25zdCBkZWZhdWx0cyA9IFsuLi50aGlzLiNxdWVyeURlZmF1bHRzLnZhbHVlcygpXTtcbiAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICBkZWZhdWx0cy5mb3JFYWNoKChxdWVyeURlZmF1bHQpID0+IHtcbiAgICAgIGlmIChwYXJ0aWFsTWF0Y2hLZXkocXVlcnlLZXksIHF1ZXJ5RGVmYXVsdC5xdWVyeUtleSkpIHtcbiAgICAgICAgT2JqZWN0LmFzc2lnbihyZXN1bHQsIHF1ZXJ5RGVmYXVsdC5kZWZhdWx0T3B0aW9ucyk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBzZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5LCBvcHRpb25zKSB7XG4gICAgdGhpcy4jbXV0YXRpb25EZWZhdWx0cy5zZXQoaGFzaEtleShtdXRhdGlvbktleSksIHtcbiAgICAgIG11dGF0aW9uS2V5LFxuICAgICAgZGVmYXVsdE9wdGlvbnM6IG9wdGlvbnNcbiAgICB9KTtcbiAgfVxuICBnZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5KSB7XG4gICAgY29uc3QgZGVmYXVsdHMgPSBbLi4udGhpcy4jbXV0YXRpb25EZWZhdWx0cy52YWx1ZXMoKV07XG4gICAgY29uc3QgcmVzdWx0ID0ge307XG4gICAgZGVmYXVsdHMuZm9yRWFjaCgocXVlcnlEZWZhdWx0KSA9PiB7XG4gICAgICBpZiAocGFydGlhbE1hdGNoS2V5KG11dGF0aW9uS2V5LCBxdWVyeURlZmF1bHQubXV0YXRpb25LZXkpKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24ocmVzdWx0LCBxdWVyeURlZmF1bHQuZGVmYXVsdE9wdGlvbnMpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgaWYgKG9wdGlvbnMuX2RlZmF1bHRlZCkge1xuICAgICAgcmV0dXJuIG9wdGlvbnM7XG4gICAgfVxuICAgIGNvbnN0IGRlZmF1bHRlZE9wdGlvbnMgPSB7XG4gICAgICAuLi50aGlzLiNkZWZhdWx0T3B0aW9ucy5xdWVyaWVzLFxuICAgICAgLi4udGhpcy5nZXRRdWVyeURlZmF1bHRzKG9wdGlvbnMucXVlcnlLZXkpLFxuICAgICAgLi4ub3B0aW9ucyxcbiAgICAgIF9kZWZhdWx0ZWQ6IHRydWVcbiAgICB9O1xuICAgIGlmICghZGVmYXVsdGVkT3B0aW9ucy5xdWVyeUhhc2gpIHtcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNoID0gaGFzaFF1ZXJ5S2V5QnlPcHRpb25zKFxuICAgICAgICBkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5S2V5LFxuICAgICAgICBkZWZhdWx0ZWRPcHRpb25zXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5yZWZldGNoT25SZWNvbm5lY3QgPT09IHZvaWQgMCkge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5yZWZldGNoT25SZWNvbm5lY3QgPSBkZWZhdWx0ZWRPcHRpb25zLm5ldHdvcmtNb2RlICE9PSBcImFsd2F5c1wiO1xuICAgIH1cbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy50aHJvd09uRXJyb3IgPT09IHZvaWQgMCkge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy50aHJvd09uRXJyb3IgPSAhIWRlZmF1bHRlZE9wdGlvbnMuc3VzcGVuc2U7XG4gICAgfVxuICAgIGlmICghZGVmYXVsdGVkT3B0aW9ucy5uZXR3b3JrTW9kZSAmJiBkZWZhdWx0ZWRPcHRpb25zLnBlcnNpc3Rlcikge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5uZXR3b3JrTW9kZSA9IFwib2ZmbGluZUZpcnN0XCI7XG4gICAgfVxuICAgIGlmIChkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5Rm4gPT09IHNraXBUb2tlbikge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5lbmFibGVkID0gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiBkZWZhdWx0ZWRPcHRpb25zO1xuICB9XG4gIGRlZmF1bHRNdXRhdGlvbk9wdGlvbnMob3B0aW9ucykge1xuICAgIGlmIChvcHRpb25zPy5fZGVmYXVsdGVkKSB7XG4gICAgICByZXR1cm4gb3B0aW9ucztcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnRoaXMuI2RlZmF1bHRPcHRpb25zLm11dGF0aW9ucyxcbiAgICAgIC4uLm9wdGlvbnM/Lm11dGF0aW9uS2V5ICYmIHRoaXMuZ2V0TXV0YXRpb25EZWZhdWx0cyhvcHRpb25zLm11dGF0aW9uS2V5KSxcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBfZGVmYXVsdGVkOiB0cnVlXG4gICAgfTtcbiAgfVxuICBjbGVhcigpIHtcbiAgICB0aGlzLiNxdWVyeUNhY2hlLmNsZWFyKCk7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZS5jbGVhcigpO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgUXVlcnlDbGllbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeUNsaWVudC5qcy5tYXAiXSwibmFtZXMiOlsiZnVuY3Rpb25hbFVwZGF0ZSIsImhhc2hLZXkiLCJoYXNoUXVlcnlLZXlCeU9wdGlvbnMiLCJub29wIiwicGFydGlhbE1hdGNoS2V5IiwicmVzb2x2ZVN0YWxlVGltZSIsInNraXBUb2tlbiIsIlF1ZXJ5Q2FjaGUiLCJNdXRhdGlvbkNhY2hlIiwiZm9jdXNNYW5hZ2VyIiwib25saW5lTWFuYWdlciIsIm5vdGlmeU1hbmFnZXIiLCJpbmZpbml0ZVF1ZXJ5QmVoYXZpb3IiLCJRdWVyeUNsaWVudCIsInF1ZXJ5Q2FjaGUiLCJtdXRhdGlvbkNhY2hlIiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyeURlZmF1bHRzIiwibXV0YXRpb25EZWZhdWx0cyIsIm1vdW50Q291bnQiLCJ1bnN1YnNjcmliZUZvY3VzIiwidW5zdWJzY3JpYmVPbmxpbmUiLCJjb25zdHJ1Y3RvciIsImNvbmZpZyIsIk1hcCIsIm1vdW50Iiwic3Vic2NyaWJlIiwiZm9jdXNlZCIsInJlc3VtZVBhdXNlZE11dGF0aW9ucyIsIm9uRm9jdXMiLCJvbmxpbmUiLCJvbk9ubGluZSIsInVubW91bnQiLCJpc0ZldGNoaW5nIiwiZmlsdGVycyIsImZpbmRBbGwiLCJmZXRjaFN0YXR1cyIsImxlbmd0aCIsImlzTXV0YXRpbmciLCJzdGF0dXMiLCJnZXRRdWVyeURhdGEiLCJxdWVyeUtleSIsIm9wdGlvbnMiLCJkZWZhdWx0UXVlcnlPcHRpb25zIiwiZ2V0IiwicXVlcnlIYXNoIiwic3RhdGUiLCJkYXRhIiwiZW5zdXJlUXVlcnlEYXRhIiwiZGVmYXVsdGVkT3B0aW9ucyIsInF1ZXJ5IiwiYnVpbGQiLCJjYWNoZWREYXRhIiwiZmV0Y2hRdWVyeSIsInJldmFsaWRhdGVJZlN0YWxlIiwiaXNTdGFsZUJ5VGltZSIsInN0YWxlVGltZSIsInByZWZldGNoUXVlcnkiLCJQcm9taXNlIiwicmVzb2x2ZSIsImdldFF1ZXJpZXNEYXRhIiwibWFwIiwic2V0UXVlcnlEYXRhIiwidXBkYXRlciIsInByZXZEYXRhIiwic2V0RGF0YSIsIm1hbnVhbCIsInNldFF1ZXJpZXNEYXRhIiwiYmF0Y2giLCJnZXRRdWVyeVN0YXRlIiwicmVtb3ZlUXVlcmllcyIsImZvckVhY2giLCJyZW1vdmUiLCJyZXNldFF1ZXJpZXMiLCJyZXNldCIsInJlZmV0Y2hRdWVyaWVzIiwidHlwZSIsImNhbmNlbFF1ZXJpZXMiLCJjYW5jZWxPcHRpb25zIiwiZGVmYXVsdGVkQ2FuY2VsT3B0aW9ucyIsInJldmVydCIsInByb21pc2VzIiwiY2FuY2VsIiwiYWxsIiwidGhlbiIsImNhdGNoIiwiaW52YWxpZGF0ZVF1ZXJpZXMiLCJpbnZhbGlkYXRlIiwicmVmZXRjaFR5cGUiLCJmZXRjaE9wdGlvbnMiLCJjYW5jZWxSZWZldGNoIiwiZmlsdGVyIiwiaXNEaXNhYmxlZCIsImlzU3RhdGljIiwicHJvbWlzZSIsImZldGNoIiwidGhyb3dPbkVycm9yIiwicmV0cnkiLCJmZXRjaEluZmluaXRlUXVlcnkiLCJiZWhhdmlvciIsInBhZ2VzIiwicHJlZmV0Y2hJbmZpbml0ZVF1ZXJ5IiwiZW5zdXJlSW5maW5pdGVRdWVyeURhdGEiLCJpc09ubGluZSIsImdldFF1ZXJ5Q2FjaGUiLCJnZXRNdXRhdGlvbkNhY2hlIiwiZ2V0RGVmYXVsdE9wdGlvbnMiLCJzZXREZWZhdWx0T3B0aW9ucyIsInNldFF1ZXJ5RGVmYXVsdHMiLCJzZXQiLCJnZXRRdWVyeURlZmF1bHRzIiwiZGVmYXVsdHMiLCJ2YWx1ZXMiLCJyZXN1bHQiLCJxdWVyeURlZmF1bHQiLCJPYmplY3QiLCJhc3NpZ24iLCJzZXRNdXRhdGlvbkRlZmF1bHRzIiwibXV0YXRpb25LZXkiLCJnZXRNdXRhdGlvbkRlZmF1bHRzIiwiX2RlZmF1bHRlZCIsInF1ZXJpZXMiLCJyZWZldGNoT25SZWNvbm5lY3QiLCJuZXR3b3JrTW9kZSIsInN1c3BlbnNlIiwicGVyc2lzdGVyIiwicXVlcnlGbiIsImVuYWJsZWQiLCJkZWZhdWx0TXV0YXRpb25PcHRpb25zIiwibXV0YXRpb25zIiwiY2xlYXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n    #gcTimeout;\n    destroy() {\n        this.clearGcTimeout();\n    }\n    scheduleGc() {\n        this.clearGcTimeout();\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n            this.#gcTimeout = setTimeout(()=>{\n                this.optionalRemove();\n            }, this.gcTime);\n        }\n    }\n    updateGcTime(newGcTime) {\n        this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3));\n    }\n    clearGcTimeout() {\n        if (this.#gcTimeout) {\n            clearTimeout(this.#gcTimeout);\n            this.#gcTimeout = void 0;\n        }\n    }\n};\n //# sourceMappingURL=removable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n    return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n    return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n    constructor(options){\n        super(\"CancelledError\");\n        this.revert = options?.revert;\n        this.silent = options?.silent;\n    }\n};\nfunction isCancelledError(value) {\n    return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n    let isRetryCancelled = false;\n    let failureCount = 0;\n    let isResolved = false;\n    let continueFn;\n    const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    const cancel = (cancelOptions)=>{\n        if (!isResolved) {\n            reject(new CancelledError(cancelOptions));\n            config.abort?.();\n        }\n    };\n    const cancelRetry = ()=>{\n        isRetryCancelled = true;\n    };\n    const continueRetry = ()=>{\n        isRetryCancelled = false;\n    };\n    const canContinue = ()=>_focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n    const canStart = ()=>canFetch(config.networkMode) && config.canRun();\n    const resolve = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onSuccess?.(value);\n            continueFn?.();\n            thenable.resolve(value);\n        }\n    };\n    const reject = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onError?.(value);\n            continueFn?.();\n            thenable.reject(value);\n        }\n    };\n    const pause = ()=>{\n        return new Promise((continueResolve)=>{\n            continueFn = (value)=>{\n                if (isResolved || canContinue()) {\n                    continueResolve(value);\n                }\n            };\n            config.onPause?.();\n        }).then(()=>{\n            continueFn = void 0;\n            if (!isResolved) {\n                config.onContinue?.();\n            }\n        });\n    };\n    const run = ()=>{\n        if (isResolved) {\n            return;\n        }\n        let promiseOrValue;\n        const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n        try {\n            promiseOrValue = initialPromise ?? config.fn();\n        } catch (error) {\n            promiseOrValue = Promise.reject(error);\n        }\n        Promise.resolve(promiseOrValue).then(resolve).catch((error)=>{\n            if (isResolved) {\n                return;\n            }\n            const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n            const retryDelay = config.retryDelay ?? defaultRetryDelay;\n            const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n            const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n            if (isRetryCancelled || !shouldRetry) {\n                reject(error);\n                return;\n            }\n            failureCount++;\n            config.onFail?.(failureCount, error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(()=>{\n                return canContinue() ? void 0 : pause();\n            }).then(()=>{\n                if (isRetryCancelled) {\n                    reject(error);\n                } else {\n                    run();\n                }\n            });\n        });\n    };\n    return {\n        promise: thenable,\n        cancel,\n        continue: ()=>{\n            continueFn?.();\n            return thenable;\n        },\n        cancelRetry,\n        continueRetry,\n        canStart,\n        start: ()=>{\n            if (canStart()) {\n                run();\n            } else {\n                pause().then(run);\n            }\n            return thenable;\n        }\n    };\n}\n //# sourceMappingURL=retryer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9yZXRyeWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEsaUJBQWlCO0FBQ2dDO0FBQ0U7QUFDSDtBQUNIO0FBQzdDLFNBQVNLLGtCQUFrQkMsWUFBWTtJQUNyQyxPQUFPQyxLQUFLQyxHQUFHLENBQUMsTUFBTSxLQUFLRixjQUFjO0FBQzNDO0FBQ0EsU0FBU0csU0FBU0MsV0FBVztJQUMzQixPQUFPLENBQUNBLGVBQWUsUUFBTyxNQUFPLFdBQVdULDREQUFhQSxDQUFDVSxRQUFRLEtBQUs7QUFDN0U7QUFDQSxJQUFJQyxpQkFBaUIsY0FBY0M7SUFDakNDLFlBQVlDLE9BQU8sQ0FBRTtRQUNuQixLQUFLLENBQUM7UUFDTixJQUFJLENBQUNDLE1BQU0sR0FBR0QsU0FBU0M7UUFDdkIsSUFBSSxDQUFDQyxNQUFNLEdBQUdGLFNBQVNFO0lBQ3pCO0FBQ0Y7QUFDQSxTQUFTQyxpQkFBaUJDLEtBQUs7SUFDN0IsT0FBT0EsaUJBQWlCUDtBQUMxQjtBQUNBLFNBQVNRLGNBQWNDLE1BQU07SUFDM0IsSUFBSUMsbUJBQW1CO0lBQ3ZCLElBQUloQixlQUFlO0lBQ25CLElBQUlpQixhQUFhO0lBQ2pCLElBQUlDO0lBQ0osTUFBTUMsV0FBV3ZCLDZEQUFlQTtJQUNoQyxNQUFNd0IsU0FBUyxDQUFDQztRQUNkLElBQUksQ0FBQ0osWUFBWTtZQUNmSyxPQUFPLElBQUloQixlQUFlZTtZQUMxQk4sT0FBT1EsS0FBSztRQUNkO0lBQ0Y7SUFDQSxNQUFNQyxjQUFjO1FBQ2xCUixtQkFBbUI7SUFDckI7SUFDQSxNQUFNUyxnQkFBZ0I7UUFDcEJULG1CQUFtQjtJQUNyQjtJQUNBLE1BQU1VLGNBQWMsSUFBTWhDLDBEQUFZQSxDQUFDaUMsU0FBUyxNQUFPWixDQUFBQSxPQUFPWCxXQUFXLEtBQUssWUFBWVQsNERBQWFBLENBQUNVLFFBQVEsRUFBQyxLQUFNVSxPQUFPYSxNQUFNO0lBQ3BJLE1BQU1DLFdBQVcsSUFBTTFCLFNBQVNZLE9BQU9YLFdBQVcsS0FBS1csT0FBT2EsTUFBTTtJQUNwRSxNQUFNRSxVQUFVLENBQUNqQjtRQUNmLElBQUksQ0FBQ0ksWUFBWTtZQUNmQSxhQUFhO1lBQ2JGLE9BQU9nQixTQUFTLEdBQUdsQjtZQUNuQks7WUFDQUMsU0FBU1csT0FBTyxDQUFDakI7UUFDbkI7SUFDRjtJQUNBLE1BQU1TLFNBQVMsQ0FBQ1Q7UUFDZCxJQUFJLENBQUNJLFlBQVk7WUFDZkEsYUFBYTtZQUNiRixPQUFPaUIsT0FBTyxHQUFHbkI7WUFDakJLO1lBQ0FDLFNBQVNHLE1BQU0sQ0FBQ1Q7UUFDbEI7SUFDRjtJQUNBLE1BQU1vQixRQUFRO1FBQ1osT0FBTyxJQUFJQyxRQUFRLENBQUNDO1lBQ2xCakIsYUFBYSxDQUFDTDtnQkFDWixJQUFJSSxjQUFjUyxlQUFlO29CQUMvQlMsZ0JBQWdCdEI7Z0JBQ2xCO1lBQ0Y7WUFDQUUsT0FBT3FCLE9BQU87UUFDaEIsR0FBR0MsSUFBSSxDQUFDO1lBQ05uQixhQUFhLEtBQUs7WUFDbEIsSUFBSSxDQUFDRCxZQUFZO2dCQUNmRixPQUFPdUIsVUFBVTtZQUNuQjtRQUNGO0lBQ0Y7SUFDQSxNQUFNQyxNQUFNO1FBQ1YsSUFBSXRCLFlBQVk7WUFDZDtRQUNGO1FBQ0EsSUFBSXVCO1FBQ0osTUFBTUMsaUJBQWlCekMsaUJBQWlCLElBQUllLE9BQU8wQixjQUFjLEdBQUcsS0FBSztRQUN6RSxJQUFJO1lBQ0ZELGlCQUFpQkMsa0JBQWtCMUIsT0FBTzJCLEVBQUU7UUFDOUMsRUFBRSxPQUFPQyxPQUFPO1lBQ2RILGlCQUFpQk4sUUFBUVosTUFBTSxDQUFDcUI7UUFDbEM7UUFDQVQsUUFBUUosT0FBTyxDQUFDVSxnQkFBZ0JILElBQUksQ0FBQ1AsU0FBU2MsS0FBSyxDQUFDLENBQUNEO1lBQ25ELElBQUkxQixZQUFZO2dCQUNkO1lBQ0Y7WUFDQSxNQUFNNEIsUUFBUTlCLE9BQU84QixLQUFLLElBQUtoRCxDQUFBQSwrQ0FBUUEsR0FBRyxJQUFJO1lBQzlDLE1BQU1pRCxhQUFhL0IsT0FBTytCLFVBQVUsSUFBSS9DO1lBQ3hDLE1BQU1nRCxRQUFRLE9BQU9ELGVBQWUsYUFBYUEsV0FBVzlDLGNBQWMyQyxTQUFTRztZQUNuRixNQUFNRSxjQUFjSCxVQUFVLFFBQVEsT0FBT0EsVUFBVSxZQUFZN0MsZUFBZTZDLFNBQVMsT0FBT0EsVUFBVSxjQUFjQSxNQUFNN0MsY0FBYzJDO1lBQzlJLElBQUkzQixvQkFBb0IsQ0FBQ2dDLGFBQWE7Z0JBQ3BDMUIsT0FBT3FCO2dCQUNQO1lBQ0Y7WUFDQTNDO1lBQ0FlLE9BQU9rQyxNQUFNLEdBQUdqRCxjQUFjMkM7WUFDOUI3QyxnREFBS0EsQ0FBQ2lELE9BQU9WLElBQUksQ0FBQztnQkFDaEIsT0FBT1gsZ0JBQWdCLEtBQUssSUFBSU87WUFDbEMsR0FBR0ksSUFBSSxDQUFDO2dCQUNOLElBQUlyQixrQkFBa0I7b0JBQ3BCTSxPQUFPcUI7Z0JBQ1QsT0FBTztvQkFDTEo7Z0JBQ0Y7WUFDRjtRQUNGO0lBQ0Y7SUFDQSxPQUFPO1FBQ0xXLFNBQVMvQjtRQUNUQztRQUNBK0IsVUFBVTtZQUNSakM7WUFDQSxPQUFPQztRQUNUO1FBQ0FLO1FBQ0FDO1FBQ0FJO1FBQ0F1QixPQUFPO1lBQ0wsSUFBSXZCLFlBQVk7Z0JBQ2RVO1lBQ0YsT0FBTztnQkFDTE4sUUFBUUksSUFBSSxDQUFDRTtZQUNmO1lBQ0EsT0FBT3BCO1FBQ1Q7SUFDRjtBQUNGO0FBTUUsQ0FDRixtQ0FBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2V4cGVydC1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9yZXRyeWVyLmpzPzE1NGQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JldHJ5ZXIudHNcbmltcG9ydCB7IGZvY3VzTWFuYWdlciB9IGZyb20gXCIuL2ZvY3VzTWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgb25saW5lTWFuYWdlciB9IGZyb20gXCIuL29ubGluZU1hbmFnZXIuanNcIjtcbmltcG9ydCB7IHBlbmRpbmdUaGVuYWJsZSB9IGZyb20gXCIuL3RoZW5hYmxlLmpzXCI7XG5pbXBvcnQgeyBpc1NlcnZlciwgc2xlZXAgfSBmcm9tIFwiLi91dGlscy5qc1wiO1xuZnVuY3Rpb24gZGVmYXVsdFJldHJ5RGVsYXkoZmFpbHVyZUNvdW50KSB7XG4gIHJldHVybiBNYXRoLm1pbigxZTMgKiAyICoqIGZhaWx1cmVDb3VudCwgM2U0KTtcbn1cbmZ1bmN0aW9uIGNhbkZldGNoKG5ldHdvcmtNb2RlKSB7XG4gIHJldHVybiAobmV0d29ya01vZGUgPz8gXCJvbmxpbmVcIikgPT09IFwib25saW5lXCIgPyBvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkgOiB0cnVlO1xufVxudmFyIENhbmNlbGxlZEVycm9yID0gY2xhc3MgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICBzdXBlcihcIkNhbmNlbGxlZEVycm9yXCIpO1xuICAgIHRoaXMucmV2ZXJ0ID0gb3B0aW9ucz8ucmV2ZXJ0O1xuICAgIHRoaXMuc2lsZW50ID0gb3B0aW9ucz8uc2lsZW50O1xuICB9XG59O1xuZnVuY3Rpb24gaXNDYW5jZWxsZWRFcnJvcih2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBDYW5jZWxsZWRFcnJvcjtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVJldHJ5ZXIoY29uZmlnKSB7XG4gIGxldCBpc1JldHJ5Q2FuY2VsbGVkID0gZmFsc2U7XG4gIGxldCBmYWlsdXJlQ291bnQgPSAwO1xuICBsZXQgaXNSZXNvbHZlZCA9IGZhbHNlO1xuICBsZXQgY29udGludWVGbjtcbiAgY29uc3QgdGhlbmFibGUgPSBwZW5kaW5nVGhlbmFibGUoKTtcbiAgY29uc3QgY2FuY2VsID0gKGNhbmNlbE9wdGlvbnMpID0+IHtcbiAgICBpZiAoIWlzUmVzb2x2ZWQpIHtcbiAgICAgIHJlamVjdChuZXcgQ2FuY2VsbGVkRXJyb3IoY2FuY2VsT3B0aW9ucykpO1xuICAgICAgY29uZmlnLmFib3J0Py4oKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IGNhbmNlbFJldHJ5ID0gKCkgPT4ge1xuICAgIGlzUmV0cnlDYW5jZWxsZWQgPSB0cnVlO1xuICB9O1xuICBjb25zdCBjb250aW51ZVJldHJ5ID0gKCkgPT4ge1xuICAgIGlzUmV0cnlDYW5jZWxsZWQgPSBmYWxzZTtcbiAgfTtcbiAgY29uc3QgY2FuQ29udGludWUgPSAoKSA9PiBmb2N1c01hbmFnZXIuaXNGb2N1c2VkKCkgJiYgKGNvbmZpZy5uZXR3b3JrTW9kZSA9PT0gXCJhbHdheXNcIiB8fCBvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkpICYmIGNvbmZpZy5jYW5SdW4oKTtcbiAgY29uc3QgY2FuU3RhcnQgPSAoKSA9PiBjYW5GZXRjaChjb25maWcubmV0d29ya01vZGUpICYmIGNvbmZpZy5jYW5SdW4oKTtcbiAgY29uc3QgcmVzb2x2ZSA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICghaXNSZXNvbHZlZCkge1xuICAgICAgaXNSZXNvbHZlZCA9IHRydWU7XG4gICAgICBjb25maWcub25TdWNjZXNzPy4odmFsdWUpO1xuICAgICAgY29udGludWVGbj8uKCk7XG4gICAgICB0aGVuYWJsZS5yZXNvbHZlKHZhbHVlKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IHJlamVjdCA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICghaXNSZXNvbHZlZCkge1xuICAgICAgaXNSZXNvbHZlZCA9IHRydWU7XG4gICAgICBjb25maWcub25FcnJvcj8uKHZhbHVlKTtcbiAgICAgIGNvbnRpbnVlRm4/LigpO1xuICAgICAgdGhlbmFibGUucmVqZWN0KHZhbHVlKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IHBhdXNlID0gKCkgPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgoY29udGludWVSZXNvbHZlKSA9PiB7XG4gICAgICBjb250aW51ZUZuID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGlmIChpc1Jlc29sdmVkIHx8IGNhbkNvbnRpbnVlKCkpIHtcbiAgICAgICAgICBjb250aW51ZVJlc29sdmUodmFsdWUpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgY29uZmlnLm9uUGF1c2U/LigpO1xuICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgY29udGludWVGbiA9IHZvaWQgMDtcbiAgICAgIGlmICghaXNSZXNvbHZlZCkge1xuICAgICAgICBjb25maWcub25Db250aW51ZT8uKCk7XG4gICAgICB9XG4gICAgfSk7XG4gIH07XG4gIGNvbnN0IHJ1biA9ICgpID0+IHtcbiAgICBpZiAoaXNSZXNvbHZlZCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBsZXQgcHJvbWlzZU9yVmFsdWU7XG4gICAgY29uc3QgaW5pdGlhbFByb21pc2UgPSBmYWlsdXJlQ291bnQgPT09IDAgPyBjb25maWcuaW5pdGlhbFByb21pc2UgOiB2b2lkIDA7XG4gICAgdHJ5IHtcbiAgICAgIHByb21pc2VPclZhbHVlID0gaW5pdGlhbFByb21pc2UgPz8gY29uZmlnLmZuKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHByb21pc2VPclZhbHVlID0gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICAgIH1cbiAgICBQcm9taXNlLnJlc29sdmUocHJvbWlzZU9yVmFsdWUpLnRoZW4ocmVzb2x2ZSkuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICBpZiAoaXNSZXNvbHZlZCkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBjb25zdCByZXRyeSA9IGNvbmZpZy5yZXRyeSA/PyAoaXNTZXJ2ZXIgPyAwIDogMyk7XG4gICAgICBjb25zdCByZXRyeURlbGF5ID0gY29uZmlnLnJldHJ5RGVsYXkgPz8gZGVmYXVsdFJldHJ5RGVsYXk7XG4gICAgICBjb25zdCBkZWxheSA9IHR5cGVvZiByZXRyeURlbGF5ID09PSBcImZ1bmN0aW9uXCIgPyByZXRyeURlbGF5KGZhaWx1cmVDb3VudCwgZXJyb3IpIDogcmV0cnlEZWxheTtcbiAgICAgIGNvbnN0IHNob3VsZFJldHJ5ID0gcmV0cnkgPT09IHRydWUgfHwgdHlwZW9mIHJldHJ5ID09PSBcIm51bWJlclwiICYmIGZhaWx1cmVDb3VudCA8IHJldHJ5IHx8IHR5cGVvZiByZXRyeSA9PT0gXCJmdW5jdGlvblwiICYmIHJldHJ5KGZhaWx1cmVDb3VudCwgZXJyb3IpO1xuICAgICAgaWYgKGlzUmV0cnlDYW5jZWxsZWQgfHwgIXNob3VsZFJldHJ5KSB7XG4gICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGZhaWx1cmVDb3VudCsrO1xuICAgICAgY29uZmlnLm9uRmFpbD8uKGZhaWx1cmVDb3VudCwgZXJyb3IpO1xuICAgICAgc2xlZXAoZGVsYXkpLnRoZW4oKCkgPT4ge1xuICAgICAgICByZXR1cm4gY2FuQ29udGludWUoKSA/IHZvaWQgMCA6IHBhdXNlKCk7XG4gICAgICB9KS50aGVuKCgpID0+IHtcbiAgICAgICAgaWYgKGlzUmV0cnlDYW5jZWxsZWQpIHtcbiAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJ1bigpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBwcm9taXNlOiB0aGVuYWJsZSxcbiAgICBjYW5jZWwsXG4gICAgY29udGludWU6ICgpID0+IHtcbiAgICAgIGNvbnRpbnVlRm4/LigpO1xuICAgICAgcmV0dXJuIHRoZW5hYmxlO1xuICAgIH0sXG4gICAgY2FuY2VsUmV0cnksXG4gICAgY29udGludWVSZXRyeSxcbiAgICBjYW5TdGFydCxcbiAgICBzdGFydDogKCkgPT4ge1xuICAgICAgaWYgKGNhblN0YXJ0KCkpIHtcbiAgICAgICAgcnVuKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBwYXVzZSgpLnRoZW4ocnVuKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0aGVuYWJsZTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBDYW5jZWxsZWRFcnJvcixcbiAgY2FuRmV0Y2gsXG4gIGNyZWF0ZVJldHJ5ZXIsXG4gIGlzQ2FuY2VsbGVkRXJyb3Jcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXRyeWVyLmpzLm1hcCJdLCJuYW1lcyI6WyJmb2N1c01hbmFnZXIiLCJvbmxpbmVNYW5hZ2VyIiwicGVuZGluZ1RoZW5hYmxlIiwiaXNTZXJ2ZXIiLCJzbGVlcCIsImRlZmF1bHRSZXRyeURlbGF5IiwiZmFpbHVyZUNvdW50IiwiTWF0aCIsIm1pbiIsImNhbkZldGNoIiwibmV0d29ya01vZGUiLCJpc09ubGluZSIsIkNhbmNlbGxlZEVycm9yIiwiRXJyb3IiLCJjb25zdHJ1Y3RvciIsIm9wdGlvbnMiLCJyZXZlcnQiLCJzaWxlbnQiLCJpc0NhbmNlbGxlZEVycm9yIiwidmFsdWUiLCJjcmVhdGVSZXRyeWVyIiwiY29uZmlnIiwiaXNSZXRyeUNhbmNlbGxlZCIsImlzUmVzb2x2ZWQiLCJjb250aW51ZUZuIiwidGhlbmFibGUiLCJjYW5jZWwiLCJjYW5jZWxPcHRpb25zIiwicmVqZWN0IiwiYWJvcnQiLCJjYW5jZWxSZXRyeSIsImNvbnRpbnVlUmV0cnkiLCJjYW5Db250aW51ZSIsImlzRm9jdXNlZCIsImNhblJ1biIsImNhblN0YXJ0IiwicmVzb2x2ZSIsIm9uU3VjY2VzcyIsIm9uRXJyb3IiLCJwYXVzZSIsIlByb21pc2UiLCJjb250aW51ZVJlc29sdmUiLCJvblBhdXNlIiwidGhlbiIsIm9uQ29udGludWUiLCJydW4iLCJwcm9taXNlT3JWYWx1ZSIsImluaXRpYWxQcm9taXNlIiwiZm4iLCJlcnJvciIsImNhdGNoIiwicmV0cnkiLCJyZXRyeURlbGF5IiwiZGVsYXkiLCJzaG91bGRSZXRyeSIsIm9uRmFpbCIsInByb21pc2UiLCJjb250aW51ZSIsInN0YXJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n    constructor(){\n        this.listeners = /* @__PURE__ */ new Set();\n        this.subscribe = this.subscribe.bind(this);\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        this.onSubscribe();\n        return ()=>{\n            this.listeners.delete(listener);\n            this.onUnsubscribe();\n        };\n    }\n    hasListeners() {\n        return this.listeners.size > 0;\n    }\n    onSubscribe() {}\n    onUnsubscribe() {}\n};\n //# sourceMappingURL=subscribable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9zdWJzY3JpYmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHNCQUFzQjtBQUN0QixJQUFJQSxlQUFlO0lBQ2pCQyxhQUFjO1FBQ1osSUFBSSxDQUFDQyxTQUFTLEdBQUcsYUFBYSxHQUFHLElBQUlDO1FBQ3JDLElBQUksQ0FBQ0MsU0FBUyxHQUFHLElBQUksQ0FBQ0EsU0FBUyxDQUFDQyxJQUFJLENBQUMsSUFBSTtJQUMzQztJQUNBRCxVQUFVRSxRQUFRLEVBQUU7UUFDbEIsSUFBSSxDQUFDSixTQUFTLENBQUNLLEdBQUcsQ0FBQ0Q7UUFDbkIsSUFBSSxDQUFDRSxXQUFXO1FBQ2hCLE9BQU87WUFDTCxJQUFJLENBQUNOLFNBQVMsQ0FBQ08sTUFBTSxDQUFDSDtZQUN0QixJQUFJLENBQUNJLGFBQWE7UUFDcEI7SUFDRjtJQUNBQyxlQUFlO1FBQ2IsT0FBTyxJQUFJLENBQUNULFNBQVMsQ0FBQ1UsSUFBSSxHQUFHO0lBQy9CO0lBQ0FKLGNBQWMsQ0FDZDtJQUNBRSxnQkFBZ0IsQ0FDaEI7QUFDRjtBQUdFLENBQ0Ysd0NBQXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9leHBlcnQtZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vc3Vic2NyaWJhYmxlLmpzP2RiM2EiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3N1YnNjcmliYWJsZS50c1xudmFyIFN1YnNjcmliYWJsZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gdGhpcy5zdWJzY3JpYmUuYmluZCh0aGlzKTtcbiAgfVxuICBzdWJzY3JpYmUobGlzdGVuZXIpIHtcbiAgICB0aGlzLmxpc3RlbmVycy5hZGQobGlzdGVuZXIpO1xuICAgIHRoaXMub25TdWJzY3JpYmUoKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyKTtcbiAgICAgIHRoaXMub25VbnN1YnNjcmliZSgpO1xuICAgIH07XG4gIH1cbiAgaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5zaXplID4gMDtcbiAgfVxuICBvblN1YnNjcmliZSgpIHtcbiAgfVxuICBvblVuc3Vic2NyaWJlKCkge1xuICB9XG59O1xuZXhwb3J0IHtcbiAgU3Vic2NyaWJhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Vic2NyaWJhYmxlLmpzLm1hcCJdLCJuYW1lcyI6WyJTdWJzY3JpYmFibGUiLCJjb25zdHJ1Y3RvciIsImxpc3RlbmVycyIsIlNldCIsInN1YnNjcmliZSIsImJpbmQiLCJsaXN0ZW5lciIsImFkZCIsIm9uU3Vic2NyaWJlIiwiZGVsZXRlIiwib25VbnN1YnNjcmliZSIsImhhc0xpc3RlbmVycyIsInNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n    let resolve;\n    let reject;\n    const thenable = new Promise((_resolve, _reject)=>{\n        resolve = _resolve;\n        reject = _reject;\n    });\n    thenable.status = \"pending\";\n    thenable.catch(()=>{});\n    function finalize(data) {\n        Object.assign(thenable, data);\n        delete thenable.resolve;\n        delete thenable.reject;\n    }\n    thenable.resolve = (value)=>{\n        finalize({\n            status: \"fulfilled\",\n            value\n        });\n        resolve(value);\n    };\n    thenable.reject = (reason)=>{\n        finalize({\n            status: \"rejected\",\n            reason\n        });\n        reject(reason);\n    };\n    return thenable;\n}\nfunction tryResolveSync(promise) {\n    let data;\n    promise.then((result)=>{\n        data = result;\n        return result;\n    }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n    if (data !== void 0) {\n        return {\n            data\n        };\n    }\n    return void 0;\n}\n //# sourceMappingURL=thenable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer =  true || 0;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n    return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n    return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n    return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n    return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n    return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n    const { type = \"all\", exact, fetchStatus, predicate, queryKey, stale } = filters;\n    if (queryKey) {\n        if (exact) {\n            if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n                return false;\n            }\n        } else if (!partialMatchKey(query.queryKey, queryKey)) {\n            return false;\n        }\n    }\n    if (type !== \"all\") {\n        const isActive = query.isActive();\n        if (type === \"active\" && !isActive) {\n            return false;\n        }\n        if (type === \"inactive\" && isActive) {\n            return false;\n        }\n    }\n    if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n        return false;\n    }\n    if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n        return false;\n    }\n    if (predicate && !predicate(query)) {\n        return false;\n    }\n    return true;\n}\nfunction matchMutation(filters, mutation) {\n    const { exact, status, predicate, mutationKey } = filters;\n    if (mutationKey) {\n        if (!mutation.options.mutationKey) {\n            return false;\n        }\n        if (exact) {\n            if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n                return false;\n            }\n        } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n            return false;\n        }\n    }\n    if (status && mutation.state.status !== status) {\n        return false;\n    }\n    if (predicate && !predicate(mutation)) {\n        return false;\n    }\n    return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n    const hashFn = options?.queryKeyHashFn || hashKey;\n    return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n    return JSON.stringify(queryKey, (_, val)=>isPlainObject(val) ? Object.keys(val).sort().reduce((result, key)=>{\n            result[key] = val[key];\n            return result;\n        }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n        return Object.keys(b).every((key)=>partialMatchKey(a[key], b[key]));\n    }\n    return false;\n}\nfunction replaceEqualDeep(a, b) {\n    if (a === b) {\n        return a;\n    }\n    const array = isPlainArray(a) && isPlainArray(b);\n    if (array || isPlainObject(a) && isPlainObject(b)) {\n        const aItems = array ? a : Object.keys(a);\n        const aSize = aItems.length;\n        const bItems = array ? b : Object.keys(b);\n        const bSize = bItems.length;\n        const copy = array ? [] : {};\n        const aItemsSet = new Set(aItems);\n        let equalItems = 0;\n        for(let i = 0; i < bSize; i++){\n            const key = array ? i : bItems[i];\n            if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n                copy[key] = void 0;\n                equalItems++;\n            } else {\n                copy[key] = replaceEqualDeep(a[key], b[key]);\n                if (copy[key] === a[key] && a[key] !== void 0) {\n                    equalItems++;\n                }\n            }\n        }\n        return aSize === bSize && equalItems === aSize ? a : copy;\n    }\n    return b;\n}\nfunction shallowEqualObjects(a, b) {\n    if (!b || Object.keys(a).length !== Object.keys(b).length) {\n        return false;\n    }\n    for(const key in a){\n        if (a[key] !== b[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isPlainArray(value) {\n    return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n    if (!hasObjectPrototype(o)) {\n        return false;\n    }\n    const ctor = o.constructor;\n    if (ctor === void 0) {\n        return true;\n    }\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot)) {\n        return false;\n    }\n    if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n        return false;\n    }\n    if (Object.getPrototypeOf(o) !== Object.prototype) {\n        return false;\n    }\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n    return new Promise((resolve)=>{\n        setTimeout(resolve, timeout);\n    });\n}\nfunction replaceData(prevData, data, options) {\n    if (typeof options.structuralSharing === \"function\") {\n        return options.structuralSharing(prevData, data);\n    } else if (options.structuralSharing !== false) {\n        if (true) {\n            try {\n                return replaceEqualDeep(prevData, data);\n            } catch (error) {\n                console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n                throw error;\n            }\n        }\n        return replaceEqualDeep(prevData, data);\n    }\n    return data;\n}\nfunction keepPreviousData(previousData) {\n    return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n    const newItems = [\n        ...items,\n        item\n    ];\n    return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n    const newItems = [\n        item,\n        ...items\n    ];\n    return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n    if (true) {\n        if (options.queryFn === skipToken) {\n            console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n        }\n    }\n    if (!options.queryFn && fetchOptions?.initialPromise) {\n        return ()=>fetchOptions.initialPromise;\n    }\n    if (!options.queryFn || options.queryFn === skipToken) {\n        return ()=>Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n    }\n    return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n    if (typeof throwOnError === \"function\") {\n        return throwOnError(...params);\n    }\n    return !!throwOnError;\n}\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;