{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "useSafeAreaFrame", "useSafeAreaInsets", "getDefaultHeaderHeight", "HeaderBackground", "HeaderShownContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warnIfHeaderStylesDefined", "styles", "Object", "keys", "for<PERSON>ach", "styleProp", "value", "console", "warn", "undefined", "Header", "props", "insets", "frame", "isParentHeaderShown", "useContext", "hasDynamicIsland", "OS", "top", "statusBarHeight", "layout", "modal", "title", "headerTitle", "customTitle", "headerTitleAlign", "select", "ios", "default", "headerLeft", "headerLeftLabelVisible", "headerTransparent", "headerTintColor", "headerBackground", "headerRight", "headerTitleAllowFontScaling", "titleAllowFontScaling", "headerTitleStyle", "titleStyle", "headerLeftContainerStyle", "leftContainerStyle", "headerRightContainerStyle", "rightContainerStyle", "headerTitleContainerStyle", "titleContainerStyle", "headerBackgroundContainerStyle", "backgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerShadowVisible", "headerPressColor", "headerPressOpacity", "headerStatusBarHeight", "defaultHeight", "height", "minHeight", "maxHeight", "backgroundColor", "borderBottomColor", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderBottomWidth", "borderColor", "borderEndColor", "borderEndWidth", "borderLeftColor", "borderLeftWidth", "borderRadius", "borderRightColor", "borderRightWidth", "borderStartColor", "borderStartWidth", "borderStyle", "borderTopColor", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "borderTopWidth", "borderWidth", "boxShadow", "elevation", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "opacity", "transform", "unsafeStyles", "flatten", "process", "env", "NODE_ENV", "safeStyles", "backgroundStyle", "leftButton", "tintColor", "pressColor", "pressOpacity", "labelVisible", "rightB<PERSON>on", "absoluteFill", "zIndex", "style", "content", "left", "expand", "marginStart", "max<PERSON><PERSON><PERSON>", "width", "Math", "max", "right", "children", "allowFontScaling", "marginEnd", "create", "flex", "flexDirection", "alignItems", "marginHorizontal", "justifyContent", "flexGrow", "flexBasis"], "sourceRoot": "../../../src", "sources": ["Header/Header.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAC9E,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAGvC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,WAAW,MAAM,eAAe;AAiBvC,MAAMC,yBAAyB,GAAIC,MAA2B,IAAK;EACjEC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAEC,SAAS,IAAK;IACzC,MAAMC,KAAK,GAAGL,MAAM,CAACI,SAAS,CAAC;IAE/B,IAAIA,SAAS,KAAK,UAAU,IAAIC,KAAK,KAAK,UAAU,EAAE;MACpDC,OAAO,CAACC,IAAI,CACV,iJAAiJ,CAClJ;IACH,CAAC,MAAM,IAAIF,KAAK,KAAKG,SAAS,EAAE;MAC9BF,OAAO,CAACC,IAAI,CACT,GAAEH,SAAU,yBAAwBC,KAAM,sCAAqC,CACjF;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAe,SAASI,MAAM,CAACC,KAAY,EAAE;EAC3C,MAAMC,MAAM,GAAGjB,iBAAiB,EAAE;EAClC,MAAMkB,KAAK,GAAGnB,gBAAgB,EAAE;EAEhC,MAAMoB,mBAAmB,GAAGzB,KAAK,CAAC0B,UAAU,CAACjB,kBAAkB,CAAC;;EAEhE;EACA,MAAMkB,gBAAgB,GAAGzB,QAAQ,CAAC0B,EAAE,KAAK,KAAK,IAAIL,MAAM,CAACM,GAAG,GAAG,EAAE;EACjE,MAAMC,eAAe,GAAGH,gBAAgB,GAAGJ,MAAM,CAACM,GAAG,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG;EAEtE,MAAM;IACJE,MAAM,GAAGP,KAAK;IACdQ,KAAK,GAAG,KAAK;IACbC,KAAK;IACLC,WAAW,EAAEC,WAAW;IACxBC,gBAAgB,GAAGlC,QAAQ,CAACmC,MAAM,CAAC;MACjCC,GAAG,EAAE,QAAQ;MACbC,OAAO,EAAE;IACX,CAAC,CAAC;IACFC,UAAU;IACVC,sBAAsB;IACtBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,WAAW;IACXC,2BAA2B,EAAEC,qBAAqB;IAClDC,gBAAgB,EAAEC,UAAU;IAC5BC,wBAAwB,EAAEC,kBAAkB;IAC5CC,yBAAyB,EAAEC,mBAAmB;IAC9CC,yBAAyB,EAAEC,mBAAmB;IAC9CC,8BAA8B,EAAEC,wBAAwB;IACxDC,WAAW,EAAEC,iBAAiB;IAC9BC,mBAAmB;IACnBC,gBAAgB;IAChBC,kBAAkB;IAClBC,qBAAqB,GAAGtC,mBAAmB,GAAG,CAAC,GAAGK;EACpD,CAAC,GAAGR,KAAK;EAET,MAAM0C,aAAa,GAAGzD,sBAAsB,CAC1CwB,MAAM,EACNC,KAAK,EACL+B,qBAAqB,CACtB;EAED,MAAM;IACJE,MAAM,GAAGD,aAAa;IACtBE,SAAS;IACTC,SAAS;IACTC,eAAe;IACfC,iBAAiB;IACjBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,uBAAuB;IACvBC,iBAAiB;IACjBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,kBAAkB;IAClBC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACX;IACAC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,OAAO;IACPC,SAAS;IACT,GAAGC;EACL,CAAC,GAAGlG,UAAU,CAACmG,OAAO,CAAC3C,iBAAiB,IAAI,CAAC,CAAC,CAAc;EAE5D,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC9F,yBAAyB,CAAC0F,YAAY,CAAC;EACzC;EAEA,MAAMK,UAAqB,GAAG;IAC5BtC,eAAe;IACfC,iBAAiB;IACjBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,uBAAuB;IACvBC,iBAAiB;IACjBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,kBAAkB;IAClBC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACX;IACAC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,OAAO;IACPC;EACF,CAAC;;EAED;EACA;EACA;EACA,KAAK,MAAMpF,SAAS,IAAI0F,UAAU,EAAE;IAClC;IACA,IAAIA,UAAU,CAAC1F,SAAS,CAAC,KAAKI,SAAS,EAAE;MACvC;MACA;MACA,OAAOsF,UAAU,CAAC1F,SAAS,CAAC;IAC9B;EACF;EAEA,MAAM2F,eAAe,GAAG,CACtBD,UAAU,EACV9C,mBAAmB,KAAK,KAAK,IAAI;IAC/BkC,SAAS,EAAE,CAAC;IACZG,aAAa,EAAE,CAAC;IAChBvB,iBAAiB,EAAE;EACrB,CAAC,CACF;EAED,MAAMkC,UAAU,GAAGpE,UAAU,GACzBA,UAAU,CAAC;IACTqE,SAAS,EAAElE,eAAe;IAC1BmE,UAAU,EAAEjD,gBAAgB;IAC5BkD,YAAY,EAAEjD,kBAAkB;IAChCkD,YAAY,EAAEvE;EAChB,CAAC,CAAC,GACF,IAAI;EAER,MAAMwE,WAAW,GAAGpE,WAAW,GAC3BA,WAAW,CAAC;IACVgE,SAAS,EAAElE,eAAe;IAC1BmE,UAAU,EAAEjD,gBAAgB;IAC5BkD,YAAY,EAAEjD;EAChB,CAAC,CAAC,GACF,IAAI;EAER,MAAM5B,WAAW,GACf,OAAOC,WAAW,KAAK,UAAU,GAC5Bb,KAA+C,iBAC9C,oBAAC,WAAW,EAAKA,KAAK,CACvB,GACDa,WAAW;EAEjB,oBACE,oBAAC,KAAK,CAAC,QAAQ,qBACb,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLhC,UAAU,CAAC+G,YAAY,EACvB;MAAEC,MAAM,EAAE;IAAE,CAAC,EACb1D,wBAAwB;EACxB,GAEDb,gBAAgB,GACfA,gBAAgB,CAAC;IAAEwE,KAAK,EAAET;EAAgB,CAAC,CAAC,GAC1CjE,iBAAiB,GAAG,IAAI,gBAC1B,oBAAC,gBAAgB;IAAC,KAAK,EAAEiE;EAAgB,EAC1C,CACa,eAChB,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CAAC;MAAE1C,MAAM;MAAEC,SAAS;MAAEC,SAAS;MAAEgC,OAAO;MAAEC;IAAU,CAAC;EAAE,gBAE9D,oBAAC,IAAI;IAAC,aAAa,EAAC,MAAM;IAAC,KAAK,EAAE;MAAEnC,MAAM,EAAEF;IAAsB;EAAE,EAAG,eACvE,oBAAC,IAAI;IAAC,aAAa,EAAC,UAAU;IAAC,KAAK,EAAEnD,MAAM,CAACyG;EAAQ,gBACnD,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLzG,MAAM,CAAC0G,IAAI,EACXlF,gBAAgB,KAAK,QAAQ,IAAIxB,MAAM,CAAC2G,MAAM,EAC9C;MAAEC,WAAW,EAAEjG,MAAM,CAAC+F;IAAK,CAAC,EAC5BnE,kBAAkB;EAClB,GAEDyD,UAAU,CACG,eAChB,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLhG,MAAM,CAACqB,KAAK,EACZ;MACE;MACAwF,QAAQ,EACNrF,gBAAgB,KAAK,QAAQ,GACzBL,MAAM,CAAC2F,KAAK,GACZ,CAAC,CAACd,UAAU,GACRnE,sBAAsB,KAAK,KAAK,GAC9B,EAAE,GACF,EAAE,GACJ,EAAE,IACJkF,IAAI,CAACC,GAAG,CAACrG,MAAM,CAAC+F,IAAI,EAAE/F,MAAM,CAACsG,KAAK,CAAC,IACnC,CAAC,GACH9F,MAAM,CAAC2F,KAAK,IACX,CAACd,UAAU,GAAG,EAAE,GAAG,EAAE,KACnBK,WAAW,GAAG,EAAE,GAAG,EAAE,CAAC,GACvB1F,MAAM,CAAC+F,IAAI,GACX/F,MAAM,CAACsG,KAAK;IACtB,CAAC,EACDtE,mBAAmB;EACnB,GAEDrB,WAAW,CAAC;IACX4F,QAAQ,EAAE7F,KAAK;IACf8F,gBAAgB,EAAEhF,qBAAqB;IACvC8D,SAAS,EAAElE,eAAe;IAC1ByE,KAAK,EAAEnE;EACT,CAAC,CAAC,CACY,eAChB,oBAAC,QAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLrC,MAAM,CAACiH,KAAK,EACZjH,MAAM,CAAC2G,MAAM,EACb;MAAES,SAAS,EAAEzG,MAAM,CAACsG;IAAM,CAAC,EAC3BxE,mBAAmB;EACnB,GAED4D,WAAW,CACE,CACX,CACO,CACD;AAErB;AAEA,MAAMrG,MAAM,GAAGT,UAAU,CAAC8H,MAAM,CAAC;EAC/BZ,OAAO,EAAE;IACPa,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACDnG,KAAK,EAAE;IACLoG,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDhB,IAAI,EAAE;IACJgB,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDP,KAAK,EAAE;IACLS,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDb,MAAM,EAAE;IACNgB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE;EACb;AACF,CAAC,CAAC"}