{"version": 3, "names": ["warnIfHeaderStylesDefined", "styles", "Object", "keys", "for<PERSON>ach", "styleProp", "value", "console", "warn", "undefined", "Header", "props", "insets", "useSafeAreaInsets", "frame", "useSafeAreaFrame", "isParentHeaderShown", "React", "useContext", "HeaderShownContext", "hasDynamicIsland", "Platform", "OS", "top", "statusBarHeight", "layout", "modal", "title", "headerTitle", "customTitle", "headerTitleAlign", "select", "ios", "default", "headerLeft", "headerLeftLabelVisible", "headerTransparent", "headerTintColor", "headerBackground", "headerRight", "headerTitleAllowFontScaling", "titleAllowFontScaling", "headerTitleStyle", "titleStyle", "headerLeftContainerStyle", "leftContainerStyle", "headerRightContainerStyle", "rightContainerStyle", "headerTitleContainerStyle", "titleContainerStyle", "headerBackgroundContainerStyle", "backgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerShadowVisible", "headerPressColor", "headerPressOpacity", "headerStatusBarHeight", "defaultHeight", "getDefaultHeaderHeight", "height", "minHeight", "maxHeight", "backgroundColor", "borderBottomColor", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderBottomWidth", "borderColor", "borderEndColor", "borderEndWidth", "borderLeftColor", "borderLeftWidth", "borderRadius", "borderRightColor", "borderRightWidth", "borderStartColor", "borderStartWidth", "borderStyle", "borderTopColor", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "borderTopWidth", "borderWidth", "boxShadow", "elevation", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "opacity", "transform", "unsafeStyles", "StyleSheet", "flatten", "process", "env", "NODE_ENV", "safeStyles", "backgroundStyle", "leftButton", "tintColor", "pressColor", "pressOpacity", "labelVisible", "rightB<PERSON>on", "absoluteFill", "zIndex", "style", "content", "left", "expand", "marginStart", "max<PERSON><PERSON><PERSON>", "width", "Math", "max", "right", "children", "allowFontScaling", "marginEnd", "create", "flex", "flexDirection", "alignItems", "marginHorizontal", "justifyContent", "flexGrow", "flexBasis"], "sourceRoot": "../../../src", "sources": ["Header/Header.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AAAwC;AAAA;AAAA;AAiBxC,MAAMA,yBAAyB,GAAIC,MAA2B,IAAK;EACjEC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAEC,SAAS,IAAK;IACzC,MAAMC,KAAK,GAAGL,MAAM,CAACI,SAAS,CAAC;IAE/B,IAAIA,SAAS,KAAK,UAAU,IAAIC,KAAK,KAAK,UAAU,EAAE;MACpDC,OAAO,CAACC,IAAI,CACV,iJAAiJ,CAClJ;IACH,CAAC,MAAM,IAAIF,KAAK,KAAKG,SAAS,EAAE;MAC9BF,OAAO,CAACC,IAAI,CACT,GAAEH,SAAU,yBAAwBC,KAAM,sCAAqC,CACjF;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AAEc,SAASI,MAAM,CAACC,KAAY,EAAE;EAC3C,MAAMC,MAAM,GAAG,IAAAC,6CAAiB,GAAE;EAClC,MAAMC,KAAK,GAAG,IAAAC,4CAAgB,GAAE;EAEhC,MAAMC,mBAAmB,GAAGC,KAAK,CAACC,UAAU,CAACC,2BAAkB,CAAC;;EAEhE;EACA,MAAMC,gBAAgB,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIV,MAAM,CAACW,GAAG,GAAG,EAAE;EACjE,MAAMC,eAAe,GAAGJ,gBAAgB,GAAGR,MAAM,CAACW,GAAG,GAAG,CAAC,GAAGX,MAAM,CAACW,GAAG;EAEtE,MAAM;IACJE,MAAM,GAAGX,KAAK;IACdY,KAAK,GAAG,KAAK;IACbC,KAAK;IACLC,WAAW,EAAEC,WAAW;IACxBC,gBAAgB,GAAGT,qBAAQ,CAACU,MAAM,CAAC;MACjCC,GAAG,EAAE,QAAQ;MACbC,OAAO,EAAE;IACX,CAAC,CAAC;IACFC,UAAU;IACVC,sBAAsB;IACtBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,WAAW;IACXC,2BAA2B,EAAEC,qBAAqB;IAClDC,gBAAgB,EAAEC,UAAU;IAC5BC,wBAAwB,EAAEC,kBAAkB;IAC5CC,yBAAyB,EAAEC,mBAAmB;IAC9CC,yBAAyB,EAAEC,mBAAmB;IAC9CC,8BAA8B,EAAEC,wBAAwB;IACxDC,WAAW,EAAEC,iBAAiB;IAC9BC,mBAAmB;IACnBC,gBAAgB;IAChBC,kBAAkB;IAClBC,qBAAqB,GAAGzC,mBAAmB,GAAG,CAAC,GAAGQ;EACpD,CAAC,GAAGb,KAAK;EAET,MAAM+C,aAAa,GAAG,IAAAC,+BAAsB,EAC1ClC,MAAM,EACNC,KAAK,EACL+B,qBAAqB,CACtB;EAED,MAAM;IACJG,MAAM,GAAGF,aAAa;IACtBG,SAAS;IACTC,SAAS;IACTC,eAAe;IACfC,iBAAiB;IACjBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,uBAAuB;IACvBC,iBAAiB;IACjBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,kBAAkB;IAClBC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACX;IACAC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,OAAO;IACPC,SAAS;IACT,GAAGC;EACL,CAAC,GAAGC,uBAAU,CAACC,OAAO,CAAC7C,iBAAiB,IAAI,CAAC,CAAC,CAAc;EAE5D,IAAI8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCrG,yBAAyB,CAACgG,YAAY,CAAC;EACzC;EAEA,MAAMM,UAAqB,GAAG;IAC5BvC,eAAe;IACfC,iBAAiB;IACjBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,uBAAuB;IACvBC,iBAAiB;IACjBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,kBAAkB;IAClBC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACX;IACAC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,OAAO;IACPC;EACF,CAAC;;EAED;EACA;EACA;EACA,KAAK,MAAM1F,SAAS,IAAIiG,UAAU,EAAE;IAClC;IACA,IAAIA,UAAU,CAACjG,SAAS,CAAC,KAAKI,SAAS,EAAE;MACvC;MACA;MACA,OAAO6F,UAAU,CAACjG,SAAS,CAAC;IAC9B;EACF;EAEA,MAAMkG,eAAe,GAAG,CACtBD,UAAU,EACVhD,mBAAmB,KAAK,KAAK,IAAI;IAC/BmC,SAAS,EAAE,CAAC;IACZG,aAAa,EAAE,CAAC;IAChBvB,iBAAiB,EAAE;EACrB,CAAC,CACF;EAED,MAAMmC,UAAU,GAAGtE,UAAU,GACzBA,UAAU,CAAC;IACTuE,SAAS,EAAEpE,eAAe;IAC1BqE,UAAU,EAAEnD,gBAAgB;IAC5BoD,YAAY,EAAEnD,kBAAkB;IAChCoD,YAAY,EAAEzE;EAChB,CAAC,CAAC,GACF,IAAI;EAER,MAAM0E,WAAW,GAAGtE,WAAW,GAC3BA,WAAW,CAAC;IACVkE,SAAS,EAAEpE,eAAe;IAC1BqE,UAAU,EAAEnD,gBAAgB;IAC5BoD,YAAY,EAAEnD;EAChB,CAAC,CAAC,GACF,IAAI;EAER,MAAM5B,WAAW,GACf,OAAOC,WAAW,KAAK,UAAU,GAC5BlB,KAA+C,iBAC9C,oBAAC,oBAAW,EAAKA,KAAK,CACvB,GACDkB,WAAW;EAEjB,oBACE,oBAAC,KAAK,CAAC,QAAQ,qBACb,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLoE,uBAAU,CAACa,YAAY,EACvB;MAAEC,MAAM,EAAE;IAAE,CAAC,EACb5D,wBAAwB;EACxB,GAEDb,gBAAgB,GACfA,gBAAgB,CAAC;IAAE0E,KAAK,EAAET;EAAgB,CAAC,CAAC,GAC1CnE,iBAAiB,GAAG,IAAI,gBAC1B,oBAAC,yBAAgB;IAAC,KAAK,EAAEmE;EAAgB,EAC1C,CACa,eAChB,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CAAC;MAAE3C,MAAM;MAAEC,SAAS;MAAEC,SAAS;MAAEgC,OAAO;MAAEC;IAAU,CAAC;EAAE,gBAE9D,oBAAC,iBAAI;IAAC,aAAa,EAAC,MAAM;IAAC,KAAK,EAAE;MAAEnC,MAAM,EAAEH;IAAsB;EAAE,EAAG,eACvE,oBAAC,iBAAI;IAAC,aAAa,EAAC,UAAU;IAAC,KAAK,EAAExD,MAAM,CAACgH;EAAQ,gBACnD,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLhH,MAAM,CAACiH,IAAI,EACXpF,gBAAgB,KAAK,QAAQ,IAAI7B,MAAM,CAACkH,MAAM,EAC9C;MAAEC,WAAW,EAAExG,MAAM,CAACsG;IAAK,CAAC,EAC5BrE,kBAAkB;EAClB,GAED2D,UAAU,CACG,eAChB,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACLvG,MAAM,CAAC0B,KAAK,EACZ;MACE;MACA0F,QAAQ,EACNvF,gBAAgB,KAAK,QAAQ,GACzBL,MAAM,CAAC6F,KAAK,GACZ,CAAC,CAACd,UAAU,GACRrE,sBAAsB,KAAK,KAAK,GAC9B,EAAE,GACF,EAAE,GACJ,EAAE,IACJoF,IAAI,CAACC,GAAG,CAAC5G,MAAM,CAACsG,IAAI,EAAEtG,MAAM,CAAC6G,KAAK,CAAC,IACnC,CAAC,GACHhG,MAAM,CAAC6F,KAAK,IACX,CAACd,UAAU,GAAG,EAAE,GAAG,EAAE,KACnBK,WAAW,GAAG,EAAE,GAAG,EAAE,CAAC,GACvBjG,MAAM,CAACsG,IAAI,GACXtG,MAAM,CAAC6G,KAAK;IACtB,CAAC,EACDxE,mBAAmB;EACnB,GAEDrB,WAAW,CAAC;IACX8F,QAAQ,EAAE/F,KAAK;IACfgG,gBAAgB,EAAElF,qBAAqB;IACvCgE,SAAS,EAAEpE,eAAe;IAC1B2E,KAAK,EAAErE;EACT,CAAC,CAAC,CACY,eAChB,oBAAC,qBAAQ,CAAC,IAAI;IACZ,aAAa,EAAC,UAAU;IACxB,KAAK,EAAE,CACL1C,MAAM,CAACwH,KAAK,EACZxH,MAAM,CAACkH,MAAM,EACb;MAAES,SAAS,EAAEhH,MAAM,CAAC6G;IAAM,CAAC,EAC3B1E,mBAAmB;EACnB,GAED8D,WAAW,CACE,CACX,CACO,CACD;AAErB;AAEA,MAAM5G,MAAM,GAAGgG,uBAAU,CAAC4B,MAAM,CAAC;EAC/BZ,OAAO,EAAE;IACPa,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACDrG,KAAK,EAAE;IACLsG,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDhB,IAAI,EAAE;IACJgB,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDP,KAAK,EAAE;IACLS,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDb,MAAM,EAAE;IACNgB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE;EACb;AACF,CAAC,CAAC"}