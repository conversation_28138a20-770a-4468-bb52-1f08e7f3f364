{"version": 3, "names": ["Animated", "add", "multiply", "conditional", "condition", "main", "fallback", "interpolate", "inputRange", "outputRange"], "sourceRoot": "../../../src", "sources": ["utils/conditional.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,MAAM;EAAEC,GAAG;EAAEC;AAAS,CAAC,GAAGF,QAAQ;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASG,WAAW,CACjCC,SAAgD,EAChDC,IAA4C,EAC5CC,QAAgD,EAChD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOL,GAAG,CACRC,QAAQ,CAACE,SAAS,EAAEC,IAAI,CAAC,EACzBH,QAAQ,CACNE,SAAS,CAACG,WAAW,CAAC;IACpBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,EACFH,QAAQ,CACT,CACF;AACH"}