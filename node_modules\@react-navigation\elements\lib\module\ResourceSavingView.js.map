{"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "FAR_FAR_AWAY", "ResourceSavingScene", "visible", "children", "style", "rest", "OS", "display", "styles", "container", "attached", "detached", "create", "flex", "overflow", "top"], "sourceRoot": "../../src", "sources": ["ResourceSavingView.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAaC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAQ/E,MAAMC,YAAY,GAAG,KAAK,CAAC,CAAC;;AAE5B,eAAe,SAASC,mBAAmB,OAKjC;EAAA,IALkC;IAC1CC,OAAO;IACPC,QAAQ;IACRC,KAAK;IACL,GAAGC;EACE,CAAC;EACN,IAAIR,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;IACzB,oBACE,oBAAC;IACC;IAAA;MACA,MAAM,EAAE,CAACJ,OAAQ;MACjB,KAAK,EAAE,CACL;QAAEK,OAAO,EAAEL,OAAO,GAAG,MAAM,GAAG;MAAO,CAAC,EACtCM,MAAM,CAACC,SAAS,EAChBL,KAAK,CACL;MACF,aAAa,EAAEF,OAAO,GAAG,MAAM,GAAG;IAAO,GACrCG,IAAI,GAEPF,QAAQ,CACJ;EAEX;EAEA,oBACE,oBAAC,IAAI;IACH,KAAK,EAAE,CAACK,MAAM,CAACC,SAAS,EAAEL,KAAK;IAC/B;IAAA;IACA,aAAa,EAAEF,OAAO,GAAG,MAAM,GAAG;EAAO,gBAEzC,oBAAC,IAAI;IACH,WAAW,EAAE,KAAM;IACnB,qBAAqB;IACnB;IACA;IACAL,QAAQ,CAACS,EAAE,KAAK,KAAK,IAAIT,QAAQ,CAACS,EAAE,KAAK,OAAO,GAAG,CAACJ,OAAO,GAAG,IAC/D;IACD,aAAa,EAAEA,OAAO,GAAG,MAAM,GAAG,MAAO;IACzC,KAAK,EAAEA,OAAO,GAAGM,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAACG;EAAS,GAElDR,QAAQ,CACJ,CACF;AAEX;AAEA,MAAMK,MAAM,GAAGV,UAAU,CAACc,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDJ,QAAQ,EAAE;IACRG,IAAI,EAAE;EACR,CAAC;EACDF,QAAQ,EAAE;IACRE,IAAI,EAAE,CAAC;IACPE,GAAG,EAAEf;EACP;AACF,CAAC,CAAC"}