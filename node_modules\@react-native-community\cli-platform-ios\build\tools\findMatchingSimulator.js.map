{"version": 3, "names": ["findMatchingSimulator", "simulators", "findOptions", "devices", "simulatorVersion", "simulatorName", "simulator", "parsedSimulatorName", "match", "undefined", "fallbackMatch", "versionDescriptor", "device", "version", "test", "replace", "includes", "endsWith", "i", "availability", "isAvailable", "booted", "state", "lastBootedAt", "simulatorDescriptor", "udid", "name"], "sources": ["../../src/tools/findMatchingSimulator.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {Device} from '../types';\n\n/**\n * Takes in a parsed simulator list and a desired name, and returns an object with the matching simulator. The desired\n * name can optionally include the iOS version in between parenthesis after the device name. Ex: \"iPhone 6 (9.2)\" in\n * which case it'll attempt to find a simulator with the exact version specified.\n *\n * If the simulatorString argument is null, we'll go into default mode and return the currently booted simulator,\n * the last booted simulator or\n * if none is booted, it will be the first in the list.\n *\n * @param simulators a parsed list from `xcrun simctl list --json devices` command\n * @param findOptions null or an object containing:\n * ```\n * {\n *    simulator: name of desired simulator\n *    udid: udid of desired simulator\n * }\n * ```\n * If null, it will use the currently booted simulator, or if none are booted, the first in the list.\n */\nfunction findMatchingSimulator(\n  simulators: {devices: {[index: string]: Array<Device>}},\n  findOptions?: null | {simulator?: string; udid?: string},\n) {\n  if (!simulators.devices) {\n    return null;\n  }\n  const devices = simulators.devices;\n  let simulatorVersion;\n  let simulatorName = null;\n\n  if (findOptions && findOptions.simulator) {\n    const parsedSimulatorName = findOptions.simulator.match(\n      /(.*)? (?:\\((\\d+\\.\\d+)?\\))$/,\n    );\n    if (parsedSimulatorName && parsedSimulatorName[2] !== undefined) {\n      simulatorVersion = parsedSimulatorName[2];\n      simulatorName = parsedSimulatorName[1];\n    } else {\n      simulatorName = findOptions.simulator;\n    }\n  }\n\n  let match;\n  let fallbackMatch;\n\n  for (const versionDescriptor in devices) {\n    const device = devices[versionDescriptor];\n    let version = versionDescriptor;\n\n    if (/^com\\.apple\\.CoreSimulator\\.SimRuntime\\./g.test(version)) {\n      // Transform \"com.apple.CoreSimulator.SimRuntime.iOS-12-2\" into \"iOS 12.2\"\n      version = version.replace(\n        /^com\\.apple\\.CoreSimulator\\.SimRuntime\\.([^-]+)-([^-]+)-([^-]+)$/g,\n        '$1 $2.$3',\n      );\n    }\n\n    // Making sure the version of the simulator is an iOS or tvOS (Removes Apple Watch, etc)\n    if (!version.includes('iOS')) {\n      continue;\n    }\n    if (simulatorVersion && !version.endsWith(simulatorVersion)) {\n      continue;\n    }\n    for (const i in device) {\n      const simulator = device[i];\n      // Skipping non-available simulator\n      if (\n        simulator.availability !== '(available)' &&\n        // @ts-ignore verify isAvailable parameter\n        simulator.isAvailable !== 'YES' &&\n        simulator.isAvailable !== true\n      ) {\n        continue;\n      }\n      const booted = simulator.state === 'Booted';\n      const lastBootedAt = simulator.lastBootedAt;\n      const simulatorDescriptor = {\n        udid: simulator.udid,\n        name: simulator.name,\n        booted,\n        version,\n      };\n      if (findOptions && findOptions.udid) {\n        if (simulator.udid === findOptions.udid) {\n          return simulatorDescriptor;\n        }\n      } else {\n        if (booted && simulatorName === null) {\n          return simulatorDescriptor;\n        }\n        if (simulator.name === simulatorName && !match) {\n          match = simulatorDescriptor;\n        }\n        // If no match found, use first available simulator that was booted before\n        if (!!lastBootedAt && !match) {\n          fallbackMatch = simulatorDescriptor;\n        }\n        // Keeps track of the first available simulator for use if we can't find one above.\n        if (simulatorName === null && !match) {\n          match = simulatorDescriptor;\n        }\n      }\n    }\n  }\n\n  return match ?? fallbackMatch ?? null;\n}\n\nexport default findMatchingSimulator;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,CAC5BC,UAAuD,EACvDC,WAAwD,EACxD;EACA,IAAI,CAACD,UAAU,CAACE,OAAO,EAAE;IACvB,OAAO,IAAI;EACb;EACA,MAAMA,OAAO,GAAGF,UAAU,CAACE,OAAO;EAClC,IAAIC,gBAAgB;EACpB,IAAIC,aAAa,GAAG,IAAI;EAExB,IAAIH,WAAW,IAAIA,WAAW,CAACI,SAAS,EAAE;IACxC,MAAMC,mBAAmB,GAAGL,WAAW,CAACI,SAAS,CAACE,KAAK,CACrD,4BAA4B,CAC7B;IACD,IAAID,mBAAmB,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAKE,SAAS,EAAE;MAC/DL,gBAAgB,GAAGG,mBAAmB,CAAC,CAAC,CAAC;MACzCF,aAAa,GAAGE,mBAAmB,CAAC,CAAC,CAAC;IACxC,CAAC,MAAM;MACLF,aAAa,GAAGH,WAAW,CAACI,SAAS;IACvC;EACF;EAEA,IAAIE,KAAK;EACT,IAAIE,aAAa;EAEjB,KAAK,MAAMC,iBAAiB,IAAIR,OAAO,EAAE;IACvC,MAAMS,MAAM,GAAGT,OAAO,CAACQ,iBAAiB,CAAC;IACzC,IAAIE,OAAO,GAAGF,iBAAiB;IAE/B,IAAI,2CAA2C,CAACG,IAAI,CAACD,OAAO,CAAC,EAAE;MAC7D;MACAA,OAAO,GAAGA,OAAO,CAACE,OAAO,CACvB,mEAAmE,EACnE,UAAU,CACX;IACH;;IAEA;IACA,IAAI,CAACF,OAAO,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC5B;IACF;IACA,IAAIZ,gBAAgB,IAAI,CAACS,OAAO,CAACI,QAAQ,CAACb,gBAAgB,CAAC,EAAE;MAC3D;IACF;IACA,KAAK,MAAMc,CAAC,IAAIN,MAAM,EAAE;MACtB,MAAMN,SAAS,GAAGM,MAAM,CAACM,CAAC,CAAC;MAC3B;MACA,IACEZ,SAAS,CAACa,YAAY,KAAK,aAAa;MACxC;MACAb,SAAS,CAACc,WAAW,KAAK,KAAK,IAC/Bd,SAAS,CAACc,WAAW,KAAK,IAAI,EAC9B;QACA;MACF;MACA,MAAMC,MAAM,GAAGf,SAAS,CAACgB,KAAK,KAAK,QAAQ;MAC3C,MAAMC,YAAY,GAAGjB,SAAS,CAACiB,YAAY;MAC3C,MAAMC,mBAAmB,GAAG;QAC1BC,IAAI,EAAEnB,SAAS,CAACmB,IAAI;QACpBC,IAAI,EAAEpB,SAAS,CAACoB,IAAI;QACpBL,MAAM;QACNR;MACF,CAAC;MACD,IAAIX,WAAW,IAAIA,WAAW,CAACuB,IAAI,EAAE;QACnC,IAAInB,SAAS,CAACmB,IAAI,KAAKvB,WAAW,CAACuB,IAAI,EAAE;UACvC,OAAOD,mBAAmB;QAC5B;MACF,CAAC,MAAM;QACL,IAAIH,MAAM,IAAIhB,aAAa,KAAK,IAAI,EAAE;UACpC,OAAOmB,mBAAmB;QAC5B;QACA,IAAIlB,SAAS,CAACoB,IAAI,KAAKrB,aAAa,IAAI,CAACG,KAAK,EAAE;UAC9CA,KAAK,GAAGgB,mBAAmB;QAC7B;QACA;QACA,IAAI,CAAC,CAACD,YAAY,IAAI,CAACf,KAAK,EAAE;UAC5BE,aAAa,GAAGc,mBAAmB;QACrC;QACA;QACA,IAAInB,aAAa,KAAK,IAAI,IAAI,CAACG,KAAK,EAAE;UACpCA,KAAK,GAAGgB,mBAAmB;QAC7B;MACF;IACF;EACF;EAEA,OAAOhB,KAAK,IAAIE,aAAa,IAAI,IAAI;AACvC;AAAC,eAEcV,qBAAqB;AAAA"}