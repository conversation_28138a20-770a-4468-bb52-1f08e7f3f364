"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _MaskedViewNative.default;
  }
});
var _MaskedViewNative = _interopRequireDefault(require("./MaskedViewNative"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=MaskedView.ios.js.map