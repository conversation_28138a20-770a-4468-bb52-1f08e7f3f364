{"version": 3, "names": ["nanoid", "createMemoryHistory", "index", "items", "pending", "interrupt", "for<PERSON>ach", "it", "cb", "history", "id", "window", "state", "findIndex", "item", "get", "backIndex", "path", "i", "push", "slice", "length", "pushState", "replace", "pathWithHash", "location", "hash", "replaceState", "go", "n", "nextIndex", "lastItemIndex", "Promise", "resolve", "reject", "done", "interrupted", "clearTimeout", "timer", "Error", "title", "document", "ref", "setTimeout", "splice", "onPopState", "currentIndex", "Math", "max", "last", "pop", "removeEventListener", "addEventListener", "listen", "listener"], "sourceRoot": "../../src", "sources": ["createMemoryHistory.tsx"], "mappings": "AACA,SAASA,MAAM,QAAQ,mBAAmB;AAW1C,eAAe,SAASC,mBAAmB,GAAG;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAsB,GAAG,EAAE;;EAE/B;EACA;EACA,MAAMC,OAAgE,GAAG,EAAE;EAE3E,MAAMC,SAAS,GAAG,MAAM;IACtB;IACA;IACA;IACAD,OAAO,CAACE,OAAO,CAAEC,EAAE,IAAK;MACtB,MAAMC,EAAE,GAAGD,EAAE,CAACC,EAAE;MAChBD,EAAE,CAACC,EAAE,GAAG,MAAMA,EAAE,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,OAAO,GAAG;IACd,IAAIP,KAAK,GAAW;MAAA;MAClB;MACA;MACA,MAAMQ,EAAE,4BAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,0DAApB,sBAAsBF,EAAE;MAEnC,IAAIA,EAAE,EAAE;QACN,MAAMR,KAAK,GAAGC,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC;QAEvD,OAAOR,KAAK,GAAG,CAAC,CAAC,GAAGA,KAAK,GAAG,CAAC;MAC/B;MAEA,OAAO,CAAC;IACV,CAAC;IAEDa,GAAG,CAACb,KAAa,EAAE;MACjB,OAAOC,KAAK,CAACD,KAAK,CAAC;IACrB,CAAC;IAEDc,SAAS,OAA6B;MAAA,IAA5B;QAAEC;MAAuB,CAAC;MAClC;MACA,KAAK,IAAIC,CAAC,GAAGhB,KAAK,GAAG,CAAC,EAAEgB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnC,MAAMJ,IAAI,GAAGX,KAAK,CAACe,CAAC,CAAC;QAErB,IAAIJ,IAAI,CAACG,IAAI,KAAKA,IAAI,EAAE;UACtB,OAAOC,CAAC;QACV;MACF;MAEA,OAAO,CAAC,CAAC;IACX,CAAC;IAEDC,IAAI,QAA4D;MAAA,IAA3D;QAAEF,IAAI;QAAEL;MAAgD,CAAC;MAC5DP,SAAS,EAAE;MAEX,MAAMK,EAAE,GAAGV,MAAM,EAAE;;MAEnB;MACA;MACAG,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAElB,KAAK,GAAG,CAAC,CAAC;MAEjCC,KAAK,CAACgB,IAAI,CAAC;QAAEF,IAAI;QAAEL,KAAK;QAAEF;MAAG,CAAC,CAAC;MAC/BR,KAAK,GAAGC,KAAK,CAACkB,MAAM,GAAG,CAAC;;MAExB;MACA;MACA;MACA;MACAV,MAAM,CAACF,OAAO,CAACa,SAAS,CAAC;QAAEZ;MAAG,CAAC,EAAE,EAAE,EAAEO,IAAI,CAAC;IAC5C,CAAC;IAEDM,OAAO,QAA4D;MAAA;MAAA,IAA3D;QAAEN,IAAI;QAAEL;MAAgD,CAAC;MAC/DP,SAAS,EAAE;MAEX,MAAMK,EAAE,GAAG,2BAAAC,MAAM,CAACF,OAAO,CAACG,KAAK,2DAApB,uBAAsBF,EAAE,KAAIV,MAAM,EAAE;;MAE/C;MACA;MACA,IAAIwB,YAAY,GAAGP,IAAI;MAEvB,IAAI,CAACd,KAAK,CAACkB,MAAM,IAAIlB,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC,GAAG,CAAC,EAAE;QAClE;QACA;QACA;QACA;QACA;QACA;QACAc,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC3CvB,KAAK,GAAG,CAAC;UAAEc,IAAI,EAAEO,YAAY;UAAEZ,KAAK;UAAEF;QAAG,CAAC,CAAC;QAC3CR,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACL,IAAIC,KAAK,CAACD,KAAK,CAAC,CAACe,IAAI,KAAKA,IAAI,EAAE;UAC9BO,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC7C;QACAvB,KAAK,CAACD,KAAK,CAAC,GAAG;UAAEe,IAAI;UAAEL,KAAK;UAAEF;QAAG,CAAC;MACpC;MAEAC,MAAM,CAACF,OAAO,CAACkB,YAAY,CAAC;QAAEjB;MAAG,CAAC,EAAE,EAAE,EAAEc,YAAY,CAAC;IACvD,CAAC;IAED;IACA;IACA;IACA;IACA;IACAI,EAAE,CAACC,CAAS,EAAE;MACZxB,SAAS,EAAE;;MAEX;MACA;MACA,MAAMyB,SAAS,GAAG5B,KAAK,GAAG2B,CAAC;MAC3B,MAAME,aAAa,GAAG5B,KAAK,CAACkB,MAAM,GAAG,CAAC;MACtC,IAAIQ,CAAC,GAAG,CAAC,IAAI,CAAC1B,KAAK,CAAC2B,SAAS,CAAC,EAAE;QAC9B;QACAD,CAAC,GAAG,CAAC3B,KAAK;QACVA,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAI2B,CAAC,GAAG,CAAC,IAAIC,SAAS,GAAGC,aAAa,EAAE;QAC7C;QACAF,CAAC,GAAGE,aAAa,GAAG7B,KAAK;QACzBA,KAAK,GAAG6B,aAAa;MACvB,CAAC,MAAM;QACL7B,KAAK,GAAG4B,SAAS;MACnB;MAEA,IAAID,CAAC,KAAK,CAAC,EAAE;QACX;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAIG,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;QAC5C,MAAMC,IAAI,GAAIC,WAAqB,IAAK;UACtCC,YAAY,CAACC,KAAK,CAAC;UAEnB,IAAIF,WAAW,EAAE;YACfF,MAAM,CAAC,IAAIK,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC3D;UACF;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM;YAAEC;UAAM,CAAC,GAAG7B,MAAM,CAAC8B,QAAQ;UAEjC9B,MAAM,CAAC8B,QAAQ,CAACD,KAAK,GAAG,EAAE;UAC1B7B,MAAM,CAAC8B,QAAQ,CAACD,KAAK,GAAGA,KAAK;UAE7BP,OAAO,EAAE;QACX,CAAC;QAED7B,OAAO,CAACe,IAAI,CAAC;UAAEuB,GAAG,EAAEP,IAAI;UAAE3B,EAAE,EAAE2B;QAAK,CAAC,CAAC;;QAErC;QACA;QACA;QACA;QACA;QACA,MAAMG,KAAK,GAAGK,UAAU,CAAC,MAAM;UAC7B,MAAMzC,KAAK,GAAGE,OAAO,CAACS,SAAS,CAAEN,EAAE,IAAKA,EAAE,CAACmC,GAAG,KAAKP,IAAI,CAAC;UAExD,IAAIjC,KAAK,GAAG,CAAC,CAAC,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,CAACM,EAAE,EAAE;YACnBJ,OAAO,CAACwC,MAAM,CAAC1C,KAAK,EAAE,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,MAAM2C,UAAU,GAAG,MAAM;UAAA;UACvB,MAAMnC,EAAE,6BAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,2DAApB,uBAAsBF,EAAE;UACnC,MAAMoC,YAAY,GAAG3C,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC;;UAE9D;UACA;UACAR,KAAK,GAAG6C,IAAI,CAACC,GAAG,CAACF,YAAY,EAAE,CAAC,CAAC;UAEjC,MAAMG,IAAI,GAAG7C,OAAO,CAAC8C,GAAG,EAAE;UAE1BvC,MAAM,CAACwC,mBAAmB,CAAC,UAAU,EAAEN,UAAU,CAAC;UAClDI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEzC,EAAE,EAAE;QACZ,CAAC;QAEDG,MAAM,CAACyC,gBAAgB,CAAC,UAAU,EAAEP,UAAU,CAAC;QAC/ClC,MAAM,CAACF,OAAO,CAACmB,EAAE,CAACC,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACA;IACA;IACAwB,MAAM,CAACC,QAAoB,EAAE;MAC3B,MAAMT,UAAU,GAAG,MAAM;QACvB,IAAIzC,OAAO,CAACiB,MAAM,EAAE;UAClB;UACA;QACF;QAEAiC,QAAQ,EAAE;MACZ,CAAC;MAED3C,MAAM,CAACyC,gBAAgB,CAAC,UAAU,EAAEP,UAAU,CAAC;MAE/C,OAAO,MAAMlC,MAAM,CAACwC,mBAAmB,CAAC,UAAU,EAAEN,UAAU,CAAC;IACjE;EACF,CAAC;EAED,OAAOpC,OAAO;AAChB"}