{"name": "@tanstack/react-table", "version": "8.21.3", "description": "Headless UI for building powerful tables & datagrids for React.", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/table.git", "directory": "packages/react-table"}, "homepage": "https://tanstack.com/table", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "table", "react-table", "datagrid"], "type": "commonjs", "module": "build/lib/index.esm.js", "main": "build/lib/index.js", "types": "build/lib/index.d.ts", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "engines": {"node": ">=12"}, "files": ["build/lib/*", "build/umd/*", "src"], "dependencies": {"@tanstack/table-core": "8.21.3"}, "devDependencies": {"@types/react": "^18.3.3", "react": "^18.3.1"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}, "scripts": {}}