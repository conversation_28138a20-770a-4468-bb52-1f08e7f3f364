/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(b,d){"object"===typeof exports&&"undefined"!==typeof module?d(exports):"function"===typeof define&&define.amd?define(["exports"],d):(b=b||self,d(b.ReactIs={}))})(this,function(b){function d(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case q:switch(a=a.type,a){case e:case f:case g:case h:case t:return a;default:switch(a=a&&a.$$typeof,a){case k:case l:case m:case n:case p:return a;default:return b}}case r:return b}}}var q=60103,r=60106,e=60107,g=60108,f=60114,
p=60109,k=60110,l=60112,h=60113,t=60120,n=60115,m=60116,u=60121,v=60122,w=60117,x=60129,y=60131;if("function"===typeof Symbol&&Symbol.for){var c=Symbol.for;q=c("react.element");r=c("react.portal");e=c("react.fragment");g=c("react.strict_mode");f=c("react.profiler");p=c("react.provider");k=c("react.context");l=c("react.forward_ref");h=c("react.suspense");t=c("react.suspense_list");n=c("react.memo");m=c("react.lazy");u=c("react.block");v=c("react.server.block");w=c("react.fundamental");x=c("react.debug_trace_mode");
y=c("react.legacy_hidden")}b.ContextConsumer=k;b.ContextProvider=p;b.Element=q;b.ForwardRef=l;b.Fragment=e;b.Lazy=m;b.Memo=n;b.Portal=r;b.Profiler=f;b.StrictMode=g;b.Suspense=h;b.isAsyncMode=function(a){return!1};b.isConcurrentMode=function(a){return!1};b.isContextConsumer=function(a){return d(a)===k};b.isContextProvider=function(a){return d(a)===p};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===q};b.isForwardRef=function(a){return d(a)===l};b.isFragment=function(a){return d(a)===
e};b.isLazy=function(a){return d(a)===m};b.isMemo=function(a){return d(a)===n};b.isPortal=function(a){return d(a)===r};b.isProfiler=function(a){return d(a)===f};b.isStrictMode=function(a){return d(a)===g};b.isSuspense=function(a){return d(a)===h};b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===e||a===f||a===x||a===g||a===h||a===t||a===y||"object"===typeof a&&null!==a&&(a.$$typeof===m||a.$$typeof===n||a.$$typeof===p||a.$$typeof===k||a.$$typeof===l||a.$$typeof===
w||a.$$typeof===u||a[0]===v)?!0:!1};b.typeOf=d});
})();
