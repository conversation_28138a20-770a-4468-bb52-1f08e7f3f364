{"version": 3, "names": ["logIOS", "_argv", "_ctx", "args", "simulators", "getSimulators", "bootedSimulators", "Object", "keys", "devices", "map", "key", "reduce", "acc", "val", "concat", "filter", "state", "listIOSDevices", "availableSimulators", "type", "isAvailable", "bootedAndAvailableSimulators", "booted", "available", "find", "udid", "length", "logger", "error", "interactive", "prompts", "name", "message", "choices", "simulator", "title", "value", "tailDeviceLogs", "logDir", "path", "join", "os", "homedir", "log", "spawnSync", "stdio", "description", "func", "options"], "sources": ["../../../src/commands/logIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {spawnSync} from 'child_process';\nimport os from 'os';\nimport path from 'path';\nimport {logger} from '@react-native-community/cli-tools';\nimport listIOSDevices from '../../tools/listIOSDevices';\nimport getSimulators from '../../tools/getSimulators';\nimport {Config} from '@react-native-community/cli-types';\nimport prompts from 'prompts';\n\n/**\n * Starts iOS device syslog tail\n */\n\ntype Args = {\n  interactive: boolean;\n};\n\nasync function logIOS(_argv: Array<string>, _ctx: Config, args: Args) {\n  // Here we're using two command because first command `xcrun simctl list --json devices` outputs `state` but doesn't return `available`. But second command `xcrun xcdevice list` outputs `available` but doesn't output `state`. So we need to connect outputs of both commands.\n  const simulators = getSimulators();\n  const bootedSimulators = Object.keys(simulators.devices)\n    .map((key) => simulators.devices[key])\n    .reduce((acc, val) => acc.concat(val), [])\n    .filter(({state}) => state === 'Booted');\n\n  const devices = await listIOSDevices();\n  const availableSimulators = devices.filter(\n    ({type, isAvailable}) => type === 'simulator' && isAvailable,\n  );\n\n  const bootedAndAvailableSimulators = bootedSimulators.map((booted) => {\n    const available = availableSimulators.find(\n      ({udid}) => udid === booted.udid,\n    );\n    return {...available, ...booted};\n  });\n\n  if (bootedAndAvailableSimulators.length === 0) {\n    logger.error('No active iOS device found');\n    return;\n  }\n\n  if (args.interactive && bootedAndAvailableSimulators.length > 1) {\n    const {udid} = await prompts({\n      type: 'select',\n      name: 'udid',\n      message: 'Select iOS simulators to tail logs from',\n      choices: bootedAndAvailableSimulators.map((simulator) => ({\n        title: simulator.name,\n        value: simulator.udid,\n      })),\n    });\n\n    tailDeviceLogs(udid);\n  } else {\n    tailDeviceLogs(bootedAndAvailableSimulators[0].udid);\n  }\n}\n\nfunction tailDeviceLogs(udid: string) {\n  const logDir = path.join(\n    os.homedir(),\n    'Library',\n    'Logs',\n    'CoreSimulator',\n    udid,\n    'asl',\n  );\n\n  const log = spawnSync('syslog', ['-w', '-F', 'std', '-d', logDir], {\n    stdio: 'inherit',\n  });\n\n  if (log.error !== null) {\n    throw log.error;\n  }\n}\n\nexport default {\n  name: 'log-ios',\n  description: 'starts iOS device syslog tail',\n  func: logIOS,\n  options: [\n    {\n      name: '--interactive',\n      description:\n        'Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator.',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA8B;AAf9B;AACA;AACA;AACA;AACA;AACA;AACA;;AAmBA,eAAeA,MAAM,CAACC,KAAoB,EAAEC,IAAY,EAAEC,IAAU,EAAE;EACpE;EACA,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAAE;EAClC,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAAC,CACrDC,GAAG,CAAEC,GAAG,IAAKP,UAAU,CAACK,OAAO,CAACE,GAAG,CAAC,CAAC,CACrCC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,CAACE,MAAM,CAACD,GAAG,CAAC,EAAE,EAAE,CAAC,CACzCE,MAAM,CAAC,CAAC;IAACC;EAAK,CAAC,KAAKA,KAAK,KAAK,QAAQ,CAAC;EAE1C,MAAMR,OAAO,GAAG,MAAM,IAAAS,uBAAc,GAAE;EACtC,MAAMC,mBAAmB,GAAGV,OAAO,CAACO,MAAM,CACxC,CAAC;IAACI,IAAI;IAAEC;EAAW,CAAC,KAAKD,IAAI,KAAK,WAAW,IAAIC,WAAW,CAC7D;EAED,MAAMC,4BAA4B,GAAGhB,gBAAgB,CAACI,GAAG,CAAEa,MAAM,IAAK;IACpE,MAAMC,SAAS,GAAGL,mBAAmB,CAACM,IAAI,CACxC,CAAC;MAACC;IAAI,CAAC,KAAKA,IAAI,KAAKH,MAAM,CAACG,IAAI,CACjC;IACD,OAAO;MAAC,GAAGF,SAAS;MAAE,GAAGD;IAAM,CAAC;EAClC,CAAC,CAAC;EAEF,IAAID,4BAA4B,CAACK,MAAM,KAAK,CAAC,EAAE;IAC7CC,kBAAM,CAACC,KAAK,CAAC,4BAA4B,CAAC;IAC1C;EACF;EAEA,IAAI1B,IAAI,CAAC2B,WAAW,IAAIR,4BAA4B,CAACK,MAAM,GAAG,CAAC,EAAE;IAC/D,MAAM;MAACD;IAAI,CAAC,GAAG,MAAM,IAAAK,kBAAO,EAAC;MAC3BX,IAAI,EAAE,QAAQ;MACdY,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,yCAAyC;MAClDC,OAAO,EAAEZ,4BAA4B,CAACZ,GAAG,CAAEyB,SAAS,KAAM;QACxDC,KAAK,EAAED,SAAS,CAACH,IAAI;QACrBK,KAAK,EAAEF,SAAS,CAACT;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFY,cAAc,CAACZ,IAAI,CAAC;EACtB,CAAC,MAAM;IACLY,cAAc,CAAChB,4BAA4B,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC;EACtD;AACF;AAEA,SAASY,cAAc,CAACZ,IAAY,EAAE;EACpC,MAAMa,MAAM,GAAGC,eAAI,CAACC,IAAI,CACtBC,aAAE,CAACC,OAAO,EAAE,EACZ,SAAS,EACT,MAAM,EACN,eAAe,EACfjB,IAAI,EACJ,KAAK,CACN;EAED,MAAMkB,GAAG,GAAG,IAAAC,0BAAS,EAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAEN,MAAM,CAAC,EAAE;IACjEO,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,IAAIF,GAAG,CAACf,KAAK,KAAK,IAAI,EAAE;IACtB,MAAMe,GAAG,CAACf,KAAK;EACjB;AACF;AAAC,eAEc;EACbG,IAAI,EAAE,SAAS;EACfe,WAAW,EAAE,+BAA+B;EAC5CC,IAAI,EAAEhD,MAAM;EACZiD,OAAO,EAAE,CACP;IACEjB,IAAI,EAAE,eAAe;IACrBe,WAAW,EACT;EACJ,CAAC;AAEL,CAAC;AAAA"}