{"version": 3, "names": ["getScrollableNode", "ref", "current", "getScrollResponder", "getNode", "useScrollToTop", "navigation", "React", "useContext", "NavigationContext", "route", "useRoute", "undefined", "Error", "useEffect", "tabNavigations", "currentNavigation", "getState", "type", "push", "getParent", "length", "unsubscribers", "map", "tab", "addListener", "e", "isFocused", "<PERSON><PERSON><PERSON><PERSON>", "includes", "routes", "key", "requestAnimationFrame", "scrollable", "defaultPrevented", "scrollToTop", "scrollTo", "y", "animated", "scrollToOffset", "offset", "scrollResponderScrollTo", "for<PERSON>ach", "unsubscribe"], "sourceRoot": "../../src", "sources": ["useScrollToTop.tsx"], "mappings": ";;;;;;AAAA;AAOA;AAA+B;AAAA;AAgB/B,SAASA,iBAAiB,CAACC,GAAuC,EAAE;EAClE,IAAIA,GAAG,CAACC,OAAO,IAAI,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,IACE,aAAa,IAAID,GAAG,CAACC,OAAO,IAC5B,UAAU,IAAID,GAAG,CAACC,OAAO,IACzB,gBAAgB,IAAID,GAAG,CAACC,OAAO,IAC/B,yBAAyB,IAAID,GAAG,CAACC,OAAO,EACxC;IACA;IACA,OAAOD,GAAG,CAACC,OAAO;EACpB,CAAC,MAAM,IAAI,oBAAoB,IAAID,GAAG,CAACC,OAAO,EAAE;IAC9C;IACA;IACA,OAAOD,GAAG,CAACC,OAAO,CAACC,kBAAkB,EAAE;EACzC,CAAC,MAAM,IAAI,SAAS,IAAIF,GAAG,CAACC,OAAO,EAAE;IACnC;IACA;IACA;IACA;IACA,OAAOD,GAAG,CAACC,OAAO,CAACE,OAAO,EAAE;EAC9B,CAAC,MAAM;IACL,OAAOH,GAAG,CAACC,OAAO;EACpB;AACF;AAEe,SAASG,cAAc,CACpCJ,GAAuC,EACvC;EACA,MAAMK,UAAU,GAAGC,KAAK,CAACC,UAAU,CAACC,uBAAiB,CAAC;EACtD,MAAMC,KAAK,GAAG,IAAAC,cAAQ,GAAE;EAExB,IAAIL,UAAU,KAAKM,SAAS,EAAE;IAC5B,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;EACH;EAEAN,KAAK,CAACO,SAAS,CAAC,MAAM;IACpB,MAAMC,cAA+C,GAAG,EAAE;IAC1D,IAAIC,iBAAiB,GAAGV,UAAU;IAClC;IACA;IACA,OAAOU,iBAAiB,EAAE;MACxB,IAAIA,iBAAiB,CAACC,QAAQ,EAAE,CAACC,IAAI,KAAK,KAAK,EAAE;QAC/CH,cAAc,CAACI,IAAI,CAACH,iBAAiB,CAAC;MACxC;MAEAA,iBAAiB,GAAGA,iBAAiB,CAACI,SAAS,EAAE;IACnD;IAEA,IAAIL,cAAc,CAACM,MAAM,KAAK,CAAC,EAAE;MAC/B;IACF;IAEA,MAAMC,aAAa,GAAGP,cAAc,CAACQ,GAAG,CAAEC,GAAG,IAAK;MAChD,OAAOA,GAAG,CAACC,WAAW;MACpB;MACA;MACA;MACA,UAAU,EACTC,CAA6B,IAAK;QACjC;QACA,MAAMC,SAAS,GAAGrB,UAAU,CAACqB,SAAS,EAAE;;QAExC;QACA;QACA,MAAMC,OAAO,GACXb,cAAc,CAACc,QAAQ,CAACvB,UAAU,CAAC,IACnCA,UAAU,CAACW,QAAQ,EAAE,CAACa,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG,KAAKrB,KAAK,CAACqB,GAAG;;QAEnD;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,MAAMC,UAAU,GAAGjC,iBAAiB,CAACC,GAAG,CAAsB;UAE9D,IAAI0B,SAAS,IAAIC,OAAO,IAAIK,UAAU,IAAI,CAACP,CAAC,CAACQ,gBAAgB,EAAE;YAC7D,IAAI,aAAa,IAAID,UAAU,EAAE;cAC/BA,UAAU,CAACE,WAAW,EAAE;YAC1B,CAAC,MAAM,IAAI,UAAU,IAAIF,UAAU,EAAE;cACnCA,UAAU,CAACG,QAAQ,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,IAAI,gBAAgB,IAAIL,UAAU,EAAE;cACzCA,UAAU,CAACM,cAAc,CAAC;gBAAEC,MAAM,EAAE,CAAC;gBAAEF,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC1D,CAAC,MAAM,IAAI,yBAAyB,IAAIL,UAAU,EAAE;cAClDA,UAAU,CAACQ,uBAAuB,CAAC;gBAAEJ,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC9D;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CACF;IACH,CAAC,CAAC;IAEF,OAAO,MAAM;MACXhB,aAAa,CAACoB,OAAO,CAAEC,WAAW,IAAKA,WAAW,EAAE,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAACrC,UAAU,EAAEL,GAAG,EAAES,KAAK,CAACqB,GAAG,CAAC,CAAC;AAClC"}