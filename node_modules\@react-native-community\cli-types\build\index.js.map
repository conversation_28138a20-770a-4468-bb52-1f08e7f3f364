{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  IOSProjectConfig,\n  IOSProjectParams,\n  IOSDependencyConfig,\n  IOSDependencyParams,\n  IOSProjectInfo,\n} from './ios';\nimport {\n  AndroidProjectConfig,\n  AndroidProjectParams,\n  AndroidDependencyConfig,\n  AndroidDependencyParams,\n} from './android';\n\nexport type Prompt = any;\n\nexport type CommandFunction<Args = Object> = (\n  argv: Array<string>,\n  ctx: Config,\n  args: Args,\n) => Promise<void> | void;\n\nexport type OptionValue = string | boolean | number;\n\nexport type CommandOption<T = (ctx: Config) => OptionValue> = {\n  name: string;\n  description?: string;\n  parse?: (val: string) => any;\n  default?: OptionValue | T;\n};\n\nexport type DetachedCommandFunction<Args = Object> = (\n  argv: string[],\n  args: Args,\n  ctx: Config,\n) => Promise<void> | void;\n\nexport type Command<IsDetached extends boolean = false> = {\n  name: string;\n  description?: string;\n  detached?: IsDetached;\n  examples?: Array<{\n    desc: string;\n    cmd: string;\n  }>;\n  pkg?: {\n    name: string;\n    version: string;\n  };\n  func: IsDetached extends true\n    ? DetachedCommandFunction<Object>\n    : CommandFunction<Object>;\n  options?: Array<\n    CommandOption<\n      IsDetached extends true ? () => OptionValue : (ctx: Config) => OptionValue\n    >\n  >;\n};\n\nexport type DetachedCommand = Command<true>;\n\ninterface PlatformConfig<\n  ProjectConfig,\n  ProjectParams,\n  DependencyConfig,\n  DependencyParams\n> {\n  npmPackageName?: string;\n  projectConfig: (\n    projectRoot: string,\n    projectParams: ProjectParams | void,\n  ) => ProjectConfig | void;\n  dependencyConfig: (\n    dependency: string,\n    params: DependencyParams,\n  ) => DependencyConfig | void;\n}\n\ntype AndroidPlatformConfig = PlatformConfig<\n  AndroidProjectConfig,\n  AndroidProjectParams,\n  AndroidDependencyConfig,\n  AndroidDependencyParams\n>;\n\ntype IOSPlatformConfig = PlatformConfig<\n  IOSProjectConfig,\n  IOSProjectParams,\n  IOSDependencyConfig,\n  IOSDependencyParams\n>;\n\nexport type ProjectConfig = {\n  android?: Exclude<ReturnType<AndroidPlatformConfig['projectConfig']>, void>;\n  ios?: Exclude<ReturnType<IOSPlatformConfig['projectConfig']>, void>;\n  [key: string]: any;\n};\n\nexport interface DependencyConfig {\n  name: string;\n  root: string;\n  platforms: {\n    android?: Exclude<\n      ReturnType<AndroidPlatformConfig['dependencyConfig']>,\n      void\n    >;\n    ios?: Exclude<ReturnType<IOSPlatformConfig['dependencyConfig']>, void>;\n    [key: string]: any;\n  };\n}\n\nexport interface Config {\n  root: string;\n  reactNativePath: string;\n  reactNativeVersion: string;\n  project: ProjectConfig;\n  dependencies: {\n    [key: string]: DependencyConfig;\n  };\n  platforms: {\n    android: AndroidPlatformConfig;\n    ios: IOSPlatformConfig;\n    [name: string]: PlatformConfig<any, any, any, any>;\n  };\n  commands: Command[];\n  // @todo this should be removed: https://github.com/react-native-community/cli/issues/1261\n  healthChecks: [];\n}\n\nexport type UserConfig = Omit<Config, 'root'> & {\n  reactNativePath: string | void;\n  // Additional project settings\n  project: {\n    android?: AndroidProjectParams;\n    ios?: IOSProjectParams;\n    [key: string]: any;\n  };\n};\n\nexport type UserDependencyConfig = {\n  // Additional dependency settings\n  dependency: Omit<DependencyConfig, 'name' | 'root'>;\n  // An array of commands that ship with the dependency\n  commands: Command[];\n  // An array of extra platforms to load\n  platforms: Config['platforms'];\n  // Additional health checks\n  healthChecks: [];\n};\n\nexport {\n  IOSProjectConfig,\n  IOSProjectParams,\n  IOSDependencyConfig,\n  IOSDependencyParams,\n  IOSProjectInfo,\n};\n\nexport {\n  AndroidProjectConfig,\n  AndroidProjectParams,\n  AndroidDependencyConfig,\n  AndroidDependencyParams,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAOA"}