{"version": 3, "names": ["LinkPressable", "children", "style", "onPress", "onLongPress", "onPressIn", "onPressOut", "to", "accessibilityRole", "rest", "Platform", "OS", "styles", "button", "e", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "preventDefault", "undefined", "DrawerItem", "props", "colors", "useTheme", "icon", "label", "labelStyle", "focused", "allowFontScaling", "activeTintColor", "primary", "inactiveTintColor", "Color", "text", "alpha", "rgb", "string", "activeBackgroundColor", "inactiveBackgroundColor", "pressColor", "pressOpacity", "testID", "accessibilityLabel", "borderRadius", "StyleSheet", "flatten", "color", "backgroundColor", "iconNode", "size", "container", "wrapper", "selected", "marginLeft", "marginVertical", "fontWeight", "create", "marginHorizontal", "overflow", "flexDirection", "alignItems", "padding", "marginRight", "flex", "display"], "sourceRoot": "../../../src", "sources": ["views/DrawerItem.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAQsB;AAAA;AAAA;AAAA;AAkFtB,MAAMA,aAAa,GAAG,QAgBhB;EAAA,IAhBiB;IACrBC,QAAQ;IACRC,KAAK;IACLC,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC,UAAU;IACVC,EAAE;IACFC,iBAAiB;IACjB,GAAGC;EAOL,CAAC;EACC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIJ,EAAE,EAAE;IAC/B;IACA;IACA,oBACE,oBAAC,YAAI,eACCE,IAAI;MACR,EAAE,EAAEF,EAAG;MACP,KAAK,EAAE,CAACK,MAAM,CAACC,MAAM,EAAEX,KAAK,CAAE;MAC9B,OAAO,EAAGY,CAAM,IAAK;QACnB,IACE,EAAEA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ,CAAC;QAAI;QACtDJ,CAAC,CAACD,MAAM,IAAI,IAAI,IAAIC,CAAC,CAACD,MAAM,KAAK,CAAC,CAAC,CAAC;QAAA,EACrC;UACAC,CAAC,CAACK,cAAc,EAAE;UAClBhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGW,CAAC,CAAC;QACd;MACF;MACA;MACA;MAAA;MACA,WAAW,EAAEV,WAAW,IAAIgB,SAAU;MACtC,SAAS,EAAEf,SAAS,IAAIe,SAAU;MAClC,UAAU,EAAEd,UAAU,IAAIc;IAAU,IAEnCnB,QAAQ,CACJ;EAEX,CAAC,MAAM;IACL,oBACE,oBAAC,2BAAiB,eACZQ,IAAI;MACR,iBAAiB,EAAED,iBAAkB;MACrC,OAAO,EAAEL;IAAQ,iBAEjB,oBAAC,iBAAI;MAAC,KAAK,EAAED;IAAM,GAAED,QAAQ,CAAQ,CACnB;EAExB;AACF,CAAC;;AAED;AACA;AACA;AACe,SAASoB,UAAU,CAACC,KAAY,EAAE;EAC/C,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAE7B,MAAM;IACJC,IAAI;IACJC,KAAK;IACLC,UAAU;IACVpB,EAAE;IACFqB,OAAO,GAAG,KAAK;IACfC,gBAAgB;IAChBC,eAAe,GAAGP,MAAM,CAACQ,OAAO;IAChCC,iBAAiB,GAAG,IAAAC,cAAK,EAACV,MAAM,CAACW,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,CAACC,MAAM,EAAE;IACjEC,qBAAqB,GAAG,IAAAL,cAAK,EAACH,eAAe,CAAC,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,CAACC,MAAM,EAAE;IACzEE,uBAAuB,GAAG,aAAa;IACvCrC,KAAK;IACLC,OAAO;IACPqC,UAAU;IACVC,YAAY;IACZC,MAAM;IACNC,kBAAkB;IAClB,GAAGlC;EACL,CAAC,GAAGa,KAAK;EAET,MAAM;IAAEsB,YAAY,GAAG;EAAE,CAAC,GAAGC,uBAAU,CAACC,OAAO,CAAC5C,KAAK,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM6C,KAAK,GAAGnB,OAAO,GAAGE,eAAe,GAAGE,iBAAiB;EAC3D,MAAMgB,eAAe,GAAGpB,OAAO,GAC3BU,qBAAqB,GACrBC,uBAAuB;EAE3B,MAAMU,QAAQ,GAAGxB,IAAI,GAAGA,IAAI,CAAC;IAAEyB,IAAI,EAAE,EAAE;IAAEtB,OAAO;IAAEmB;EAAM,CAAC,CAAC,GAAG,IAAI;EAEjE,oBACE,oBAAC,iBAAI;IACH,WAAW,EAAE;EAAM,GACftC,IAAI;IACR,KAAK,EAAE,CAACG,MAAM,CAACuC,SAAS,EAAE;MAAEP,YAAY;MAAEI;IAAgB,CAAC,EAAE9C,KAAK;EAAE,iBAEpE,oBAAC,aAAa;IACZ,MAAM,EAAEwC,MAAO;IACf,OAAO,EAAEvC,OAAQ;IACjB,KAAK,EAAE,CAACS,MAAM,CAACwC,OAAO,EAAE;MAAER;IAAa,CAAC,CAAE;IAC1C,kBAAkB,EAAED,kBAAmB;IACvC,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAE;MAAEU,QAAQ,EAAEzB;IAAQ,CAAE;IAC1C,UAAU,EAAEY,UAAW;IACvB,YAAY,EAAEC,YAAa;IAC3B,EAAE,EAAElC;EAAG,gBAEP,oBAAC,KAAK,CAAC,QAAQ,QACZ0C,QAAQ,eACT,oBAAC,iBAAI;IACH,KAAK,EAAE,CACLrC,MAAM,CAACc,KAAK,EACZ;MAAE4B,UAAU,EAAEL,QAAQ,GAAG,EAAE,GAAG,CAAC;MAAEM,cAAc,EAAE;IAAE,CAAC;EACpD,GAED,OAAO7B,KAAK,KAAK,QAAQ,gBACxB,oBAAC,iBAAI;IACH,aAAa,EAAE,CAAE;IACjB,gBAAgB,EAAEG,gBAAiB;IACnC,KAAK,EAAE,CACL;MACEkB,KAAK;MACLS,UAAU,EAAE;IACd,CAAC,EACD7B,UAAU;EACV,GAEDD,KAAK,CACD,GAEPA,KAAK,CAAC;IAAEqB,KAAK;IAAEnB;EAAQ,CAAC,CACzB,CACI,CACQ,CACH,CACX;AAEX;AAEA,MAAMhB,MAAM,GAAGiC,uBAAU,CAACY,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,gBAAgB,EAAE,EAAE;IACpBH,cAAc,EAAE,CAAC;IACjBI,QAAQ,EAAE;EACZ,CAAC;EACDP,OAAO,EAAE;IACPQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDpC,KAAK,EAAE;IACLqC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE;EACR,CAAC;EACDnD,MAAM,EAAE;IACNoD,OAAO,EAAE;EACX;AACF,CAAC,CAAC"}