load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")

fb_native.android_library(
    name = "soloader",
    visibility = ["PUBLIC"],
    exported_deps = [
        ":annotation-binary",
        ":nativeloader-binary",
        ":soloader-binary",
    ],
)

fb_native.prebuilt_jar(
    name = "annotation-binary",
    binary_jar = ":annotation-binary.jar",
)

fb_native.prebuilt_jar(
    name = "nativeloader-binary",
    binary_jar = ":nativeloader-binary.jar",
)

fb_native.android_prebuilt_aar(
    name = "soloader-binary",
    aar = ":soloader-binary-aar",
)

fb_native.remote_file(
    name = "annotation-binary.jar",
    sha1 = "dc58463712cb3e5f03d8ee5ac9743b9ced9afa77",
    url = "mvn:com.facebook.soloader:annotation:jar:0.10.4",
)

fb_native.remote_file(
    name = "nativeloader-binary.jar",
    sha1 = "171783d0f3f525fb9e18eb3af758c0b3aaeaed69",
    url = "mvn:com.facebook.soloader:nativeloader:jar:0.10.4",
)

fb_native.remote_file(
    name = "soloader-binary-aar",
    sha1 = "3fda45c99faf58310107fb8c0857e6a671b89854",
    url = "mvn:com.facebook.soloader:soloader:aar:0.10.4",
)
