{"version": 3, "names": ["openURLMiddleware", "req", "res", "next", "rawBody", "Error", "url", "JSON", "parse", "logger", "info", "launchDefaultBrowser", "end", "connect", "use", "rawBodyMiddleware"], "sources": ["../src/openURLMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\nimport {launchDefaultBrowser, logger} from '@react-native-community/cli-tools';\nimport connect from 'connect';\nimport rawBodyMiddleware from './rawBodyMiddleware';\n\n/**\n * Handle request from JS to open an arbitrary URL in Chrome\n */\nfunction openURLMiddleware(\n  req: http.IncomingMessage & {rawBody?: string},\n  res: http.ServerResponse,\n  next: (err?: any) => void,\n) {\n  if (!req.rawBody) {\n    return next(new Error('missing request body'));\n  }\n  const {url} = JSON.parse(req.rawBody);\n  logger.info(`Opening ${url}...`);\n  launchDefaultBrowser(url);\n  res.end('OK');\n}\n\nexport default connect().use(rawBodyMiddleware).use(openURLMiddleware);\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAoD;AATpD;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA,SAASA,iBAAiB,CACxBC,GAA8C,EAC9CC,GAAwB,EACxBC,IAAyB,EACzB;EACA,IAAI,CAACF,GAAG,CAACG,OAAO,EAAE;IAChB,OAAOD,IAAI,CAAC,IAAIE,KAAK,CAAC,sBAAsB,CAAC,CAAC;EAChD;EACA,MAAM;IAACC;EAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACP,GAAG,CAACG,OAAO,CAAC;EACrCK,kBAAM,CAACC,IAAI,CAAE,WAAUJ,GAAI,KAAI,CAAC;EAChC,IAAAK,gCAAoB,EAACL,GAAG,CAAC;EACzBJ,GAAG,CAACU,GAAG,CAAC,IAAI,CAAC;AACf;AAAC,eAEc,IAAAC,kBAAO,GAAE,CAACC,GAAG,CAACC,0BAAiB,CAAC,CAACD,GAAG,CAACd,iBAAiB,CAAC;AAAA"}