{"version": 3, "names": ["_mergeOptions", "_interopRequireDefault", "require", "obj", "__esModule", "default", "merge", "mergeOptions", "bind", "concatArrays", "ignoreUndefined", "mergeLocalStorageItem", "key", "value", "oldValue", "window", "localStorage", "getItem", "oldObject", "JSON", "parse", "newObject", "nextValue", "stringify", "setItem", "createPromise", "getValue", "callback", "Promise", "resolve", "reject", "err", "createPromiseAll", "promises", "processResult", "all", "then", "result", "errors", "AsyncStorage", "removeItem", "mergeItem", "clear", "getAllKeys", "numberOfKeys", "length", "keys", "i", "push", "flushGetRequests", "undefined", "multiGet", "map", "multiSet", "keyValuePairs", "item", "multiRemove", "multiMerge", "_default", "exports"], "sources": ["AsyncStorage.ts"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport mergeOptions from \"merge-options\";\nimport type {\n  AsyncStorageStatic,\n  MultiCallback,\n  MultiGetCallback,\n} from \"./types\";\n\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype OnMultiResult = Function;\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype OnResult = Function;\n\nconst merge = mergeOptions.bind({\n  concatArrays: true,\n  ignoreUndefined: true,\n});\n\nfunction mergeLocalStorageItem(key: string, value: string) {\n  const oldValue = window.localStorage.getItem(key);\n  if (oldValue) {\n    const oldObject = JSON.parse(oldValue);\n    const newObject = JSON.parse(value);\n    const nextValue = JSON.stringify(merge(oldObject, newObject));\n    window.localStorage.setItem(key, nextValue);\n  } else {\n    window.localStorage.setItem(key, value);\n  }\n}\n\nfunction createPromise<Result, Callback extends OnResult>(\n  getValue: () => Result,\n  callback?: Callback\n): Promise<Result> {\n  return new Promise((resolve, reject) => {\n    try {\n      const value = getValue();\n      callback?.(null, value);\n      resolve(value);\n    } catch (err) {\n      callback?.(err);\n      reject(err);\n    }\n  });\n}\n\nfunction createPromiseAll<\n  ReturnType,\n  Result,\n  ResultProcessor extends OnMultiResult\n>(\n  promises: Promise<Result>[],\n  callback?: MultiCallback | MultiGetCallback,\n  processResult?: ResultProcessor\n): Promise<ReturnType> {\n  return Promise.all(promises).then(\n    (result) => {\n      const value = processResult?.(result) ?? null;\n      callback?.(null, value);\n      return Promise.resolve(value);\n    },\n    (errors) => {\n      callback?.(errors);\n      return Promise.reject(errors);\n    }\n  );\n}\n\nconst AsyncStorage: AsyncStorageStatic = {\n  /**\n   * Fetches `key` value.\n   */\n  getItem: (key, callback) => {\n    return createPromise(() => window.localStorage.getItem(key), callback);\n  },\n\n  /**\n   * Sets `value` for `key`.\n   */\n  setItem: (key, value, callback) => {\n    return createPromise(\n      () => window.localStorage.setItem(key, value),\n      callback\n    );\n  },\n\n  /**\n   * Removes a `key`\n   */\n  removeItem: (key, callback) => {\n    return createPromise(() => window.localStorage.removeItem(key), callback);\n  },\n\n  /**\n   * Merges existing value with input value, assuming they are stringified JSON.\n   */\n  mergeItem: (key, value, callback) => {\n    return createPromise(() => mergeLocalStorageItem(key, value), callback);\n  },\n\n  /**\n   * Erases *all* AsyncStorage for the domain.\n   */\n  clear: (callback) => {\n    return createPromise(() => window.localStorage.clear(), callback);\n  },\n\n  /**\n   * Gets *all* keys known to the app, for all callers, libraries, etc.\n   */\n  getAllKeys: (callback) => {\n    return createPromise(() => {\n      const numberOfKeys = window.localStorage.length;\n      const keys: string[] = [];\n      for (let i = 0; i < numberOfKeys; i += 1) {\n        const key = window.localStorage.key(i) || \"\";\n        keys.push(key);\n      }\n      return keys;\n    }, callback);\n  },\n\n  /**\n   * (stub) Flushes any pending requests using a single batch call to get the data.\n   */\n  flushGetRequests: () => undefined,\n\n  /**\n   * multiGet resolves to an array of key-value pair arrays that matches the\n   * input format of multiSet.\n   *\n   *   multiGet(['k1', 'k2']) -> [['k1', 'val1'], ['k2', 'val2']]\n   */\n  multiGet: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.getItem(key));\n    const processResult = (result: string[]) =>\n      result.map((value, i) => [keys[i], value]);\n    return createPromiseAll(promises, callback, processResult);\n  },\n\n  /**\n   * Takes an array of key-value array pairs.\n   *   multiSet([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiSet: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.setItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Delete all the keys in the `keys` array.\n   */\n  multiRemove: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.removeItem(key));\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Takes an array of key-value array pairs and merges them with existing\n   * values, assuming they are stringified JSON.\n   *\n   *   multiMerge([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiMerge: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.mergeItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n};\n\nexport default AsyncStorage;\n"], "mappings": ";;;;;;AAQA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAyC,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AARzC;AACA;AACA;AACA;AACA;AACA;AACA;;AASA;;AAEA;;AAGA,MAAMG,KAAK,GAAGC,qBAAY,CAACC,IAAI,CAAC;EAC9BC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE;AACnB,CAAC,CAAC;AAEF,SAASC,qBAAqBA,CAACC,GAAW,EAAEC,KAAa,EAAE;EACzD,MAAMC,QAAQ,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC;EACjD,IAAIE,QAAQ,EAAE;IACZ,MAAMI,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC;IACtC,MAAMO,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACP,KAAK,CAAC;IACnC,MAAMS,SAAS,GAAGH,IAAI,CAACI,SAAS,CAACjB,KAAK,CAACY,SAAS,EAAEG,SAAS,CAAC,CAAC;IAC7DN,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEU,SAAS,CAAC;EAC7C,CAAC,MAAM;IACLP,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEC,KAAK,CAAC;EACzC;AACF;AAEA,SAASY,aAAaA,CACpBC,QAAsB,EACtBC,QAAmB,EACF;EACjB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI;MACF,MAAMjB,KAAK,GAAGa,QAAQ,CAAC,CAAC;MACxBC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,IAAI,EAAEd,KAAK,CAAC;MACvBgB,OAAO,CAAChB,KAAK,CAAC;IAChB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGI,GAAG,CAAC;MACfD,MAAM,CAACC,GAAG,CAAC;IACb;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,gBAAgBA,CAKvBC,QAA2B,EAC3BN,QAA2C,EAC3CO,aAA+B,EACV;EACrB,OAAON,OAAO,CAACO,GAAG,CAACF,QAAQ,CAAC,CAACG,IAAI,CAC9BC,MAAM,IAAK;IACV,MAAMxB,KAAK,GAAG,CAAAqB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGG,MAAM,CAAC,KAAI,IAAI;IAC7CV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,IAAI,EAAEd,KAAK,CAAC;IACvB,OAAOe,OAAO,CAACC,OAAO,CAAChB,KAAK,CAAC;EAC/B,CAAC,EACAyB,MAAM,IAAK;IACVX,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGW,MAAM,CAAC;IAClB,OAAOV,OAAO,CAACE,MAAM,CAACQ,MAAM,CAAC;EAC/B,CACF,CAAC;AACH;AAEA,MAAMC,YAAgC,GAAG;EACvC;AACF;AACA;EACEtB,OAAO,EAAEA,CAACL,GAAG,EAAEe,QAAQ,KAAK;IAC1B,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC,EAAEe,QAAQ,CAAC;EACxE,CAAC;EAED;AACF;AACA;EACEH,OAAO,EAAEA,CAACZ,GAAG,EAAEC,KAAK,EAAEc,QAAQ,KAAK;IACjC,OAAOF,aAAa,CAClB,MAAMV,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEC,KAAK,CAAC,EAC7Cc,QACF,CAAC;EACH,CAAC;EAED;AACF;AACA;EACEa,UAAU,EAAEA,CAAC5B,GAAG,EAAEe,QAAQ,KAAK;IAC7B,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAACwB,UAAU,CAAC5B,GAAG,CAAC,EAAEe,QAAQ,CAAC;EAC3E,CAAC;EAED;AACF;AACA;EACEc,SAAS,EAAEA,CAAC7B,GAAG,EAAEC,KAAK,EAAEc,QAAQ,KAAK;IACnC,OAAOF,aAAa,CAAC,MAAMd,qBAAqB,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEc,QAAQ,CAAC;EACzE,CAAC;EAED;AACF;AACA;EACEe,KAAK,EAAGf,QAAQ,IAAK;IACnB,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAAC0B,KAAK,CAAC,CAAC,EAAEf,QAAQ,CAAC;EACnE,CAAC;EAED;AACF;AACA;EACEgB,UAAU,EAAGhB,QAAQ,IAAK;IACxB,OAAOF,aAAa,CAAC,MAAM;MACzB,MAAMmB,YAAY,GAAG7B,MAAM,CAACC,YAAY,CAAC6B,MAAM;MAC/C,MAAMC,IAAc,GAAG,EAAE;MACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,IAAI,CAAC,EAAE;QACxC,MAAMnC,GAAG,GAAGG,MAAM,CAACC,YAAY,CAACJ,GAAG,CAACmC,CAAC,CAAC,IAAI,EAAE;QAC5CD,IAAI,CAACE,IAAI,CAACpC,GAAG,CAAC;MAChB;MACA,OAAOkC,IAAI;IACb,CAAC,EAAEnB,QAAQ,CAAC;EACd,CAAC;EAED;AACF;AACA;EACEsB,gBAAgB,EAAEA,CAAA,KAAMC,SAAS;EAEjC;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAEA,CAACL,IAAI,EAAEnB,QAAQ,KAAK;IAC5B,MAAMM,QAAQ,GAAGa,IAAI,CAACM,GAAG,CAAExC,GAAG,IAAK2B,YAAY,CAACtB,OAAO,CAACL,GAAG,CAAC,CAAC;IAC7D,MAAMsB,aAAa,GAAIG,MAAgB,IACrCA,MAAM,CAACe,GAAG,CAAC,CAACvC,KAAK,EAAEkC,CAAC,KAAK,CAACD,IAAI,CAACC,CAAC,CAAC,EAAElC,KAAK,CAAC,CAAC;IAC5C,OAAOmB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,EAAEO,aAAa,CAAC;EAC5D,CAAC;EAED;AACF;AACA;AACA;EACEmB,QAAQ,EAAEA,CAACC,aAAa,EAAE3B,QAAQ,KAAK;IACrC,MAAMM,QAAQ,GAAGqB,aAAa,CAACF,GAAG,CAAEG,IAAI,IACtChB,YAAY,CAACf,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CACvC,CAAC;IACD,OAAOvB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;EACE6B,WAAW,EAAEA,CAACV,IAAI,EAAEnB,QAAQ,KAAK;IAC/B,MAAMM,QAAQ,GAAGa,IAAI,CAACM,GAAG,CAAExC,GAAG,IAAK2B,YAAY,CAACC,UAAU,CAAC5B,GAAG,CAAC,CAAC;IAChE,OAAOoB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE8B,UAAU,EAAEA,CAACH,aAAa,EAAE3B,QAAQ,KAAK;IACvC,MAAMM,QAAQ,GAAGqB,aAAa,CAACF,GAAG,CAAEG,IAAI,IACtChB,YAAY,CAACE,SAAS,CAACc,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CACzC,CAAC;IACD,OAAOvB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C;AACF,CAAC;AAAC,IAAA+B,QAAA,GAEanB,YAAY;AAAAoB,OAAA,CAAAtD,OAAA,GAAAqD,QAAA"}