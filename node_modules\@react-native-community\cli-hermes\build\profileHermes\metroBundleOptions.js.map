{"version": 3, "names": ["getMetroBundleOptions", "downloadedProfileFilePath", "host", "options", "platform", "dev", "minify", "contents", "JSON", "parse", "fs", "readFileSync", "encoding", "matchBundleUrl", "containsExpoDevMenu", "hadMatch", "frame", "Object", "values", "stackFrames", "name", "includes", "match", "exec", "parsed", "URL", "searchParams", "get", "logger", "warn", "e"], "sources": ["../../src/profileHermes/metroBundleOptions.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport type {HermesCPUProfile} from 'hermes-profile-transformer/dist/types/HermesProfile';\n\nexport interface MetroBundleOptions {\n  platform: string;\n  dev: boolean;\n  minify: boolean;\n  host: string;\n}\n\nexport function getMetroBundleOptions(\n  downloadedProfileFilePath: string,\n  host: string,\n): MetroBundleOptions {\n  let options: MetroBundleOptions = {\n    platform: 'android',\n    dev: true,\n    minify: false,\n    host,\n  };\n\n  try {\n    const contents: HermesCPUProfile = JSON.parse(\n      fs.readFileSync(downloadedProfileFilePath, {\n        encoding: 'utf8',\n      }),\n    );\n    const matchBundleUrl = /^.*\\((.*index\\.bundle.*)\\)/;\n    let containsExpoDevMenu = false;\n    let hadMatch = false;\n    for (const frame of Object.values(contents.stackFrames)) {\n      if (frame.name.includes('EXDevMenuApp')) {\n        containsExpoDevMenu = true;\n      }\n      const match = matchBundleUrl.exec(frame.name);\n      if (match) {\n        const parsed = new URL(match[1]);\n        const platform = parsed.searchParams.get('platform'),\n          dev = parsed.searchParams.get('dev'),\n          minify = parsed.searchParams.get('minify');\n        if (platform) {\n          options.platform = platform;\n        }\n        if (dev) {\n          options.dev = dev === 'true';\n        }\n        if (minify) {\n          options.minify = minify === 'true';\n        }\n\n        hadMatch = true;\n        break;\n      }\n    }\n    if (containsExpoDevMenu && !hadMatch) {\n      logger.warn(`Found references to the Expo Dev Menu in your profiling sample.\nYou might have accidentally recorded the Expo Dev Menu instead of your own application.\nTo work around this, please reload your app twice before starting a profiler recording.`);\n    }\n  } catch (e) {\n    throw e;\n  }\n\n  return options;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAUb,SAASA,qBAAqB,CACnCC,yBAAiC,EACjCC,IAAY,EACQ;EACpB,IAAIC,OAA2B,GAAG;IAChCC,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbJ;EACF,CAAC;EAED,IAAI;IACF,MAAMK,QAA0B,GAAGC,IAAI,CAACC,KAAK,CAC3CC,aAAE,CAACC,YAAY,CAACV,yBAAyB,EAAE;MACzCW,QAAQ,EAAE;IACZ,CAAC,CAAC,CACH;IACD,MAAMC,cAAc,GAAG,4BAA4B;IACnD,IAAIC,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,QAAQ,GAAG,KAAK;IACpB,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACC,MAAM,CAACX,QAAQ,CAACY,WAAW,CAAC,EAAE;MACvD,IAAIH,KAAK,CAACI,IAAI,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;QACvCP,mBAAmB,GAAG,IAAI;MAC5B;MACA,MAAMQ,KAAK,GAAGT,cAAc,CAACU,IAAI,CAACP,KAAK,CAACI,IAAI,CAAC;MAC7C,IAAIE,KAAK,EAAE;QACT,MAAME,MAAM,GAAG,IAAIC,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,MAAMlB,QAAQ,GAAGoB,MAAM,CAACE,YAAY,CAACC,GAAG,CAAC,UAAU,CAAC;UAClDtB,GAAG,GAAGmB,MAAM,CAACE,YAAY,CAACC,GAAG,CAAC,KAAK,CAAC;UACpCrB,MAAM,GAAGkB,MAAM,CAACE,YAAY,CAACC,GAAG,CAAC,QAAQ,CAAC;QAC5C,IAAIvB,QAAQ,EAAE;UACZD,OAAO,CAACC,QAAQ,GAAGA,QAAQ;QAC7B;QACA,IAAIC,GAAG,EAAE;UACPF,OAAO,CAACE,GAAG,GAAGA,GAAG,KAAK,MAAM;QAC9B;QACA,IAAIC,MAAM,EAAE;UACVH,OAAO,CAACG,MAAM,GAAGA,MAAM,KAAK,MAAM;QACpC;QAEAS,QAAQ,GAAG,IAAI;QACf;MACF;IACF;IACA,IAAID,mBAAmB,IAAI,CAACC,QAAQ,EAAE;MACpCa,kBAAM,CAACC,IAAI,CAAE;AACnB;AACA,wFAAwF,CAAC;IACrF;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,MAAMA,CAAC;EACT;EAEA,OAAO3B,OAAO;AAChB"}