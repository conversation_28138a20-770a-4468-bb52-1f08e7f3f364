load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "rn_android_library")

rn_android_library(
    name = "yoga",
    srcs = glob(["yoga/**/*.java"]),
    autoglob = False,
    language = "JAVA",
    visibility = ["PUBLIC"],
    deps = [
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
    ],
)
