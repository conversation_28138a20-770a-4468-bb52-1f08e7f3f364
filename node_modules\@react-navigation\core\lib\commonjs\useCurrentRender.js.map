{"version": 3, "names": ["useCurrentRender", "state", "navigation", "descriptors", "current", "React", "useContext", "CurrentRenderContext", "isFocused", "options", "routes", "index", "key"], "sourceRoot": "../../src", "sources": ["useCurrentRender.tsx"], "mappings": ";;;;;;AACA;AAEA;AAA0D;AAAA;AAAA;AAiB1D;AACA;AACA;AACA;AACe,SAASA,gBAAgB,OAI5B;EAAA,IAJ6B;IACvCC,KAAK;IACLC,UAAU;IACVC;EACO,CAAC;EACR,MAAMC,OAAO,GAAGC,KAAK,CAACC,UAAU,CAACC,6BAAoB,CAAC;EAEtD,IAAIH,OAAO,IAAIF,UAAU,CAACM,SAAS,EAAE,EAAE;IACrCJ,OAAO,CAACK,OAAO,GAAGN,WAAW,CAACF,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG,CAAC,CAACH,OAAO;EACtE;AACF"}